# React + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh


## Install

{{yarn install}}

This will install all the dependencies and the project will be ready to run.

## Usage

For Dev mode works on port 5173
{{yarn dev}}

For Build mode
{{yarn build}}

Storybook works on port 6006
{{yarn storybook}}