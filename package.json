{"name": "adoptionv2-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "dev:localhost": "vite --mode localhost", "dev:development": "vite --mode development", "build": "vite build", "build:dev": "VITE_APP_ENV=development vite build", "build:prod": "VITE_APP_ENV=production vite build", "build:preprod": "VITE_APP_ENV=preprod vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@azure/msal-browser": "^4.5.1", "@azure/msal-react": "^3.0.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.3.0", "@mui/material": "^6.3.0", "@mui/styled-engine-sc": "^6.3.0", "@react-pdf/renderer": "^4.2.1", "@reduxjs/toolkit": "^2.5.0", "@tinymce/tinymce-react": "^5.1.1", "axios": "^1.7.9", "chart.js": "^4.4.7", "dompurify": "^3.2.5", "driver.js": "^1.3.6", "hls.js": "^1.5.7", "html-react-parser": "^5.1.8", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.2", "i18next-http-backend": "^3.0.2", "i18next-react": "^0.2.2", "i18next-resources-to-backend": "^1.2.1", "jwt-decode": "^4.0.0", "pdfjs-dist": "3.11.174", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.0", "react-markdown": "^9.0.3", "react-player": "^2.16.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.1", "react-sortablejs": "^6.1.4", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^11.0.3", "react-youtube": "^10.1.0", "redux-persist": "^6.0.0", "redux-saga": "^1.3.0", "sortablejs": "^1.15.6", "styled-components": "^6.1.13", "swiper": "^11.2.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@iconify/react": "^5.1.0", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/addon-onboarding": "8.4.7", "@storybook/blocks": "8.4.7", "@storybook/react": "^8.4.7", "@storybook/react-vite": "^8.4.7", "@storybook/test": "8.4.7", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "eslint-plugin-storybook": "^0.11.1", "globals": "^15.14.0", "prettier": "^3.4.2", "prop-types": "15.8.1", "sass": "^1.83.0", "storybook": "8.4.7", "vite": "^6.0.5"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}