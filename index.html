<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Loading.</title>
  <script>
    // Dinamik loading dots efekti için JavaScript
    let dotCount = 1;
    const maxDots = 3;

    function updateLoadingTitle() {
      const dots = '.'.repeat(dotCount);
      document.title = 'Loading' + dots;
      dotCount = dotCount >= maxDots ? 1 : dotCount + 1;
    }

    // Her 500ms'de bir title'ı güncelle
    const loadingInterval = setInterval(updateLoadingTitle, 100);

    // Sayfa yüklendiğinde interval'ı temizle
    window.addEventListener('load', () => {
      clearInterval(loadingInterval);
    });
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/index.jsx"></script>
</body>

</html>