import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Ortam değişkenlerini yükle
  const env = loadEnv(mode, process.cwd(), '');

  console.log(`Building for ${mode} environment`);

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': '/src',
        '@components': '/src/components',
        '@domains': '/src/domains',
        '@hooks': '/src/hooks',
        '@utils': '/src/utils',
      },
    },
    define: {
      // Ortama göre doğru API URL'sini tanımla
      'process.env.VITE_API_URL': JSON.stringify(
        mode === 'development'
          ? env.VITE_API_URL_DEV
          : mode === 'preprod'
            ? env.VITE_API_URL_PREPROD
            : env.VITE_API_URL_PROD
      ),
      'process.env.VITE_CDS_API_URL': JSON.stringify(
        mode === 'development'
          ? env.VITE_CDS_API_URL_DEV
          : mode === 'preprod'
            ? env.VITE_CDS_API_URL_PREPROD
            : env.VITE_CDS_API_URL_PROD
      ),
    },
    preview: {
      allowedHosts: [
        'client',
        'localhost',
        'adoptionv2dev.aibusinessschool.com',
        'adoptionv2preprod.aibusinessschool.com',
        'adoptionv2.aibusinessschool.com',
      ],
    },
    server: { host: true, port: 5173 },
  };
});
