/** @type { import('@storybook/react-vite').StorybookConfig } */
const config = {
  stories: ['../src/**/*.stories.@(js|jsx)'],
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials'
  ],
  framework: {
    name: '@storybook/react-vite',
    options: {},
  },
  docs: {
    autodocs: true,
  },
  viteFinal: async (config) => {
    // Add JSX support for .js files
    config.esbuild = {
      loader: 'jsx',
      include: /.*\.jsx?$/,
      exclude: [],
    };
    return config;
  },
};

export default config;
