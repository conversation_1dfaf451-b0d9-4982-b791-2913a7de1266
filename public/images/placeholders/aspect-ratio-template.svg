<svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient arka plan -->
  <defs>
    <linearGradient id="gradientBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f0f8ff" />
      <stop offset="100%" stop-color="#a6d4fa" />
    </linearGradient>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#3498db" stroke-width="0.5" stroke-opacity="0.2"/>
    </pattern>
  </defs>
  
  <!-- Ana arka plan -->
  <rect width="100%" height="100%" fill="url(#gradientBg)" />
  
  <!-- Grid overlay -->
  <rect width="100%" height="100%" fill="url(#grid)" />
  
  <!-- Merkezdeki artı işareti -->
  <line x1="200" y1="150" x2="200" y2="250" stroke="#3498db" stroke-width="2" />
  <line x1="150" y1="200" x2="250" y2="200" stroke="#3498db" stroke-width="2" />
  
  <!-- Dört köşedeki işaretçiler -->
  <path d="M 50,50 L 50,80 M 50,50 L 80,50" stroke="#3498db" stroke-width="2" fill="none" />
  <path d="M 350,50 L 350,80 M 350,50 L 320,50" stroke="#3498db" stroke-width="2" fill="none" />
  <path d="M 50,350 L 50,320 M 50,350 L 80,350" stroke="#3498db" stroke-width="2" fill="none" />
  <path d="M 350,350 L 350,320 M 350,350 L 320,350" stroke="#3498db" stroke-width="2" fill="none" />
  
  <!-- Orta noktalar -->
  <circle cx="200" cy="50" r="4" fill="#3498db" />
  <circle cx="200" cy="350" r="4" fill="#3498db" />
  <circle cx="50" cy="200" r="4" fill="#3498db" />
  <circle cx="350" cy="200" r="4" fill="#3498db" />
</svg> 