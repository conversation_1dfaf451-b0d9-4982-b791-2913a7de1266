<svg width="320" height="180" viewBox="0 0 320 180" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient arka plan -->
  <defs>
    <linearGradient id="gradientBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f0f8ff" />
      <stop offset="100%" stop-color="#a6d4fa" />
    </linearGradient>
    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
      <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#3498db" stroke-width="0.5" stroke-opacity="0.2"/>
    </pattern>
  </defs>
  
  <!-- Ana arka plan -->
  <rect width="100%" height="100%" fill="url(#gradientBg)" />
  
  <!-- Grid overlay -->
  <rect width="100%" height="100%" fill="url(#grid)" />
  
  <!-- Merkezdeki artı işareti -->
  <line x1="160" y1="65" x2="160" y2="115" stroke="#3498db" stroke-width="2" />
  <line x1="135" y1="90" x2="185" y2="90" stroke="#3498db" stroke-width="2" />
  
  <!-- Dört köşedeki işaretçiler -->
  <path d="M 25,25 L 25,40 M 25,25 L 40,25" stroke="#3498db" stroke-width="2" fill="none" />
  <path d="M 295,25 L 295,40 M 295,25 L 280,25" stroke="#3498db" stroke-width="2" fill="none" />
  <path d="M 25,155 L 25,140 M 25,155 L 40,155" stroke="#3498db" stroke-width="2" fill="none" />
  <path d="M 295,155 L 295,140 M 295,155 L 280,155" stroke="#3498db" stroke-width="2" fill="none" />
  
  <!-- Geniş boyut metni -->
  <text x="160" y="170" text-anchor="middle" font-family="Arial" font-size="10" font-weight="bold" fill="#3498db">16:9 · 1792×1024 px</text>
</svg> 