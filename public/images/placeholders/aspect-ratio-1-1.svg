<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient arka plan -->
  <defs>
    <linearGradient id="gradientBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f0f8ff" />
      <stop offset="100%" stop-color="#a6d4fa" />
    </linearGradient>
    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
      <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#3498db" stroke-width="0.5" stroke-opacity="0.2"/>
    </pattern>
  </defs>
  
  <!-- Ana arka plan -->
  <rect width="100%" height="100%" fill="url(#gradientBg)" />
  
  <!-- Grid overlay -->
  <rect width="100%" height="100%" fill="url(#grid)" />
  
  <!-- Merkezdeki artı işareti -->
  <line x1="100" y1="75" x2="100" y2="125" stroke="#3498db" stroke-width="2" />
  <line x1="75" y1="100" x2="125" y2="100" stroke="#3498db" stroke-width="2" />
  
  <!-- Dört köşedeki işaretçiler -->
  <path d="M 25,25 L 25,40 M 25,25 L 40,25" stroke="#3498db" stroke-width="2" fill="none" />
  <path d="M 175,25 L 175,40 M 175,25 L 160,25" stroke="#3498db" stroke-width="2" fill="none" />
  <path d="M 25,175 L 25,160 M 25,175 L 40,175" stroke="#3498db" stroke-width="2" fill="none" />
  <path d="M 175,175 L 175,160 M 175,175 L 160,175" stroke="#3498db" stroke-width="2" fill="none" />
  
  <!-- Kare boyut metni -->
  <text x="100" y="190" text-anchor="middle" font-family="Arial" font-size="10" font-weight="bold" fill="#3498db">1:1 · 1024×1024 px</text>
</svg> 