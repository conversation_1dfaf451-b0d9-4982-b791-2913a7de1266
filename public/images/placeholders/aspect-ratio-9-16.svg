<svg width="180" height="320" viewBox="0 0 180 320" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient arka plan -->
  <defs>
    <linearGradient id="gradientBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f0f8ff" />
      <stop offset="100%" stop-color="#a6d4fa" />
    </linearGradient>
    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
      <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#3498db" stroke-width="0.5" stroke-opacity="0.2"/>
    </pattern>
  </defs>
  
  <!-- Ana arka plan -->
  <rect width="100%" height="100%" fill="url(#gradientBg)" />
  
  <!-- Grid overlay -->
  <rect width="100%" height="100%" fill="url(#grid)" />
  
  <!-- Merkezdeki artı işareti -->
  <line x1="90" y1="135" x2="90" y2="185" stroke="#3498db" stroke-width="2" />
  <line x1="65" y1="160" x2="115" y2="160" stroke="#3498db" stroke-width="2" />
  
  <!-- Dört köşedeki işaretçiler -->
  <path d="M 25,25 L 25,40 M 25,25 L 40,25" stroke="#3498db" stroke-width="2" fill="none" />
  <path d="M 155,25 L 155,40 M 155,25 L 140,25" stroke="#3498db" stroke-width="2" fill="none" />
  <path d="M 25,295 L 25,280 M 25,295 L 40,295" stroke="#3498db" stroke-width="2" fill="none" />
  <path d="M 155,295 L 155,280 M 155,295 L 140,295" stroke="#3498db" stroke-width="2" fill="none" />
  
  <!-- Portre boyut metni -->
  <text x="90" y="310" text-anchor="middle" font-family="Arial" font-size="10" font-weight="bold" fill="#3498db">9:16 · 1024×1792 px</text>
</svg> 