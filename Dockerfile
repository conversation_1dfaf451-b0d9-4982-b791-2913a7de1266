FROM node:20-alpine

WORKDIR /app

# Önce package.json ve yarn.lock dosyalarını kopyalayın
COPY /adoption-v2-frontend/package.json /adoption-v2-frontend/yarn.lock ./

# Bağımlılıkları yükleyin
RUN yarn install

# Sonra geri kalan dosyaları kopyalayın
COPY /adoption-v2-frontend ./

# Build ortamı için argüman tanımlama
ARG BUILD_ENV=production
ENV BUILD_ENV=$BUILD_ENV
ENV VITE_APP_ENV=$BUILD_ENV

# Build ortamını göster
RUN echo "Building with BUILD_ENV=$BUILD_ENV and VITE_APP_ENV=$VITE_APP_ENV"

# Ortama göre build işlemini gerçekleştir
RUN if [ "$BUILD_ENV" = "development" ]; then \
    echo "Building for DEVELOPMENT environment" && \
    yarn build:dev; \
    elif [ "$BUILD_ENV" = "preprod" ]; then \
    echo "Building for PRE-PRODUCTION environment" && \
    yarn build:preprod; \
    elif [ "$BUILD_ENV" = "production" ]; then \
    echo "Building for PRODUCTION environment" && \
    yarn build:prod; \
    else \
    echo "BUILD_ENV is not recognized, using default (production)" && \
    yarn build:prod; \
    fi

EXPOSE 4173

# Başlatma komutunu da yarn ile değiştirin
CMD ["yarn", "start", "--host"]