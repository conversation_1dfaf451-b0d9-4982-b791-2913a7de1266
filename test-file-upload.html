<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-data {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        pre {
            background: #fff;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>File Upload Test for ContentBlock Component</h1>
    
    <div class="instructions">
        <h2>Test Instructions</h2>
        <p>To test the file upload functionality:</p>
        <ol>
            <li>Use the test data below in your form configuration</li>
            <li>Navigate to a course that uses forms in your application</li>
            <li>The form should now render a file upload field</li>
            <li>Test uploading different file types (images, PDFs, documents)</li>
            <li>Verify file size validation (max 10MB)</li>
            <li>Test file type validation</li>
            <li>Submit the form and check that file information is included</li>
        </ol>
    </div>

    <div class="test-data">
        <h2>Test Form Data Structure</h2>
        <p>Use this JSON structure to test the file upload functionality:</p>
        <pre><code>{
    "_id": "6835795af53e3c9f57446fdd",
    "title": "File Upload Test Form",
    "description": "Testing file upload functionality",
    "topics": [
        {
            "title": "File Upload Test Topic",
            "description": "Test topic with file upload field",
            "fields": [
                {
                    "validation": {
                        "min": null,
                        "max": null,
                        "pattern": "",
                        "message": ""
                    },
                    "name": "test_file_upload",
                    "label": "Upload Test Files",
                    "type": "file",
                    "description": "Please upload one or more test files",
                    "required": true,
                    "options": [],
                    "fields": [],
                    "conditional_logic": {
                        "enabled": false,
                        "rules": [
                            [
                                {
                                    "field": "",
                                    "operator": "has_any_value",
                                    "value": "",
                                    "_id": "6835794df53e3c9f57446ce5"
                                }
                            ]
                        ],
                        "_id": "6835794df53e3c9f57446ce4"
                    },
                    "min": null,
                    "max": null,
                    "_id": "6835794df53e3c9f57446ce3"
                },
                {
                    "validation": {
                        "min": null,
                        "max": null,
                        "pattern": "",
                        "message": ""
                    },
                    "name": "optional_file_upload",
                    "label": "Optional File Upload",
                    "type": "file",
                    "description": "This file upload is optional",
                    "required": false,
                    "options": [],
                    "fields": [],
                    "conditional_logic": {
                        "enabled": false,
                        "rules": [],
                        "_id": "6835794df53e3c9f57446ce6"
                    },
                    "min": null,
                    "max": null,
                    "_id": "6835794df53e3c9f57446ce4"
                }
            ],
            "_id": "6835794df53e3c9f57446ce2"
        }
    ],
    "fields": []
}</code></pre>
    </div>

    <div class="test-data">
        <h2>Expected Behavior</h2>
        <ul>
            <li><strong>File Upload Area:</strong> Should display a dashed border area with upload icon and instructions</li>
            <li><strong>Click to Upload:</strong> Clicking the area should open file selection dialog</li>
            <li><strong>Multiple Files:</strong> Should support selecting multiple files at once</li>
            <li><strong>File Validation:</strong> Should validate file size (max 10MB) and file types</li>
            <li><strong>File Display:</strong> Should show uploaded files as chips with file name and size</li>
            <li><strong>File Removal:</strong> Should allow removing individual files with delete button</li>
            <li><strong>Required Validation:</strong> Should show error if required field has no files</li>
            <li><strong>Form Submission:</strong> Should include file information in form submission</li>
            <li><strong>Disabled State:</strong> Should disable upload when form is submitted</li>
        </ul>
    </div>

    <div class="test-data">
        <h2>Supported File Types</h2>
        <ul>
            <li>Images: JPG, PNG, GIF, WebP</li>
            <li>Documents: PDF, DOC, DOCX, TXT, CSV</li>
        </ul>
    </div>

    <div class="test-data">
        <h2>Error Cases to Test</h2>
        <ul>
            <li>Upload file larger than 10MB</li>
            <li>Upload unsupported file type (e.g., .exe, .zip)</li>
            <li>Submit form without uploading required files</li>
            <li>Try to upload files after form submission</li>
        </ul>
    </div>
</body>
</html>
