<svg width="481" height="497" viewBox="0 0 481 497" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_115_38)">
<mask id="mask0_115_38" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="481" height="497">
<path d="M121.788 409.08C116.762 406.625 114.059 409.802 114.059 417.519C114.059 425.236 114.353 452.972 120.365 464.769C125.914 475.659 134.768 484.513 145.658 490.062C155.121 494.883 166.819 496.019 186.281 496.287C239.879 493.897 286.785 465.778 314.946 423.982C317.446 420.271 314.696 415.425 310.221 415.425H182.276C164.51 415.425 137.823 416.912 121.788 409.08ZM99.7499 170.45C99.7499 162.554 89.3099 159.725 85.3629 166.564C68.7727 195.301 52.1807 224.038 35.5869 252.773C-4.86314 322.82 9.07087 416.753 87.0459 469.607C96.6919 476.146 104.409 465.749 102.537 457.606C98.5559 440.96 99.7499 422.478 99.7499 405.393V170.45ZM410.215 243.856C410.602 249.436 414.705 250.189 421.388 246.33C428.072 242.472 451.944 228.349 459.155 217.244C465.812 206.993 469.052 194.899 468.413 182.693C467.857 172.087 462.992 161.388 453.493 144.4C424.624 99.177 376.819 72.615 326.542 69.126C322.078 68.816 319.256 73.62 321.494 77.495L385.467 188.3C394.349 203.685 408.981 226.053 410.215 243.856ZM214.572 382.254C207.734 386.202 210.504 396.657 218.4 396.657C239.174 396.656 271.756 396.655 317.947 396.659C398.835 396.666 473.216 337.632 480.002 243.677C480.841 232.054 467.979 230.569 461.863 236.262C449.438 248.032 432.835 256.24 418.039 264.782L214.572 382.254ZM126.794 78.177C131.432 75.053 130.033 71.123 123.35 67.264C116.667 63.406 92.4989 49.793 79.2769 49.101C67.0714 48.4613 54.9766 51.7018 44.7259 58.358C35.8189 64.143 28.9859 73.706 19.0229 90.426C-5.70614 138.039 -4.80714 192.72 17.3089 238.006C19.2729 242.027 24.8439 242.068 27.0819 238.193L91.0539 127.389C99.9369 112.003 111.993 88.148 126.794 78.177ZM344.475 178.411C351.313 182.359 358.982 174.733 355.034 167.895C344.646 149.905 328.355 121.688 305.263 81.683C264.825 11.629 176.51 -23.27 91.7489 17.831C81.2629 22.915 86.4089 34.797 94.3969 37.247C110.803 42.123 126.212 52.397 141.008 60.94L344.475 178.411Z" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_115_38)">
<path d="M-36.4263 -24.273H519.191V523.627H-36.4263V-24.273Z" fill="#28A7E5"/>
<g filter="url(#filter0_f_115_38)">
<path d="M675.94 854.685C966.266 854.685 1201.62 619.329 1201.62 329.003C1201.62 38.6768 966.266 -196.679 675.94 -196.679C385.614 -196.679 150.258 38.6768 150.258 329.003C150.258 619.329 385.614 854.685 675.94 854.685Z" fill="#9F59FF"/>
</g>
<g filter="url(#filter1_f_115_38)">
<path d="M666.928 339.018C794.395 339.018 897.728 235.685 897.728 108.218C897.728 -19.2494 794.395 -122.582 666.928 -122.582C539.461 -122.582 436.128 -19.2494 436.128 108.218C436.128 235.685 539.461 339.018 666.928 339.018Z" fill="#FF2FC5"/>
</g>
<g filter="url(#filter2_f_115_38)">
<path d="M-154.128 840.184C5.69415 840.184 135.256 710.622 135.256 550.8C135.256 390.978 5.69415 261.416 -154.128 261.416C-313.951 261.416 -443.512 390.978 -443.512 550.8C-443.512 710.622 -313.951 840.184 -154.128 840.184Z" fill="#42C7F1"/>
</g>
</g>
<g style="mix-blend-mode:overlay" opacity="0.6">
<path d="M99.7688 381.351V170.441C99.7688 162.545 89.3168 159.745 85.3688 166.582L32.2578 258.573L99.7688 381.351Z" fill="url(#paint0_linear_115_38)"/>
<path d="M397.477 276.684L214.823 382.14C207.985 386.087 210.786 396.54 218.681 396.54H324.903L397.477 276.684Z" fill="url(#paint1_linear_115_38)"/>
<path d="M161.704 72.775L344.358 178.23C351.195 182.178 358.847 174.526 354.899 167.689L301.788 75.698L161.704 72.775Z" fill="url(#paint2_linear_115_38)"/>
</g>
</g>
<defs>
<filter id="filter0_f_115_38" x="-312.756" y="-659.693" width="1977.39" height="1977.39" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="231.507" result="effect1_foregroundBlur_115_38"/>
</filter>
<filter id="filter1_f_115_38" x="275.92" y="-282.79" width="782.016" height="782.016" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="80.104" result="effect1_foregroundBlur_115_38"/>
</filter>
<filter id="filter2_f_115_38" x="-635.26" y="69.668" width="962.264" height="962.264" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="95.874" result="effect1_foregroundBlur_115_38"/>
</filter>
<linearGradient id="paint0_linear_115_38" x1="62.2468" y1="141.641" x2="62.2468" y2="380.866" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_115_38" x1="208.643" y1="429.035" x2="415.818" y2="309.422" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_115_38" x1="388.06" y1="160.135" x2="180.885" y2="40.5221" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_115_38">
<rect width="481" height="497" fill="white"/>
</clipPath>
</defs>
</svg>
