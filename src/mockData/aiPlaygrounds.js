import React from 'react';
import ChatGPTIcon from '../assets/images/logos/chatgpt.svg';
import MSCopilotIcon from '../assets/images/logos/ms-copilot.svg';
import DALLEIcon from '../assets/images/logos/dall-e.svg';
import HeyGenIcon from '../assets/images/logos/heygen.svg';
import ChatGPTForDevelopersIcon from '../assets/images/logos/chatgpt-logo.png';
import GithubCopilotIcon from '../assets/images/logos/github-copilot-icon.svg';

export const aiPlaygrounds = [
  {
    id: 'pg1',
    icon: ChatGPTIcon,
    iconAlt: 'ChatGPT',
    iconStyle: { width: 40, height: 40 },
    title: 'ChatGPT',
    translations: {
      title: {
        en: 'ChatGPT',
        de: 'ChatGPT',
      },
      description: {
        en: "Try ChatGPT's language capabilities, get a quick crash course on prompting, download cheatsheets, and practice with prompts.",
        de: '<PERSON>rnen und üben Sie effektives Prompting mit dem Pionier der GenAI.',
      },
    },
    description:
      "Try ChatGPT's language capabilities, get a quick crash course on prompting, download cheatsheets, and practice with prompts.",
    variant: 'playground',
    buttonURL: '/playgrounds/chatgpt/',
    buttonType: 'URL',
    newTab: false,
    sorting: {
      default_order: 1,
      rules: [
        {
          order: 3,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'pg2',
    icon: MSCopilotIcon,
    iconAlt: 'Microsoft Copilot',
    iconStyle: { width: 40, height: 40 },
    title: 'Microsoft Copilot',
    translations: {
      title: {
        en: 'Microsoft Copilot',
        de: 'Microsoft Copilot',
      },
      description: {
        en: 'Access Microsoft Copilot trainings, learn to build your own copilots, explore the prompt library, and try out.',
        de: 'Probieren Sie einzigartige, funktionsspezifische und interaktive Trainings-Playgrounds aus.',
      },
    },
    description:
      'Access Microsoft Copilot trainings, learn to build your own copilots, explore the prompt library, and try out.',
    variant: 'playground',
    buttonURL: '/microsoft-copilot/',
    buttonType: 'URL',
    newTab: false,
    sorting: {
      default_order: 2,
      rules: [
        {
          order: 1,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'pg3',
    icon: DALLEIcon,
    iconAlt: 'DALL-E',
    iconStyle: { width: 40, height: 40 },
    translations: {
      title: {
        en: 'DALL-E',
        de: 'DALL-E',
      },
      description: {
        en: 'Generate tailored images from text inputs, discover top tips & prompts to refine visuals for your slides or creative projects.',
        de: 'Erstellen Sie individuelle Bilder aus Text, entdecken Sie Tipps zur Verfeinerung Ihrer Visualisierungen.',
      },
    },
    variant: 'playground',
    buttonURL: '/playgrounds/dall-e/',
    buttonType: 'URL',
    newTab: false,
    condition: [
      {
        field: 'technical_background_label',
        value: 'it-specialist',
        operator: '!=',
      },
    ],
  },
  {
    id: 'pg4',
    icon: HeyGenIcon,
    iconAlt: 'HeyGen',
    iconStyle: { width: 40, height: 40 },
    translations: {
      title: {
        en: 'HeyGen',
        de: 'HeyGen',
      },
      description: {
        en: 'Convert scripts into videos, deploy tailored AI avatars, create videos clips in multiple languages.',
        de: 'Konvertieren Sie Skripte in Videos, erstellen Sie maßgeschneiderte AI-Avatare und erzeugen Sie Videoclips in mehreren Sprachen.',
      },
    },
    variant: 'playground',
    buttonURL: '/playgrounds/heygen/',
    buttonType: 'URL',
    newTab: false,

    condition: [
      {
        field: 'technical_background_label',
        value: 'it-specialist',
        operator: '!=',
      },
    ],
  },
  {
    id: 'pg5',
    icon: GithubCopilotIcon,
    iconAlt: 'GitHub Copilot',
    iconStyle: { width: 40, height: 40 },
    translations: {
      title: {
        en: 'GitHub Copilot',
        de: 'GitHub Copilot',
      },
      description: {
        en: 'Access training to use GitHub Copilot, write and refine code with AI-powered suggestions.',
        de: 'Automatisierte Code-Entwicklung und -Optimierung lernen und anwenden.',
      },
    },
    variant: 'playground',
    buttonURL: '/tools/github-copilot/',
    buttonType: 'URL',
    newTab: false,
    condition: [
      {
        field: 'technical_background_label',
        value: 'it-specialist',
        operator: '==',
      },
    ],

    sorting: {
      default_order: 5,
      rules: [
        {
          order: 2,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'pg6',
    icon: ChatGPTForDevelopersIcon,
    iconAlt: 'ChatGPT for Developers',
    iconStyle: { width: 40, height: 40 },
    translations: {
      title: {
        en: 'ChatGPT for Developers',
        de: 'ChatGPT für Entwickler',
      },
      description: {
        en: 'Leverage ChatGPT for coding, efficient debugging, and innovative solution-building.',
        de: 'Nutzen Sie ChatGPT für Programmierung, effizientes Debugging und innovative Lösungsentwicklung.',
      },
    },
    variant: 'playground',
    buttonURL: '/playgrounds/chatgpt-developer-tools/',
    buttonType: 'URL',
    newTab: false,
    condition: [
      {
        field: 'technical_background_label',
        value: 'it-specialist',
        operator: '==',
      },
    ],

    sorting: {
      default_order: 6,
      rules: [
        {
          order: 4,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
];
