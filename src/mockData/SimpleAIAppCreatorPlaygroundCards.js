import LibraryBooksOutlinedIcon from '@mui/icons-material/LibraryBooksOutlined';
import SchoolOutlinedIcon from '@mui/icons-material/SchoolOutlined';
import EmojiObjectsOutlinedIcon from '@mui/icons-material/EmojiObjectsOutlined';
import i18n from 'i18next';

// Dillere göre kart bilgileri
const cardsData = {
  en: [
    {
      title: 'Simple AI App Creator tutorial',
      description:
        'Learn how to create powerful AI solutions with just a few commands with our step-by-step guide.',
      tag: 'Learn',
      icon: SchoolOutlinedIcon,
      link: '/tutorials/simple-ai-app-creator/',
      buttonText: 'Start learning',
    },
    {
      title: 'Simple AI App Creator tool',
      description: 'Effortlessly create unique AI applications with just a few clicks.',
      tag: 'Try',
      icon: LibraryBooksOutlinedIcon,
      link: '/simple-ai-app/create/',
      buttonText: 'Use now',
    },
    {
      title: 'App examples',
      description: 'Explore a range of apps built with this tool for inspiration.',
      tag: 'Apply',
      icon: EmojiObjectsOutlinedIcon,
      link: '/ai-usecases/',
      buttonText: 'View examples',
    },
  ],
  de: [
    {
      title: 'Anleitung',
      description:
        'Mit unserer Schritt-für-Schritt-Anleitung lernen Sie, wie einfache AI-Apps in nur wenigen Schritten erstellt werden können.',
      tag: 'Lernen',
      icon: SchoolOutlinedIcon,
      link: '/tutorials/simple-ai-app-creator/',
      buttonText: 'Jetzt lernen',
    },
    {
      title: 'Ausprobieren',
      description: 'Erstellen Sie mühelos mit wenigen Klicks einzigartige AI Apps.',
      tag: 'Ausprobieren',
      icon: LibraryBooksOutlinedIcon,
      link: '/simple-ai-app/create/',
      buttonText: 'Jetzt ausprobieren',
    },
    {
      title: 'App-Beispiele',
      description: 'Lassen Sie sich von App-Beispielen inspirieren.',
      tag: 'Anwendung',
      icon: EmojiObjectsOutlinedIcon,
      link: '/ai-usecases/',
      buttonText: 'Beispiele ansehen',
    },
  ],
};

// i18n.language değerini kullanarak mevcut dile göre doğru kartları döndür
export const SimpleAIAppCreatorPlaygroundCards = () => {
  const currentLanguage = i18n.language || 'en';
  // Eğer mevcut dil için kart seti yoksa İngilizce setini varsayılan olarak kullan
  return cardsData[currentLanguage] || cardsData.en;
};
