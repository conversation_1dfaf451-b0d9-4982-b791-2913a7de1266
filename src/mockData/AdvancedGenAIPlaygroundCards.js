import LibraryBooksOutlinedIcon from '@mui/icons-material/LibraryBooksOutlined';
import SchoolOutlinedIcon from '@mui/icons-material/SchoolOutlined';
import i18n from 'i18next';

// Dillere göre kart bilgileri
const cardsData = {
  en: [
    {
      title: '«Advanced GenAI creator» tutorial',
      description:
        'Learn how to create powerful AI solutions with just a few commands with our step-by-step guide.',
      tag: 'Learn',
      icon: SchoolOutlinedIcon,
      link: '/tutorials/advanced-genai-creator',
      buttonText: 'Start learning',
    },
    {
      title: '«Advanced GenAI creator» tool',
      description:
        'Effortlessly build advanced AI-powered solutions using simple prompts. From simple chatbots to intelligent apps, explore the full potential of Generative AI—all in one place.',
      tag: 'Try',
      icon: LibraryBooksOutlinedIcon,
      link: '/genai-stack',
      buttonText: 'Use now',
    },
  ],
  de: [
    {
      title: 'Anleitung',
      description:
        'Unsere  Schritt-für-Schritt-Anleitung, um zu lernen, wie Sie mit einigen der besten AI Tools der Welt leistungsstarke, eigene AI Lösungen erstellen können.',
      tag: 'Lernen',
      icon: SchoolOutlinedIcon,
      link: '/tutorials/advanced-genai-creator',
      buttonText: 'Jetzt lernen',
    },
    {
      title: 'Ausprobieren',
      description: 'Erstellen Sie ihre eigenen AI Apps, Chatbots, Workflows oder Agenten.',
      tag: 'Anwenden',
      icon: LibraryBooksOutlinedIcon,
      link: '/genai-stack',
      buttonText: 'Jetzt ausprobieren',
    },
  ],
};

// i18n.language değerini kullanarak mevcut dile göre doğru kartları döndür
export const AdvancedGenAIPlaygroundCards = () => {
  const currentLanguage = i18n.language || 'en';
  // Eğer mevcut dil için kart seti yoksa İngilizce setini varsayılan olarak kullan
  return cardsData[currentLanguage] || cardsData.en;
};
