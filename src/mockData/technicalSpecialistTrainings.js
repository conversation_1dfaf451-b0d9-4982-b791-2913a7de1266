export const technicalSpecialistTrainings = [
  {
    id: 'tech-spec-001',
    image: 'https://aibsassets.blob.core.windows.net/img/core-ai-essentials.jpg',
    title: 'Core AI Essentials: Machine and Deep Learning',
    description:
      ' Lay the foundation with a deep dive into classical AI techniques. Explore core supervised and unsupervised algorithms, neural network architectures, optimization methods, and best practices in model evaluation and feature engineering. ',
    lock: true,
    visible: true,
    slug: 'core-ai-essentials',
    translation: {
      en: {
        title: 'Core AI Essentials: Machine and Deep Learning',
        description:
          'Lay the foundation with a deep dive into classical AI techniques. Explore core supervised and unsupervised algorithms, neural network architectures, optimization methods, and best practices in model evaluation and feature engineering.',
      },
      de: {
        title: 'AI Grundlagen: Maschinelles Lernen und Deep Learning',
        description:
          'Legen Sie den Grundstein mit tiefen Einblicken in klassische AI-Techniken. Entdecken Sie Supervised und Unsupervised Algorithmen, neuronale Netzwerkarchitekturen, Optimierungsmethoden und Best Practices in der Modellbewertung und im Feature Engineering.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 1,
      rules: [
        {
          order: 1,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'tech-spec-002',
    image:
      'https://aibsassets.blob.core.windows.net/img/mastering-prompt-engineering-and-llm-fine-tuning.jpg',
    title: 'Mastering Prompt Engineering and LLM Fine-Tuning',
    description:
      'Learn to craft effective prompts, apply chain-of-thought strategies, and perform targeted fine-tuning.',
    lock: true,
    visible: true,
    slug: 'prompt-engineering-llm-fine-tuning',
    translation: {
      en: {
        title: 'Mastering Prompt Engineering and LLM Fine-Tuning',
        description:
          'Learn to craft effective prompts, apply chain-of-thought strategies, and perform targeted fine-tuning on pre-trained large language models—maximizing relevance, accuracy, and reliability in your AI-driven applications.',
      },
      de: {
        title: 'Prompt Engineering und LLM Fine-Tuning meistern',
        description:
          'Lernen Sie, effektive Prompts zu erstellen, Chain-of-Thought Strategien anzuwenden und vortrainierte große Sprachmodelle gezielt zu optimieren – für maximale Relevanz, Genauigkeit und Zuverlässigkeit Ihrer AI-gesteuerten Anwendungen.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 2,
      rules: [
        {
          order: 2,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'tech-spec-003',
    image: 'https://aibsassets.blob.core.windows.net/img/genai-software.jpg',
    title: 'Integrating GenAI into Software Engineering Workflows',
    description: 'Seamlessly embed generative AI into your development lifecycle.',
    lock: true,
    visible: true,
    slug: 'genai-software-engineering-workflows',
    translation: {
      en: {
        title: 'Integrating GenAI into Software Engineering Workflows',
        description:
          'Seamlessly embed generative AI into your development lifecycle: from IDE plugins and code-completion tools to automated pull-request reviews and CI/CD pipelines, accelerating delivery while ensuring code quality.',
      },
      de: {
        title: 'GenAI in Software-Engineering-Workflows integrieren',
        description:
          'Integrieren Sie generative AI nahtlos in Ihren Entwicklungslebenszyklus: von IDE-Plugins und Code-Vervollständigungstools bis hin zu automatisierten Pull-Request-Reviews und CI/CD-Pipelines, um die Bereitstellung zu beschleunigen und gleichzeitig die Codequalität sicherzustellen.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 3,
      rules: [
        {
          order: 3,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'tech-spec-004',
    image: 'https://aibsassets.blob.core.windows.net/img/ai-agents.jpg',
    title: 'Designing and Deploying Autonomous AI Agents',
    description:
      'Architect self-governing agents capable of multi-step reasoning and dynamic decision-making.',
    lock: true,
    visible: true,
    slug: 'autonomous-ai-agents',
    translation: {
      en: {
        title: 'Designing and Deploying Autonomous AI Agents',
        description:
          'Architect self-governing agents capable of multi-step reasoning and dynamic decision-making. Cover agent frameworks, orchestration patterns, safety constraints, and real-world deployment strategies for autonomous workflows.',
      },
      de: {
        title: 'Autonome AI Agenten entwerfen und einsetzen',
        description:
          'Entwickeln Sie autonome Agenten, die mehrstufiges Denken und dynamische Entscheidungsfindung ermöglichen. Befassen Sie sich mit Agenten-Frameworks, Orchestrierungsmustern, Sicherheitsbeschränkungen und praxisnahen Bereitstellungsstrategien für autonome Workflows.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 4,
      rules: [
        {
          order: 4,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'tech-spec-005',
    image: 'https://aibsassets.blob.core.windows.net/img/genai-enterprise.jpg',
    title: 'Scaling and Managing GenAI Solutions in Enterprise Environments',
    description:
      'Build robust, enterprise-grade GenAI platforms with a focus on scalability and governance.',
    lock: true,
    visible: true,
    slug: 'scaling-genai-enterprise',
    translation: {
      en: {
        title: 'Scaling and Managing GenAI Solutions in Enterprise Environments',
        description:
          'Build robust, enterprise-grade GenAI platforms with a focus on scalability, observability, cost optimization, and governance. Master containerization, orchestration (Kubernetes), monitoring stacks, and policy-driven compliance.',
      },
      de: {
        title: 'Skalierung und Management von GenAI in Unternehmensumgebungen',
        description:
          'Erstellen Sie robuste GenAI-Plattformen auf Unternehmensniveau mit Fokus auf Skalierbarkeit, Beobachtbarkeit, Kostenoptimierung und Governance. Meistern Sie Containerisierung, Orchestrierung (Kubernetes), Monitoring-Stacks und richtliniengesteuerte Compliance.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 5,
      rules: [
        {
          order: 5,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'tech-spec-006',
    image: 'https://aibsassets.blob.core.windows.net/img/genai-cloud.jpg',
    title: 'Applying GenAI in Cloud Infrastructure and Cybersecurity',
    description:
      'Harness generative models to enhance threat detection and automate incident response.',
    lock: true,
    visible: true,
    slug: 'genai-cloud-cybersecurity',
    translation: {
      en: {
        title: 'Applying GenAI in Cloud Infrastructure and Cybersecurity',
        description:
          'Harness generative models to enhance threat detection, automate incident response, and enforce secure infrastructure-as-code. Combine AI-driven anomaly detection with cloud-native security frameworks for resilient, self-healing systems.',
      },
      de: {
        title: 'GenAI in Cloud-Infrastrukturen und Cybersicherheit',
        description:
          'Nutzen Sie generative Modelle, um die Bedrohungserkennung zu verbessern, die Reaktion auf Vorfälle zu automatisieren und um sichere Infrastructure-as-Code durchzusetzen. Kombinieren Sie AI-gesteuerte Anomalie-Erkennung mit Cloud-nativen Sicherheits-Frameworks für robuste, selbstheilende Systeme.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 6,
      rules: [
        {
          order: 6,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
];
