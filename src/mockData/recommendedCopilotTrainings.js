import noThumbnail from '../assets/images/no-thumbnail.jpeg';

export const getRecommendedCopilotTrainings = (userFunction) => {
  const trainings = [
    {
      id: 1,
      title: 'Copilot Prompting',
      description: 'Learn to craft effective and contextual prompts for Microsoft 365 Copilot that create, simplify, transform, and compile content across Microsoft 365 applications.',
      buttonText: 'View',
      buttonType: 'URL',
      buttonURL: '/course/67d7f6672238849a6dc0f1cf',
      newTab: false,
      imageSrc: noThumbnail,
      locked: false
    },
    {
      id: 2,
      title: 'Copilot Studio',
      description: 'Learn how to use some methods you can use to enhance your Microsoft Copilot Studio agents and integrate other technologies to provide the experience you want.',
      buttonText: 'View',
      buttonType: 'URL',
      buttonURL: '/training/copilot/studio',
      newTab: false,
      imageSrc: noThumbnail,
      locked: true,
      tooltipText: "This part is not included in your current subscription scheme – please contact your AI Business School account manager for more information."
    }
  ];
  
  if (userFunction) {
    const slug = typeof userFunction === 'object' ? userFunction.slug : userFunction;
    const functionName = typeof userFunction === 'object' ? 
      (userFunction.translations?.en || userFunction.slug) : userFunction;
    
    if (slug === 'sales' || slug === 'customer-care-after-sales') {
      let title = '';
      let description = '';
      if (slug === 'sales') {
        title = `Copilot for ${functionName}`;
        description = 'Learn about Copilot for Sales, which integrates with CRM tools to provide Sales insights directly within the tools Sales professionals use daily, such as Microsoft Outlook, Teams, and other Microsoft 365 apps.';
      } else if (slug === 'customer-care-after-sales') {
        title = `Microsoft 365 Copilot for Service`;
        description = 'Copilot for Service brings together Microsoft 365 Copilot with a role-based Copilot agent that enhances service experiences by adding generative AI to your existing contact center. ';
      }
      
      trainings.push({
        id: 3,
        title: title,
        description: description,
        buttonText: 'View',
        buttonType: 'URL',
        buttonURL: `/training/copilot/${slug}`,
        newTab: false,
        imageSrc: noThumbnail,
        locked: true,
        tooltipText: "This part is not included in your current subscription scheme – please contact your AI Business School account manager for more information."
      });
    }
  }
  
  trainings.push({
    id: trainings.length + 1,
    title: 'Copilot for Power Platform',
    description: 'Gain experience using Copilot to build solutions and apps with Microsoft Power Platform to quickly solve business problems.',
    buttonText: 'View',
    buttonType: 'URL',
    buttonURL: '/training/copilot/power-platform',
    newTab: false,
    imageSrc: noThumbnail,
    locked: true,
    tooltipText: "This part is not included in your current subscription scheme – please contact your AI Business School account manager for more information."
  });
  
  return trainings;
};

export const recommendedCopilotTrainings = getRecommendedCopilotTrainings(); 