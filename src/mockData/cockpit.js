export const cockpitData = {
  aiScore: {
    score: 850,
    level: 'AI Beginner',
  },
  statistics: [
    {
      value: 12,
      label: 'cockpit.statistics.appliedTotalUseCases',
    },
    {
      value: 3,
      label: 'cockpit.statistics.createdApp',
    },
    {
      value: 5,
      label: 'cockpit.statistics.createdWorkflow',
    },
    {
      value: 8,
      label: 'cockpit.statistics.appliedUniqueUseCases',
    },
    {
      value: 4,
      label: 'cockpit.statistics.submittedIdeas',
    },
    {
      value: 2,
      label: 'cockpit.statistics.obtainedCertificates',
    },
    {
      value: 3,
      label: 'cockpit.statistics.completedTrainingJourneys',
      isLocked: true,
    },
  ],
  favoriteUseCases: [
    {
      id: 'fav1',
      title: 'Sales innovation idea generator',
      usecase_icon_url:
        'https://cdn.aibusinessschool.com/usecases/2024-09-21_13-18-48-svgviewer-output-2024-09-21T161829.918.svg',
      usedCount: 15,
      url: '/apply/sales-innovation-idea-generator',
    },
    {
      id: 'fav2',
      title: 'Customer engagement tactics',
      usecase_icon_url:
        'https://cdn.aibusinessschool.com/usecases/2024-09-11_09-55-28-svgviewer-output-79.svg',
      usedCount: 12,
      url: '/apply/customer-engagement-tactics',
    },
    {
      id: 'fav3',
      title: 'Email follow-up assistant',
      usecase_icon_url:
        'https://cdn.aibusinessschool.com/usecases/2024-09-11_09-55-28-svgviewer-output-79.svg',
      usedCount: 8,
      url: '/apply/email-analysis-follow-up-creator',
    },
    {
      id: 'fav4',
      title: 'Document analysis and summary',
      usecase_icon_url:
        'https://cdn.aibusinessschool.com/usecases/2024-06-04_09-22-24-report-svgrepo-com.svg',
      usedCount: 20,
      url: '/apply/document-analysis-and-summary',
    },
    {
      id: 'fav5',
      title: 'Meeting agenda creator',
      usecase_icon_url:
        'https://cdn.aibusinessschool.com/usecases/2024-09-20_08-01-47-svgviewer-output-2024-09-20T110135.108.svg',
      usedCount: 18,
      url: '/apply/meeting-agenda-creator',
    },
    {
      id: 'fav6',
      title: 'Sales jargon explainer',
      usecase_icon_url:
        'https://cdn.aibusinessschool.com/usecases/2024-06-03_11-05-09-cloud-1168.svg',
      usedCount: 25,
      url: '/apply/sales-jargon-explainer',
    },
  ],
  submittedIdeas: [
    {
      title: 'AI-Powered Customer Support Chatbot Implementation',
      description:
        'Implementing an AI-powered customer support chatbot can significantly enhance user experience by providing instant responses, reducing workload on human agents, ensuring 24/7 availability, and leveraging natural language processing (NLP) to understand and resolve customer queries efficiently.',
      badge: 'New',
      url: '#/submitted-ideas/ai-chatbot-implementation',
      date: 'February 18, 2024',
      icon: 'mdi:lightbulb',
    },
    {
      title: 'Automated Data Analysis Dashboard for Sales Teams',
      description:
        'Developing an Automated Data Analysis Dashboard for Sales Teams can revolutionize the decision-making process by aggregating real-time sales data, providing insightful visualizations, identifying trends and patterns through AI-driven analytics, and generating predictive forecasts.',
      url: '#/submitted-ideas/sales-dashboard',
      date: 'February 15, 2024',
      icon: 'mdi:lightbulb',
    },
    {
      title: 'AI-Enhanced Personalization Engine for E-commerce',
      description:
        'Creating an AI-Enhanced Personalization Engine for E-commerce platforms that analyzes customer behavior, purchase history, and preferences to deliver tailored product recommendations, personalized marketing communications, and customized shopping experiences.',
      url: '#/submitted-ideas/ecommerce-personalization',
      date: 'January 28, 2024',
      icon: 'mdi:lightbulb',
    },
    {
      title: 'Predictive Maintenance System for Manufacturing',
      description:
        'Developing a Predictive Maintenance System that uses IoT sensors and AI algorithms to monitor equipment health, predict failures before they occur, optimize maintenance schedules, and minimize costly downtime in manufacturing facilities.',
      url: '#/submitted-ideas/predictive-maintenance',
      date: 'January 15, 2024',
      icon: 'mdi:lightbulb',
    },
    {
      title: 'AI-Driven Talent Acquisition and Management Platform',
      description:
        'Building an AI-Driven Talent Acquisition and Management Platform that streamlines recruiting processes, identifies ideal candidates through advanced matching algorithms, reduces hiring biases, and provides insights for employee development and retention strategies.',
      url: '#/submitted-ideas/talent-acquisition',
      date: 'December 10, 2023',
      icon: 'mdi:lightbulb',
    },
    {
      title: 'Virtual Reality Training Simulator for Employee Onboarding',
      description:
        'Creating an immersive Virtual Reality Training Simulator for employee onboarding that provides hands-on experience in a risk-free environment, accelerates learning curves, standardizes training procedures, and improves knowledge retention through experiential learning.',
      url: '#/submitted-ideas/vr-training',
      date: 'November 28, 2023',
      icon: 'mdi:lightbulb',
    },
  ],
  frequentUseCases: [
    {
      id: 'freq1',
      title: 'Sales innovation idea generator',
      usecase_icon_url:
        'https://cdn.aibusinessschool.com/usecases/2024-09-21_13-18-48-svgviewer-output-2024-09-21T161829.918.svg',
      usedCount: 32,
      url: '/apply/sales-innovation-idea-generator',
    },
    {
      id: 'freq2',
      title: 'Document analysis and summary',
      usecase_icon_url:
        'https://cdn.aibusinessschool.com/usecases/2024-06-04_09-22-24-report-svgrepo-com.svg',
      usedCount: 28,
      url: '/apply/document-analysis-and-summary',
    },
    {
      id: 'freq3',
      title: 'Meeting agenda creator',
      usecase_icon_url:
        'https://cdn.aibusinessschool.com/usecases/2024-09-20_08-01-47-svgviewer-output-2024-09-20T110135.108.svg',
      usedCount: 25,
      url: '/apply/meeting-agenda-creator',
    },
    {
      id: 'freq4',
      title: 'Sales jargon explainer',
      usecase_icon_url:
        'https://cdn.aibusinessschool.com/usecases/2024-06-03_11-05-09-cloud-1168.svg',
      usedCount: 22,
      url: '/apply/sales-jargon-explainer',
    },
    {
      id: 'freq5',
      title: 'Customer engagement tactics',
      usecase_icon_url:
        'https://cdn.aibusinessschool.com/usecases/2024-09-11_09-55-28-svgviewer-output-79.svg',
      usedCount: 20,
      url: '/apply/customer-engagement-tactics',
    },
    {
      id: 'freq6',
      title: 'Email follow-up assistant',
      usecase_icon_url:
        'https://cdn.aibusinessschool.com/usecases/2024-09-11_09-55-28-svgviewer-output-79.svg',
      usedCount: 18,
      url: '/apply/email-analysis-follow-up-creator',
    },
  ],
  createdApps: [
    {
      title: 'Maintenance schedule optimizer',
      url: '#/apps/maintenance-scheduler-1',
    },

    {
      title: 'Job-specific use case training for marketing',
      url: '#/submitted-ideas/job-specific-training',
    },
    {
      title: 'Master journey assessment',
      url: '#/submitted-ideas/master-journey-assessment',
    },
    {
      title: 'Maintenance schedule optimizer',
      url: '#/apps/maintenance-scheduler-2',
    },
    {
      title: 'Maintenance schedule optimizer',
      url: '#/apps/maintenance-scheduler-3',
    },
    {
      title: 'Maintenance schedule optimizer',
      url: '#/apps/maintenance-scheduler-3',
    },
    {
      title: 'Master journey assessment',
      url: '#/submitted-ideas/master-journey-assessment',
    },
    {
      title: 'Maintenance schedule optimizer',
      url: '#/apps/maintenance-scheduler-2',
    },
    {
      title: 'Maintenance schedule optimizer',
      url: '#/apps/maintenance-scheduler-3',
    },
    {
      title: 'Maintenance schedule optimizer',
      url: '#/apps/maintenance-scheduler-3',
    },
    {
      title: 'Maintenance schedule optimizer',
      url: '#/apps/maintenance-scheduler-3',
    },
    {
      title: 'Maintenance schedule optimizer',
      url: '#/apps/maintenance-scheduler-3',
    },
  ],
  certificates: [
    {
      title: 'AI Beginner in IT, AI & Data assessment & certificate',
      action: 'View certificate',
      url: '#/certificates/ai-beginner',
      date: '2025/01/20 12:30',
    },
    {
      title: 'Successfully navigate AI risks',
      action: 'View certificate',
      url: '#/certificates/ai-risks',
      date: '2025/01/15 14:45',
    },
    {
      title: 'AI Beginner in IT, AI & Data assessment & certificate',
      action: 'View certificate',
      url: '#/certificates/ai-data',
      date: '2025/01/10 09:15',
    },
    {
      title: 'AI Beginner in IT, AI & Data assessment & certificate',
      action: 'View certificate',
      url: '#/certificates/ai-beginner',
      date: '2025/01/05 16:20',
    },
    {
      title: 'Successfully navigate AI risks',
      action: 'View certificate',
      url: '#/certificates/ai-risks',
      date: '2025/01/01 11:00',
    },
    {
      title: 'AI Beginner in IT, AI & Data assessment & certificate',
      action: 'View certificate',
      url: '#/certificates/ai-data',
      date: '2024/12/28 15:30',
    },
  ],
  technicalTrainings: [
    {
      id: 'tech1',
      title: 'Machine Learning Fundamentals',
      description:
        'Understanding core ML concepts, algorithms, and practical applications in business scenarios.',
      buttonType: 'URL',
      buttonURL: 'https://example.com/ml-fundamentals',
      imageSrc:
        'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-fauxels-3183197-1.jpg',
      courseCompany: 'microsoft',
      newTab: true,
    },
    {
      id: 'tech2',
      title: 'Natural Language Processing',
      description:
        'Deep dive into NLP technologies, text analysis, sentiment analysis, and language model implementation.',
      buttonType: 'URL',
      buttonURL: 'https://example.com/nlp-course',
      imageSrc:
        'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-fauxels-3184292-1.jpg',
      courseCompany: 'aibusinessschool',
      newTab: true,
    },
    {
      id: 'tech3',
      title: 'Computer Vision Applications',
      description:
        'Learn about image recognition, object detection, and visual AI applications in business contexts.',
      buttonType: 'URL',
      buttonURL: 'https://example.com/computer-vision',
      imageSrc:
        'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-mikael-blomkvist-6476783-1.jpg',
      courseCompany: 'microsoft',
      newTab: true,
    },
    {
      id: 'tech4',
      title: 'AI Model Deployment',
      description:
        'Master the process of deploying AI models to production, including scaling, monitoring, and maintenance.',
      buttonType: 'URL',
      buttonURL: 'https://example.com/ai-deployment',
      imageSrc:
        'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-divinetechygirl-1181359-1.jpg',
      courseCompany: 'aibusinessschool',
      newTab: true,
    },
  ],
};
