import GENAIThumbnail from '@/assets/images/GenAI_in_orijinal-4_enhanced.jpg';
import i18n from 'i18next';

const cardsData = {
  en: [
    {
      id: 'advanced-genai-creator',
      title: 'Advanced GenAI Creator',
      description:
        'Learn how to leverage some of the most powerful AI tools to create sophisticated AI apps, chatbots, workflows and agents',
      buttonText: 'Use now',
      buttonType: 'URL',
      buttonURL: '/playgrounds/advanced-genai-creator',
      imageSrc: GENAIThumbnail,
      newTab: false,
    },
    {
      id: 'open-agi',
      title: 'Open AGI',
      description:
        'Lets build our first Agent use cases by AI Planet, you can create powerful AI solutions with just a few commands',
      buttonText: 'Learn now',
      buttonType: 'URL',
      buttonURL: '/tools/microsoft/azure-ai-foundry',
      locked: true,
      tooltipText:
        'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
      imageSrc:
        'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/The-Evolution-of-AI-Introducing-Autonomous-AI-Agents-_3.jpg',
      newTab: false,
    },
  ],
  de: [
    {
      id: 'advanced-genai-creator',
      title: 'Advanced GenAI Creator',
      description:
        'Mit dieser hochmodernen Lösung von AI Planet können Sie mit einigen der besten AI Tools der Welt leistungsstarke, eigene AI Lösungen erstellen.',
      buttonText: 'Jetzt verwenden',
      buttonType: 'URL',
      buttonURL: '/playgrounds/advanced-genai-creator',
      imageSrc: GENAIThumbnail,
      newTab: false,
    },
    {
      id: 'open-agi',
      title: 'Open AGI',
      description:
        'Lasst uns unsere ersten Use Cases für Agenten erstellen! Mit AI Planet kannst du leistungsstarke AI-Lösungen mit nur wenigen Prompts erstellen.',
      buttonText: 'Jetzt verwenden',
      buttonType: 'URL',
      buttonURL: '/tools/microsoft/azure-ai-foundry',
      locked: true,
      tooltipText:
        'Dieser Teil ist nicht in Ihrem Zugangsschema enthalten - bitte kontaktieren Sie Ihren AI Business School Account Manager für weitere Informationen.',
      imageSrc:
        'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/The-Evolution-of-AI-Introducing-Autonomous-AI-Agents-_3.jpg',
      newTab: false,
    },
  ],
};

export const AIPlanetToolCards = () => {
  const currentLanguage = i18n.language || 'en';
  return cardsData[currentLanguage] || cardsData.en;
};
