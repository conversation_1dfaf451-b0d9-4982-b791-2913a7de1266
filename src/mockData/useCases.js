import FavoriteIcon from '@mui/icons-material/Favorite';
import GridViewIcon from '@mui/icons-material/GridView';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import BuildIcon from '@mui/icons-material/Build';
import { useSelector } from 'react-redux';

export const useCases = (user) => [
  {
    id: 'uc1',
    icon: FavoriteIcon,
    title: 'Your personal favorites',
    translations: {
      title: {
        en: 'Your personal favorites',
        de: 'Deine persönlichen Favoriten',
      },
      description: {
        en: 'Easy access to your favorite use cases, apps, workflows and agents.',
        de: 'Einfacher Zugriff auf deine bevorzugten Use Cases, Apps, Workflows und Agenten.',
      },
    },
    description: 'Easy access to your favorite use cases, apps, workflows and agents.',
    variant: 'favorite',
    buttonType: 'URL',
    buttonURL: '/apply#my-favorites',
    newTab: false,
    sorting: {
      default_order: 1,
      rules: [
        {
          order: 1,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
        {
          order: 1,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'uc2',
    icon: GridViewIcon,
    title: `${user?.onboarding?.function_label.translations?.en} use cases`,
    translations: {
      title: {
        en: `${user?.onboarding?.function_label.translations?.en} use cases`,
        de: `Use Cases für ${user?.onboarding?.function_label.translations?.de || user?.onboarding?.function_label.translations?.en}`,
      },
      description: {
        en: 'Discover and apply job-specific use cases.',
        de: 'Entdecke und wende berufsspezifische Use Cases an.',
      },
    },
    description: 'Discover and apply job-specific use cases.',
    variant: 'sales',
    buttonType: 'URL',
    buttonURL: '/ai-usecases',
    newTab: false,
    condition: [
      {
        field: 'function_label',
        value: user?.onboarding?.function_label.slug,
        operator: '==',
      },
    ],
    sorting: {
      default_order: 2,
      rules: [
        {
          order: 2,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },

        {
          order: 2,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
      ],
    },
  },
  {
    id: 'uc3',
    icon: ManageAccountsIcon,
    title: 'Management use cases',
    translations: {
      title: {
        en: 'Management use cases',
        de: 'Use Cases für Management',
      },
      description: {
        en: 'Discover and apply use cases tailored for daily management routines.',
        de: 'Entdecke und wende Use Cases für tägliche Management-Aufgaben an.',
      },
    },
    description: 'Discover and apply use cases tailored for daily management routines.',
    variant: 'management',
    buttonType: 'URL',
    buttonURL: '/apply',
    newTab: false,
    condition: [
      {
        field: 'management_role_label',
        value: 'yes',
        operator: '==',
      },
      {
        field: 'function_label',
        value: 'management-management-support',
        operator: '!=',
      },
    ],
  },

  {
    id: 'uc4',
    icon: GridViewIcon,
    title: 'IT use cases',
    translations: {
      title: {
        en: 'IT use cases',
        de: 'Use Cases für IT',
      },
      description: {
        en: 'Discover and apply use cases tailored for daily IT routines.',
        de: 'Entdecke und wende Use Cases für tägliche IT-Aufgaben an.  ',
      },
    },
    description: 'Discover and apply use cases tailored for daily IT routines.',
    variant: 'management',
    buttonType: 'URL',
    buttonURL: '/apply',
    newTab: false,
    condition: [
      {
        field: 'technical_background_label',
        value: 'it-specialist',
        operator: '==',
      },
      {
        field: 'function_label',
        value: 'it-ai-data',
        operator: '!=',
      },
    ],
  },
  {
    id: 'uc5',
    icon: BuildIcon,
    title: 'Specific toolkits',
    translations: {
      title: {
        en: 'Specific toolkits',
        de: 'Spezifische Toolkits',
      },
      description: {
        en: 'Easy access to use cases recommended by your company.',
        de: 'Einfacher Zugriff auf von Deinem Unternehmen empfohlene Use Cases.',
      },
      tooltipText: {
        en: 'This part is not included in your current subscription scheme – please contact your AI Business School account manager for more information.',
        de: 'Dieser Teil ist nicht in Ihrem aktuellen Abonnement enthalten - bitte kontaktieren Sie Ihren AI Business School Account Manager für weitere Informationen.',
      },
    },
    description: 'Easy access to use cases recommended by your company.',
    variant: 'toolkit',
    locked: true,
    tooltipText:
      'This part is not included in your current subscription scheme – please contact your AI Business School account manager for more information.',
    buttonType: 'URL',
    buttonURL: '/apply',
    newTab: false,
  },
];
