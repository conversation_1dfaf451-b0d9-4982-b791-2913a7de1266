import OpenAILogo from '../assets/images/logos/openai.svg';
import MicrosoftLogo from '../assets/images/logos/microsoft.svg';
import GitHubLogo from '../assets/images/logos/github.svg';
import AIPlanetLogo from '../assets/images/logos/aiplanet.svg';
import AIBusinessSchoolLogo from '../assets/images/logos/aibs.png';
import AWSLogo from '../assets/images/logos/aws-logo.svg';
import WorkdayLogo from '../assets/images/logos/workday.svg';
import SAPLogo from '../assets/images/logos/sap.svg';
import ProalphaLogo from '../assets/images/logos/proalpha.svg';
import UIPathLogo from '../assets/images/logos/uipath.svg';

export const techCompanies = [
  {
    id: 'tc1',
    image: OpenAILogo,
    title: 'OpenAI',
    buttonURL: '/tools/openai',
    buttonType: 'URL',
    variant: 'logo',
    class: 'openai',
  },
  {
    id: 'tc2',
    image: Microsoft<PERSON><PERSON>,
    title: 'Microsoft',
    buttonURL: '/tools/microsoft',
    buttonType: 'URL',
    class: 'microsoft',
    variant: 'logo',
  },
  {
    id: 'tc3',
    image: GitHubLogo,
    title: 'GitHub',
    buttonURL: '/tools/github-copilot',
    buttonType: 'URL',
    variant: 'logo',
    class: 'github',
  },
  {
    id: 'tc4',
    image: AIPlanetLogo,
    title: 'AI Planet',
    buttonURL: '/tools/ai-planet',
    buttonType: 'URL',
    variant: 'logo',
    class: 'aiplanet',
  },
  {
    id: 'tc5',
    image: AIBusinessSchoolLogo,
    title: 'AI Business School',
    buttonURL: '/tools/ai-business-school',
    buttonType: 'URL',
    variant: 'logo',
    class: 'aibs',
  },
  {
    id: 'tc6',
    image: AWSLogo,
    title: 'AWS',
    locked: true,
    tooltipText:
      'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
    variant: 'logo',
    class: 'aws-logo',
  },
  {
    id: 'tc7',
    image: WorkdayLogo,
    title: 'Workday',
    locked: true,
    tooltipText:
      'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
    variant: 'logo',
    class: 'workday',
  },
  {
    id: 'tc8',
    image: SAPLogo,
    title: 'SAP',
    locked: true,
    tooltipText:
      'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
    variant: 'logo',
    class: 'sap',
  },
  {
    id: 'tc9',
    image: ProalphaLogo,
    title: 'SAP',
    locked: true,
    tooltipText:
      'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
    variant: 'logo',
    class: 'proalpha',
  },
  {
    id: 'tc10',
    image: UIPathLogo,
    title: 'UIPath',
    locked: true,
    tooltipText:
      'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
    variant: 'logo',
    class: 'uipath',
  },
];
