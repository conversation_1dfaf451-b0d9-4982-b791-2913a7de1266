import LibraryBooksOutlinedIcon from '@mui/icons-material/LibraryBooksOutlined';
import SchoolOutlinedIcon from '@mui/icons-material/SchoolOutlined';
import i18n from 'i18next';

// Dillere göre kart bilgileri
const cardsData = {
  en: [
    {
      title: 'Agentic workflow creator tutorial',
      description:
        'Learn to create agentic workflows quickly with a simple, step-by-step tutorial.',
      tag: 'Learn',
      icon: SchoolOutlinedIcon,
      link: '/tutorials/agentic-workflow-creator/',
      buttonText: 'Start learning',
    },
    {
      title: 'Agentic workflow creator tool',
      description: 'Effortlessly build AI-powered agentic workflows using simple prompt flows.',
      tag: 'Try',
      icon: LibraryBooksOutlinedIcon,
      link: '/workflow-creator/',
      buttonText: 'Use now',
    },
  ],
  de: [
    {
      title: 'Anleitung',
      description:
        'Mit unserer Schritt-für-Schritt-Anleitung lernen Sie, spielerisch einfach erste Agentic Workflows zu erstellen.',
      tag: '<PERSON>rne<PERSON>',
      icon: SchoolOutlinedIcon,
      link: '/tutorials/agentic-workflow-creator/',
      buttonText: 'Jetzt lernen',
    },
    {
      title: 'Ausprobieren',
      description: 'Erstellen Sie mühelos ihre eigenen, einfachen Agentic Workflows.',
      tag: 'Ausprobieren',
      icon: LibraryBooksOutlinedIcon,
      link: '/workflow-creator/',
      buttonText: 'Jetzt ausprobieren',
    },
  ],
};

// i18n.language değerini kullanarak mevcut dile göre doğru kartları döndür
export const AgenticWorkflowCreatorPlaygroundCards = () => {
  const currentLanguage = i18n.language || 'en';
  // Eğer mevcut dil için kart seti yoksa İngilizce setini varsayılan olarak kullan
  return cardsData[currentLanguage] || cardsData.en;
};
