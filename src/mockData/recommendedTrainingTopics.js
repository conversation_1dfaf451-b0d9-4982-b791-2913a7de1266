export const recommendedTrainingTopics = [
  {
    id: '67cf0af6c1297ce0b4ff4ed8',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-googledeepmind-18069695-1.jpg',
    title: 'Prompting for beginners',
    description: 'Prompting crash course for beginners.',
    lock: false,
    visible: false,
    slug: 'effective-prompting-crash-course',
    translation: {
      en: {
        title: 'Prompting for beginners',
        description: 'Prompting crash course for beginners.',
      },
      de: {
        title: 'Prompting für Einsteiger',
        description: 'Prompting-Crashkurs für Anfänger.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 1,
      rules: [
        {
          order: 1,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 1,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 1,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d7edae2238849a6dbbf357',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-googledeepmind-18069697-1.jpg',
    title: 'Advanced prompting',
    description:
      'Advanced prompting techniques like zero-shot, recursive, chain-of-thought and more.',
    lock: false,
    visible: false,
    slug: 'advanced-prompt-engineering-lab',
    translation: {
      en: {
        title: 'Advanced Prompting',
        description:
          'Advanced prompting techniques like zero-shot, recursive, chain-of-thought and more.',
      },
      de: {
        title: 'Advanced Prompting',
        description:
          'Advanced prompting techniques like zero-shot, recursive, chain-of-thought and more.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 2,
      rules: [
        {
          order: 2,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 2,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 2,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67cf139cc1297ce0b4ffa55c',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-pixabay-373543-1.jpg',
    title: 'Foundations of AI',
    description:
      'Get familiar with basic AI definitions, its foundations and terminology as well as different types of AI.',
    lock: false,
    visible: false,
    slug: 'foundations-of-ai',
    translation: {
      en: {
        title: 'Foundations of AI',
        description:
          'Get familiar with basic AI definitions, its foundations and terminology as well as different types of AI.',
      },
      de: {
        title: 'Grundlagen der AI ',
        description:
          'Verschiedene Arten von AI, grundlegende Basisinformationen und wichtige Begriffe.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 3,
      rules: [
        {
          order: 3,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 3,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 3,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d42cbb0d0f8a59e2652e6a',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-cottonbro-8721318-1.jpg',
    title: 'AI technologies in action',
    description:
      'Discover the most relevant AI technologies like machine learning, deep learning, speech recognition, image recognition and more.',
    lock: false,
    visible: false,
    slug: 'ai-technologies-in-action',
    translation: {
      en: {
        title: 'AI Technologies in Action',
        description:
          'Discover the most relevant AI technologies like machine learning, deep learning, speech recognition, image recognition and more.',
      },
      de: {
        title: 'AI Technologien in Aktion',
        description:
          'Die relevantesten AI Technologien, wie z.B. Machine Learning, Deep Learning, Sprach- und Bilderkennung, Roboter und mehr.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 4,
      rules: [
        {
          order: 4,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 4,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 4,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67cf1ddfc1297ce0b4003f5f',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-ferarcosn-211151-1.jpg',
    title: 'Social impact and risks of AI',
    description:
      'Simple introduction to basic AI opportunities and risks for society as well as relevant ethical aspects and the need for regulation.',
    lock: false,
    visible: false,
    slug: 'social-impact-and-risks-of-ai',
    translation: {
      en: {
        title: 'Social Impact and Risks of AI',
        description:
          'Simple introduction to basic AI opportunities and risks for society as well as relevant ethical aspects and the need for regulation.',
      },
      de: {
        title: 'Gesellschaftliche Folgen und Risiken der AI',
        description:
          'Chancen und Risiken der AI für die Gesellschaft sowie ethische Aspekte und die Notwendigkeit für Regulierung.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 6,
      rules: [
        {
          order: 6,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 6,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 6,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d424c4285ece9f41786b1e',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-tara-winstead-8386369-1.jpg',
    title: 'The long-term role of humans vs. machines',
    description: 'Introduction to long-term scenarios for the coexistence of humans and machines.',
    lock: false,
    visible: false,
    slug: 'the-long-term-roles-of-humans-vs-machines',
    translation: {
      en: {
        title: 'The long-term role of humans vs. machines',
        description:
          'Introduction to long-term scenarios for the coexistence of humans and machines.',
      },
      de: {
        title: 'Das langfristige Miteinander von Mensch und Maschine',
        description: 'Langfristige Szenarien der Koexistenz von Mensch und Maschine.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 7,
      rules: [
        {
          order: 7,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 7,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 7,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d416a42b70564b9f109389',
    image: 'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/Vector_2652-1.jpg',
    title: 'The global AI race',
    description:
      'Introduction to the global competition and collaboration between nations in the field of AI.',
    lock: false,
    visible: false,
    slug: 'the-global-ai-race',
    translation: {
      en: {
        title: 'The global AI race',
        description:
          'Introduction to the global competition and collaboration between nations in the field of AI.',
      },
      de: {
        title: 'Das globale Wettrennen',
        description:
          'Der globale AI Wettbewerb einerseits, aber auch die Zusammenarbeit der Nationen.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 8,
      rules: [
        {
          order: 8,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 8,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 8,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d03182c1297ce0b40614bc',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-weekendplayer-187041-1.jpg',
    title: 'Successfully navigate AI risks',
    description: 'Navigate safely through your AI journey by understanding and mitigating risks.',
    lock: false,
    visible: false,
    slug: 'successfully-navigate-ai-risks',
    translation: {
      en: {
        title: 'Actively Manage AI Risks',
        description: 'Understand and avoid risks related to AI.',
      },
      de: {
        title: 'AI Risiken aktiv managen',
        description: 'Mit AI verbundene Risiken verstehen und vermeiden.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 9,
      rules: [
        {
          order: 9,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 9,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 9,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d3e239963b9159e43fa6b0',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-energepic-com-27411-159888_1-1.jpg',
    title: 'Introduction to data management',
    description:
      'Introduction to basic data-related principles and terminology as well as success factors for effective data management.',
    lock: false,
    visible: false,
    slug: 'introduction-to-data-management',
    translation: {
      en: {
        title: 'Introduction to Data Management',
        description:
          'Introduction to basic data-related principles and terminology as well as success factors for effective data management.',
      },
      de: {
        title: 'Introduction to Data Management',
        description:
          'Introduction to basic data-related principles and terminology as well as success factors for effective data management.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 10,
      rules: [
        {
          order: 19,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 16,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 10,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d7fb322238849a6dc5c2b8',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/ai-technology-brain-background-digital-transformation-concept-1.jpg',
    title: 'Hyperautomation for everyone',
    description:
      'Introduction to process automation leveraging RPA and hyperautomation with deep dives related to UiPath technology.',
    lock: false,
    visible: false,
    slug: 'hyperautomation-for-everyone',
    translation: {
      en: {
        title: 'Hyperautomation for Everyone',
        description:
          'Introduction to process automation leveraging RPA and hyperautomation with deep dives related to UiPath technology.',
      },
      de: {
        title: 'Hyperautomation for Everyone',
        description:
          'Introduction to process automation leveraging RPA and hyperautomation with deep dives related to UiPath technology.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 11,
      rules: [
        {
          order: 11,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 15,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 11,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d7e42c2238849a6dba1e2a',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-fauxels-3184292-1.jpg',
    title: 'Business value creation by AI',
    description: 'Overview of different ways to create tangible business value through AI.',
    lock: false,
    visible: false,
    slug: 'business-value-creation-by-ai',
    translation: {
      en: {
        title: 'Business value creation by AI',
        description: 'Overview of different ways to create tangible business value through AI.',
      },
      de: {
        title: 'Business-Nutzen durch AI kreieren',
        description:
          'Übersicht verschiedener Arten durch AI konkreten Business-Nutzen zu schaffen.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 12,
      rules: [
        {
          order: 12,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 10,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 12,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d417192b70564b9f10d2d9',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-fauxels-3183150-1.jpg',
    title: 'AI as a business',
    description:
      'Success factors for creating a new, innovative AI-driven business model include clear objectives.',
    lock: false,
    visible: false,
    slug: 'ai-as-a-business',
    translation: {
      en: {
        title: 'AI as a business',
        description:
          'Success factors for creating a new, innovative AI-driven business model include clear objectives.',
      },
      de: {
        title: 'AI als Business ',
        description: 'Erfolgreich neue und innovative, AI-getriebene Geschäftsmodelle aufbauen.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 13,
      rules: [
        {
          order: 13,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 11,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 13,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d6d0442238849a6db4aa74',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-fauxels-3183197-1.jpg',
    title: 'AI leadership playbook',
    description:
      'Guidance and key success factors for leaders on how to effectively manage AI in large organizations.',
    lock: false,
    visible: false,
    slug: 'ai-leadership-playbook',
    translation: {
      en: {
        title: 'AI leadership playbook',
        description:
          'Guidance and key success factors for leaders on how to effectively manage AI in large organizations.',
      },
      de: {
        title: 'AI Leadership Playbook',
        description:
          'Guidance and key success factors for leaders on how to effectively manage AI in large organizations.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 14,
      rules: [
        {
          order: 14,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 12,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 14,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d4006b963b9159e4453d0c',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-mikael-blomkvist-6476783-1.jpg',
    title: 'Anchoring AI in the organisation',
    description:
      'Essential elements for establishing AI in an enterprise effectively and sustainably.',
    lock: false,
    visible: false,
    slug: 'anchoring-ai-in-the-organisation',
    translation: {
      en: {
        title: 'Anchoring AI in the Organisation',
        description:
          'Essential elements for establishing AI in an enterprise effectively and sustainably.',
      },
      de: {
        title: 'Anchoring AI in the Organisation',
        description:
          'Essential elements for establishing AI in an enterprise effectively and sustainably.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 15,
      rules: [
        {
          order: 15,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 13,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 15,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d3f441963b9159e4427a09',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-olly-3760093-1.jpg',
    title: 'New leadership challenges',
    description: 'Overview of totally new challenges for leaders in the AI age.',
    lock: false,
    visible: false,
    slug: 'new-leadership-challenges',
    translation: {
      en: {
        title: 'New leadership challenges',
        description: 'Overview of totally new challenges for leaders in the AI age.',
      },
      de: {
        title: 'Neue Führungsherausforderungen',
        description: 'Völlig neue Herausforderungen für Führen im Zeitalter der AI.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 16,
      rules: [
        {
          order: 16,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 14,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 16,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d7f8482238849a6dc2ab26',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-divinetechygirl-1181359-1.jpg',
    title: 'Introduction to Python',
    description:
      'Gain foundational Python skills by creating programs, handling data, and building interactive applications.',
    lock: false,
    visible: false,
    slug: 'introduction-to-python',
    translation: {
      en: {
        title: 'Introduction to Python',
        description:
          'Gain foundational Python skills by creating programs, handling data, and building interactive applications.',
      },
      de: {
        title: 'Introduction to Python',
        description:
          'Gain foundational Python skills by creating programs, handling data, and building interactive applications.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 17,
      rules: [
        {
          order: 17,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 17,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 17,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d7f79d2238849a6dc1ad5b',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-danny-meneses-340146-943096_1-1.jpg',
    title: 'Introduction to Machine Learning',
    description:
      'Build a strong foundation in machine learning, covering key terms, algorithms and performance evaluation.',
    lock: false,
    visible: false,
    slug: 'introduction-to-machine-learning',
    translation: {
      en: {
        title: 'Introduction to Machine Learning',
        description:
          'Build a strong foundation in machine learning, covering key terms, algorithms and performance evaluation.',
      },
      de: {
        title: 'Introduction to Machine Learning',
        description:
          'Build a strong foundation in machine learning, covering key terms, algorithms and performance evaluation.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 18,
      rules: [
        {
          order: 18,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 18,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 18,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d7f8522238849a6dc2d48d',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-markusspiske-177598-1.jpg',
    title: 'Python for Machine Learning',
    description:
      'Improve Python skills and learn machine learning basics to solve real-world problems and take the first step into AI.',
    lock: false,
    visible: false,
    slug: 'python-for-machine-learning',
    translation: {
      en: {
        title: 'Python for Machine Learning',
        description:
          'Improve Python skills and learn machine learning basics to solve real-world problems and take the first step into AI.',
      },
      de: {
        title: 'Python for Machine Learning',
        description:
          'Improve Python skills and learn machine learning basics to solve real-world problems and take the first step into AI.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 19,
      rules: [
        {
          order: 19,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 19,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 19,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
  {
    id: '67d7f5a52238849a6dc07518',
    image:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/pexels-pixabay-276452-1-1.jpg',
    title: 'Introduction to Deep Learning',
    description:
      'Learn deep learning fundamentals, model structures, and techniques to build and optimize models for real-world applications.',
    lock: false,
    visible: false,
    slug: 'introduction-to-deep-learning',
    translation: {
      en: {
        title: 'Introduction to Deep Learning',
        description:
          'Learn deep learning fundamentals, model structures, and techniques to build and optimize models for real-world applications.',
      },
      de: {
        title: 'Introduction to Deep Learning ',
        description:
          'Learn deep learning fundamentals, model structures, and techniques to build and optimize models for real-world applications.',
      },
    },
    condition: [
      {
        field: 'technical_background_label',
        value: ['it-specialist', 'business', 'power-user'],
        operator: 'includes',
      },
    ],
    sorting: {
      default_order: 20,
      rules: [
        {
          order: 20,
          conditions: [{ field: 'technical_background_label', value: 'it-specialist' }],
        },
        {
          order: 20,
          conditions: [{ field: 'technical_background_label', value: 'business' }],
        },
        {
          order: 20,
          conditions: [{ field: 'technical_background_label', value: 'power-user' }],
        },
      ],
    },
  },
];
