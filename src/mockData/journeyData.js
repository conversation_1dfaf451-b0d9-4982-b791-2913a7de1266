export const journeyData = (user, selectedLevel) => {
  const getFunctionLabel = () => {
    const label = user?.onboarding?.function_label;
    if (!label) return '';
    if (typeof label === 'string') return label;
    return label.translations?.en || label.slug || '';
  };

  const functionLabel = getFunctionLabel();

  const allJourneyData = [
    {
      id: 1,
      title: 'Overview and orientation',
      description: 'Gain exclusive insights into your learning journey.',
      isLocked: false,
      buttonText: 'Watch video',
      newTab: false,
      journeyType: 'content',
      content: [
        '<video id="complete_video" disablepictureinpicture="" style="border-radius:8px;overflow:hidden;width: 100%; object-fit: fill;" autoplay="" src="https://d25yi7ujgmvrf2.cloudfront.net/Video_AI+Adaption+Plattform+demo_EN_14.10.2024.mp4/Video_AI+Adaption+Plattform+demo_EN_14.10.2024.m3u8"></video>',
        'Test 2',
      ],
      buttonType: 'URL',
      buttonURL: 'https://example.com/getting-started',
      order: 1,
      percent: 0,
      levels: ['beginner'],
    },
    {
      id: 2,
      title: 'Introduction to GenAI and ChatGPT',
      description: 'Explore Generative AI and ChatGPT capabilities.',
      isLocked: true,
      buttonText: 'Start',
      newTab: false,
      buttonType: 'URL',
      buttonURL: 'https://example.com/files/ai-guide.pdf',
      order: 2,
      percent: 0,
      levels: ['beginner'],
    },
    {
      id: 3,
      title: `Introduction to AI in ${functionLabel}`,
      description: `Take a deep dive into the opportunities for ${functionLabel} in GenAI.`,
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/advanced-ai',
      order: 3,
      percent: 0,
      levels: ['beginner'],
    },
    {
      id: 4,
      title: `Use case: ${functionLabel} innovation idea generator`,
      description: 'Practice with job-specific use cases.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/ai-strategy',
      order: 4,
      percent: 0,
      levels: ['beginner'],
    },
    {
      id: 5,
      title: 'Use case: Email follow-up assistant',
      description: 'Practice with job-specific use cases.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/ai-strategy',
      order: 5,
      percent: 0,
      levels: ['beginner'],
    },
    {
      id: 6,
      title: 'Successfully navigate AI risks',
      description: 'Navigate safely through your AI journey by understanding and mitigating risks.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/ai-strategy',
      order: 5,
      percent: 0,
      levels: ['beginner'],
    },

    {
      id: 7,
      title: `AI Beginner in ${functionLabel} assessment & certificate`,
      description: 'Assess your proficiency in your job domain & get your certificate.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/ai-strategy',
      order: 5,
      percent: 0,
      levels: ['beginner'],
    },
    {
      id: 8,
      title: `Job-specific use case training for ${functionLabel}`,
      description: 'Start practicing and applying job-specific use cases.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/sales-use-cases',
      order: 8,
      percent: 0,
      levels: ['expert'],
    },
    {
      id: 9,
      title: 'Technology deep dive: ChatGPT',
      description: 'Master ChatGPT prompting with hands-on exercises.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/chatgpt-deep-dive',
      order: 9,
      percent: 0,
      levels: ['expert'],
    },
    {
      id: 10,
      title: 'Technology deep dive: DALL·E',
      description: 'Transform creativity with DALL·E & expert your prompting skills.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/dalle-deep-dive',
      order: 10,
      percent: 0,
      levels: ['expert'],
    },
    {
      id: 11,
      title: 'Technology deep dive: HeyGen',
      description: 'Dive into video generation with AI and create videos.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/heygen-deep-dive',
      order: 11,
      percent: 0,
      levels: ['expert'],
    },
    {
      id: 12,
      title: 'Use Case Ideation',
      description: 'Create use case ideas for real-world scenarios.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/use-case-ideation',
      order: 12,
      percent: 0,
      levels: ['expert'],
    },
    {
      id: 13,
      title: `AI Expert in ${functionLabel} assessment & certificate`,
      description: 'Evaluate your abilities & obtain your AI expert certificate.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/expert-assessment',
      order: 13,
      percent: 0,
      levels: ['expert'],
    },
    // Master seviyesi kartları
    {
      id: 14,
      title: 'Overview and orientation',
      description: 'Gain inclusive insights about your training journey.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/master-orientation',
      order: 14,
      percent: 0,
      levels: ['master'],
    },
    {
      id: 15,
      title: 'AI productivity essentials',
      description: "Discover productivity essentials and AI's impact.",
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/ai-productivity',
      order: 15,
      percent: 0,
      levels: ['master'],
    },
    {
      id: 16,
      title: 'Overview of the creator tools',
      description: 'Gain insights into powerful AI tools in this journey.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/creator-tools',
      order: 16,
      percent: 0,
      levels: ['master'],
    },
    {
      id: 17,
      title: '«Simple AI app creator» tutorial',
      description: 'Learn how to create job-specific AI apps with this simple tool.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/app-creator-tutorial',
      order: 17,
      percent: 0,
      levels: ['master'],
    },
    {
      id: 18,
      title: '«Simple AI APP creator» exercise',
      description: 'Easily create job specific AI apps.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/app-creator-exercise',
      order: 18,
      percent: 0,
      levels: ['master'],
    },
    {
      id: 19,
      title: '«Agentic workflow creator» tutorial',
      description: 'Learn how to use the agentic workflow creator tool.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/workflow-creator-tutorial',
      order: 19,
      percent: 0,
      levels: ['master'],
    },
    {
      id: 20,
      title: '«Agentic workflow creator» exercise',
      description: 'Simply create your own personalized agentic workflows.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/workflow-creator-exercise',
      order: 20,
      percent: 0,
      levels: ['master'],
    },
    {
      id: 21,
      title: '«Advanced GenAI creator» tutorial',
      description: 'Learn how to create your custom AI chatbot.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/advanced-genai-tutorial',
      order: 21,
      percent: 0,
      levels: ['master'],
    },
    {
      id: 22,
      title: '«Advanced GenAI creator» exercise',
      description: 'Create your own chatbot without any coding skills.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/advanced-genai-exercise',
      order: 22,
      percent: 0,
      levels: ['master'],
    },
    {
      id: 23,
      title: 'Productivity challenge',
      description: 'Create a use case idea that can boost productivity within your job area.',
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/productivity-challenge',
      order: 23,
      percent: 0,
      levels: ['master'],
    },
    {
      id: 24,
      title: `AI Master in ${functionLabel} assessment & certificate`,
      description: `Evaluate your abilities & obtain your AI Master in ${functionLabel} certificate.`,
      isLocked: true,
      buttonText: 'Start',
      newTab: true,
      buttonType: 'URL',
      buttonURL: 'https://example.com/master-assessment',
      order: 24,
      percent: 0,
      levels: ['master'],
    },
  ];

  // Seçilen level'a göre filtreleme
  return allJourneyData.filter((item) => item.levels.includes(selectedLevel));
};
