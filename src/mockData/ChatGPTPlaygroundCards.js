import LibraryBooksOutlinedIcon from '@mui/icons-material/LibraryBooksOutlined';
import SchoolOutlinedIcon from '@mui/icons-material/SchoolOutlined';
import EmojiObjectsOutlinedIcon from '@mui/icons-material/EmojiObjectsOutlined';
import i18n from 'i18next';

// Card data for different languages
const cardsData = {
  en: [
    {
      title: 'Prompt library',
      description: 'Access our curated collection of effective prompts for various use cases',
      tag: 'Apply',
      icon: LibraryBooksOutlinedIcon,
      link: '/prompt-library/open-ai/chatgpt',
      buttonText: 'Browse library',
    },
    {
      title: 'Effective prompting crash course',
      description: 'Master the art of crafting powerful prompts in our comprehensive course',
      tag: 'Learn',
      icon: SchoolOutlinedIcon,
      link: '/course/67cf0af6c1297ce0b4ff4ed8',
      buttonText: 'Start learning',
    },
    {
      title: 'ChatGPT cheat sheet',
      description: 'Level up your ChatGPT skills with our quick reference guide',
      tag: 'Level up',
      icon: EmojiObjectsOutlinedIcon,
      link: '/sheets/chatgpt',
      buttonText: 'View cheat sheet',
    },
  ],
  de: [
    {
      title: 'Prompt Bibliothek',
      description:
        'Greifen Sie auf unsere kuratierte Sammlung effektiver Prompts für verschiedene Anwendungsfälle zu',
      tag: 'Anwendung',
      icon: LibraryBooksOutlinedIcon,
      link: '/prompt-library/open-ai/chatgpt',
      buttonText: 'Bibliothek durchsuchen',
    },
    {
      title: 'Prompting für EInsteiger',
      description: 'Erlernen Sie die Kunst des Erstellens wirksamer Prompts ',
      tag: 'Lernen',
      icon: SchoolOutlinedIcon,
      link: '/course/67cf0af6c1297ce0b4ff4ed8',
      buttonText: 'Jetzt lernen',
    },
    {
      title: 'ChatGPT Cheat Sheet',
      description:
        'Verbessern Sie Ihre ChatGPT-Fähigkeiten mit einem kompakten Cheat Sheet (Spickzettel) ',
      tag: 'Mehr anzeigen',
      icon: EmojiObjectsOutlinedIcon,
      link: '/sheets/chatgpt',
      buttonText: 'Spickzettel ansehen',
    },
  ],
};

// Return cards based on current language set in i18n
export const ChatGPTPlaygroundCards = () => {
  const currentLanguage = i18n.language || 'en';
  // Use English as fallback if current language cards are not available
  return cardsData[currentLanguage] || cardsData.en;
};
