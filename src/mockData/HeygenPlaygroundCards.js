import LibraryBooksOutlinedIcon from '@mui/icons-material/LibraryBooksOutlined';
import SchoolOutlinedIcon from '@mui/icons-material/SchoolOutlined';
import EmojiObjectsOutlinedIcon from '@mui/icons-material/EmojiObjectsOutlined';
import i18n from 'i18next';

// Dillere göre kart bilgileri
const cardsData = {
  en: [
    {
      title: 'Create your video',
      description:
        'Start creating professional AI-generated videos in minutes. Choose from various avatars, voices, and styles to bring your content to life.',
      tag: 'Apply',
      icon: LibraryBooksOutlinedIcon,
      link: '/heygen-video-creator/',
      buttonText: 'Create your video',
    },
    {
      title: 'Introduction to HeyGen',
      description:
        'Discover how HeyGen’s AI avatars are made through quick tutorials, behind-the-scenes tips, and advanced insights.',
      tag: 'Learn',
      icon: SchoolOutlinedIcon,
      link: '/tutorials/heygen/',
      buttonText: 'Start learning',
    },
  ],
  de: [
    {
      title: 'Video erstellen',
      description:
        'Beginnen Sie mit der Erstellung professioneller, AI-generierter Videos. Wählen Sie ein Avatar sowie die bevorzugte Sprache und Stimme aus, um Ihre Inhalte zum Leben zu erwecken.',
      tag: 'Anwendung',
      icon: LibraryBooksOutlinedIcon,
      link: '/heygen-video-creator/',
      buttonText: 'Video erstellen',
    },
    {
      title: 'Anleitung',
      description:
        'Lernen Sie, wie Sie spielerisch einfach und schnell ihre eigenen Videos erstellen können.',
      tag: 'Lernen',
      icon: SchoolOutlinedIcon,
      link: '/tutorials/heygen/',
      buttonText: 'Jetzt lernen',
    },
  ],
};

// i18n.language değerini kullanarak mevcut dile göre doğru kartları döndür
export const HeygenPlaygroundCards = () => {
  const currentLanguage = i18n.language || 'en';
  // Eğer mevcut dil için kart seti yoksa İngilizce setini varsayılan olarak kullan
  return cardsData[currentLanguage] || cardsData.en;
};
