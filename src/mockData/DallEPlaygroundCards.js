import LibraryBooksOutlinedIcon from '@mui/icons-material/LibraryBooksOutlined';
import SchoolOutlinedIcon from '@mui/icons-material/SchoolOutlined';
import EmojiObjectsOutlinedIcon from '@mui/icons-material/EmojiObjectsOutlined';
import i18n from 'i18next';

// Dillere göre kart bilgileri
const cardsData = {
  en: [
    {
      title: '10 most useful DALL·E Prompts',
      description: 'Access our curated collection of effective prompts for various use cases',
      tag: 'Apply',
      icon: LibraryBooksOutlinedIcon,
      link: '/prompt-library/open-ai/dall-e',
      buttonText: 'Browse library',
    },
    {
      title: 'Introduction to DALL·E',
      description: 'Master the art of crafting powerful prompts in our comprehensive course',
      tag: 'Learn',
      icon: SchoolOutlinedIcon,
      link: '/course/67d17c34bbac7c0b9eea2e77',
      buttonText: 'Start learning',
    },
    {
      title: 'DALL·E cheat sheet',
      description: 'Level up your DALL·E skills with our quick reference guide',
      tag: 'Level up',
      icon: EmojiObjectsOutlinedIcon,
      link: '/sheets/dall-e',
      buttonText: 'View cheat sheet',
    },
  ],
  de: [
    {
      title: 'Die besten 10 DALL·E Prompts',
      description:
        'Greifen Sie auf unsere kuratierte Sammlung effektiver Prompts für verschiedene Anwendungsfälle zu',
      tag: 'Anwendung',
      icon: LibraryBooksOutlinedIcon,
      link: '/prompt-library/open-ai/dall-e',
      buttonText: 'Bibliothek durchsuchen',
    },
    {
      title: 'Einführung',
      description: 'Bilder erstellen mit Dall-E erlernen',
      tag: 'Lernen',
      icon: SchoolOutlinedIcon,
      link: '/course/67d17c34bbac7c0b9eea2e77',
      buttonText: 'Jetzt lernen',
    },
    {
      title: 'DALL·E Cheat Sheet',
      description:
        'Verbessern Sie Ihre DALL·E-Fähigkeiten mit einem kompakten Cheat Sheet (Spickzettel)',
      tag: 'Mehr anzeigen',
      icon: EmojiObjectsOutlinedIcon,
      link: '/sheets/dall-e',
      buttonText: 'Spickzettel ansehen',
    },
  ],
};

// i18n.language değerini kullanarak mevcut dile göre doğru kartları döndür
export const DallEPlaygroundCards = () => {
  const currentLanguage = i18n.language || 'en';
  // Eğer mevcut dil için kart seti yoksa İngilizce setini varsayılan olarak kullan
  return cardsData[currentLanguage] || cardsData.en;
};
