import i18n from 'i18next';

const cardsData = {
  en: [
    {
      id: 'simple-ai-app-creator',
      title: 'Simple AI App Creator',
      description: 'Easily create your own custom AI apps',
      buttonText: 'Use now',
      buttonType: 'URL',
      buttonURL: '/playgrounds/simple-ai-app-creator',
      imageSrc:
        'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/create-app-orijinal-3.png',
      newTab: false,
    },
    {
      id: 'agentic-workflow-creator',
      title: 'Agentic Workflow Creator',
      description: 'Create agentic workflows through simple prompt flows',
      buttonText: 'Use now',
      buttonType: 'URL',
      buttonURL: '/playgrounds/agentic-workflow-creator',
      imageSrc:
        'https://aitrainer.aibusinessschool.com/resources/uploads/2024/03/enercity-thumbnail-1.jpg',
      newTab: false,
    },
  ],
  de: [
    {
      id: 'simple-ai-app-creator',
      title: 'Simple AI App Creator',
      description: 'Auf spielerisch einfache Weise eigene AI Apps kreieren.',
      buttonText: 'Jetzt verwenden',
      buttonType: 'URL',
      buttonURL: '/playgrounds/simple-ai-app-creator',
      imageSrc:
        'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/create-app-orijinal-3.png',
      newTab: false,
    },
    {
      id: 'agentic-workflow-creator',
      title: 'Agentic Workflow Creator',
      description: 'Einfache Agentic Workflows in Form von Prompt-Ketten kreieren.',
      buttonText: 'Jetzt verwenden',
      buttonType: 'URL',
      buttonURL: '/playgrounds/agentic-workflow-creator',
      imageSrc:
        'https://aitrainer.aibusinessschool.com/resources/uploads/2024/03/enercity-thumbnail-1.jpg',
      newTab: false,
    },
  ],
};

export const AIBusinessSchoolToolCards = () => {
  const currentLanguage = i18n.language || 'en';
  return cardsData[currentLanguage] || cardsData.en;
};
