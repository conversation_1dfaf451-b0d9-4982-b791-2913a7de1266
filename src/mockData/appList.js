export const appList = [
  {
    "_id": "674eb52f4782524f9eaf1fd2",
    "title": "Sales innovation idea generator",
    "slug": "sales-innovation-idea-generator",
    "function": [
      "sales"
    ],
    "use_case_type": [
      "text-generation"
    ],
    "uniqueid": "uid-266",
    "usecase_icon_url": "https://cdn.aibusinessschool.com/usecases/2024-09-21_13-18-48-svgviewer-output-2024-09-21T161829.918.svg",
    "api_type": "completions",
    "completions_settings": {
      "form_fields": [
        {
          "_id": "67a47e25ec3fdac96b939496",
          "type": "text",
          "choices": "",
          "label": "Business function",
          "name": "business_function",
          "default_value": "Sales"
        },
        {
          "_id": "67a47e25ec3fdac96b939497",
          "type": "select",
          "choices": "Incremental innovation \r\nRadical innovation \r\nDisruptive innovation",
          "label": "Type of innovation",
          "name": "type_of_innovation",
          "default_value": "Type of innovation"
        }
      ],
      "completions_prompt": "Generate {type_of_innovation} ideas for {business_function} that will  improve efficiency and create new opportunities within the {business_function} function. Focus on practical, impactful solutions that align with current trends and customer needs.",
      "add_manual_translation": true,
      "show_language_selection": false,
      "disable_language_selection_prompt": false,
      "model_selection": "gpt-4o-2024-08-06",
      "max_token": "1024",
      "temperature": "1",
      "top_p": "1",
      "frequency_penalty": "0",
      "presence_penalty": "0",
      "custom_system_prompt": "",
      "_id": "67a47e25ec3fdac96b939498"
    },
    "usecase_order": 980,
    "is_that_interesting": false,
    "translations": {
      "de": {
        "completions_prompt": "Generiere {type_of_innovation}-Ideen für {business_function}, die die Effizienz verbessern und neue Chancen innerhalb der {business_function} Funktion schaffen. Konzentriere dich auf praktische, wirkungsvolle Lösungen, die mit aktuellen Trends und den Bedürfnissen der Kunden übereinstimmen.",
        "_id": "67a47e25ec3fdac96b93949a"
      }
    },
    "function_order": {
      "sales": 990,
      "marketing": 100,
      "customer_care_after_sales": 100,
      "finance": 100,
      "hr": 100,
      "it_ai_data": 100,
      "project_management_consulting": 100,
      "management_management_support": 100,
      "corporate_development_strategy_esg": 100,
      "supply_chain_logistics": 100,
      "operations_processing": 100,
      "production": 100,
      "maintenance": 100,
      "quality_management": 100,
      "legal_compliance": 100,
      "risk_management": 100,
      "rd": 100,
      "other": 100,
      "_id": "67a47e25ec3fdac96b939499"
    }
  },
  {
    "_id": "674eb52f4782524f9eaf1fd3",
    "title": "Customer engagement tactics",
    "slug": "customer-engagement-tactics",
    "function": [
      "corporate-development-strategy-esg",
      "customer-care-after-sales",
      "management-management-support",
      "marketing",
      "production",
      "rd",
      "sales"
    ],
    "use_case_type": [
      "text-generation"
    ],
    "uniqueid": "uid-265",
    "usecase_icon_url": "",
    "api_type": "completions",
    "completions_settings": {
      "form_fields": [
        {
          "_id": "67a47e25ec3fdac96b93949b",
          "type": "text",
          "choices": "",
          "label": "Product or service",
          "name": "product_or_service",
          "default_value": "Wearable tech"
        },
        {
          "_id": "67a47e25ec3fdac96b93949c",
          "type": "textarea",
          "choices": "",
          "label": "Target audience",
          "name": "target_auidence",
          "default_value": "Health-conscious consumers aged 25-55 are a prime target for wearable tech at Target, seeking devices to track fitness goals"
        }
      ],
      "completions_prompt": "Generate customer engagement tactics for {product_or_service} product area that focus on increasing customer retention, driving brand loyalty, and enhancing user experience for {target_auidence} audience.",
      "add_manual_translation": true,
      "show_language_selection": false,
      "disable_language_selection_prompt": false,
      "model_selection": "gpt-4o-2024-08-06",
      "max_token": "1024",
      "temperature": "1",
      "top_p": "1",
      "frequency_penalty": "0",
      "presence_penalty": "0",
      "custom_system_prompt": "",
      "_id": "67a47e25ec3fdac96b93949d"
    },
    "usecase_order": 810,
    "is_that_interesting": false,
    "translations": {
      "de": {
        "completions_prompt": "Generiere Kundenbindungsstrategien für den Bereich {product_or_service}, die sich auf die Steigerung der Kundenbindung, die Förderung der Markenloyalität und die Verbesserung der Benutzererfahrung konzentrieren.",
        "_id": "67a47e25ec3fdac96b93949f"
      }
    },
    "function_order": {
      "sales": 980,
      "marketing": 920,
      "customer_care_after_sales": 940,
      "finance": 100,
      "hr": 100,
      "it_ai_data": 100,
      "project_management_consulting": 100,
      "management_management_support": 950,
      "corporate_development_strategy_esg": 950,
      "supply_chain_logistics": 100,
      "operations_processing": 100,
      "production": 950,
      "maintenance": 100,
      "quality_management": 100,
      "legal_compliance": 100,
      "risk_management": 100,
      "rd": 980,
      "other": 100,
      "_id": "67a47e25ec3fdac96b93949e"
    }
  },
  {
    "_id": "674eb52f4782524f9eaf2023",
    "title": "Email follow-up assistant",
    "slug": "email-analysis-follow-up-creator",
    "function": [
      "sales"
    ],
    "use_case_type": [
      "text-generation"
    ],
    "uniqueid": "uid-176",
    "usecase_icon_url": "https://cdn.aibusinessschool.com/usecases/2024-09-11_09-55-28-svgviewer-output-79.svg",
    "api_type": "completions",
    "completions_settings": {
      "form_fields": [
        {
          "_id": "67a47e25ec3fdac96b9394a0",
          "type": "textarea",
          "choices": "",
          "label": "Input original text",
          "name": "input_original_text",
          "default_value": "Dear Customer Service, I hope this message finds you well. I am writing to report a faulty television set I purchased from your website on July 15, 2023. The details are: - Brand: Zenix400 - Model: ZT-55XHD - Order Number: 123456789 Since August 1, 2023, I have experienced the following issues: 1. Screen flickering intermittently. 2. Sound cutting out after 30 minutes of use. 3. Remote control unresponsive at times. I request a technician visit to inspect and repair the television or arrange for a replacement. Attached are the purchase receipt and a video showing the issues. Please advise on the next steps to resolve this matter promptly. Thank you. Best regards, John Smith"
        },
        {
          "_id": "67a47e25ec3fdac96b9394a1",
          "type": "text",
          "choices": "",
          "label": "Person who sent original email",
          "name": "person_who_sent_original_email",
          "default_value": "New customer who purchased a product from Zenix400 for the first time"
        },
        {
          "_id": "67a47e25ec3fdac96b9394a2",
          "type": "textarea",
          "choices": "",
          "label": "Point to address in follow-up",
          "name": "point_to_address_in_follow_up",
          "default_value": "The TV will be fully replaced next week and as an apology, Zenix400 will also supply free cable TV for 6 months"
        },
        {
          "_id": "67a47e25ec3fdac96b9394a3",
          "type": "text",
          "choices": "",
          "label": "Tone",
          "name": "tone",
          "default_value": "Understanding, clear and polite"
        }
      ],
      "completions_prompt": "Your task comes in two parts: Firstly: Analyse the following text and give an output in bullet point form to say what the text says, including a sentiment analysis: {input_original_text}. Secondly, write a follow up email. The person who wrote the original text is {person_who_sent_original_email}. In this follow-up email, you should address the following points: {point_to_address_in_follow_up}. The tone should be {tone}.",
      "add_manual_translation": true,
      "show_language_selection": false,
      "disable_language_selection_prompt": false,
      "model_selection": "gpt-4o-2024-08-06",
      "max_token": "1024",
      "temperature": "0.1",
      "top_p": "0.1",
      "frequency_penalty": "0",
      "presence_penalty": "0",
      "custom_system_prompt": "",
      "_id": "67a47e25ec3fdac96b9394a4"
    },
    "usecase_order": 965,
    "is_that_interesting": false,
    "translations": {
      "de": {
        "completions_prompt": "Deine Aufgabe besteht aus zwei Teilen: Erstens: Analysiere den folgenden Text und gib eine Ausgabe in Stichpunktform wieder, was der Text sagt, einschließlich einer Stimmungsanalyse: {input_original_text}. Zweitens: Schreibe eine Follow-up-E-Mail. Die Person, die den ursprünglichen Text geschrieben hat, ist {person_who_sent_original_email}. In dieser Follow-up-E-Mail solltest du die folgenden Punkte ansprechen: {point_to_address_in_follow_up}. Der Ton sollte {tone} sein.",
        "_id": "67a47e25ec3fdac96b9394a6"
      }
    },
    "function_order": {
      "sales": 970,
      "marketing": 100,
      "customer_care_after_sales": 100,
      "finance": 100,
      "hr": 100,
      "it_ai_data": 100,
      "project_management_consulting": 100,
      "management_management_support": 100,
      "corporate_development_strategy_esg": 100,
      "supply_chain_logistics": 100,
      "operations_processing": 100,
      "production": 100,
      "maintenance": 100,
      "quality_management": 100,
      "legal_compliance": 100,
      "risk_management": 100,
      "rd": 100,
      "other": 100,
      "_id": "67a47e25ec3fdac96b9394a5"
    }
  },
  {
    "_id": "674eb52f4782524f9eaf2052",
    "title": "Document analysis and summary",
    "slug": "document-analysis-and-summary",
    "function": [
      "sales"
    ],
    "use_case_type": [
      "text-generation"
    ],
    "uniqueid": "uid-89",
    "usecase_icon_url": "https://cdn.aibusinessschool.com/usecases/2024-06-04_09-22-24-report-svgrepo-com.svg",
    "api_type": "assistants",
    "assistant_settings": {
      "assitant_name": "Sales Performance Analyzer",
      "assistant_instructions": "You are a Sales Performance Analyzer, Given the uploaded sales file, analyze and answer the questions.",
      "assistant_model": "gpt-4-1106-preview",
      "assistant_type": "retrieval",
      "open_ai_assistant_file_name": "HubSpots-2024-Sales-Trends-Report.pdf",
      "open_ai_assistant_file_url": "https://cdn.aibusinessschool.com/usecases/2024-09-21_15-29-09-HubSpots-2024-Sales-Trends-Report.pdf",
      "open_ai_assistant_id": "asst_5a9VB24DbVdtlYBQ2iH7UvPI",
      "open_ai_file_id": "file-WaInGvfoup13V8cyk1zbk6Ku",
      "assistants_questions": [
        false
      ],
      "custom_system_prompt": "",
      "_id": "67a47e25ec3fdac96b9394a7"
    },
    "usecase_order": 955,
    "is_that_interesting": false,
    "translations": {},
    "function_order": {
      "sales": 960,
      "marketing": 100,
      "customer_care_after_sales": 100,
      "finance": 100,
      "hr": 100,
      "it_ai_data": 100,
      "project_management_consulting": 100,
      "management_management_support": 100,
      "corporate_development_strategy_esg": 100,
      "supply_chain_logistics": 100,
      "operations_processing": 100,
      "production": 100,
      "maintenance": 100,
      "quality_management": 100,
      "legal_compliance": 100,
      "risk_management": 100,
      "rd": 100,
      "other": 100,
      "_id": "67a47e25ec3fdac96b9394a8"
    }
  },
  {
    "_id": "674eb52f4782524f9eaf201d",
    "title": "Meeting agenda creator",
    "slug": "meeting-agenda-creator",
    "function": [
      "corporate-development-strategy-esg",
      "customer-care-after-sales",
      "finance",
      "hr",
      "it-ai-data",
      "legal-compliance",
      "maintenance",
      "management-management-support",
      "marketing",
      "operations-processing",
      "other",
      "production",
      "project-management-consulting",
      "quality-management",
      "rd",
      "risk-management",
      "sales",
      "supply-chain-logistics"
    ],
    "use_case_type": [
      "image-generation"
    ],
    "uniqueid": "uid-186",
    "usecase_icon_url": "https://cdn.aibusinessschool.com/usecases/2024-09-20_08-01-47-svgviewer-output-2024-09-20T110135.108.svg",
    "api_type": "completions",
    "completions_settings": {
      "form_fields": [
        {
          "_id": "67a47e25ec3fdac96b9394a9",
          "type": "text",
          "choices": "",
          "label": "Meeting title",
          "name": "meeting_title",
          "default_value": "Planning Next Quarter"
        },
        {
          "_id": "67a47e25ec3fdac96b9394aa",
          "type": "text",
          "choices": "",
          "label": "Key topics",
          "name": "key_topics",
          "default_value": "Overview of current performance metrics \r\nChallenges and areas for improvement \r\nStrategic initiatives for the next quarter \r\nResource allocation and budget considerations \r\nOpen discussion and next steps "
        },
        {
          "_id": "67a47e25ec3fdac96b9394ab",
          "type": "text",
          "choices": "",
          "label": "Participants",
          "name": "participants",
          "default_value": "Department Head \r\nTeam Leads \r\nKey Project Managers \r\nHR Representative \r\nFinance Manager "
        },
        {
          "_id": "67a47e25ec3fdac96b9394ac",
          "type": "select",
          "choices": "Virtual meeting\r\nHybrid meeting\r\nIn-person meeting",
          "label": "Meeting setting",
          "name": "meeting_setting",
          "default_value": "Meeting setting"
        },
        {
          "_id": "67a47e25ec3fdac96b9394ad",
          "type": "text",
          "choices": "",
          "label": "Time duration",
          "name": "time_duration",
          "default_value": "1.5 hours"
        }
      ],
      "completions_prompt": "Generate a meeting agenda for a meeting titled {meeting_title} which will cover the following topics: {key_topics}. The meeting will last for {time_duration}, and the participants attending include: {participants}. Please structure the agenda in a way that evenly distributes the time across the topics, ensuring clear and concise coverage of each point. The meeting is taking place in this setting: {meeting_setting}. Keep your output professional and easy to follow, suitable for distribution to attendees before the meeting.",
      "add_manual_translation": true,
      "show_language_selection": false,
      "disable_language_selection_prompt": false,
      "model_selection": "gpt-4o-2024-08-06",
      "max_token": "1024",
      "temperature": "0.1",
      "top_p": "0.1",
      "frequency_penalty": "0",
      "presence_penalty": "0",
      "custom_system_prompt": "",
      "_id": "67a47e25ec3fdac96b9394ae"
    },
    "usecase_order": 750,
    "is_that_interesting": false,
    "translations": {
      "de": {
        "completions_prompt": "Erstelle eine Tagesordnung (Agenda) für ein Meeting mit dem Titel {meeting_title}, das die folgenden Themen abdecken wird: {key_topics}. Das Meeting wird {time_duration} dauern, und die Teilnehmer sind: {participants}. Strukturiere die Agenda so, dass die Zeit gleichmäßig über die Themen verteilt wird, um eine klare und prägnante Abdeckung jedes Punktes zu gewährleisten. Das Meeting findet in folgendem Format statt: {meeting_format}. Halte die Agenda professionell und leicht verständlich, sodass sie vor dem Meeting an die Teilnehmer verteilt werden kann.",
        "_id": "67a47e25ec3fdac96b9394b0"
      }
    },
    "function_order": {
      "sales": 950,
      "marketing": 900,
      "customer_care_after_sales": 880,
      "finance": 890,
      "hr": 910,
      "it_ai_data": 960,
      "project_management_consulting": 940,
      "management_management_support": 930,
      "corporate_development_strategy_esg": 930,
      "supply_chain_logistics": 940,
      "operations_processing": 950,
      "production": 940,
      "maintenance": 920,
      "quality_management": 910,
      "legal_compliance": 970,
      "risk_management": 940,
      "rd": 940,
      "other": 960,
      "_id": "67a47e25ec3fdac96b9394af"
    }
  },
  {
    "_id": "674eb52f4782524f9eaf1fe5",
    "title": "Sales jargon explainer",
    "slug": "sales-jargon-explainer",
    "function": [
      "sales"
    ],
    "use_case_type": [
      "video-generation"
    ],
    "uniqueid": "uid-247",
    "usecase_icon_url": "https://cdn.aibusinessschool.com/usecases/2024-06-03_11-05-09-cloud-1168.svg",
    "api_type": "completions",
    "completions_settings": {
      "form_fields": [
        {
          "_id": "67a47e25ec3fdac96b9394b1",
          "type": "text",
          "choices": "",
          "label": "Technical term",
          "name": "technical_term",
          "default_value": "Sales funnel optimization"
        }
      ],
      "completions_prompt": "Explain the concept of {technical_term} in simple, clear language that a business professional without a technical background can easily understand. Focus on practical applications, business relevance, and avoid overly technical jargon, while ensuring the key details are conveyed.",
      "add_manual_translation": true,
      "show_language_selection": true,
      "disable_language_selection_prompt": false,
      "model_selection": "gpt-4o",
      "max_token": "1024",
      "temperature": "0.1",
      "top_p": "0.1",
      "frequency_penalty": "0",
      "presence_penalty": "0",
      "custom_system_prompt": "",
      "_id": "67a47e25ec3fdac96b9394b2"
    },
    "usecase_order": 940,
    "translations": {
      "de": {
        "completions_prompt": "Erkläre das Konzept von {technical_term} in einfacher, klarer Sprache, die ein Geschäftsmann ohne technischen Hintergrund leicht verstehen kann. Konzentriere dich auf praktische Anwendungen, geschäftliche Relevanz und vermeide übermässig technische Fachbegriffe, während du sicherstellst, dass die wichtigsten Details vermittelt werden.",
        "_id": "67a47e25ec3fdac96b9394b4"
      }
    },
  }
];
