export const AdvancedCreatorTools = [
  {
   "status": "success",
  "message": "Forms retrieved successfully",
  "data": {
    "forms": [
      {
        "_id": "67b2fb876c0ae3d4399dbfc0",
        "title": "Describe your use case idea",
        "description": "Please submit your idea by filling out the following form. You can fill in a similar form as many times as you like.​ ",
        "fields": [
          {
            "validation": {
              "min": null,
              "max": null,
              "pattern": "",
              "message": ""
            },
            "name": "textfieldname",
            "label": "Title of your idea",
            "type": "text",
            "required": true,
            "options": [],
            "fields": [],
            "conditional_logic": {
              "enabled": false,
              "rules": [
                [
                  {
                    "field": "",
                    "operator": "has_any_value",
                    "value": "",
                    "_id": "67b2fb876c0ae3d4399dbfc3"
                  }
                ]
              ],
              "_id": "67b2fb876c0ae3d4399dbfc2"
            },
            "min": null,
            "max": null,
            "_id": "67b2fb876c0ae3d4399dbfc1"
          },
          {
            "validation": {
              "min": null,
              "max": null,
              "pattern": "",
              "message": ""
            },
            "name": "textareaname",
            "label": "Describe your idea",
            "type": "textarea",
            "required": true,
            "options": [],
            "fields": [],
            "conditional_logic": {
              "enabled": false,
              "rules": [
                [
                  {
                    "field": "",
                    "operator": "has_any_value",
                    "value": "",
                    "_id": "67b2fb876c0ae3d4399dbfcc"
                  }
                ]
              ],
              "_id": "67b2fb876c0ae3d4399dbfcb"
            },
            "min": null,
            "max": null,
            "_id": "67b2fb876c0ae3d4399dbfca"
          },
        ],
        "status": "draft",
        "createdAt": "2025-02-17T09:04:07.596Z",
        "updatedAt": "2025-02-17T09:04:07.596Z",
        "__v": 0
      },
      {
        "_id": "67b2fb876c0ae3d4399dbff0",
        "title": "Classify your idea",
        "description": "Please submit your idea by filling out the following form. You can fill in a similar form as many times as you like.​ ",
        "fields": [
          
          {
            "validation": {
              "min": null,
              "max": null,
              "pattern": "",
              "message": ""
            },
            "name": "idea_category",
            "label": "What is the primary expected business benefit of your idea? * (please select only one as the most relevant and dominant benefit dimension)",
            "type": "select",
            "required": true,
            "options": [
              {
                "label": "Better customer experience",
                "value": "better_customer_experience",
                "_id": "67b2fb876c0ae3d4399dc001"
              },
              {
                "label": "Increase in revenues",
                "value": "increase_in_revenues",
                "_id": "67b2fb876c0ae3d4399dc002"
              },
              {
                "label": "Cost reduction",
                "value": "cost_reduction",
                "_id": "67b2fb876c0ae3d4399dc003"
              },
              {
                "label": "Better efficiency",
                "value": "better_efficiency",
                "_id": "67b2fb876c0ae3d4399dc004"
              },
              {
                "label": "Higher quality",
                "value": "higher_quality",
                "_id": "67b2fb876c0ae3d4399dc005"
              },
              {
                "label": "Better internal decision making",
                "value": "better_internal_decision_making",
                "_id": "67b2fb876c0ae3d4399dc006"
              },
              {
                "label": "Better employee satisfaction",
                "value": "better_employee_satisfaction",
                "_id": "67b2fb876c0ae3d4399dc007"
              },
              {
                "label": "Risk reduction",
                "value": "risk_reduction",
                "_id": "67b2fb876c0ae3d4399dc008"
              },
              {
                "label": "New business model",
                "value": "new_business_model",
                "_id": "67b2fb876c0ae3d4399dc009"
              },
              {
                "label": "Other",
                "value": "other",
                "_id": "67b2fb876c0ae3d4399dc010"
              }
            ],
            "fields": [],
            "conditional_logic": {
              "enabled": false,
              "rules": [
                [
                  {
                    "field": "",
                    "operator": "has_any_value",
                    "value": "",
                    "_id": "67b2fb876c0ae3d4399dc005"
                  }
                ]
              ],
              "_id": "67b2fb876c0ae3d4399dc004"
            },
            "min": null,
            "max": null,
            "_id": "67b2fb876c0ae3d4399dc000"
          },
          {
            "validation": {
              "min": null,
              "max": null,
              "pattern": "",
              "message": ""
            },
            "name": "business_area",
            "label": "What is the targeted business area of your idea?",
            "type": "select",
            "required": true,
            "options": [
              {
                "label": "Marketing",
                "value": "marketing",
                "_id": "67b2fb876c0ae3d4399dc011"
              },
              {
                "label": "Sales",
                "value": "sales",
                "_id": "67b2fb876c0ae3d4399dc012"
              },
              {
                "label": "Finance",
                "value": "finance",
                "_id": "67b2fb876c0ae3d4399dc013"
              },
              {
                "label": "Customer Care & After Sales",
                "value": "customer_care",
                "_id": "67b2fb876c0ae3d4399dc014"
              },
              {
                "label": "HR",
                "value": "hr",
                "_id": "67b2fb876c0ae3d4399dc015"
              },
              {
                "label": "IT",
                "value": "it",
                "_id": "67b2fb876c0ae3d4399dc016"
              },
              {
                "label": "R&D",
                "value": "rd",
                "_id": "67b2fb876c0ae3d4399dc017"
              }
            ],
            "fields": [],
            "conditional_logic": {
              "enabled": false,
              "rules": [
                [
                  {
                    "field": "",
                    "operator": "has_any_value",
                    "value": "",
                    "_id": "67b2fb876c0ae3d4399dc019"
                  }
                ]
              ],
              "_id": "67b2fb876c0ae3d4399dc018"
            },
            "min": null,
            "max": null,
            "_id": "67b2fb876c0ae3d4399dc020"
          },
        ],
        "status": "draft",
        "createdAt": "2025-02-17T09:04:07.596Z",
        "updatedAt": "2025-02-17T09:04:07.596Z",
        "__v": 0
      }
    ],
    "pagination": {
      "total": 2,
      "page": 1,
      "pages": 1
    }
  }
  },
];
