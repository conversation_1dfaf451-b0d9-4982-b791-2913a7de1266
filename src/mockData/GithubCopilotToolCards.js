import LibraryBooksOutlinedIcon from '@mui/icons-material/LibraryBooksOutlined';
import SchoolOutlinedIcon from '@mui/icons-material/SchoolOutlined';
import i18n from 'i18next';

// Dillere göre kart bilgileri
const cardsData = {
  en: [
    {
      title: "GitHub Copilot playground",
      description: "Experiment with AI-driven coding in a dynamic environment. Explore new coding ideas, test features, and enhance your development workflow effortlessly.",
      tag: "Apply",
      icon: LibraryBooksOutlinedIcon,
      link: "https://github.com/codespaces/new/AI-Business-School/copilot-playground",
      buttonText: "Try it now",
      newTab: true
    },
    {
      title: "GitHub Copilot course",
      description: "Unlock the power of AI-assisted coding with our comprehensive course. Learn key techniques and best practices to boost your productivity.",
      tag: "Learn",
      icon: SchoolOutlinedIcon,
      link: "/course/67d7f7272238849a6dc17218",
      buttonText: "Start learning"
    }
  ],
  de: [
    {
      title: "GitHub Copilot Playground",
      description: "Experimentieren Sie mit AI-gesteuerter Programmierung in einer dynamischen Umgebung. Erkunden Sie neue Programmierideen, testen Sie Funktionen und verbessern Sie Ihren Entwicklungsworkflow mühelos.",
      tag: "Anwenden",
      icon: LibraryBooksOutlinedIcon,
      link: "https://github.com/codespaces/new/AI-Business-School/copilot-playground",
      buttonText: "Versuchen Sie es jetzt",
      newTab: true
    },
    {
      title: "GitHub Copilot Kurs",
      description: "Nutzen Sie die Leistungsfähigkeit von AI-unterstützter Programmierung mit unserem umfassenden Kurs. Lernen Sie wichtige Techniken und Best Practices, um Ihre Produktivität zu steigern.",
      tag: "Lernen",
      icon: SchoolOutlinedIcon,
      link: "/course/67d7f7272238849a6dc17218",
      buttonText: "Jetzt lernen"
    }
  ]
};

// i18n.language değerini kullanarak mevcut dile göre doğru kartları döndür
export const GithubCopilotToolCards = () => {
  const currentLanguage = i18n.language || 'en';
  // Eğer mevcut dil için kart seti yoksa İngilizce setini varsayılan olarak kullan
  return cardsData[currentLanguage] || cardsData.en;
}; 