export const MicrosoftTools = [
  {
    id: 'ms365-copilot',
    translations: {
      en: {
        title: 'Microsoft 365 Copilot',
        description:
          'Microsoft Copilot is an AI-powered assistant that enhances productivity by helping you create, manage, and collaborate across Microsoft 365 tools like Word, Excel, and Teams.',
        buttonText: 'Learn more',
        tooltipText:
          'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
      },
      de: {
        title: 'Microsoft 365 Copilot',
        description:
          'Microsoft Copilot ist ein AI-gestützter Assistent, der Ihre Produktivität steigert, indem er Sie bei der Erstellung, Verwaltung und Zusammenarbeit in Microsoft 365-Tools wie Word, Excel und Teams unterstützt.',
        buttonText: 'Mehr erfahren',
        tooltipText:
          'Dieser Teil ist nicht in Ihrem Zugriffsschema enthalten - bitte kontaktieren Sie Ihren AI Business School Account Manager für weitere Informationen.',
      },
    },
    buttonType: 'URL',
    buttonURL: '/microsoft-copilot',
    imageSrc:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/microsoft-365-copilot.jpg',
    newTab: false,
  },
  {
    id: 'github-copilot',
    translations: {
      en: {
        title: 'GitHub Copilot',
        description:
          'GitHub Copilot is an AI coding assistant that helps you write software code faster and with less effort, allowing you to focus more energy on problem solving and collaboration. GitHub Copilot has been proven to increase developer productivity and accelerate the pace of software development.',
        buttonText: 'Learn more',
        tooltipText:
          'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
      },
      de: {
        title: 'GitHub Copilot',
        description:
          'GitHub Copilot ist ein AI Programmierassistent, der Ihnen hilft, Software-Code schneller und mit weniger Aufwand zu schreiben, sodass Sie mehr Energie auf Problemlösung und Zusammenarbeit konzentrieren können. GitHub Copilot steigert nachweislich die Entwicklerproduktivität und beschleunigt die Softwareentwicklung.',
        buttonText: 'Mehr erfahren',
        tooltipText:
          'Dieser Teil ist nicht in Ihrem Zugriffsschema enthalten - bitte kontaktieren Sie Ihren AI Business School Account Manager für weitere Informationen.',
      },
    },
    buttonType: 'URL',
    buttonURL: '/tools/github-copilot',
    locked: false,
    imageSrc: 'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/github-copilot.jpg',
    newTab: false,
  },
  {
    id: 'azure-ai-foundry',
    translations: {
      en: {
        title: 'Azure AI Foundry',
        description:
          'Azure AI Foundry is a Microsoft platform offering AI tools, frameworks, and best practices to help businesses integrate AI into operations, driving innovation, efficiency, and smarter decisions.',
        buttonText: 'Learn more',
        tooltipText:
          'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
      },
      de: {
        title: 'Azure AI Foundry',
        description:
          'Azure AI Foundry ist eine Microsoft-Plattform, die AI-Tools, Frameworks und Best Practices bietet, um Unternehmen dabei zu unterstützen, AI operative Abläufe zu integrieren und so Innovation, Effizienz und intelligentere Entscheidungen voranzutreiben.',
        buttonText: 'Mehr erfahren',
        tooltipText:
          'Dieser Teil ist nicht in Ihrem Zugriffsschema enthalten - bitte kontaktieren Sie Ihren AI Business School Account Manager für weitere Informationen.',
      },
    },
    buttonType: 'URL',
    buttonURL: '/course/67d7f0692238849a6dbce008',
    locked: false,
    imageSrc:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/azure-ai-foundry.jpg',
    newTab: false,
  },
  {
    id: 'copilot-studio',
    translations: {
      en: {
        title: 'Microsoft Copilot Studio',
        description:
          'Build your own agents or enhance Microsoft 365 Copilot with one. Agents are expert systems that work on behalf of a person, team, or organization. Use low code and generative AI to build an agent today.',
        buttonText: 'Learn more',
        tooltipText:
          'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
      },
      de: {
        title: 'Microsoft Copilot Studio',
        description:
          'Erstellen Sie Ihre eigenen AI Agenten oder erweitern Sie Microsoft 365 Copilot mit einem solchen Agenten. AI Agenten sind intelligente Expertensysteme, die wie virtuelle Mitarbeitende im Auftrag einer Person, eines Teams oder einer Organisation arbeiten.',
        buttonText: 'Mehr erfahren',
        tooltipText:
          'Dieser Teil ist nicht in Ihrem Zugriffsschema enthalten - bitte kontaktieren Sie Ihren AI Business School Account Manager für weitere Informationen.',
      },
    },
    buttonType: 'MODAL',
    buttonURL: '',
    locked: false,
    imageSrc:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/microsoft-copilot-studio.jpg',
    newTab: false,
  },
  {
    id: 'copilot-chat',
    translations: {
      en: {
        title: 'Microsoft Copilot Chat',
        description:
          'Microsoft 365 Copilot Chat is an AI chat available at no additional cost with Microsoft 365 products. It includes web grounding, the latest large language models, file uploads, Copilot Pages, IT controls, and enterprise-grade privacy and security.',
        buttonText: 'Learn more',
        tooltipText:
          'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
      },
      de: {
        title: 'Microsoft Copilot Chat',
        description:
          'Microsoft 365 Copilot Chat ist ein AI-Chat, der ohne zusätzliche Kosten mit Microsoft 365 Produkten verfügbar ist. Er umfasst Web-Grounding, die neuesten großen Sprachmodelle, Datei-Uploads, Copilot-Seiten, IT-Kontrollen sowie Datenschutz und Sicherheit auf Unternehmensniveau.',
        buttonText: 'Mehr erfahren',
        tooltipText:
          'Dieser Teil ist nicht in Ihrem Zugriffsschema enthalten - bitte kontaktieren Sie Ihren AI Business School Account Manager für weitere Informationen.',
      },
    },
    buttonType: 'URL',
    buttonURL: '',
    locked: true,
    imageSrc:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/microsoft-365-copilot.jpg',
    newTab: false,
  },
  {
    id: 'power-bi',
    translations: {
      en: {
        title: 'Power BI',
        description:
          'Power BI is a business analytics tool from Microsoft that allows users to connect to various data sources, transform and analyze data, and create interactive visual reports and dashboards.',
        buttonText: 'Learn more',
        tooltipText:
          'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
      },
      de: {
        title: 'Power BI',
        description:
          'Power BI ist ein Business-Analytics-Tool von Microsoft, mit dem Nutzer verschiedene Datenquellen anbinden, Daten transformieren und analysieren sowie interaktive visuelle Berichte und Dashboards erstellen können.',
        buttonText: 'Mehr erfahren',
        tooltipText:
          'Dieser Teil ist nicht in Ihrem Zugriffsschema enthalten - bitte kontaktieren Sie Ihren AI Business School Account Manager für weitere Informationen.',
      },
    },
    buttonType: 'URL',
    buttonURL: '/tools/microsoft/power-bi',
    locked: true,
    imageSrc:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/microsoft-powerbi.jpg',
    newTab: false,
  },
  {
    id: 'fabric',
    translations: {
      en: {
        title: 'Microsoft Fabric',
        description:
          'Microsoft Fabric is an end-to-end data analytics platform that unifies data engineering, integration, warehousing, and more, combining tools like Power BI, Azure Data Factory, and Synapse.',
        buttonText: 'Learn more',
        tooltipText:
          'This part is not included in your access scheme - please contact your AI Business School account manager for more information.',
      },
      de: {
        title: 'Microsoft Fabric',
        description:
          'Microsoft Fabric ist eine umfassende Datenanalyseplattform, die Datentechnik, Integration, Warehousing und mehr vereint und Tools wie Power BI, Azure Data Factory und Synapse kombiniert.',
        buttonText: 'Mehr erfahren',
        tooltipText:
          'Dieser Teil ist nicht in Ihrem Zugriffsschema enthalten - bitte kontaktieren Sie Ihren AI Business School Account Manager für weitere Informationen.',
      },
    },
    buttonType: 'URL',
    buttonURL: '/tools/microsoft/fabric',
    locked: true,
    imageSrc:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/microsoft-fabric.jpg',
    newTab: false,
  },
];

export const CopilotStudioCourses = [
  {
    id: 'copilot-foundations',
    translations: {
      en: {
        title: 'Copilot Foundations',
        description:
          'Copilot Foundations introduces the core concepts of Microsoft Copilot, teaching you how to use its AI-powered features to assist with tasks like writing, analyzing data, and collaborating in tools like Word, Excel, and Teams.',
        buttonText: 'Start now »',
        courseType: 'Microsoft Official Course',
        buttonURL: '/course/67d7eef52238849a6dbc5849',
      },
      de: {
        title: 'Copilot-Grundlagen',
        description:
          'Copilot-Grundlagen führt Sie in die Kernkonzepte von Microsoft Copilot ein und zeigt Ihnen, wie Sie die Funktionen von AI nutzen können, um Aufgaben wie das Schreiben, die Analyse von Daten und die Zusammenarbeit in Tools wie Word, Excel und Teams zu unterstützen.',
        buttonText: 'Jetzt starten »',
        courseType: 'Microsoft Offizieller Kurs',
        buttonURL: '/course/67d7efe62238849a6dbca5b1',
      },
    },
    duration: '2h 30m',
    imageSrc:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/copilot-foundations.png',
    newTab: false,
  },
  {
    id: 'create-copilots',
    translations: {
      en: {
        title: 'Create copilots with Microsoft Copilot Studio',
        description:
          'Learn how to create and customize AI copilots using Microsoft Copilot Studio to enhance productivity, streamline workflows, and tailor AI-driven solutions to your specific business needs.',
        buttonText: 'Start now »',
        courseType: 'Offizieller Microsoft Kurs',
        buttonURL: '/course/67d7f1782238849a6dbd7f62',
      },
      de: {
        title: 'Copiloten mit Microsoft Copilot Studio erstellen',
        description:
          'Erfahren Sie, wie Sie AI copilots mit Microsoft Copilot Studio erstellen und anpassen können, um die Produktivität zu steigern, Arbeitsabläufe zu rationalisieren und AI-driven Lösungen auf Ihre spezifischen Geschäftsanforderungen zuzuschneiden.',
        buttonText: 'Jetzt starten »',
        courseType: 'Offizieller Microsoft Kurs',
        buttonURL: '/course/67d7f20b2238849a6dbe0df2',
      },
    },
    duration: '3h 15m',
    imageSrc:
      'https://aitrainer.aibusinessschool.com/resources/uploads/2024/11/create-copilots.png',
    newTab: false,
  },
];
