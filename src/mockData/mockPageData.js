export const puckContent = [
  {
    type: 'HeadingBlock',
    props: {
      id: 'HeadingBlock-acfecc95-affc-4934-8bb1-ec71d94ddb12',
      title: 'Hello {username}',
    },
  },
  {
    type: 'HeadingBlock',
    props: {
      id: 'HeadingBlock-acfecc95-affc-4934-8bb1-ec71d94ddccc',
      title: 'Your job role or specialization: {userFunction}',
      condition: [
        {
          field: 'function_label',
          value: 'it-ai-data',
          operator: '==',
        },
      ],
    },
  },

  {
    type: 'useCaseBlock',
    props: {
      id: 'usecaseBlock-acfecc95-affc-4934-8bb1-ec71d94ddccc',
      title: 'Your job role or specialization: marketing {userFunction}',
      content: '[usecase_list]',
      condition: [
        {
          field: 'function_label',
          value: 'userOnboarding',
          operator: '==',
        },
        {
          field: 'language',
          value: 'userLanguage',
          operator: '==',
        },
        {
          field: 'segment',
          value: 'segment2',
          operator: '==',
        },
      ],
    },
  },

  {
    type: 'CardsBlock',
    props: {
      id: 'cardsBlock-acfecc95-affc-4934-8bb1-ec71d94ddccc',
      title: 'Most popular AI Playgrounds',
      description: 'Deep dives into some of the "hottest" AI technologies.',
      children: [
        {
          type: 'Card',
          props: {
            id: 'card-acfecc95-affc-4934-8bb1-ec71d94ddccc',
            title: 'ChatGPT',
            description:
              'Get a crash course on prompting, download cheat sheets, and practice with ChatGPT prompts.',
            image:
              'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/ChatGPT_logo.svg',
            link: '#',
            condition: [
              {
                field: 'function_label',
                value: 'all',
                operator: 'all',
              },
            ],
          },
        },
        {
          type: 'Card',
          props: {
            id: 'card-acfecc95-affc-4934-8bb1-ec71d94ddcc2',
            title: 'GitHub Copilot',
            description:
              'GitHub Copilot is a tool that helps you write code faster and more efficiently.',
            image:
              'https://aitrainer.aibusinessschool.com/resources/uploads/2025/01/Microsoft_365_Copilot_Icon.svg',
            link: '#',
            condition: [
              {
                field: 'function_label',
                value: 'it-ai-data',
                operator: '==',
              },
            ],
          },
        },
      ],
    },
  },
  {
    type: 'CardsBlock',
    props: {
      id: 'cardsBlock-example',
      title: 'Segment Specific Content',
      condition: [
        {
          segment: '65f1a1b1e5c57e001234a001', // Technical User segment ID'si
        },
      ],
    },
  },
];
