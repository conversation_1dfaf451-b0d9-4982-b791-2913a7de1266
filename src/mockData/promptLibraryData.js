export const promptLibraryData = [
  {
    "function": [],
    "_id": "674965e830785ee63cb341e9",
    "Slug": "quiz-yourself-7dde2420-4203-4af0-ac72-7351df849b41",
    "Rank": 1,
    "showOnWork": false,
    "showOnPersonal": true,
    "Id": "7dde2420-4203-4af0-ac72-7351df849b41_en-us",
    "PromptId": "7dde2420-4203-4af0-ac72-7351df849b41",
    "Title": "Quiz yourself",
    "DisplayText": "Create a quiz for 5th grade math",
    "DisplayDescription": "",
    "DisplayCategory": "Create",
    "CommandText": "Create a quiz for 5th grade math",
    "Products": [
        "OneNote"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468eea"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341ea",
    "Slug": "add-an-image-5c0c3267-1396-4b0c-9af5-46a65e139516",
    "Rank": 3,
    "showOnWork": false,
    "showOnPersonal": true,
    "Id": "5c0c3267-1396-4b0c-9af5-46a65e139516_en-us",
    "PromptId": "5c0c3267-1396-4b0c-9af5-46a65e139516",
    "Title": "Add an image",
    "DisplayText": "Add an image of <placeholder>a celebration</placeholder>",
    "DisplayDescription": "<b>Take it to the next level</b><br>You can be more specific if you like. For example, you could specify the type of celebration.",
    "DisplayCategory": "Edit",
    "CommandText": "Add an image of",
    "Products": [
        "PowerPoint"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468eeb"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341eb",
    "Slug": "get-in-shape-4a830339-5ded-411e-85d5-a80d6d96e049",
    "Rank": 4,
    "showOnWork": false,
    "showOnPersonal": true,
    "Id": "4a830339-5ded-411e-85d5-a80d6d96e049_en-us",
    "PromptId": "4a830339-5ded-411e-85d5-a80d6d96e049",
    "Title": "Get in shape",
    "DisplayText": "Suggest 3 outdoor activities I can do for exercise in Seattle in March. Include any equipment or fees that might be needed.",
    "DisplayDescription": "",
    "DisplayCategory": "Create",
    "CommandText": "Suggest 3 outdoor activities I can do for exercise in Seattle in March. Include any equipment or fees that might be needed.",
    "Products": [
        "OneNote"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468eec"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341ec",
    "Slug": "create-a-vacation-presentation-a58f5af2-5c43-470a-81d3-90fb8dd2b00e",
    "Rank": 5,
    "showOnWork": false,
    "showOnPersonal": true,
    "Id": "a58f5af2-5c43-470a-81d3-90fb8dd2b00e_en-us",
    "PromptId": "a58f5af2-5c43-470a-81d3-90fb8dd2b00e",
    "Title": "Create a technology presentation",
    "DisplayText": "Create a presentation about <placeholder>innovations in technology</placeholder>",
    "DisplayDescription": "<b>Take it to the next level</b><br>You can specify particular slides you want to make sure are included. For example, &quot;Be sure to include slides for telecommunications, green technologies, and robotics.&quot;<br><br><b>More to try</b><br>After it creates the presentation, consider changing some of the images Copilot added to your own pictures.",
    "DisplayCategory": "Create",
    "CommandText": "Create a presentation about",
    "Products": [
        "PowerPoint"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468eed"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": ["Human Resources"]
},
{
    "function": [],
    "_id": "674965e830785ee63cb341ed",
    "Slug": "write-more-confidently-11ab595e-3179-400f-8a11-3064d1e75202",
    "Rank": 6,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "11ab595e-3179-400f-8a11-3064d1e75202_en-us",
    "PromptId": "11ab595e-3179-400f-8a11-3064d1e75202",
    "Title": "Write more confidently",
    "DisplayText": "How can I more concisely describe <placeholder>time management</placeholder>?",
    "DisplayDescription": "<b>More to try</b><br>Consider asking Copilot in PowerPoint to create a presentation from your document.",
    "DisplayCategory": "Ask",
    "CommandText": "How can I more concisely describe",
    "Products": [
        "Word"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468eee"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": ["Marketing"]
},
{
    "function": [],
    "_id": "674965e830785ee63cb341ee",
    "Slug": "find-specific-information-176314a9-bcde-4307-b53f-aa3525c7393b",
    "Rank": 7,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "176314a9-bcde-4307-b53f-aa3525c7393b_en-us",
    "PromptId": "176314a9-bcde-4307-b53f-aa3525c7393b",
    "Title": "Find specific information",
    "DisplayText": "What does the document say about <placeholder>roles and responsibilities</placeholder>?",
    "DisplayDescription": "<b>Experiment</b><br>You can ask Copilot to explain different parts of the document. Change the prompt wording to suit your needs.",
    "DisplayCategory": "Understand",
    "CommandText": "What does the document say about",
    "Products": [
        "Word"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468eef"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": ["Marketing"]
},
{
    "function": [],
    "_id": "674965e830785ee63cb341ef",
    "Slug": "create-a-shopping-list-9364c80d-9e0e-4b14-86d9-a59cb35a0af1",
    "Rank": 8,
    "showOnWork": false,
    "showOnPersonal": true,
    "Id": "9364c80d-9e0e-4b14-86d9-a59cb35a0af1_en-us",
    "PromptId": "9364c80d-9e0e-4b14-86d9-a59cb35a0af1",
    "Title": "Create a shopping list",
    "DisplayText": "Create a birthday shopping list",
    "DisplayDescription": "<b>Take it to the next level</b><br>Consider giving Copilot more details about the sort of things you need. For example, &quot;Create a shopping list for a birthday celebration for a group of kids aged 6-12. Include snacks, drinks, and activities.&quot;",
    "DisplayCategory": "Create",
    "CommandText": "Create a birthday shopping list",
    "Products": [
        "OneNote"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468ef0"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341f0",
    "Slug": "write-a-formula-f634575a-a16f-4ad5-baf8-a3f6d20d319e",
    "Rank": 9,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "f634575a-a16f-4ad5-baf8-a3f6d20d319e_en-us",
    "PromptId": "f634575a-a16f-4ad5-baf8-a3f6d20d319e",
    "Title": "Write a formula",
    "DisplayText": "Write a formula <placeholder>for the average of each column</placeholder>",
    "DisplayDescription": "<b>More to try</b><br>Include your own details to make the prompt specific to your needs.",
    "DisplayCategory": "Create",
    "CommandText": "Write a formula ",
    "Products": [
        "Excel"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468ef1"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341f1",
    "Slug": "compare-columns-a2e2f680-6ddc-4cac-9551-a8a3f3a25033",
    "Rank": 10,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "a2e2f680-6ddc-4cac-9551-a8a3f3a25033_en-us",
    "PromptId": "a2e2f680-6ddc-4cac-9551-a8a3f3a25033",
    "Title": "Compare columns",
    "DisplayText": "Add a column to compare <placeholder>FY24 sales</placeholder> and <placeholder>FY23 sales</placeholder>",
    "DisplayDescription": "<b>To be clear</b><br>This prompt creates a column that compares the two named columns.",
    "DisplayCategory": "Create",
    "CommandText": "Add a column to compare",
    "Products": [
        "Excel"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468ef2"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341f2",
    "Slug": "add-a-formula-column-9273f38d-3bb0-472d-b61f-f1daeb597434",
    "Rank": 15,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "9273f38d-3bb0-472d-b61f-f1daeb597434_en-us",
    "PromptId": "9273f38d-3bb0-472d-b61f-f1daeb597434",
    "Title": "Add a formula column",
    "DisplayText": "Add a column <placeholder>that calculates the difference of two dates</placeholder>",
    "DisplayDescription": "<b>To be clear</b><br>This prompt creates a column that compares the difference of two named date columns.<br><br><b>More to try</b><br>Include your own details to make the prompt specific to your needs.",
    "DisplayCategory": "Create",
    "CommandText": "Add a column ",
    "Products": [
        "Excel"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468ef3"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341f3",
    "Slug": "understand-quickly-6b7bbde2-3d1a-432b-84da-f58df516492c",
    "Rank": 20,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "6b7bbde2-3d1a-432b-84da-f58df516492c_en-us",
    "PromptId": "6b7bbde2-3d1a-432b-84da-f58df516492c",
    "Title": "Understand quickly",
    "DisplayText": "Explain this document in three sentences",
    "DisplayDescription": "<b>More to try</b><br>If something in those three sentences catches your eye, ask Copilot to tell you more about that thing",
    "DisplayCategory": "Understand",
    "CommandText": "Explain this document in three sentences",
    "Products": [
        "Word"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468ef4"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341f4",
    "Slug": "next-steps-6eace38a-6625-474d-902b-6721894fc733",
    "Rank": 20,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "6eace38a-6625-474d-902b-6721894fc733_en-us",
    "PromptId": "6eace38a-6625-474d-902b-6721894fc733",
    "Title": "Next steps",
    "DisplayText": "Break down the fundraising event plan into steps.",
    "DisplayDescription": "<b>Take it to the next level</b><br>For a particular step you could ask Copilot to identify potential risks to successfully completing it.<br><br><b>More to try</b><br>Consider asking Copilot to &quot;Rewrite this as a table with a column for who is responsible for each line item.&quot;",
    "DisplayCategory": "Catch up",
    "CommandText": "Break down the fundraising event plan into steps.",
    "Products": [
        "OneNote"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468ef5"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341f5",
    "Slug": "spot-trends-052468d6-1c84-4b33-a2d4-bc703135a5ce",
    "Rank": 20,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "052468d6-1c84-4b33-a2d4-bc703135a5ce_en-us",
    "PromptId": "052468d6-1c84-4b33-a2d4-bc703135a5ce",
    "Title": "Spot trends",
    "DisplayText": "Are there any trends in my data?",
    "DisplayDescription": "<b>Take it to the next level</b><br>You can specify what you'd like the result to look like. For example, &quot;Show it as a chart&quot; or &quot;Show me trends over time&quot;.",
    "DisplayCategory": "Understand",
    "CommandText": "Are there any trends in my data?",
    "Products": [
        "Excel"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468ef6"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341f6",
    "Slug": "calculate-based-on-other-table-e9639355-4370-4c7f-8a62-e4fb5ecf9e79",
    "Rank": 23,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "e9639355-4370-4c7f-8a62-e4fb5ecf9e79_en-us",
    "PromptId": "e9639355-4370-4c7f-8a62-e4fb5ecf9e79",
    "Title": "Calculate based on other table",
    "DisplayText": "Create a column that calculates <placeholder>the total sales for each employee and insert it to column B</placeholder>",
    "DisplayDescription": "<b>To be clear</b><br>This prompt looks up sales and calculates totals for each employee",
    "DisplayCategory": "Create",
    "CommandText": "Create a column that calculates",
    "Products": [
        "Excel"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468ef7"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341f7",
    "Slug": "split-text-ff45205e-58ee-4e43-bf6c-30ac74f5e68c",
    "Rank": 25,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "ff45205e-58ee-4e43-bf6c-30ac74f5e68c_en-us",
    "PromptId": "ff45205e-58ee-4e43-bf6c-30ac74f5e68c",
    "Title": "Split text",
    "DisplayText": "Add two columns that split <placeholder>employees' combined name into separate first and last names</placeholder>",
    "DisplayDescription": "<b>To be clear</b><br>Using a table with a column of data, such as first and last names together, you're splitting them into separate columns",
    "DisplayCategory": "Create",
    "CommandText": "Add two columns that split ",
    "Products": [
        "Excel"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468ef8"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341f8",
    "Slug": "tips-and-tricks-e20a03a5-9080-4f44-9e51-e80aeab5639c",
    "Rank": 28,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "e20a03a5-9080-4f44-9e51-e80aeab5639c_en-us",
    "PromptId": "e20a03a5-9080-4f44-9e51-e80aeab5639c",
    "Title": "Tips and tricks",
    "DisplayText": "What are some tricks for working with dates?",
    "DisplayDescription": "<b>Experiment</b><br>Replace &quot;dates&quot; with the subject you want more information on.<br><br> <b>More to try</b><br>Include your own details to make the prompt specific to your needs.<br>",
    "DisplayCategory": "Ask",
    "CommandText": "What are some tricks for working with dates?",
    "Products": [
        "Excel"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468ef9"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341f9",
    "Slug": "list-pros-and-cons-fdbbba6c-f86e-43eb-a98b-4f0c7e98de7c",
    "Rank": 30,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "fdbbba6c-f86e-43eb-a98b-4f0c7e98de7c_en-us",
    "PromptId": "fdbbba6c-f86e-43eb-a98b-4f0c7e98de7c",
    "Title": "List pros and cons",
    "DisplayText": "List the pros and cons of <placeholder>the different project ideas mentioned in this document</placeholder>",
    "DisplayDescription": "<b>More to try</b><br>Change the placeholder to be specific to your pros and cons.",
    "DisplayCategory": "Understand",
    "CommandText": "List the pros and cons of",
    "Products": [
        "Word"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468efa"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": ["Human Resources"]
},
{
    "function": [],
    "_id": "674965e830785ee63cb341fa",
    "Slug": "add-drop-down-menu-9e662090-1fe2-4da4-b114-b8638aebef54",
    "Rank": 35,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "9e662090-1fe2-4da4-b114-b8638aebef54_en-us",
    "PromptId": "9e662090-1fe2-4da4-b114-b8638aebef54",
    "Title": "Add drop-down menu",
    "DisplayText": "How do I create a drop-down menu?",
    "DisplayDescription": "",
    "DisplayCategory": "Ask",
    "CommandText": "How do I create a drop-down menu?",
    "Products": [
        "Excel"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468efb"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341fb",
    "Slug": "add-a-slide-ab66e07e-3c4f-4075-81e0-239b49fad71b",
    "Rank": 40,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "ab66e07e-3c4f-4075-81e0-239b49fad71b_en-us",
    "PromptId": "ab66e07e-3c4f-4075-81e0-239b49fad71b",
    "Title": "Add a slide",
    "DisplayText": "Add a slide about <placeholder>the impact of traveling</placeholder>.",
    "DisplayDescription": "<b>Take it to the next level</b><br>The more detail you can give Copilot the more complete your slide will be. Try giving it more details like the type of travel you have in mind.<br><br><b>Experiment</b><br>You can ask Copilot to add slides about almost anything. Such as &quot;Add a slide about best practices in time management.&quot;",
    "DisplayCategory": "Edit",
    "CommandText": "Add a slide about",
    "Products": [
        "PowerPoint"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468efc"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341fc",
    "Slug": "understand-my-data-26ded1c4-6f40-45ba-b90f-6ce456a1b8de",
    "Rank": 40,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "26ded1c4-6f40-45ba-b90f-6ce456a1b8de_en-us",
    "PromptId": "26ded1c4-6f40-45ba-b90f-6ce456a1b8de",
    "Title": "Understand my data",
    "DisplayText": "Which items have the most <placeholder>remaining inventory</placeholder>?",
    "DisplayDescription": "<b>More to try</b><br>You can use some visual cues to understand your data as well. For example you can ask Copilot to &quot;Highlight the item with the most inventory in green and the item with the least inventory in red.&quot;",
    "DisplayCategory": "Understand",
    "CommandText": "Which items have the most",
    "Products": [
        "Excel"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468efd"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341fd",
    "Slug": "extract-key-information-81d38c8c-5911-44ec-b6ae-2a882dd7da9e",
    "Rank": 50,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "81d38c8c-5911-44ec-b6ae-2a882dd7da9e_en-us",
    "PromptId": "81d38c8c-5911-44ec-b6ae-2a882dd7da9e",
    "Title": "Extract key information",
    "DisplayText": "Summarize this presentation",
    "DisplayDescription": "<b>To be clear</b><br>You need a slide deck with enough content to summarize. Something with at least 200 words will work best.<br><br><b>More to try</b><br>Consider asking PowerPoint to show any action items or due dates in this presentation.",
    "DisplayCategory": "Understand",
    "CommandText": "Summarize this presentation  ",
    "Products": [
        "PowerPoint"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468efe"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341fe",
    "Slug": "brainstorm-1f198974-171d-4ac4-b02f-f7f86c225a12",
    "Rank": 50,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "1f198974-171d-4ac4-b02f-f7f86c225a12_en-us",
    "PromptId": "1f198974-171d-4ac4-b02f-f7f86c225a12",
    "Title": "Brainstorm",
    "DisplayText": "Give me ideas for <placeholder>ways to improve productivity and better manage my time</placeholder>",
    "DisplayDescription": "<b>Take it to the next level</b><br>You can specify how many ideas you want and ask it to be more detailed if you like. For example &quot;Include pros and cons of each idea.&quot;<br><br><b>More to try</b><br>If one of the ideas appeals to you, ask Copilot to help you create a plan to implement that idea.",
    "DisplayCategory": "Create",
    "CommandText": "Give me ideas for",
    "Products": [
        "OneNote"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468eff"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb341ff",
    "Slug": "draft-an-email-deea439a-3dde-468a-a710-773b5a29d4e5",
    "Rank": 50,
    "showOnWork": false,
    "showOnPersonal": true,
    "Id": "deea439a-3dde-468a-a710-773b5a29d4e5_en-us",
    "PromptId": "deea439a-3dde-468a-a710-773b5a29d4e5",
    "Title": "Draft an email",
    "DisplayText": "Write an email to <placeholder>invite parents to the PTSA bake sale</placeholder>",
    "DisplayDescription": "<b>Take it to the next level</b><br>The more details you can give it, the better. Consider including the date, time, and location for example.<br><br><b>More to try</b><br>You can ask Copilot to modify it's first draft. For example, &quot;Make it friendly and energetic&quot; to set an upbeat tone.\n\nIf you don't love Copilot's first effort, select the Regenerate button to have it try again.",
    "DisplayCategory": "Create",
    "CommandText": "Write an email to",
    "Products": [
        "Outlook"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468f00"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb34200",
    "Slug": "create-a-plan-075a8b1b-759d-4bff-aa2d-b72341322d00",
    "Rank": 60,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "075a8b1b-759d-4bff-aa2d-b72341322d00_en-us",
    "PromptId": "075a8b1b-759d-4bff-aa2d-b72341322d00",
    "Title": "Create a plan",
    "DisplayText": "Help me plan <placeholder>my career growth as a lawyer, including goals and key milestones</placeholder>",
    "DisplayDescription": "<b>More to try</b><br>After you review and edit the plan, copy it and ask Copilot in Word to create a planning document for you.",
    "DisplayCategory": "Create",
    "CommandText": "Help me plan",
    "Products": [
        "OneNote"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468f01"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb34201",
    "Slug": "summarize-this-doc-907725d0-7e57-46d1-ab52-ad69ba6624c3",
    "Rank": 60,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "907725d0-7e57-46d1-ab52-ad69ba6624c3_en-us",
    "PromptId": "907725d0-7e57-46d1-ab52-ad69ba6624c3",
    "Title": "Summarize this doc",
    "DisplayText": "Summarize this document <placeholder>in 3 key points</placeholder>",
    "DisplayDescription": "<b>More to try</b><br>Consider asking Copilot if there are any action items in the document. ",
    "DisplayCategory": "Understand",
    "CommandText": "Summarize this document",
    "Products": [
        "Word"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468f02"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb34202",
    "Slug": "extract-the-month-f06908e1-2f90-4487-932c-079289131e45",
    "Rank": 60,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "f06908e1-2f90-4487-932c-079289131e45_en-us",
    "PromptId": "f06908e1-2f90-4487-932c-079289131e45",
    "Title": "Extract the month",
    "DisplayText": "Add a column to extract the month from the date",
    "DisplayDescription": "<b>To be clear</b><br>This prompt works best when the column that contains the dates is formatted as a date number format. For more information on formatting your dates in Excel see <a target='_blank' href='https://support.microsoft.com/office/8e10019e-d5d8-47a1-ba95-db95123d273e'>Format a date the way you want</a>.",
    "DisplayCategory": "Create",
    "CommandText": "Add a column to extract the month from the date",
    "Products": [
        "Excel"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468f03"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb34203",
    "Slug": "organize-your-thoughts-bafc6790-47a5-4513-8ced-8cad5c1055be",
    "Rank": 70,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "bafc6790-47a5-4513-8ced-8cad5c1055be_en-us",
    "PromptId": "bafc6790-47a5-4513-8ced-8cad5c1055be",
    "Title": "Organize your thoughts",
    "DisplayText": "Organize this presentation more effectively",
    "DisplayDescription": "<b>To be clear</b><br>Copilot can help organize your presentation by adding an agenda slide, section headers, and section divider slides.",
    "DisplayCategory": "Edit",
    "CommandText": "Organize this presentation more effectively",
    "Products": [
        "PowerPoint"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468f04"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb34204",
    "Slug": "combine-columns-96d1ba63-e2a9-4274-bd45-61a1c16740fc",
    "Rank": 70,
    "showOnWork": true,
    "showOnPersonal": true,
    "Id": "96d1ba63-e2a9-4274-bd45-61a1c16740fc_en-us",
    "PromptId": "96d1ba63-e2a9-4274-bd45-61a1c16740fc",
    "Title": "Combine columns",
    "DisplayText": "Add a column that combines <placeholder>the customer's first and last name</placeholder>",
    "DisplayDescription": "<b>Take it to the next level</b><br>You can tell Copilot more about how you'd like the data formatted. For example, add &quot;...as 'Last name, First name'.&quot;<br><br><b>Experiment</b><br>You can combine almost any kind of columns. For example, Date and Time or City, State, and Zip Code.",
    "DisplayCategory": "Create",
    "CommandText": "Add a column that combines",
    "Products": [
        "Excel"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468f05"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
    "function": [],
    "_id": "674965e830785ee63cb34205",
    "Slug": "brainstorm-funny-names-8a196677-0801-4364-843a-490888b97f72",
    "Rank": 70,
    "showOnWork": false,
    "showOnPersonal": true,
    "Id": "8a196677-0801-4364-843a-490888b97f72_en-us",
    "PromptId": "8a196677-0801-4364-843a-490888b97f72",
    "Title": "Brainstorm funny names",
    "DisplayText": "I'm writing a children's story about a friendly dragon. What are some funny names I could use for the dragon?",
    "DisplayDescription": "<b>More to try</b><br>Change the details of the prompt to fit your story.",
    "DisplayCategory": "Ask",
    "CommandText": "I'm writing a children's story about a friendly dragon. What are some funny names I could use for the dragon?",
    "Products": [
        "Word"
    ],
    "UserActivity": {
        "Favorite": {
            "IsFavorite": false,
            "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
        },
        "_id": "67c709280cbe6cb603468f06"
    },
    "Metadata": [],
    "Text": "",
    "ReferenceId": "",
    "Origin": "Global",
    "ProviderId": "CopilotLab",
    "ProviderName": "Microsoft",
    "ProviderPartyType": "FirstParty",
    "DeveloperName": null,
    "Function": []
},
{
  "function": [],
  "_id": "674965e830785ee63cb34250",
  "Slug": "summarize-columns-b428a45c-b9a5-4b82-9af0-5cb8f3ef5c65",
  "Rank": 3300,
  "showOnWork": true,
  "showOnPersonal": true,
  "Id": "b428a45c-b9a5-4b82-9af0-5cb8f3ef5c65_en-us",
  "PromptId": "b428a45c-b9a5-4b82-9af0-5cb8f3ef5c65",
  "Title": "Summarize columns",
  "DisplayText": "Add a formula that calculates the <placeholder>average revenue and the average budget for campaigns with an ROI that is greater than 75%</placeholder>",
  "DisplayDescription": "<b>To be clear</b><br>Customize the details of the prompt to fit your scenario.",
  "DisplayCategory": "Create",
  "CommandText": "Add a formula that calculates the",
  "Products": [
      "Excel"
  ],
  "UserActivity": {
      "Favorite": {
          "IsFavorite": false,
          "ActivityDateTimeInUtc": "0001-01-01T00:00:00+00:00"
      },
      "_id": "67c709280cbe6cb603468f51"
  },
  "Metadata": [],
  "Text": "",
  "ReferenceId": "",
  "Origin": "Global",
  "ProviderId": "CopilotLab",
  "ProviderName": "Microsoft",
  "ProviderPartyType": "FirstParty",
  "DeveloperName": null,
  "Function": []
},
];

// Provider seçenekleri - Mevcut promptlardan dinamik olarak oluşturuluyor
export const providers = [
  {
    value: "Microsoft",
    label: "Microsoft"
  },
  {
    value: "Open AI",
    label: "OpenAI"
  }
];

// Her provider için mevcut app'leri belirle
export const getAppsForProvider = (provider) => {
  if (provider === 'all') {
    // Tüm uygulamaları göster
    const microsoftApps = ["OneNote", "PowerPoint", "Word", "Excel", "Outlook"].map(product => ({
      value: product,
      label: product
    }));
    
    const openAiApps = ["ChatGPT", "Dall-E"].map(product => ({
      value: product,
      label: product
    }));
    
    return [...microsoftApps, ...openAiApps];
  } else if (provider === 'Microsoft') {
    // Sadece Microsoft uygulamalarını göster
    return ["OneNote", "PowerPoint", "Word", "Excel", "Outlook"].map(product => ({
      value: product,
      label: product
    }));
  } else if (provider === 'Open AI') {
    // Sadece OpenAI uygulamalarını göster
    return ["ChatGPT", "Dall-E"].map(product => ({
      value: product,
      label: product
    }));
  }
  
  // Diğer durumlar için boş dizi döndür
  return [];
};

// Seçili provider ve app için mevcut fonksiyonları belirle
export const getFunctionsForProviderAndApp = (provider, app) => {
  const funcs = [...new Set(
    promptLibraryData
      .filter(prompt => 
        prompt.ProviderName === provider && 
        (app ? prompt.Products.includes(app) : true)
      )
      .flatMap(prompt => {
        const functionField = prompt.Function || prompt.function || [];
        return Array.isArray(functionField) ? functionField : [functionField];
      })
      .filter(func => func) // Boş değerleri filtrele
  )].map(func => ({
    value: func.toLowerCase(),
    label: func
  }));
  return funcs;
};

// Fonksiyonlar
export const functions = [
  { value: 'human-resources', label: 'Human Resources' },
  { value: 'marketing', label: 'Marketing' },
  { value: 'communications', label: 'Communications' },
  { value: 'finance', label: 'Finance' },
  { value: 'sales', label: 'Sales' },
  { value: 'information-technology', label: 'Information Technology' },
  { value: 'customer-service', label: 'Customer Service' },
  { value: 'legal', label: 'Legal' },
  { value: 'design', label: 'Design' }
];

// Provider URL'lerini oluşturmak için yardımcı fonksiyon
export const getProviderUrl = (provider) => {
  if (!provider || provider === 'all') return '';
  return provider.toLowerCase().replace(/\s+/g, '-');
};

// URL'den provider değerini almak için yardımcı fonksiyon
export const getProviderFromUrl = (url) => {
  const provider = providers.find(p => 
    getProviderUrl(p.value) === url.toLowerCase()
  );
  return provider ? provider.value : 'all';
};

// App URL'lerini oluşturmak için yardımcı fonksiyon
export const getAppUrl = (app) => {
  if (!app || app === 'all') return '';
  return app.toLowerCase().replace(/\s+/g, '-');
};

// URL'den app değerini almak için yardımcı fonksiyon
export const getAppFromUrl = (provider, urlApp) => {
  if (!urlApp) return 'all';
  
  const apps = getAppsForProvider(provider);
  const app = apps.find(a => getAppUrl(a.value) === urlApp.toLowerCase());
  return app ? app.value : 'all';
};

// Function URL'lerini oluşturmak için yardımcı fonksiyon
export const getFunctionUrl = (func) => {
  if (!func || func === 'all') return '';
  return func.toLowerCase().replace(/\s+/g, '-');
};

// URL'den function değerini almak için yardımcı fonksiyon
export const getFunctionFromUrl = (provider, app, urlFunction) => {
  if (!urlFunction) return 'all';
  
  const functions = getFunctionsForProviderAndApp(provider, app);
  const func = functions.find(f => getFunctionUrl(f.value) === urlFunction.toLowerCase());
  return func ? func.value : 'all';
}; 