import LibraryBooksOutlinedIcon from '@mui/icons-material/LibraryBooksOutlined';
import SchoolOutlinedIcon from '@mui/icons-material/SchoolOutlined';
import EmojiObjectsOutlinedIcon from '@mui/icons-material/EmojiObjectsOutlined';
import i18n from 'i18next';

// Dillere göre kart bilgileri
const cardsData = {
  en: [
    {
      title: 'Most useful IT Prompts',
      description: 'Access our curated collection of effective prompts for various use cases',
      tag: 'Apply',
      icon: LibraryBooksOutlinedIcon,
      link: '/prompt-library/open-ai/chatgpt',
      buttonText: 'Browse library',
    },
    {
      title: 'IT Prompt cheat sheet',
      description: 'Level up your ChatGPT skills with our quick reference guide',
      tag: 'Level up',
      icon: EmojiObjectsOutlinedIcon,
      link: '/sheets/it',
      buttonText: 'View cheat sheet',
    },
  ],
  de: [
    {
      title: 'Nützliche IT-Prompts',
      description:
        'Greifen Sie auf unsere kuratierte Sammlung effektiver Prompts für verschiedene Anwendungsfälle zu',
      tag: 'Anwendung',
      icon: LibraryBooksOutlinedIcon,
      link: '/prompt-library/open-ai/chatgpt',
      buttonText: 'Bibliothek durchsuchen',
    },
    {
      title: 'IT Prompt Cheat Sheet',
      description:
        'Verbessern Sie Ihre ChatGPT-Fähigkeiten mit einem kompakten Cheat Sheet (Spickzettel)',
      tag: 'Mehr anzeigen',
      icon: EmojiObjectsOutlinedIcon,
      link: '/sheets/it',
      buttonText: 'Spickzettel ansehen',
    },
  ],
};

// i18n.language değerini kullanarak mevcut dile göre doğru kartları döndür
export const ChatGPTDeveloperToolsPlaygroundCards = () => {
  const currentLanguage = i18n.language || 'en';
  // Eğer mevcut dil için kart seti yoksa İngilizce setini varsayılan olarak kullan
  return cardsData[currentLanguage] || cardsData.en;
};
