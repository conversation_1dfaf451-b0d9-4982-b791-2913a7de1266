// Colors
$primary-color: #377dff;
$primary-color-light: #2d5aa8;
$primary-color-dark: #183160;
$primary-text-color:#677788;
$primary-text-color-dark: #1E2022;

$secondary-color: #4A90E2;
$secondary-color-light: #6ba5e9;
$secondary-color-dark: #3672c4;

$success-color: #27AE60;
$success-color-light: #2ecc71;
$success-color-dark: #219a52;

$warning-color: #F2C94C;
$warning-color-dark: #B76E00;
$error-color: #EB5757;
$info-color: #2F80ED;

// Text Colors
$text-primary: rgba(0, 0, 0, 0.87);
$text-secondary: rgba(0, 0, 0, 0.60);
$text-disabled: rgba(0, 0, 0, 0.38);

// Background Colors
$bg-light: #f9f9f9;
$bg-light-dark: #E0E0E0;
$bg-paper: #FFFFFF;
$bg-default: #FAFAFA;
$bg-dark: #1E2022;
$bg-dark-light: #2E3133;
$text-light: #FFFFFF;

// Border Colors
$divider-color: #ecede8;
$border-color: #ecede8;

// Spacing
$spacing-1: 4px;
$spacing-2: 8px;
$spacing-3: 16px;
$spacing-4: 24px;
$spacing-5: 32px;
$spacing-6: 40px;

// Border Radius
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;

// Shadows
$shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
$shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);

// Font Sizes
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

// Font Weights
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// CSS Variables (for MUI integration)
:root {
  --primary-color: #{$primary-color};
  --primary-color-light: #{$primary-color-light};
  --primary-color-dark: #{$primary-color-dark};

  --secondary-color: #{$secondary-color};
  --secondary-color-light: #{$secondary-color-light};
  --secondary-color-dark: #{$secondary-color-dark};

  --success-color: #{$success-color};
  --success-color-light: #{$success-color-light};
  --success-color-dark: #{$success-color-dark};
  
  --warning-color: #{$warning-color};
  --warning-color-dark: #{$warning-color-dark};
  --error-color: #{$error-color};
  --info-color: #{$info-color};
  
  --text-primary: #{$text-primary};
  --text-secondary: #{$text-secondary};
  --text-disabled: #{$text-disabled};
  
  --bg-light: #{$bg-light};
  --bg-paper: #{$bg-paper};
  --bg-default: #{$bg-default};
  
  --bg-light-dark: #{$bg-light-dark};
  
  --divider-color: #{$divider-color};
  --border-color: #{$border-color};
  
  --spacing-1: #{$spacing-1};
  --spacing-2: #{$spacing-2};
  --spacing-3: #{$spacing-3};
  --spacing-4: #{$spacing-4};
  --spacing-5: #{$spacing-5};
  --spacing-6: #{$spacing-6};
  
  --border-radius-sm: #{$border-radius-sm};
  --border-radius-md: #{$border-radius-md};
  --border-radius-lg: #{$border-radius-lg};
}

// Breakpoints
$mobile: 576px;
$tablet: 768px;
$desktop: 1024px;

body{
    padding:0;
    margin:0; 
    font-family: "Inter", serif;
}

// Icon Colors
$icon-favorite: #F4A7A3;     // Pembe/kırmızı tonu
$icon-sales: #90CAF9;        // Açık mavi
$icon-management: #CE93D8;   // Mor
$icon-toolkit: #80CBC4;      // Turkuaz/yeşil

// Icon Background Colors (daha açık tonlar)
$icon-bg-favorite: rgba($icon-favorite, 0.1);
$icon-bg-sales: rgba($icon-sales, 0.1);
$icon-bg-management: rgba($icon-management, 0.1);
$icon-bg-toolkit: rgba($icon-toolkit, 0.1);