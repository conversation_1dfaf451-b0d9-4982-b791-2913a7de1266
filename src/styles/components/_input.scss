@use '../abstracts/variables' as *;

// Ortak form alanı stilleri
.MuiFormControl-root {
  width: 100%;
  margin-bottom: $spacing-3;

  &--no-margin {
    margin-bottom: 0;
  }

  &.Mui-disabled {
    opacity: 0.7;
    cursor: not-allowed;
    
    .MuiInputLabel-root,
    .MuiInputBase-root,
    .MuiFormHelperText-root {
      cursor: not-allowed;
    }
  }
}

// Input Label stilleri
.MuiInputLabel-root {
  color: $text-secondary;
  transform: translate(14px, 14px) scale(1);
  
  &.Mui-focused {
    color: $primary-color;
  }

  &.Mui-error {
    color: $error-color;
  }

  &.Mui-disabled {
    color: $text-disabled !important;
  }

  &.MuiInputLabel-shrink {
    transform: translate(14px, -6px) scale(0.75);
  }
}

// Outlined Input stilleri
.MuiOutlinedInput-root {
  height: 52px;
  background-color: $bg-paper !important;
  border-radius: $border-radius-md !important;
  transition: all 0.2s ease-in-out;

  // Hover durumu
  &:hover:not(.Mui-disabled) {
    .MuiOutlinedInput-notchedOutline {
      border-color: $primary-color;
    }
  }

  // Focus durumu
  &.Mui-focused {
    
    background-color: $bg-paper !important;
    .MuiOutlinedInput-notchedOutline {
      border-color: $primary-color;
      border-width: 1px;
    }
  }

  // Hata durumu
  &.Mui-error {
    background-color: rgba($error-color, 0.02);
    
    .MuiOutlinedInput-notchedOutline {
      border-color: $error-color;
    }
  }

  // Disabled durumu
  &.Mui-disabled {
    background-color: $bg-light !important;
    cursor: not-allowed;
    
    .MuiOutlinedInput-notchedOutline {
      border-color: rgba($border-color, 0.5);
    }

    input, textarea {
      color: $text-disabled;
      -webkit-text-fill-color: $text-disabled;
      cursor: not-allowed;
    }

    .MuiInputAdornment-root {
      .MuiIconButton-root {
        color: $text-disabled;
        pointer-events: none;
      }
    }
  }

  // Input border
  .MuiOutlinedInput-notchedOutline {
    border-color: $border-color;
    transition: border-color 0.2s ease-in-out;
  }

  // Input alanı
  input {
    padding: $spacing-2 $spacing-3;
    font-size: $font-size-sm;
    color: $text-primary;
    
    &::placeholder {
      color: $text-disabled;
      opacity: 1;
    }
  }

  // Textarea için özel stiller
  textarea {
    padding: $spacing-2 $spacing-3;
    font-size: $font-size-sm;
    color: $text-primary;
    
    &::placeholder {
      color: $text-disabled;
      opacity: 1;
    }
  }
}

// Multiline input için height override
.MuiOutlinedInput-root.MuiInputBase-multiline {
  height: auto;
  min-height: 120px;
  padding: $spacing-2 0;
}

// Input ikonları
.MuiInputAdornment-root {
  margin: 0 $spacing-3;
  
  .MuiIconButton-root {
    color: $text-secondary;
    padding: $spacing-1;
    width: 24px !important;
    height: 24px !important;
    
    &:hover {
      background-color: rgba($primary-color, 0.04);
    }

    &.Mui-disabled {
      color: $text-disabled;
    }
  }

  .MuiTypography-root {
    color: $text-secondary;
    font-size: $font-size-sm;
  }
}

// Helper text ve error mesajları
.MuiFormHelperText-root {
  margin: $spacing-1 0 0 $spacing-2;
  font-size: $font-size-xs;
  
  &.Mui-error {
    color: $error-color;
  }
}

// Select komponenti için özel stiller
.MuiSelect-select {
  padding: $spacing-2 $spacing-3 !important;
  font-size: $font-size-sm !important;
  
  &.MuiSelect-outlined {
    padding-right: 32px !important;
  }
}

// Checkbox ve Radio için özel stiller
.MuiCheckbox-root,
.MuiRadio-root {
  color: $text-secondary;
  padding: $spacing-1;
  
  &.Mui-checked {
    color: $primary-color;
  }
  
  &:hover {
    background-color: rgba($primary-color, 0.04);
  }
  
  &.Mui-disabled {
    color: $text-disabled;
  }
}

// Form Group
.MuiFormGroup-root {
  margin: $spacing-1 0;
  
  .MuiFormControlLabel-root {
    margin-right: $spacing-4;
    
    .MuiTypography-root {
      font-size: $font-size-xs !important;
      color: $text-primary;
    }
  }
} 