@use '../abstracts/variables' as *;

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  letter-spacing: 0.3px;

  // Size variants - Tüm varyantlar için aynı padding ve boyutlar
  &--small {
    padding: 8px 16px;
    font-size: 13px;
  }

  &--medium {
    padding: 12px 16px; // Tüm medium butonlar için aynı padding
    font-size: 14px;
  }

  &--large {
    padding: 16px 24px;
    font-size: 15px;
  }

  // Variants
  &--outlined {
    background-color: transparent;
    border: 1px solid;

    &.button--primary {
      color: $primary-color;
      border-color: $primary-color;

      &:hover:not(:disabled) {
        background-color: $primary-color;
        color: $bg-paper;
        border-color: $primary-color;
        transform: translateY(-1px);
      }

      &:active:not(:disabled) {
        background-color: $primary-color;
        color: $bg-paper;
        border-color: $primary-color;
        transform: translateY(0);
      }
    }

    &.button--success {
      color: $success-color;
      border-color: $success-color;

      &:hover:not(:disabled) {
        background-color: rgba($success-color, 0.04);
        border-color: $success-color-dark;
      }

      &:active:not(:disabled) {
        background-color: rgba($success-color, 0.08);
        border-color: $success-color-dark;
      }
    }

    &.button--default {
      color: rgba(0, 0, 0, 0.87);
      border-color: rgba(0, 0, 0, 0.23);

      &:hover:not(:disabled) {
        background-color: rgba(0, 0, 0, 0.04);
        border-color: rgba(0, 0, 0, 0.87);
      }

      &:active:not(:disabled) {
        background-color: rgba(0, 0, 0, 0.08);
        border-color: rgba(0, 0, 0, 0.87);
      }
    }

    &:disabled {
      border-color: rgba(0, 0, 0, 0.12);
      color: rgba(0, 0, 0, 0.38);
    }
  }

  // Variants - Sadece renk ve arkaplan değişiklikleri
  &--text {
    background-color: transparent;

    &.button--primary {
      color: $primary-color;
      padding:0 !important;
      margin:0 !important;
      height: auto !important;
      width: max-content !important;

      &:hover:not(:disabled) {
        background-color: transparent;
        text-decoration: underline;
        box-shadow: none !important;
        transform: translateY(-1px);
      }

      &:active:not(:disabled) {
        background-color: transparent;
        text-decoration: underline;
        box-shadow: none !important;
        transform: translateY(0);
      }
    }

    &.button--success {
      color: $success-color;

      &:hover:not(:disabled) {
        background-color: rgba($success-color, 0.08);
        transform: translateY(-1px);
      }

      &:active:not(:disabled) {
        background-color: rgba($success-color, 0.12);
        transform: translateY(0);
      }
    }

    &.button--default {
      color: rgba(0, 0, 0, 0.87);

      &:hover:not(:disabled) {
        background-color: rgba(0, 0, 0, 0.06);
        transform: translateY(-1px);
      }

      &:active:not(:disabled) {
        background-color: rgba(0, 0, 0, 0.08);
        transform: translateY(0);
      }
    }
  }

  &--contained {
    background-color: $primary-color;
    color: white;
    box-shadow: 0 2px 4px rgba($primary-color, 0.2);

    &:hover:not(:disabled) {
      background-color:   ($primary-color, 8%);
      box-shadow: 0 4px 8px rgba($primary-color, 0.3);
      transform: translateY(-1px);
    }

    &:active:not(:disabled) {
      background-color: $primary-color-dark;
      box-shadow: 0 2px 4px rgba($primary-color, 0.2);
      transform: translateY(0);
    }

    &.button--default {
      background-color: #f5f5f5;
      color: rgba(0, 0, 0, 0.87);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover:not(:disabled) {
        background-color: #eeeeee;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      &:active:not(:disabled) {
        background-color: #e0e0e0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }

  // Status styles
  &--completed {
    &.button--text {
      color: $success-color;
      font-weight: 500;

      &:hover:not(:disabled) {
        background-color: rgba($success-color, 0.08);
      }
    }
  }

  &--in-progress {
    &.button--text {
      color: $primary-color;
      font-weight: 500;

      &:hover:not(:disabled) {
        background-color: rgba($primary-color, 0.08);
      }
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
    box-shadow: none !important;
    transform: none !important;
    background-color: #f5f5f5;
    color: rgba(0, 0, 0, 0.38);
  }

  &.locked {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
    box-shadow: none !important;
    transform: none !important;
  }

  // Icon styles
  .button-start-icon {
    display: inline-flex;
    margin-right: 8px;
  }

  .button-end-icon {
    display: inline-flex;
    margin-left: 8px;
  }
}