@use '../abstracts/variables' as *;

.white-container {
 
.swiper {
    padding: 12px 0;
    .swiper-slide {
      height: auto !important;
      transition: transform 0.3s ease;
      position: relative !important;
  
      &:hover {
        transform: translateY(-5px);
      }
    }
  
    .swiper-pagination {
      bottom: 0;
      margin-top:12px;
      margin-bottom:-16px;
      position: static !important;
      .swiper-pagination-lock {
        display: none;
      }
      .swiper-pagination-bullet {
        width: 8px;
        height: 8px;
        background: #0072E5;
        opacity: 0.3;
        transition: all 0.3s ease;
        
        &-active {
          opacity: 1;
          width: 24px;
          border-radius: 4px;
        }
      }
    }
  }
}
 