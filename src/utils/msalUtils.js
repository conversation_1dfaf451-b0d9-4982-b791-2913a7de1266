import { MSAL_CONFIG } from '../config-global';

/**
 * Checks if MSAL configuration is valid and has proper keys configured
 * @returns {boolean} True if MSAL is properly configured, false otherwise
 */
export const isMsalConfigured = () => {
  // Sadece clientId ve tenantId'nin boş olup olmadığını kontrol et
  // Env dosyasında değerler varsa, bunları geçerli kabul et
  const isClientIdValid = MSAL_CONFIG.clientId && MSAL_CONFIG.clientId.trim() !== '';
  const isTenantIdValid = MSAL_CONFIG.tenantId && MSAL_CONFIG.tenantId.trim() !== '';

  return isClientIdValid && isTenantIdValid;
};
