/**
 * Topic içeriğinin tipini belirleyen yardımcı fonksiyon.
 * Döngüler içinde de kullanılabilir, hook kullanmaz.
 * @param {Object} topic - Topic nesnesi
 * @param {Object} options - Opsiyonel parametreler
 * @param {Function} options.t - Çeviri fonksiyonu (opsiyonel)
 * @returns {Object} - tipini ve ikonAdını içeren nesne
 */
export const getTopicTypeInfo = (topic, options = {}) => {
  // t fonksiyonu varsa kullan, yoksa basit bir varsayılan fonksiyon tanımla
  const t = options.t || ((key, defaultValue) => defaultValue);
  
  // Varsayılan değerleri önce tanımla
  const defaultType = t('course.contentTypes.video', 'Video');
  const defaultIconName = 'video';
  
  // Topic yoksa varsayılan değerleri döndür
  if (!topic) {
    return { type: defaultType, iconName: defaultIconName };
  }

  // Topic içeriğinin tipini belirleyen fonksiyon
  const getTopicContentType = () => {
    // Eğer herhangi bir blokta playground_type varsa Playground göster
    if (topic.contentBlocks && topic.contentBlocks.some((block) => block.playground_type)) {
      return t('course.contentTypes.playground', 'Playground');
    }

    // VIDEO TİP KONTROLÜ - daha yüksek öncelik veriyoruz
    // Doğrudan type kontrolü
    if (topic.type && topic.type.toLowerCase() === 'video') {
      return t('course.contentTypes.video', 'Video');
    }

    // PDF TİP KONTROLÜ - yüksek öncelikli
    // Doğrudan topic.type kontrolü
    if (topic.type && topic.type.toLowerCase() === 'pdf') {
      return t('course.contentTypes.pdf', 'PDF');
    }

    // FORM TİP KONTROLÜ - yüksek öncelikli
    // Doğrudan topic.type kontrolü
    if (topic.type && topic.type.toLowerCase() === 'form') {
      return t('course.contentTypes.form', 'Form');
    }
    
    // TEXT TİP KONTROLÜ - eğer displayType değeri usecase ise text generation olarak gelmeli
    if (topic.type && topic.type.toLowerCase() === 'text') {
      if (topic.usecase_type && topic.usecase_type.toLowerCase() === 'text') {
        return t('course.contentTypes.textGeneration', 'Text generation');
      }
      return t('course.contentTypes.text', 'Text');
    }

    // Eğer usecase_slug'da dall-e varsa Image generation göster
    if (topic.usecase_slug && topic.usecase_slug.toLowerCase().includes('dall-e')) {
      return t('course.contentTypes.imageGeneration', 'Image generation');
    } else if (topic.usecase_type) {
      // usecase_slug'da dall-e yoksa ve usecase_type varsa onu göster
      return t('course.contentTypes.generation', '{{type}} generation', {
        type: topic.usecase_type.charAt(0).toUpperCase() + topic.usecase_type.slice(1),
      });
    }

    // contentBlocks kontrolü - diğer tipler için
    if (topic.contentBlocks && topic.contentBlocks.length > 0) {
      // dall-e içeren usecase_slug için kontrol - tüm bloklarda ara
      const hasDallE = topic.contentBlocks.some(
        (block) => block.usecase_slug && block.usecase_slug.toLowerCase().includes('dall-e')
      );
      if (hasDallE) {
        return t('course.contentTypes.imageGeneration', 'Image generation');
      }

      // Doğrudan video tipi kontrolü
      const hasVideoType = topic.contentBlocks.some(
        (block) => block.type && block.type.toLowerCase() === 'video'
      );
      if (hasVideoType) {
        return t('course.contentTypes.video', 'Video');
      }

      // Doğrudan PDF tipi kontrolü
      const hasPdfType = topic.contentBlocks.some(
        (block) => block.type && block.type.toLowerCase() === 'pdf'
      );
      if (hasPdfType) {
        return t('course.contentTypes.pdf', 'PDF');
      }

      // Doğrudan FORM tipi kontrolü
      const hasFormType = topic.contentBlocks.some(
        (block) => block.type && block.type.toLowerCase() === 'form'
      );
      if (hasFormType) {
        return t('course.contentTypes.form', 'Form');
      }

      // videoContent kontrolü
      const hasVideoContent = topic.contentBlocks.some(
        (block) =>
          block.videoContent &&
          block.videoContent !== null &&
          Object.keys(block.videoContent).length > 0
      );
      if (hasVideoContent) {
        return t('course.contentTypes.video', 'Video');
      }

      // textContent kontrolü - ayrıca displayType kontrolü yapılacak
      const textContentBlock = topic.contentBlocks.find(
        (block) => block.textContent && Object.keys(block.textContent).length > 0
      );
      
      if (textContentBlock) {
        // Eğer block'un displayType'ı 'usecase' ise "Text generation" göster
        if (textContentBlock.usecase_type && textContentBlock.usecase_type.toLowerCase() === 'text') {
          return t('course.contentTypes.textGeneration', 'Text generation');
        }
        return t('course.contentTypes.text', 'Text');
      }

      // usecase_type için kontrol - eğer dall-e yoksa
      const blockWithUsecaseType = topic.contentBlocks.find((block) => block.usecase_type);
      if (blockWithUsecaseType) {
        return t('course.contentTypes.generation', '{{type}} generation', {
          type:
            blockWithUsecaseType.usecase_type.charAt(0).toUpperCase() +
            blockWithUsecaseType.usecase_type.slice(1),
        });
      }
    }

    // Varsayılan değer
    return t('course.contentTypes.video', 'Video');
  };

  // İçerik tipini hesapla
  const contentType = getTopicContentType();

  // Type'a göre ikon adını belirle
  const isVideoType = contentType.toLowerCase().includes('video');
  const isPdfType = contentType.toLowerCase() === 'pdf';
  const isTextType = contentType.toLowerCase() === 'text';
  const isTextGenerationType = contentType.toLowerCase().includes('text generation');
  const isImageType = contentType.toLowerCase().includes('image');
  const isPlaygroundType = contentType.toLowerCase() === 'playground';

  // İçerik tipine göre uygun ikon adını döndür
  let iconName = 'apps';
  
  if (isVideoType) {
    iconName = 'video';
  } else if (isPdfType || isTextType) {
    iconName = 'text';
  } else if (isTextGenerationType) {
    iconName = 'playground'; // Text generation için playground ikonu kullanıyoruz
  } else if (isImageType) {
    iconName = 'image';
  } else if (isPlaygroundType) {
    iconName = 'playground';
  }

  return { type: contentType, iconName };
};

/**
 * React hook'u olarak topic type alma fonksiyonu.
 * Bu, bileşenlerin üst seviyesinde kullanılmalı, döngüler içinde kullanılmamalıdır.
 * @param {Object} topic - Topic nesnesi
 * @returns {Object} - tipini ve iconName'i içeren nesne
 */
export const useTopicType = (topic) => {
  const { t } = require('react-i18next').useTranslation();
  
  // Sadece t fonksiyonunu parametre olarak geçiriyoruz
  return getTopicTypeInfo(topic, { t });
}; 