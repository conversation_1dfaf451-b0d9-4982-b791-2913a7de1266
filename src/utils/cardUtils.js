/**
 * Card utility functions
 * Kart bileşenleri ile ilgili yardımcı fonksiyonlar
 */

/**
 * Kullanıcı rolüne göre kartları kilitler
 * @param {Array} cards - Ka<PERSON>lar<PERSON><PERSON> bulunduğu array
 * @param {Object} user - <PERSON><PERSON><PERSON><PERSON><PERSON> objesi
 * @param {Function} t - i18n translate fonksiyonu
 * @returns {Array} - Güncellenen kartlar
 */
export const checkAndLockCards = (cards, user, t) => {
  // Eğer kullanıcı role değeri 'limited-use' ise veya erişim şeması 'limited-use' ise
  if (user?.role === 'limited-use' || user?.accessScheme === 'limited-use') {
    return cards.map((card) => ({
      ...card,
      props: {
        ...card.props,
        locked: true,
        tooltipText: t('common.lockedTooltip', 'This feature is locked for your access level'),
      },
    }));
  }
  return cards;
};
