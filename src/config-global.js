import { configs } from 'eslint-plugin-react-refresh';

// Ortam değişkenlerine göre API URL'lerini yapılandırma
const ENV = import.meta.env.VITE_APP_ENV || 'production';

// API URL'leri
const API_URLS = {
  local: import.meta.env.VITE_API_URL_LOCAL,
  development: import.meta.env.VITE_API_URL_DEV,
  preprod: import.meta.env.VITE_API_URL_PREPROD,
  production: import.meta.env.VITE_API_URL_PROD,
};

// CDS API URL'leri
const CDS_API_URLS = {
  local: import.meta.env.VITE_CDS_API_URL_LOCAL,
  development: import.meta.env.VITE_CDS_API_URL_DEV,
  preprod: import.meta.env.VITE_CDS_API_URL_PREPROD,
  production: import.meta.env.VITE_CDS_API_URL_PROD,
};

// MSAL yapılandırmaları - Ortama göre de<PERSON>yor
const MSAL_CONFIGS = {
  local: {
    clientId: import.meta.env.VITE_MSAL_CLIENT_ID,
    tenantId: import.meta.env.VITE_MSAL_TENANT_ID,
    redirectUri: window.location.origin,
    scopes: ['User.Read', 'Files.Read', 'Sites.Read.All'],
  },
  development: {
    clientId: import.meta.env.VITE_MSAL_CLIENT_ID,
    tenantId: import.meta.env.VITE_MSAL_TENANT_ID,
    redirectUri: window.location.origin,
    scopes: ['User.Read', 'Files.Read', 'Sites.Read.All'],
  },
  preprod: {
    clientId: import.meta.env.VITE_MSAL_PREPROD_CLIENT_ID,
    tenantId: import.meta.env.VITE_MSAL_PREPROD_TENANT_ID,
    redirectUri: window.location.origin,
    scopes: ['User.Read', 'Files.Read', 'Sites.Read.All'],
  },
  production: {
    clientId: import.meta.env.VITE_MSAL_PROD_CLIENT_ID,
    tenantId: import.meta.env.VITE_MSAL_PROD_TENANT_ID,
    redirectUri: window.location.origin,
    scopes: ['User.Read', 'Files.Read', 'Sites.Read.All'],
  },
};

// SSO için yapılandırmalar - Ortama göre değişiyor
const SSO_MSAL_CONFIGS = {
  local: {
    clientId: 'a0d72a1a-59d3-487f-8f25-63278f66356f', // SSO için Application ID (Development)
    tenantId: 'b3a862e0-7dae-4d5d-b360-694a45d2a030', // Directory ID
    redirectUri: window.location.origin,
    scopes: ['User.Read', 'openid', 'profile', 'email'],
  },
  development: {
    clientId: 'a0d72a1a-59d3-487f-8f25-63278f66356f', // SSO için Application ID (Development)
    tenantId: 'b3a862e0-7dae-4d5d-b360-694a45d2a030', // Directory ID
    redirectUri: 'https://adoptionv2dev.aibusinessschool.com/login',
    scopes: ['User.Read', 'openid', 'profile', 'email'],
  },
  preprod: {
    clientId: '08cf26fb-5636-4e29-b940-72c6b1eacc66', // SSO için Application ID (Preprod)
    tenantId: 'b3a862e0-7dae-4d5d-b360-694a45d2a030', // Directory ID
    redirectUri: 'https://adoptionv2preprod.aibusinessschool.com/login',
    scopes: ['User.Read', 'openid', 'profile', 'email'],
  },
  production: {
    clientId: 'b6f095a0-0867-4463-bacd-cc1f10376ba6', // SSO için Application ID (Production)
    tenantId: 'b3a862e0-7dae-4d5d-b360-694a45d2a030', // Directory ID
    redirectUri: 'https://ai.aibusinessschool.com/login',
    scopes: ['User.Read', 'openid', 'profile', 'email'],
  },
};

// Ortama göre doğru API URL'sini seç
export const API_URL = API_URLS[ENV] || API_URLS.production;
export const CDS_API_URL = CDS_API_URLS[ENV] || CDS_API_URLS.production;

// Ortama göre MSAL yapılandırmasını seç
export const MSAL_CONFIG = MSAL_CONFIGS[ENV] || MSAL_CONFIGS.production;
// Ortama göre SSO için yapılandırmayı seç
export const SSO_MSAL_CONFIG = SSO_MSAL_CONFIGS[ENV] || SSO_MSAL_CONFIGS.production;

// Diğer yapılandırma değişkenleri
export const CDS_API_KEY = import.meta.env.VITE_CDS_API_KEY;
export const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY;

// Geçerli ortamı dışa aktar
export const ENVIRONMENT = ENV;

// Genel yapılandırma
export const HOST_API = {
  base: API_URL,
  cds: CDS_API_URL,
};

// Diğer global yapılandırmalar
export const PATH_AFTER_LOGIN = '/dashboard';
export const PATH_BEFORE_LOGIN = '/login';

// Sayfa başlıkları
export const SITE_TITLE = 'AI Business School - Adoption Platform';

// Tema ayarları
export const HEADER = {
  H_MOBILE: 64,
  H_MAIN_DESKTOP: 88,
  H_DASHBOARD_DESKTOP: 92,
  H_DASHBOARD_DESKTOP_OFFSET: 92 - 32,
};

export const NAV = {
  W_BASE: 260,
  W_DASHBOARD: 280,
  W_DASHBOARD_MINI: 88,
  //
  H_DASHBOARD_ITEM: 48,
  H_DASHBOARD_ITEM_SUB: 36,
  //
  H_DASHBOARD_ITEM_HORIZONTAL: 32,
};

// Geriye dönük uyumluluk için CONFIG nesnesi
export const CONFIG = {
  siteName: SITE_TITLE,
  apiUrl: API_URL,
  cdsApiUrl: CDS_API_URL,
  cdsApiKey: CDS_API_KEY,
  openaiApiKey: OPENAI_API_KEY,
  environment: ENVIRONMENT,
  msalConfig: MSAL_CONFIG,
  ssoMsalConfig: SSO_MSAL_CONFIG,
  ssoMsalConfigs: SSO_MSAL_CONFIGS,
};

export default {
  HOST_API,
  SITE_TITLE,
  PATH_AFTER_LOGIN,
  PATH_BEFORE_LOGIN,
  HEADER,
  NAV,
  ENVIRONMENT,
  API_URL,
  CDS_API_URL,
  CDS_API_KEY,
  OPENAI_API_KEY,
  MSAL_CONFIG,
  SSO_MSAL_CONFIG,
  SSO_MSAL_CONFIGS,
  CONFIG,
};
