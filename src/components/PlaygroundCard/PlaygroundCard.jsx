import { Box, Typography } from '@mui/material';
import Button from '../../components/Button/Button';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import './PlaygroundCard.scss';

const PlaygroundCard = ({ title, description, tag, icon: Icon, link, buttonText, newTab = false }) => {
  const navigate = useNavigate();

  return (
    <Box className="playground-card">
      <Box className="playground-card__tag">{tag}</Box>
      <Box className="playground-card__icon">
        <Icon />
      </Box>
      <Typography variant="h3" className="playground-card__title">
        {title}
      </Typography>
      <Typography variant="body1" className="playground-card__description">
        {description}
      </Typography>
      <Button
        variant="contained"
        color="primary"
        onClick={() => {
          if (newTab) {
            window.open(link, '_blank', 'noopener,noreferrer');
          } else {
            navigate(link);
          }
        }}
      >
        {buttonText}
      </Button>
    </Box>
  );
};

PlaygroundCard.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  tag: PropTypes.string.isRequired,
  icon: PropTypes.elementType.isRequired,
  link: PropTypes.string.isRequired,
  buttonText: PropTypes.string.isRequired,
  newTab: PropTypes.bool
};

export default PlaygroundCard;