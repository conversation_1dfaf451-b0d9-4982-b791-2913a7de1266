@use '../../styles/abstracts/variables' as *;

.playground-card {
  position: relative;
  background: url('@/assets/images/effect.png') no-repeat top -80px right -75px $bg-paper;
  border-radius: $border-radius-lg;
  padding: $spacing-4 $spacing-4;
  border: 1px solid $border-color;
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
  height: calc(100% - $spacing-4*2);

  &__tag {
    position: absolute;
    top: $spacing-4;
    right: $spacing-4;
    display: inline-block;
    width: max-content;
    padding: $spacing-2 $spacing-2;
    color: $text-secondary !important;
    background-color: #F7F7F8 !important;
    border: 1px solid $border-color;
    border-radius: $border-radius-sm;
    font-size: $font-size-xs;
    font-weight: $font-weight-medium;
    margin-bottom: $spacing-2;
  }

  &__icon {
    font-size: 32px;
    border: 1px solid $border-color;
    width: max-content;
    width: 64px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 64px;
    border-radius: $border-radius-lg;
    margin-bottom: $spacing-3;
    
    svg {
      width: 32px;
      height: 32px;
      color: $primary-color;
    }
  }

  &__title {
    font-size: $font-size-lg !important;
    font-weight: $font-weight-bold !important;
    color: $text-primary !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  &__description {
    color: $text-secondary !important;
    font-size: calc($font-size-md - 2px) !important;
    line-height: 1.6 !important;
    margin-bottom: $spacing-3 !important;
  }

  button {
   margin-top: auto;
  }

}

@media (max-width: $tablet) {
  .features-section {
    padding: $spacing-4 0;
  }
} 