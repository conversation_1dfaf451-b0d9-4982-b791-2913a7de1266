@use '../../styles/abstracts/variables' as *;

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(-20px);
  }
  50% {
    transform: translateY(0);
  }
  75% {
    transform: translateY(-10px);
  }
}

.bounce {
  animation: bounce 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  transform-origin: center bottom;
}

// Preview video styles
.heygen-video-creator__preview-video {
  @media (max-width: 768px) {
    padding-top: 0 !important;
  }
  @media (min-width: 768px) and (max-width: 899px) {
    padding-top: 0 !important;
  }
}

.heygen-video-creator {
  padding:0 !important;
  margin:0 !important;

  &__container {
    background-color: $bg-paper !important;
    border-radius: $border-radius-md !important;
    border:1px solid $border-color !important;
    margin:0 0 $spacing-4 0 !important;
    scroll-margin-top: 100px;
    scroll-margin-bottom: 100px;

    @media (max-width: 768px) {
      margin: 0 0 $spacing-2 0 !important;
    }
  }

  &__header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center !important;
    border-bottom:1px solid $border-color;
    padding: $spacing-2 $spacing-3;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      gap: $spacing-3;
      padding: $spacing-3;
    }
  }

  &__content-section {
    padding: $spacing-3;
    background-color: $bg-paper;
    border-radius: $border-radius-md;
    @media (max-width: 768px) {
      padding: $spacing-2;
    }
  }

  &__voice-select-wrapper {
    display: flex;
    align-items: center;
    gap: $spacing-2;
    padding: $spacing-2;
    background-color: $bg-paper;
    border-radius: $border-radius-md;
    border: 1px solid $border-color;

    .play-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      color: $text-secondary;
      min-width: 32px;
      width: 32px;
      height: 32px;
      padding: 0;
      
      &:hover {
        background-color: rgba($text-secondary, 0.08);
      }

      svg {
        font-size: 18px;
      }
    }

    .MuiSelect-select {
      padding: 8px 12px !important;
      background-color: transparent !important;
      font-size: $font-size-sm !important;
      color: $text-primary !important;
      
      &:focus {
        background-color: transparent !important;
      }
    }

    .MuiOutlinedInput-notchedOutline {
      border: none !important;
    }
  }

  &__title {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: $spacing-2;
    min-height: 42px;
    width: max-content;

    @media (max-width: 768px) {
      min-height: unset;
      flex: 1;
    }

    .step-number {
      background-color: #f5f5f5;
      color: $text-primary;
      width: 24px;
      height: 24px;
      display:flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      font-size: $font-size-xs;
      border-radius:100%;
      font-weight: $font-weight-regular;
      border: 1px solid $border-color;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      @media (max-width: 768px) {
        width: 20px;
        height: 20px;
      }
    }

    h2 {
      font-size: $font-size-sm !important;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      margin: 0 !important; 
    }
  }

  &__gender-tabs {
    margin:0 auto !important;
    
    @media (max-width: 768px) {
      width: calc(100% - $spacing-2 * 2) !important;
      max-width: calc(100% - $spacing-2 * 2) !important;
      margin: 0 !important;
      order: 2;
    }

    .MuiTabs-root {
      min-height: auto;
      background-color: $bg-light;
      border-radius: $border-radius-lg;
      padding: $spacing-2;

      @media (max-width: 768px) {
        width: 100%;
      }

      .MuiTabs-indicator {
        display: none;
      }

      .MuiTabs-flexContainer {
        @media (max-width: 768px) {
          width: 100%;
          display: flex;
          gap: $spacing-2;

          .MuiTab-root {
            flex: 1;
          }
        }
      }
    }

    .MuiTab-root {
      min-height: auto !important;
      padding: $spacing-2 $spacing-3 !important;
      text-transform: none;
      font-weight: $font-weight-medium;
      color: $text-secondary;
      min-width: 80px;
      border-radius: $border-radius-md;

      &.Mui-selected {
        background-color: $bg-paper;
        color: $text-primary;
        box-shadow: $shadow-sm;
      }
    }
  }


  &__avatar-slider {
    position: relative;
    padding: 0 $spacing-4;
    margin-bottom: $spacing-4;

    .swiper-slide {
      width: 12.75%;
      height: 130px;
      position: relative !important;
      border: 1px solid $border-color;
      border-radius: $border-radius-md;
      overflow: hidden !important;
      .heygen-video-creator__avatar-slider-image-container{
        height: 100%;
        width: 100%;
        position: relative;
        img{
          height: 100%;
          object-fit: cover;
        }
      }
      &:hover {
        .heygen-video-creator__avatar-slider-uses {
          opacity: 1;
        }
      }
    }
    
    .swiper-slide-active {
      margin-right: 6px !important;
    }
  }

  &__avatar-slider-skeleton {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.08);
    border-radius: $border-radius-md;
  }

  &__avatar-card {
    width: 100%;
    height: 100%;
    position: relative;
  }

  &__avatar-content {
    width: 100%;
    height: 100%;
    position: relative;
    cursor: pointer;
  }

  &__avatar-slider-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: $border-radius-md;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  &__avatar-slider-uses {
    position: absolute;
    top: $spacing-2;
    right: $spacing-2;
    opacity: 1;
    background-color: #71869d;
    min-width: 24px;
    min-height: 24px;
    border-radius: $border-radius-sm !important;
    font-size: $font-size-xs;
    color: white;
    font-weight: $font-weight-medium;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  &__navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    width: 32px;
    height: 32px;
    background-color: $bg-paper !important;
    border-radius: 50%;
    box-shadow: $shadow-md;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    padding: 0;

    &:hover {
      background-color: $primary-color !important;
      color: $bg-paper !important;
    }

    &--prev {
      position: absolute !important;
      top:50% !important;
      left: -16px;
    }

    &--next {
      position: absolute !important;
      top:50% !important;
      right: -16px;
    }

    svg {
      font-size: $font-size-lg;
    }
  }

  &__modal {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    max-width: 728px;
    background-color: $bg-paper;
    box-shadow: $shadow-lg;
    outline: none !important;
    border:none !important;
    border-radius: $border-radius-md;
    max-height: 90vh;
    overflow: auto;
  }

  &__modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-4;
    padding: $spacing-3 $spacing-3;
    border-bottom: 1px solid $border-color;

    h2 {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      margin: 0;
    }
  }

  &__modal-close {
    color: $text-secondary !important;
    transition: all 0.3s ease !important;

    &:hover {
      color: $text-primary !important;
      background-color: rgba($text-secondary, 0.1) !important;
    }

    svg {
      font-size: $font-size-xl;
    }
  }

  &__modal-variants-grid {
    padding: 0 $spacing-3 $spacing-3 $spacing-3;
  }

  &__modal-variant-box {
    position: relative;
    width: 100%;
    padding-top: 100%;
    border-radius: $border-radius-md;
    overflow: hidden;
    transition: transform 0.2s ease-in-out;
    border: 1px solid $border-color;
    box-shadow: $shadow-sm;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
    }
  }

  &__modal-variant-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__script-textarea {
    width: calc(100% - $spacing-3 * 2);
    min-height: 150px;
    padding: $spacing-3;
    border: 1px solid $border-color;
    border-radius: $border-radius-md;
    background-color: $bg-paper;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    resize: none;
    outline: none;
    transition: border-color 0.2s ease-in-out;

    &:focus {
      border-color: $primary-color;
    }

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }
  }

  &__script-section {
    display: flex;
    flex-direction: column;
    gap: $spacing-2;
  }

  &__script-wrapper {
    position: relative;
  }

  &__character-count {
    margin-left:auto;
    padding: $spacing-1 $spacing-2; 
    background-color: $bg-paper;
    font-size: $font-size-xs;
    
    &--limit {
      color: $error-color;
    }

    &--normal {
      color: $text-secondary;
    }
  }

  &__character-limit-warning {
    display: block;
    width: 100%;
    color: $error-color;
    background-color: rgba($error-color, 0.08);
    padding: $spacing-1 $spacing-2;
    border-radius: $border-radius-sm;
    margin-top: $spacing-1;
    margin-bottom: $spacing-1;
    font-size: $font-size-xs;
  }

  &__script-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-1;
    button{
      width: 100%;
    }
  }

  &__generated-by {
    margin-top: 5px;
    color: $text-secondary;
    font-size: $font-size-xs;
  }

  &__video-wrapper {
    width: 100%;
    height: 475px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $bg-light;
    border-radius: $border-radius-md;
    overflow: hidden;

    @media (max-width: 768px) {
      min-height: auto !important;
    }
  }

  &__video-player {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  &__avatar-slider-image-container {
    height: 100%;
    width: 100%;
    position: relative;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
      transition: transform 0.2s ease-in-out;
    }

    img {
      height: 100%;
      object-fit: cover;
    }
  }

  &__avatar-section {
    position: relative;
  }

  &__download {
    display: flex;
    align-items: center;
    color: $text-secondary;
    text-decoration: none;
    padding: $spacing-1 0;
    border-radius: $border-radius-md;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    width: max-content;
    @media (max-width: 768px) {
      padding:0 !important;
    }

    &:hover {
      color: $primary-color;
    }

    .MuiTypography-root {
      font-weight: $font-weight-medium;
    }

    .MuiSvgIcon-root {
      font-size: 18px;
    }
  }

  &__tooltip {
    background-color: rgba(0, 0, 0, 0.9);
    font-size: $font-size-xs;
    padding: $spacing-1 $spacing-2;
    max-width: 250px;
  }

  &__tooltip-trigger {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    cursor: pointer;
  }

  &__generate-btn-wrapper {
    width: 100%;
    position: relative;
    display: flex;
  }

  &__generate-btn {
    position: relative;
    z-index: 2;
    width: 100%;
  }
  // AI uyarı kutusu için yeni sınıf
  &__ai-disclaimer-box {
    padding: $spacing-3;
    border-radius: $border-radius-md;
    border: 1px solid #e9ecef;
    background: white;
    font-size: $font-size-sm;
    color: rgba(0, 0, 0, 0.6);
    text-align: center;
    box-shadow: $shadow-sm;
    margin-bottom: $spacing-4;
  }
}
