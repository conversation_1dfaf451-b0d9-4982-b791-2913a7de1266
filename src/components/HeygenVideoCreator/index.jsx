import {
  Box,
  Grid,
  Typography,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  Modal,
  Stack,
  Paper,
  Select,
  MenuItem,
  CircularProgress,
} from '@mui/material';
import Button from '../../components/Button/Button';
import FavoriteButton from '../../components/FavoriteButton';
import { useState, useRef, useEffect, useCallback } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import CloseIcon from '@mui/icons-material/Close';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import DownloadIcon from '@mui/icons-material/Download';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  useHeygenAvatarConnectorsQuery,
  useHeygenVoiceConnectorsQuery,
  useHeygenVideoCreateConnectorsMutation,
  useHeygenVideoStatusConnectorsQuery,
} from '../../redux/services/connectors-api';
import { useUpdateAppTrackingMutation } from '../../redux/services/app-tracking-api';
import useUpdateJourneyTracking from '../../domains/journey/utils/updateJourneyTracking';
import 'swiper/css';
import 'swiper/css/navigation';
import './styles.scss';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { useLocation } from 'react-router-dom';

// Initial state values
const getInitialState = (t) => ({
  gender: 'female',
  selectedVoice: '',
  videoUrl: '',
  scriptText: t('heygenVideoCreator.videoScript.initialText'),
  exceedsLimit: false,
});

// Avatar content component
const AvatarContent = ({ gender, onVideoChange, onAvatarsChange }) => {
  const [swiper, setSwiper] = useState(null);
  const [modalState, setModalState] = useState({ open: false, selectedAvatar: null });
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [displayedAvatars, setDisplayedAvatars] = useState([]);
  const [pageAvatars, setPageAvatars] = useState({}); // Her sayfadaki avatarları saklamak için

  const {
    data: apiResponse,
    isLoading: isLoadingAvatars,
    isFetching,
  } = useHeygenAvatarConnectorsQuery({
    page: currentPage,
    limit: 30,
  });

  const newAvatars = apiResponse?.data?.avatars || [];
  const pagination = apiResponse?.data?.pagination;

  // Her sayfa değişiminde o sayfanın avatarlarını saklayalım
  useEffect(() => {
    if (newAvatars.length === 0) {
      return;
    }

    setPageAvatars((prev) => ({
      ...prev,
      [currentPage]: newAvatars,
    }));

    // Mevcut sayfanın avatarlarını göster
    setDisplayedAvatars(newAvatars);
    onAvatarsChange(newAvatars);

    // İlk sayfada ve cinsiyet filtresine uyan ilk avatar'ı seç
    if (currentPage === 1) {
      const filteredFirstPageAvatars = newAvatars.filter((avatar) => avatar.gender === gender);
      if (filteredFirstPageAvatars.length > 0) {
        onVideoChange(filteredFirstPageAvatars[0].preview_video_url);
      }
    }
  }, [newAvatars, currentPage, onAvatarsChange, gender, onVideoChange]);

  // Pagination durumunu kontrol et
  useEffect(() => {
    if (pagination) {
      setHasMore(pagination.currentPage < pagination.totalPages);
      setIsLoadingMore(false);
    }
  }, [pagination]);

  const handleSlideChange = useCallback(() => {
    if (swiper) {
      const isLastSlide = swiper.isEnd;
      const isFirstSlide = swiper.isBeginning;
      const activeIndex = swiper.activeIndex;

      // Son slide'a geldiğimizde ve daha fazla avatar varsa
      if (isLastSlide && hasMore && !isLoadingMore && !isFetching) {
        const nextPage = currentPage + 1;
        setCurrentPage(nextPage);
        setIsLoadingMore(true);

        // Eğer bu sayfa daha önce yüklendiyse, onu göster
        if (pageAvatars[nextPage]) {
          setDisplayedAvatars(pageAvatars[nextPage]);
          onAvatarsChange(pageAvatars[nextPage]);
        }
      }
      // İlk slide'a geldiğimizde ve önceki sayfa varsa
      else if (isFirstSlide && currentPage > 1 && activeIndex === 0) {
        const prevPage = currentPage - 1;
        setCurrentPage(prevPage);

        // Önceki sayfanın avatarlarını göster
        if (pageAvatars[prevPage]) {
          setDisplayedAvatars(pageAvatars[prevPage]);
          onAvatarsChange(pageAvatars[prevPage]);
        }
      }
    }
  }, [swiper, hasMore, isLoadingMore, isFetching, currentPage, pageAvatars, onAvatarsChange]);

  const handlePrevClick = useCallback(() => {
    if (swiper) {
      swiper.slidePrev();
    }
  }, [swiper]);

  const handleNextClick = useCallback(() => {
    if (swiper && !isLoadingMore && !isFetching) {
      if (swiper.isEnd && hasMore) {
        const nextPage = currentPage + 1;
        setCurrentPage(nextPage);
        setIsLoadingMore(true);
      }
      swiper.slideNext();
    }
  }, [swiper, isLoadingMore, isFetching, hasMore, currentPage]);

  const handleAvatarClick = useCallback(
    (avatar) => {
      if (avatar.preview_video_url) {
        onVideoChange(avatar.preview_video_url);
      }

      if (avatar.variants?.length > 0) {
        setModalState({ open: true, selectedAvatar: avatar });
      }
    },
    [onVideoChange]
  );

  const handleVariantClick = useCallback(
    (variant) => {
      if (variant.preview_video_url) {
        onVideoChange(variant.preview_video_url);
      }
      setModalState({ open: false, selectedAvatar: null });
    },
    [onVideoChange]
  );

  const handleCloseModal = useCallback(() => {
    setModalState({ open: false, selectedAvatar: null });
  }, []);

  const filteredAvatars = displayedAvatars.filter((avatar) => avatar.gender === gender);

  if (isLoadingAvatars && currentPage === 1) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <>
      <Box className="heygen-video-creator__avatar-slider">
        <IconButton
          className="heygen-video-creator__navigation heygen-video-creator__navigation--prev"
          onClick={handlePrevClick}
          disabled={currentPage === 1 || isLoadingMore}
        >
          <ChevronLeftIcon />
        </IconButton>
        <IconButton
          className="heygen-video-creator__navigation heygen-video-creator__navigation--next"
          onClick={handleNextClick}
          disabled={!hasMore || isLoadingMore}
        >
          <ChevronRightIcon />
        </IconButton>
        <Swiper
          modules={[Navigation]}
          slidesPerView={2}
          breakpoints={{
            320: { slidesPerView: 3, spaceBetween: 8 },
            480: { slidesPerView: 4, spaceBetween: 12 },
            768: { slidesPerView: 6, spaceBetween: 16 },
            1024: { slidesPerView: 8, spaceBetween: 16 },
            1280: { slidesPerView: 10, spaceBetween: 16 },
          }}
          style={{ width: '100%', padding: '0 3px' }}
          onSwiper={setSwiper}
          onSlideChange={handleSlideChange}
        >
          {filteredAvatars.map((avatar) => (
            <SwiperSlide key={`${avatar.avatar_id}-${currentPage}`}>
              {avatar.variants ? (
                <Tooltip
                  title={`${avatar.avatar_name} has ${avatar.variants.length + 1} different ${avatar.variants.length + 1 === 1 ? 'look' : 'looks'}`}
                  arrow
                  placement="top"
                  classes={{ tooltip: 'heygen-video-creator__tooltip' }}
                >
                  <Box
                    className="heygen-video-creator__avatar-slider-image-container"
                    onClick={() => handleAvatarClick(avatar)}
                  >
                    <Box
                      component="img"
                      src={avatar.preview_image_url}
                      alt={avatar.avatar_name}
                      className="heygen-video-creator__avatar-slider-image"
                    />
                    <Box className="heygen-video-creator__avatar-slider-uses">
                      {avatar.variants.length + 1}
                    </Box>
                  </Box>
                </Tooltip>
              ) : (
                <Box
                  className="heygen-video-creator__avatar-slider-image-container"
                  onClick={() => handleAvatarClick(avatar)}
                >
                  <Box
                    component="img"
                    src={avatar.preview_image_url}
                    alt={avatar.avatar_name}
                    className="heygen-video-creator__avatar-slider-image"
                  />
                </Box>
              )}
            </SwiperSlide>
          ))}
          {isLoadingMore && (
            <SwiperSlide>
              <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                <CircularProgress size={24} />
              </Box>
            </SwiperSlide>
          )}
        </Swiper>
      </Box>

      <Modal
        open={modalState.open}
        onClose={handleCloseModal}
        aria-labelledby="avatar-variants-modal"
        aria-describedby="avatar-variants-modal-description"
        className="heygen-video-creator__modal"
      >
        <Box className="heygen-video-creator__modal-content">
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            className="heygen-video-creator__modal-header"
          >
            <Typography variant="h6" component="h2">
              {modalState.selectedAvatar?.avatar_name} Variants
            </Typography>
            <IconButton onClick={handleCloseModal} className="heygen-video-creator__modal-close">
              <CloseIcon />
            </IconButton>
          </Stack>
          <Grid container className="heygen-video-creator__modal-variants-grid" spacing={2}>
            {/* Ana Avatarı ekliyoruz */}
            {modalState.selectedAvatar && (
              <Grid item xs={12} sm={6} md={2} key="main-avatar">
                <Box
                  className="heygen-video-creator__modal-variant-box"
                  onClick={() =>
                    handleVariantClick({
                      preview_image_url: modalState.selectedAvatar.preview_image_url,
                      preview_video_url: modalState.selectedAvatar.preview_video_url,
                    })
                  }
                  sx={{ position: 'relative' }}
                >
                  <Box
                    component="img"
                    src={modalState.selectedAvatar.preview_image_url}
                    alt={`${modalState.selectedAvatar.avatar_name} Main Avatar`}
                    className="heygen-video-creator__modal-variant-image"
                  />
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: 5,
                      left: 5,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '0.7rem',
                      padding: '2px 6px',
                      borderRadius: '4px',
                    }}
                  >
                    Main
                  </Box>
                </Box>
              </Grid>
            )}
            {modalState.selectedAvatar?.variants?.map((variant, index) => (
              <Grid item xs={12} sm={6} md={2} key={variant.id || index}>
                <Box
                  className="heygen-video-creator__modal-variant-box"
                  onClick={() => handleVariantClick(variant)}
                >
                  <Box
                    component="img"
                    src={variant.preview_image_url}
                    alt={`${modalState.selectedAvatar.avatar_name} Variant ${index + 1}`}
                    className="heygen-video-creator__modal-variant-image"
                  />
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Modal>
    </>
  );
};

AvatarContent.propTypes = {
  gender: PropTypes.string.isRequired,
  onVideoChange: PropTypes.func.isRequired,
  onAvatarsChange: PropTypes.func.isRequired,
};

const HeygenVideoCreator = ({ id, onGenerate }) => {
  const { t } = useTranslation();
  const user = useSelector((state) => state.auth.user);
  const location = useLocation();
  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();
  const [state, setState] = useState(() => getInitialState(t));

  // Video durumu için ref kullanarak sonsuz döngüleri engelliyoruz
  const prevVideoStatusRef = useRef(null);

  // Toast konfigürasyonu
  const toastConfig = {
    position: 'top-center',
    autoClose: 5000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
  };

  const [isPlaying, setIsPlaying] = useState(false);
  const [currentVoicePage, setCurrentVoicePage] = useState(1);
  const [hasMoreVoices, setHasMoreVoices] = useState(true);
  const [isLoadingMoreVoices, setIsLoadingMoreVoices] = useState(false);
  const [allVoices, setAllVoices] = useState([]);
  const [allAvatars, setAllAvatars] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [videoId, setVideoId] = useState(null);
  const [createVideo] = useHeygenVideoCreateConnectorsMutation();
  const [updateAppTracking] = useUpdateAppTrackingMutation();
  const {
    data: voicesResponse,
    isLoading: isLoadingVoices,
    isFetching: isFetchingVoices,
  } = useHeygenVoiceConnectorsQuery({
    page: currentVoicePage,
    limit: 10,
  });
  const { data: videoStatusData, refetch: refetchVideoStatus } =
    useHeygenVideoStatusConnectorsQuery(
      { video_id: videoId },
      {
        pollingInterval: videoId ? 10000 : 0,
        skip: !videoId || !isGenerating,
      }
    );

  // URL state'inden journey parametrelerini al
  const journeyCardId = location.state?.journeyCardId;
  const journeyTrackingData = location.state?.journeyTrackingData;
  const journeyLevel = location.state?.journeyLevel;

  // API yanıtından voices dizisini çıkar
  const newVoices = voicesResponse?.data || [];

  // Yeni sesleri mevcut listeye ekle
  useEffect(() => {
    if (newVoices.length > 0) {
      if (currentVoicePage === 1) {
        setAllVoices(newVoices);
      } else {
        setAllVoices((prev) => [...prev, ...newVoices]);
      }
    } else if (currentVoicePage > 1) {
      // Eğer yeni veri gelmezse ve ilk sayfa değilse, daha fazla veri olmadığını belirt
      setHasMoreVoices(false);
    }
  }, [newVoices, currentVoicePage]);

  // Yükleme tamamlandığında loading state'ini sıfırla
  useEffect(() => {
    if (!isFetchingVoices) {
      setIsLoadingMoreVoices(false);
    }
  }, [isFetchingVoices]);

  const handleVoiceSelectScroll = useCallback(
    (event) => {
      const select = event.currentTarget;
      if (!select) return;

      const isNearBottom = select.scrollTop + select.clientHeight >= select.scrollHeight - 20;

      if (isNearBottom && hasMoreVoices && !isLoadingMoreVoices && !isFetchingVoices) {
        setIsLoadingMoreVoices(true);
        setCurrentVoicePage((prev) => prev + 1);
      }
    },
    [hasMoreVoices, isLoadingMoreVoices, isFetchingVoices]
  );

  const audioRef = useRef(new Audio());
  const avatarSectionRef = useRef(null);
  const voiceSectionRef = useRef(null);
  const scriptSectionRef = useRef(null);

  const handleGenderChange = useCallback((event, newValue) => {
    setState((prev) => ({ ...prev, gender: newValue }));
  }, []);

  const handleAvatarSelect = useCallback((url) => {
    setState((prev) => ({ ...prev, videoUrl: url }));
    if (voiceSectionRef.current) {
      voiceSectionRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
      voiceSectionRef.current.classList.remove('bounce');
      requestAnimationFrame(() => {
        voiceSectionRef.current.classList.add('bounce');
      });
    }
  }, []);

  const handleVoiceChange = useCallback(
    (event) => {
      const newVoice = event.target.value;
      setState((prev) => ({ ...prev, selectedVoice: newVoice }));

      if (isPlaying) {
        handleTogglePlay();
      }

      if (scriptSectionRef.current) {
        scriptSectionRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
        scriptSectionRef.current.classList.remove('bounce');
        requestAnimationFrame(() => {
          scriptSectionRef.current.classList.add('bounce');
        });
      }
    },
    [isPlaying]
  );

  const handleTogglePlay = useCallback(() => {
    if (!allVoices.length) return;

    const audio = audioRef.current;
    const selectedVoice = allVoices.find((voice) => voice.voice_id === state.selectedVoice);

    if (!selectedVoice?.preview_audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      audio.src = selectedVoice.preview_audio;
      audio.play();
      setIsPlaying(true);
    }
  }, [isPlaying, state.selectedVoice, allVoices]);

  // Script değişim handler'ı - exceedsLimit hesaplama işini burada yapıyoruz
  const handleScriptChange = (e) => {
    const text = e.target.value;
    const exceeds = text.length > 500;

    setState({
      ...state,
      scriptText: text,
      exceedsLimit: exceeds,
    });
  };

  // handlePaste fonksiyonunda da aynı şekilde exceedsLimit'i burada hesaplıyoruz
  const handlePaste = (e) => {
    // Panodan yapıştırılan metni al
    const pastedText = e.clipboardData.getData('text');
    const currentText = state.scriptText;
    const selectionStart = e.target.selectionStart;

    // Yapıştırılan metni mevcut metinle birleştir
    const newText =
      currentText.substring(0, selectionStart) +
      pastedText +
      currentText.substring(e.target.selectionEnd);
    const exceeds = newText.length > 500;

    setState({
      ...state,
      scriptText: newText,
      exceedsLimit: exceeds,
    });

    // Varsayılan yapıştırma davranışını engelle
    e.preventDefault();
  };

  const handleAvatarsChange = useCallback((avatars) => {
    setAllAvatars(avatars);
  }, []);

  // Video durumunu izle
  useEffect(() => {
    if (!videoStatusData?.data?.data) {
      return;
    }

    const status = videoStatusData.data.data;

    // Status değerini direkt loglayalım
    console.log('Raw video status data:', status);
    console.log('Has onGenerate prop:', onGenerate ? 'Yes' : 'No');

    // Önceki durumla karşılaştırma yapmak için ref kullanıyoruz
    const statusStr = JSON.stringify(status);
    const prevStatusStr = prevVideoStatusRef.current
      ? JSON.stringify(prevVideoStatusRef.current)
      : '';

    // Durumda değişiklik yoksa işlem yapmıyoruz
    if (statusStr === prevStatusStr) {
      console.log('No status change detected, skipping update');
      return;
    }

    console.log('Status changed from', prevVideoStatusRef.current?.status, 'to', status.status);

    // Referansı güncelle
    prevVideoStatusRef.current = status;

    console.log('Video status updated:', status);

    // Video hazır olduğunda
    if (status.status === 'completed') {
      console.log('Video status is COMPLETED, checking video_url');

      if (status.video_url) {
        console.log('Video completed with URL:', status.video_url);
        setState((prev) => ({ ...prev, videoUrl: status.video_url }));
        setVideoId(null); // Polling'i durdur
        setIsGenerating(false);

        // Başarı bildirimi
        toast.success(t('heygenVideoCreator.previewVideo.videoGenerated'), toastConfig);

        // onGenerate prop'u varsa çağır
        if (onGenerate) {
          console.log('HeygenVideoCreator: onGenerate çağrılıyor');
          try {
            onGenerate(true);
            console.log('onGenerate çağrısı tamamlandı');
          } catch (error) {
            console.error('onGenerate çağrısında hata:', error);
          }
        } else {
          console.warn('onGenerate prop yok, topic ilerleme çağrılamıyor');
        }

        // Alternatif iletişim yöntemi: window.postMessage kullanarak video tamamlandı sinyali gönder
        try {
          console.log('HeygenVideoCreator: window.postMessage çağrılıyor');
          // Parent window'a mesaj gönder
          window.parent.postMessage(
            {
              type: 'HEYGEN_VIDEO_COMPLETED',
              videoId: status.id,
              videoUrl: status.video_url,
            },
            '*'
          );
          console.log('HeygenVideoCreator: window.postMessage başarıyla çağrıldı');
        } catch (error) {
          console.error('HeygenVideoCreator: window.postMessage hatası:', error);
        }

        // Video tamamlandığında journey tracking güncellemesi
        if (journeyCardId && journeyTrackingData) {
          try {
            updateJourneyTrackingCard({
              userId: user?._id,
              journeyTrackingData,
              userLevel: journeyLevel,
              cardId: journeyCardId,
            })
              .then((response) => {
                console.log('Journey tracking updated on video completion:', response);
              })
              .catch((error) => {
                console.error('Journey tracking update failed on video completion:', error);
              });
          } catch (journeyError) {
            console.error('Journey tracking update error on video completion:', journeyError);
          }
        } else {
          console.warn('Missing journey tracking data for video completion:', {
            journeyCardId,
            journeyTrackingData,
          });
        }
      } else {
        console.warn('Video status is completed but no video_url available!');
      }
    } else if (status.status === 'failed') {
      console.error('Video generation failed:', status);
      setIsGenerating(false);
      setVideoId(null);
      // Kullanıcıya hata göster
      toast.error(t('heygenVideoCreator.previewVideo.generationFailed'), toastConfig);
    } else {
      console.log('Video is still processing, status:', status.status);
      // Her durum değişikliğinde manuel olarak yenile
      const timer = setTimeout(() => {
        if (videoId) {
          refetchVideoStatus();
        }
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [
    videoStatusData,
    videoId,
    isGenerating,
    refetchVideoStatus,
    t,
    updateJourneyTrackingCard,
    journeyCardId,
    journeyTrackingData,
    journeyLevel,
    user?._id,
    toastConfig,
    onGenerate,
  ]);

  // Video ID değiştiğinde manuel olarak refetch yap
  useEffect(() => {
    if (!videoId) return;

    console.log('VideoId changed, forcing refetch');
    // İlk durum kontrolü
    refetchVideoStatus();

    // Düzenli aralıklarla durum kontrolü
    const intervalId = setInterval(() => {
      if (videoId && isGenerating) {
        console.log('Interval check for video status');
        refetchVideoStatus();
      }
    }, 5000);

    return () => clearInterval(intervalId);
  }, [videoId, isGenerating, refetchVideoStatus]);

  const handleGenerateVideo = useCallback(async () => {
    try {
      console.log('Video generation started');
      // Button devre dışı bırakıldıysa hiçbir şey yapma
      if (isGenerating || !state.videoUrl || !state.selectedVoice || state.exceedsLimit) {
        console.log('Button is disabled, cannot generate video');
        return;
      }

      console.log('Current state:', {
        gender: state.gender,
        videoUrl: state.videoUrl,
        selectedVoice: state.selectedVoice,
        scriptText: state.scriptText ? state.scriptText.substring(0, 20) + '...' : '',
      });

      // Daha önce oluşturulmuş bir video varsa veya oluşturuluyorsa, işlemi iptal et
      if (isGenerating) {
        console.log('Already generating a video');
        return;
      }

      // Önemli değerleri kontrol et
      if (!state.selectedVoice) {
        console.error('No voice selected');
        toast.warning(t('heygenVideoCreator.videoScript.noVoiceSelected'), toastConfig);
        return;
      }

      if (!state.videoUrl) {
        console.error('No avatar selected');
        toast.warning(t('heygenVideoCreator.videoScript.noAvatarSelected'), toastConfig);
        return;
      }

      setIsGenerating(true);
      // Video URL'sini sıfırlama - kullanıcının seçtiği avatarı koruyor
      // setState((prev) => ({ ...prev, videoUrl: '' }));

      console.log(
        'Filtered avatars count:',
        allAvatars.filter((avatar) => avatar.gender === state.gender).length
      );

      const filteredAvatars = allAvatars.filter((avatar) => avatar.gender === state.gender);
      const selectedAvatar = filteredAvatars.find(
        (avatar) =>
          avatar.preview_video_url === state.videoUrl ||
          avatar.variants?.some((variant) => variant.preview_video_url === state.videoUrl)
      );

      console.log(
        'Selected avatar found:',
        !!selectedAvatar,
        selectedAvatar ? selectedAvatar.avatar_name : 'Not found'
      );

      if (!selectedAvatar) {
        console.error('Selected avatar not found');
        setIsGenerating(false);
        toast.error(t('heygenVideoCreator.videoScript.avatarNotFound'), toastConfig);
        return;
      }

      const isMainAvatar = selectedAvatar.preview_video_url === state.videoUrl;
      const avatar_id = isMainAvatar
        ? selectedAvatar.avatar_id
        : selectedAvatar.variants.find((v) => v.preview_video_url === state.videoUrl)?.id;

      console.log('Avatar ID:', avatar_id);

      if (!avatar_id) {
        console.error('Avatar ID not found');
        setIsGenerating(false);
        toast.error(t('heygenVideoCreator.videoScript.avatarIdNotFound'), toastConfig);
        return;
      }

      console.log('Making API request with:', {
        prompt: state.scriptText,
        avatar_id,
        voice_id: state.selectedVoice,
      });

      const response = await createVideo({
        prompt: state.scriptText,
        avatar_id: avatar_id,
        voice_id: state.selectedVoice,
        temperature: 1,
        frequency: 0,
        presence: 0,
      });

      if (response.error) {
        console.error('Video generation error:', response.error);
        setIsGenerating(false);

        // Don't show toast for usage limit errors as they're handled in the API layer
        if (response.error.isUsageLimitExceeded) {
          return; // Exit early, usage limit toast is already shown
        }

        toast.error(
          response.error.data?.error?.message ||
            t('heygenVideoCreator.videoScript.generationFailed'),
          toastConfig
        );
        return;
      }

      if (response.data?.data?.video_id) {
        console.log('Video ID received:', response.data.data.video_id);
        setVideoId(response.data.data.video_id);

        // Video oluşturma başladığında durum kontrolü yap
        setTimeout(() => {
          refetchVideoStatus();
        }, 2000);

        // user.id'yi kullan
        const userId = user?._id;

        if (!userId || !id) {
          console.warn('Missing userId or appId for tracking:', { userId, appId: id });
          return;
        }

        const currentDate = new Date();
        const month = currentDate.getMonth() + 1;
        const year = currentDate.getFullYear();
        const day = String(currentDate.getDate()).padStart(2, '0');
        try {
          const trackingResponse = await updateAppTracking({
            userId: userId,
            appId: id,
            appType: 'usecase',
            year: year.toString(),
            month: month.toString(),
            day: day,
          }).unwrap();
          console.log('Tracking updated:', trackingResponse);
        } catch (trackingError) {
          console.error('Tracking update failed:', trackingError);
        }
      } else {
        console.error('No video ID in response');
        setIsGenerating(false);
        toast.error(t('heygenVideoCreator.videoScript.noVideoIdInResponse'), toastConfig);
      }
    } catch (error) {
      console.error('Video generation error details:', error);
      setIsGenerating(false);
      toast.error(t('heygenVideoCreator.videoScript.unexpectedError'), toastConfig);
    }
  }, [
    createVideo,
    id,
    state,
    allAvatars,
    updateAppTracking,
    user?._id,
    isGenerating,
    refetchVideoStatus,
    t,
    toastConfig,
  ]);

  // Mount olduğunda onGenerate prop'unu kontrol et
  useEffect(() => {
    console.log('HeygenVideoCreator mounted, onGenerate prop:', onGenerate ? 'var' : 'yok');
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (avatarSectionRef.current) {
        requestAnimationFrame(() => {
          avatarSectionRef.current.classList.add('bounce');
        });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    const audio = audioRef.current;
    const handleEnded = () => setIsPlaying(false);

    audio.addEventListener('ended', handleEnded);
    return () => {
      audio.removeEventListener('ended', handleEnded);
      audio.pause();
    };
  }, []);

  return (
    <>
      <Grid item xs={12}>
        <Paper elevation={0} className="heygen-video-creator__container" ref={avatarSectionRef}>
          <Box className="heygen-video-creator">
            <Box className="heygen-video-creator__avatar-section">
              <Box className="heygen-video-creator__header">
                <Stack
                  direction="row"
                  alignItems="center"
                  spacing={2}
                  className="heygen-video-creator__title"
                >
                  <Typography className="step-number" variant="h6" component="span">
                    1
                  </Typography>
                  <Typography variant="h6" component="h2">
                    {t('heygenVideoCreator.chooseAvatar.title')}
                  </Typography>
                </Stack>
                <Box className="heygen-video-creator__gender-tabs">
                  <Tabs
                    value={state.gender}
                    onChange={handleGenderChange}
                    aria-label="avatar gender selection"
                  >
                    <Tab label={t('heygenVideoCreator.chooseAvatar.female')} value="female" />
                    <Tab label={t('heygenVideoCreator.chooseAvatar.male')} value="male" />
                  </Tabs>
                </Box>
                <FavoriteButton shortcutID="1925ed61bad86d08e4e21925" shortcutType="usecase" />
              </Box>

              <Box sx={{ position: 'relative', mt: 3 }}>
                <AvatarContent
                  gender={state.gender}
                  onVideoChange={handleAvatarSelect}
                  onAvatarsChange={handleAvatarsChange}
                />
              </Box>
            </Box>
          </Box>
        </Paper>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Paper elevation={0} className="heygen-video-creator__container" ref={voiceSectionRef}>
              <Box className="heygen-video-creator">
                <Box className="heygen-video-creator__avatar-section">
                  <Box className="heygen-video-creator__header">
                    <Stack
                      direction="row"
                      alignItems="center"
                      spacing={2}
                      className="heygen-video-creator__title"
                    >
                      <Typography className="step-number" variant="h6" component="span">
                        2
                      </Typography>
                      <Typography variant="h6" component="h2">
                        {t('heygenVideoCreator.chooseVoice.title')}
                      </Typography>
                    </Stack>
                  </Box>
                  <Box className="heygen-video-creator__content-section">
                    <Box className="heygen-video-creator__voice-select-wrapper">
                      <IconButton
                        className="play-icon"
                        onClick={handleTogglePlay}
                        disabled={!state.selectedVoice || isLoadingVoices}
                      >
                        {isPlaying ? (
                          <PauseIcon fontSize="small" />
                        ) : (
                          <PlayArrowIcon fontSize="small" />
                        )}
                      </IconButton>
                      {isLoadingVoices && currentVoicePage === 1 ? (
                        <Box display="flex" justifyContent="center" width="100%" p={2}>
                          <CircularProgress size={24} />
                        </Box>
                      ) : (
                        <Select
                          value={state.selectedVoice}
                          onChange={handleVoiceChange}
                          fullWidth
                          variant="outlined"
                          displayEmpty
                          renderValue={(selected) => {
                            if (!selected) {
                              return (
                                <Typography color="text.secondary">
                                  {t('heygenVideoCreator.chooseVoice.selectVoice')}
                                </Typography>
                              );
                            }
                            const selectedVoice = allVoices.find(
                              (voice) => voice.voice_id === selected
                            );
                            return `${selectedVoice?.name} - ${selectedVoice?.language} (${selectedVoice?.gender})`;
                          }}
                          MenuProps={{
                            PaperProps: {
                              style: {
                                maxHeight: 300,
                              },
                              onScroll: handleVoiceSelectScroll,
                            },
                            MenuListProps: {
                              style: { paddingTop: 0, paddingBottom: 0 },
                            },
                          }}
                        >
                          {allVoices.map((voice, index) => (
                            <MenuItem
                              key={`${voice.voice_id}-${currentVoicePage}-${index}`}
                              value={voice.voice_id}
                            >
                              {voice.name} - {voice.language} ({voice.gender})
                            </MenuItem>
                          ))}
                          {isLoadingMoreVoices && (
                            <MenuItem disabled>
                              <Box display="flex" justifyContent="center" width="100%" py={1}>
                                <CircularProgress size={20} />
                              </Box>
                            </MenuItem>
                          )}
                        </Select>
                      )}
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Paper>
            <Paper elevation={0} className="heygen-video-creator__container" ref={scriptSectionRef}>
              <Box className="heygen-video-creator">
                <Box className="heygen-video-creator__avatar-section">
                  <Box className="heygen-video-creator__header">
                    <Stack
                      direction="row"
                      alignItems="center"
                      spacing={2}
                      className="heygen-video-creator__title"
                    >
                      <Typography className="step-number" variant="h6" component="span">
                        3
                      </Typography>
                      <Typography variant="h6" component="h2">
                        {t('heygenVideoCreator.videoScript.title')}
                      </Typography>
                    </Stack>
                  </Box>
                  <Box className="heygen-video-creator__content-section">
                    <Box className="heygen-video-creator__script-section">
                      <Box className="heygen-video-creator__script-wrapper">
                        <textarea
                          value={state.scriptText}
                          onChange={handleScriptChange}
                          onPaste={handlePaste}
                          className="heygen-video-creator__script-textarea"
                          rows={6}
                        />
                        <Typography
                          variant="caption"
                          className={`heygen-video-creator__character-count ${
                            state.scriptText.length === 500
                              ? 'heygen-video-creator__character-count--limit'
                              : 'heygen-video-creator__character-count--normal'
                          }`}
                        >
                          {state.scriptText.length}
                          {t('heygenVideoCreator.videoScript.characterLimit')}
                        </Typography>
                        {state.exceedsLimit && (
                          <Typography
                            variant="caption"
                            className="heygen-video-creator__character-limit-warning"
                          >
                            {t(
                              'heygenVideoCreator.videoScript.limitExceeded',
                              'Text exceeds 500 character limit'
                            )}
                          </Typography>
                        )}
                      </Box>
                      <Box className="heygen-video-creator__script-actions">
                        <Box
                          position="relative"
                          className={`heygen-video-creator__generate-btn-wrapper`}
                        >
                          <Tooltip
                            title={t('heygenVideoCreator.videoScript.generateVideoTooltip')}
                            placement="top"
                            arrow
                            classes={{
                              tooltip: `heygen-video-creator__tooltip`,
                            }}
                          >
                            <span className="tooltip-wrapper" style={{ width: '100%' }}>
                              <Button
                                variant="contained"
                                color="primary"
                                onClick={() => {
                                  console.log('Generate button clicked');
                                  console.log('Button state:', {
                                    isDisabled:
                                      isGenerating ||
                                      !state.videoUrl ||
                                      !state.selectedVoice ||
                                      state.exceedsLimit,
                                    isGenerating,
                                    hasVideoUrl: !!state.videoUrl,
                                    hasSelectedVoice: !!state.selectedVoice,
                                    exceedsLimit: state.exceedsLimit,
                                  });
                                  handleGenerateVideo();
                                }}
                                disabled={
                                  isGenerating ||
                                  !state.videoUrl ||
                                  !state.selectedVoice ||
                                  state.exceedsLimit
                                }
                                className={`heygen-video-creator__generate-btn`}
                                fullWidth
                              >
                                {isGenerating ? (
                                  <CircularProgress size={24} color="inherit" />
                                ) : (
                                  t('heygenVideoCreator.videoScript.generateVideo')
                                )}
                              </Button>
                            </span>
                          </Tooltip>
                        </Box>
                        <Typography
                          variant="caption"
                          className="heygen-video-creator__generated-by"
                        >
                          {t('heygenVideoCreator.videoScript.generatedBy')}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Paper>
          </Grid>
          <Grid item xs={12} md={8} className="heygen-video-creator__preview-video">
            <Paper elevation={0} className="heygen-video-creator__container">
              <Box className="heygen-video-creator">
                <Box className="heygen-video-creator__avatar-section">
                  <Box className="heygen-video-creator__header">
                    <Stack
                      direction="row"
                      alignItems="center"
                      spacing={2}
                      className="heygen-video-creator__title"
                    >
                      <Typography variant="h6" component="h2">
                        {t('heygenVideoCreator.previewVideo.title')}
                      </Typography>
                    </Stack>
                    {state.videoUrl && (
                      <Box
                        className="heygen-video-creator__download"
                        component="a"
                        href={state.videoUrl}
                        download
                      >
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <DownloadIcon fontSize="small" />
                          <Typography variant="body2">
                            {t('heygenVideoCreator.previewVideo.download')}
                          </Typography>
                        </Stack>
                      </Box>
                    )}
                  </Box>
                  <Box className="heygen-video-creator__content-section">
                    <Box className="heygen-video-creator__video-wrapper">
                      {isGenerating ? (
                        <Box
                          display="flex"
                          flexDirection="column"
                          alignItems="center"
                          justifyContent="center"
                          height="100%"
                          p={4}
                        >
                          <CircularProgress size={48} />
                          <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                            {t('heygenVideoCreator.previewVideo.generating')}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" align="center">
                            {t('heygenVideoCreator.previewVideo.generatingDescription')}
                          </Typography>
                          <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                            {t('heygenVideoCreator.previewVideo.status')}:{' '}
                            {prevVideoStatusRef.current?.status || 'waiting'}
                          </Typography>
                        </Box>
                      ) : (
                        state.videoUrl && (
                          <video
                            className="heygen-video-creator__video-player"
                            controls
                            playsInline
                            src={state.videoUrl}
                          />
                        )
                      )}
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Paper>
          </Grid>
        </Grid>
        <Box className="heygen-video-creator__ai-disclaimer-box">
          {t('common.warnings.dataPrivacy')}
        </Box>
      </Grid>
    </>
  );
};

HeygenVideoCreator.propTypes = {
  id: PropTypes.string,
  onGenerate: PropTypes.func,
};

export default HeygenVideoCreator;
