.footer {
  background-color: #f2f2f2;
  border-top: 1px solid #e9ecef;
  padding: 24px 0;
  font-family: 'Inter', sans-serif;

  .footer-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 20px;
    }
  }

  .footer-links {
    display: flex;
    align-items: center;
    font-size: 14px !important;
    gap: 24px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      width: 100%;

      &.right {
        align-items: flex-start;
      }
    }

    &.left {
      justify-content: flex-start;
    }

    &.right {
      justify-content: flex-end;
    }
  }

  .footer-link {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #71869d;
    text-decoration: none;
    transition: color 0.2s ease;
    white-space: nowrap;

    @media (max-width: 768px) {
      white-space: normal;
      word-break: break-all;
    }

    &:hover {
      color: #0072E5;
    }

    .MuiSvgIcon-root {
      font-size: 20px;
      min-width: 20px;
    }
  }
} 