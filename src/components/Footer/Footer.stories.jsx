import { BrowserRouter } from 'react-router-dom';
import Footer from './Footer';

export default {
  title: 'Components/Footer',
  component: Footer,
  decorators: [
    (Story) => (
      <BrowserRouter>
        <Story />
      </BrowserRouter>
    )
  ],
  parameters: {
    layout: 'fullscreen',
  },
};

// Default story
export const Default = {
  args: {}
};

// Mobile view story
export const Mobile = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
};

// Tablet view story
export const Tablet = {
  parameters: {
    viewport: {
      defaultViewport: 'tablet'
    }
  }
}; 