import { useSelector } from 'react-redux';
import { Box } from '@mui/material';
import { personalizeApi } from '../../middleware/personalize-api';
import { blockComponents } from './blockComponents';
import { useTranslation } from 'react-i18next';

const ConditionPage = ({ content }) => {
  const user = useSelector((state) => state.auth.user);
  const { i18n } = useTranslation();

  const getTranslatedValue = (field) => {
    if (!field) return '';
    if (typeof field === 'string') return field;
    return field.translations?.[i18n.language] || field.translations?.en || field.slug || '';
  };

  const userData = {
    name: user?.name,
    function_label: getTranslatedValue(user?.onboarding?.function_label),
    job_role_label: getTranslatedValue(user?.onboarding?.job_role_label),
    management_role_label: getTranslatedValue(user?.onboarding?.management_role_label),
    technical_background_label: getTranslatedValue(user?.onboarding?.technical_background_label),
    ai_knowledge_label: getTranslatedValue(user?.onboarding?.ai_knowledge_label),
    industry_label: getTranslatedValue(user?.onboarding?.industry_label),
    language: user?.onboarding?.language,
    // Orijinal objeleri de ekleyelim
    function_label_obj: user?.onboarding?.function_label,
    job_role_label_obj: user?.onboarding?.job_role_label,
    management_role_label_obj: user?.onboarding?.management_role_label,
    technical_background_label_obj: user?.onboarding?.technical_background_label,
    ai_knowledge_label_obj: user?.onboarding?.ai_knowledge_label,
    industry_label_obj: user?.onboarding?.industry_label,
  };

  // Filter content based on conditions
  const filteredContent = personalizeApi.filterPageContent(content, userData);

  // Render a block with processed props
  const renderBlock = (block) => {
    const Component = blockComponents[block.type];

    if (!Component) {
      console.warn(`Unknown block type: ${block.type}`);
      return null;
    }

    const processedProps = personalizeApi.processProps(block.props, userData);
    return <Component key={block.props.id} {...processedProps} />;
  };

  return <Box>{filteredContent.map((block) => renderBlock(block))}</Box>;
};

export default ConditionPage;
