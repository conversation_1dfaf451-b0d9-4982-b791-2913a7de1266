import React from 'react';
import { Box, Button, Typography, Alert, CircularProgress } from '@mui/material';
import usePanel from '../../hooks/usePanel';

/**
 * Özel panel erişim bile<PERSON>eni
 * @returns {JSX.Element} - Özel panel bileşeni
 */
const CustomPanel = () => {
  const { openPanel, loading, error, hasAccess, isAuthenticated } = usePanel();

  const handleOpenPanel = async () => {
    // Paneli aynı sayfada açmak için newTab: false
    await openPanel({ newTab: false });
  };

  const handleOpenPanelNewTab = async () => {
    // Paneli yeni sekmede açmak için newTab: true (varsayılan)
    await openPanel({ newTab: true });
  };

  return (
    <Box
      sx={{
        padding: 3,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: 600,
        margin: '0 auto',
      }}
    >
      <Typography variant="h5" gutterBottom>
        Panel Erişimi
      </Typography>

      {!isAuthenticated && <Alert severity="warning">Panel erişimi için giriş yapmalısınız.</Alert>}

      {isAuthenticated && !hasAccess && (
        <Alert severity="warning">Panel erişimi için admin yetkilerine sahip değilsiniz.</Alert>
      )}

      {error && <Alert severity="error">{error}</Alert>}

      <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
        <Button
          variant="contained"
          onClick={handleOpenPanel}
          disabled={loading || !isAuthenticated || !hasAccess}
          startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
        >
          {loading ? 'Yönlendiriliyor...' : 'Paneli Bu Sayfada Aç'}
        </Button>

        <Button
          variant="outlined"
          onClick={handleOpenPanelNewTab}
          disabled={loading || !isAuthenticated || !hasAccess}
        >
          Paneli Yeni Sekmede Aç
        </Button>
      </Box>
    </Box>
  );
};

export default CustomPanel;
