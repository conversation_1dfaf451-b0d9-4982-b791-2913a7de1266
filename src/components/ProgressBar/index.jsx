import { Box, Typography, LinearProgress, Skeleton } from '@mui/material';
import { useTranslation } from 'react-i18next';
import './progress-bar.scss';

const ProgressBar = ({ progress = 0, isLoading = false }) => {
  const { t } = useTranslation();

  // İlerleme değerinin 100'ü geçmemesini sağla
  const safeProgress = Math.min(progress, 100);

  const getProgressStatus = () => {
    if (isLoading) return t('common.loading', 'Yükleniyor...');
    if (safeProgress === 0) return t('common.notStarted', 'Not started yet');
    if (safeProgress === 100) return t('common.completed', 'Completed');
    return t('common.inProgress', 'In progress');
  };

  return (
    <Box className="progress-bar">
      <Box className="progress-bar__header">
        <Typography className="progress-bar__title">{getProgressStatus()}</Typography>
        <Typography className="progress-bar__percentage">
          {isLoading ? (
            <Skeleton width={60} />
          ) : (
            <>
              {safeProgress}% {t('common.complete', 'Complete')}
            </>
          )}
        </Typography>
      </Box>
      {isLoading ? (
        <Skeleton variant="rectangular" height={4} width="100%" />
      ) : (
        <LinearProgress variant="determinate" value={safeProgress} />
      )}
    </Box>
  );
};

export default ProgressBar;
