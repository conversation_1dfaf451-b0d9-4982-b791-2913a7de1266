@use '../../styles/abstracts/variables' as *;

.progress-bar {
  width: 100%;
  margin-bottom: $spacing-4;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-1;
  }

  &__title {
    font-size: $font-size-xs !important;
    font-weight: $font-weight-medium !important;
    color: $text-secondary !important;
  }

  &__percentage {
    font-size: $font-size-xs !important;
    font-weight: $font-weight-semibold !important;
    color: $primary-color !important;
  }

  .MuiLinearProgress-root {
    height: 6px;
    border-radius: $border-radius-sm;
    background-color: $bg-light-dark;

    .MuiLinearProgress-bar {
      border-radius: $border-radius-sm;
      background: linear-gradient(90deg, $primary-color 0%, $secondary-color 100%);
    }
  }
} 