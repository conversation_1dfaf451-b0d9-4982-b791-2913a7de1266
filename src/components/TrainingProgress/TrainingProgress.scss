@use '../../styles/abstracts/variables' as *;

.training-progress {
  position: fixed;
  bottom: 0;
  right: 0;
  background: #fff;
  border-radius: $border-radius-md;
  border: 1px solid $divider-color;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  width: 400px;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: bottom right;
  
  
  @media (max-width: $desktop) {
    width: 350px;
  }
  
  @media (max-width: $mobile) {
    width: calc(100% - #{$spacing-5});
    bottom: $spacing-3;
    right: $spacing-3;
    
    &.collapsed {
      width: 300px;
    }
  }
  
  &.collapsed {
    width: 240px;
    transform: translateY(calc(100% - 48px));
    
    .content {
      opacity: 0;
      transform: translateY(10px);
      visibility: hidden;
    }
  }
  
  & > .header {
    padding: $spacing-2 $spacing-3;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border-bottom: 1px solid $divider-color;
    height: auto;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba($primary-color, 0.04);
    }

    .training-title {
      margin: 0;
      font-weight: $font-weight-medium;
      color: $text-primary;
      font-size: $font-size-md;
    }

    .MuiIconButton-root {
      transition: transform 0.3s ease;
    }
  }
  
  .content {
    padding: $spacing-3;
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    .progress-bar {
      height: $spacing-2;
      background: $border-color;
      border-radius: $border-radius-sm;
      margin-bottom: $spacing-1;
      
      .progress {
        height: 100%;
        background: $success-color;
        border-radius: $border-radius-sm;
        transition: width 0.3s ease;
      }
    }
    
    .progress-info {
      color: $text-secondary;
      font-size: $font-size-xs;
      margin-bottom: $spacing-3;
      display:flex;
      flex-direction: row;
      justify-content: space-between;
    }
    
    .current-status {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap:0;
      .label {
        color: $text-secondary;
        font-size: $font-size-sm;
      }
      
      .step-name {
        color: $text-primary;
        font-weight: $font-weight-semibold;
        font-size: $font-size-md;
      }
      
      .step-progress {
        color: $text-secondary;
        font-size: $font-size-sm;
      }
    }
    
    .progress-dots {
      display: flex;
      gap: $spacing-2;
      margin-bottom: $spacing-4;
      
      .dot {
        width: $spacing-3;
        height: $spacing-3;
        border-radius: 50%;
        background: $border-color;
        
        &.completed {
          background: $success-color;
        }
      }
    }
    
    .navigation-buttons {
      display: flex;
      margin-top: $spacing-4;
      width: 100%;

      a, button {
        width: 100%;
      }
    }
  }
}

.completion-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-4;
  text-align: center;
  
  .completion-icon {
    font-size: 48px;
    color: $success-color;
    margin-bottom: $spacing-2;
    animation: scaleIn 0.5s ease;
  }
  
  .completion-text {
    color: $text-primary;
    margin-bottom: $spacing-1;
    animation: slideUp 0.5s ease;
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.limited-user-warning {
  text-align: center;
  padding: 15px;
  margin-bottom: 10px;

  .warning-icon {
    font-size: 48px;
    color: #ff9800;
    margin-bottom: 10px;
  }

  .warning-title {
    margin-bottom: 8px;
    font-weight: bold;
  }

  .warning-text {
    color: rgba(0, 0, 0, 0.7);
  }
} 