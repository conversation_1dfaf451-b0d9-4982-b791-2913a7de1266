import { useEffect, useState } from 'react';
import { Box, Typography, IconButton, Stack, Fade, Grow } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import WarningIcon from '@mui/icons-material/Warning';
import Button from '../Button/Button';
import PropTypes from 'prop-types';
import { setTrainingProgressExpanded } from '../../redux/features/training/trainingSlice';
import { useTranslation } from 'react-i18next';
import { useGetUseCaseByIdQuery } from '../../redux/services/use-case-api';
import { useGetJourneyTrackingQuery } from '../../redux/services/journey-api';
import { useFormByIdQuery } from '../../redux/services/form-service';
import './TrainingProgress.scss';
import { IDEA_FORM_ID } from '../../constants/form-constants';
import useUserRole from '@/hooks/useUserRole';

const TrainingProgress = ({
  // Navigation
  nextStepUrl = '/training/next-step',
  onContinue,
  // Yeni prop'lar
  showCompletionMessage = false,
  completionDuration = 2000,
  onCompletionEnd,
  buttonType,
  modalContent,
  setIsContentModalOpen,
  setIsIdeationModalOpen,
  // Progress info
  progress,
  totalSteps,
  completedSteps,
  currentStepTitle,
}) => {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const isExpanded = useSelector((state) => state.training.trainingProgress.isExpanded);
  const navigate = useNavigate();
  const user = useSelector((state) => state.auth.user);

  // useUserRole hook'unu kullan
  const { isLimitedUser, getUserJourneyLevel } = useUserRole();

  // Limited-use kullanıcısı ve kullanıcı expert seviyesinde ise her zaman uyarı göster
  const userJourneyLevel = getUserJourneyLevel();
  const showLimitedUserWarning = isLimitedUser() && userJourneyLevel === 'expert';

  // Kullanıcının dil tercihi
  const userLanguage = i18n.language || user?.onboarding?.language?.slug || 'en';

  // Usecase ID'si için state
  const [useCaseId, setUseCaseId] = useState(null);

  // UseCase API sorgusu
  const { data: useCaseData } = useGetUseCaseByIdQuery(useCaseId, {
    skip: !useCaseId,
  });

  // Kullanıcının journey tracking verilerini getir
  const { data: journeyTrackingData } = useGetJourneyTrackingQuery(user?._id, {
    skip: !user?._id || !useCaseId,
  });

  // Ideation form verilerini çek - Sadece Ideation türündeki butonlar için
  const { data: ideationFormData } = useFormByIdQuery(
    {
      formId: IDEA_FORM_ID,
      lang: userLanguage,
    },
    {
      skip: !buttonType || (buttonType !== 'Ideation' && buttonType !== 'Ideation Project'),
      refetchOnMountOrArgChange: true,
    }
  );

  // useCaseData değiştiğinde yönlendirme yap
  useEffect(() => {
    if (useCaseId && useCaseData && useCaseData.slug) {
      // Kullanıcının journey seviyesini belirle
      const journeyLevel = user?.journeyLevel?.name || 'beginner';

      // Usecase sayfasına yönlendirirken journey tracking parametrelerini gönder
      navigate(`/usecase/${useCaseData.slug}`, {
        state: {
          cardId: modalContent?.cardId,
          journeyCardId: modalContent?.cardId,
          journeyTrackingData: journeyTrackingData,
          journeyLevel: journeyLevel,
          // Yeni usecase için temiz başlangıç yapmak için reset flag'i ekle
          resetState: true,
        },
        replace: true, // Sayfa geçmişini değiştirmek yerine mevcut sayfayı değiştir
      });
      // İşlem tamamlandıktan sonra state'i temizle
      setUseCaseId(null);
    }
  }, [useCaseData, useCaseId, navigate, modalContent, journeyTrackingData, user]);

  // Sadece completion message gösterildiğinde otomatik olarak aç
  useEffect(() => {
    if (showCompletionMessage) {
      dispatch(setTrainingProgressExpanded(true));

      const timer = setTimeout(() => {
        if (onCompletionEnd) onCompletionEnd();
      }, completionDuration);

      return () => clearTimeout(timer);
    }
  }, [showCompletionMessage, completionDuration, onCompletionEnd, dispatch]);

  const handleToggle = () => {
    // Sadece Completion mesajı durumunda toggle'a izin verme
    if (!showCompletionMessage) {
      dispatch(setTrainingProgressExpanded(!isExpanded));
    }
  };

  // UseCase sayfasına özel yönlendirme fonksiyonu
  const navigateToUseCase = (useCaseId) => {
    if (!useCaseId) return;

    // UseCase API'den veri çekme için ID'yi state'e kaydet
    setUseCaseId(useCaseId);

    // App state sıfırlama işlemi için sessionStorage'a geçici flag yazalım
    sessionStorage.setItem('useCaseReset', 'true');

    if (onContinue) onContinue();
  };

  const handleContinue = () => {
    if (!buttonType || buttonType === '' || buttonType === 'URL') {
      // URL tipinde direkt olarak yönlendirme yap
      if (onContinue) onContinue();
      if (nextStepUrl) navigate(nextStepUrl);
      return; // Fonksiyondan çık
    }

    // Diğer buton türleri
    switch (buttonType) {
      case 'Course':
        // Course türündeki butona tıklandığında ilgili kurs sayfasına yönlendir
        if (modalContent && modalContent.courseId) {
          if (onContinue) onContinue();
          // cardId değerini sessionStorage'a kaydedelim ki kurs sayfaları arasında kaybolmasın
          if (modalContent.cardId) {
            sessionStorage.setItem('journeyCardId', modalContent.cardId);
          }
          navigate(`/course/${modalContent.courseId}`, {
            state: { cardId: modalContent.cardId },
          });
        } else if (nextStepUrl) {
          // Geriye dönük uyumluluk için nextStepUrl'i kullan
          if (onContinue) onContinue();
          navigate(nextStepUrl);
        }
        break;

      case 'Content':
      case 'MODAL':
        // İçerik modalını aç
        if (modalContent && setIsContentModalOpen) {
          setIsContentModalOpen(true);
          if (onContinue) onContinue();
        }
        break;

      case 'Ideation':
      case 'Ideation Project':
        // Ideation formunu aç
        if (setIsIdeationModalOpen) {
          // Form ID'yi ve form datasını modalContent içine ekle
          const ideationModalContent = {
            ...(modalContent || {}),
            formId: IDEA_FORM_ID,
            formData: ideationFormData,
          };

          // ModalContent'i güncelleyerek Ideation modalını aç
          setIsIdeationModalOpen(ideationModalContent);

          if (onContinue) onContinue();
        }
        break;

      case 'Usecases':
        // Use case sayfasına yönlendir
        if (modalContent && modalContent.useCaseId) {
          // Özel UseCase yönlendirme fonksiyonunu kullan
          navigateToUseCase(modalContent.useCaseId);
        } else if (nextStepUrl) {
          // Geriye dönük uyumluluk için nextStepUrl'i kullan
          if (onContinue) onContinue();
          navigate(nextStepUrl);
        }
        break;

      case 'App Creator':
        // App Creator sayfasına yönlendir
        if (onContinue) onContinue();
        // Journey card ile uyumlu olması için buttonURL varsa onu kullan
        if (modalContent && modalContent.buttonURL) {
          navigate(modalContent.buttonURL, {
            state: { cardId: modalContent?.cardId },
          });
        } else {
          navigate('/app-creator', {
            state: { cardId: modalContent?.cardId },
          });
        }
        break;

      case 'Workflow Creator':
        // Workflow Creator sayfasına yönlendir
        if (onContinue) onContinue();
        // Journey card ile uyumlu olması için buttonURL varsa onu kullan
        if (modalContent && modalContent.buttonURL) {
          navigate(modalContent.buttonURL, {
            state: { cardId: modalContent?.cardId },
          });
        } else {
          navigate('/workflow-creator', {
            state: { cardId: modalContent?.cardId },
          });
        }
        break;

      case 'GenAI Playground':
        // GenAI Playground sayfasına yönlendir
        if (onContinue) onContinue();
        // Journey card ile uyumlu olması için buttonURL varsa onu kullan
        if (modalContent && modalContent.buttonURL) {
          navigate(modalContent.buttonURL, {
            state: { cardId: modalContent?.cardId },
          });
        } else {
          navigate('/genai-playground', {
            state: { cardId: modalContent?.cardId },
          });
        }
        break;

      default:
        // Tanımlanmamış buton türleri için varsayılan olarak nextStepUrl'e yönlendir
        if (onContinue) onContinue();
        if (nextStepUrl) navigate(nextStepUrl);
        break;
    }
  };

  return (
    <Box
      className={`training-progress ${isExpanded ? '' : 'collapsed'}`}
      role="complementary"
      aria-label="Training progress tracker"
    >
      <Box className="header" onClick={handleToggle} role="button" aria-expanded={isExpanded}>
        <Typography variant="h6" component="div" className="training-title">
          {t('training.progress.title', 'Training Progress')}
        </Typography>
        <IconButton size="small">
          {isExpanded ? <KeyboardArrowDownIcon /> : <KeyboardArrowUpIcon />}
        </IconButton>
      </Box>

      <Box className="content">
        {showLimitedUserWarning ? (
          <Grow in={true}>
            <Box className="limited-user-warning">
              <WarningIcon className="warning-icon" />
              <Typography variant="h6" className="warning-title">
                {t('modal.actionRequired', 'Action required')}
              </Typography>
              <Typography variant="body2" color="text.secondary" className="warning-text">
                {t('journey.limitedUser.accessWarning')}
              </Typography>
            </Box>
          </Grow>
        ) : showCompletionMessage ||
          progress === 100 ||
          (completedSteps > 0 && completedSteps === totalSteps) ? (
          <Grow in={true}>
            <Box className="completion-message">
              <CheckCircleIcon className="completion-icon" />
              <Typography variant="h6" className="completion-text">
                {t('modal.completed', 'Completed')}!
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {progress === 100 || (completedSteps > 0 && completedSteps === totalSteps)
                  ? t('training.progress.allStepsCompleted', 'All steps completed')
                  : t('training.progress.stepCompleted', "You've completed this step")}
              </Typography>
            </Box>
          </Grow>
        ) : (
          <Fade in={true}>
            <Box>
              <Box className="progress-bar">
                <Box
                  className="progress"
                  style={{ width: `${progress}%` }}
                  role="progressbar"
                  aria-valuenow={progress}
                  aria-valuemin="0"
                  aria-valuemax="100"
                />
              </Box>

              <Stack direction="row" justifyContent="space-between" className="progress-info">
                <Typography variant="caption">{progress}%</Typography>
                <Typography variant="caption">
                  {t(
                    'training.progress.stepsCompleted',
                    {
                      completed: completedSteps,
                      total: totalSteps,
                    },
                    `${completedSteps}/${totalSteps} steps`
                  )}
                </Typography>
              </Stack>

              <Stack spacing={0.5} className="current-status">
                <Typography variant="caption" color="text.secondary" className="label">
                  {t('training.progress.currentStep', 'Current step')}:
                </Typography>
                <Typography variant="subtitle1" className="step-name">
                  {currentStepTitle}
                </Typography>
              </Stack>

              <Box className="navigation-buttons">
                <Button
                  variant="contained"
                  color="primary"
                  size="medium"
                  onClick={handleContinue}
                  fullWidth
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {t('modal.continue', 'Continue')}
                  </Box>
                </Button>
              </Box>
            </Box>
          </Fade>
        )}
      </Box>
    </Box>
  );
};

TrainingProgress.propTypes = {
  // Navigation
  nextStepUrl: PropTypes.string,
  onContinue: PropTypes.func,
  // Yeni prop'lar
  showCompletionMessage: PropTypes.bool,
  completionDuration: PropTypes.number,
  onCompletionEnd: PropTypes.func,
  buttonType: PropTypes.string,
  modalContent: PropTypes.any,
  setIsContentModalOpen: PropTypes.func,
  setIsIdeationModalOpen: PropTypes.func,
  // Progress info
  progress: PropTypes.number,
  totalSteps: PropTypes.number,
  completedSteps: PropTypes.number,
  currentStepTitle: PropTypes.string,
};

export default TrainingProgress;
