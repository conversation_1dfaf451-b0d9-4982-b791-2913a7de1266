import React from 'react';
import { Button, CircularProgress, Tooltip } from '@mui/material';
import usePanel from '../../hooks/usePanel';

const PanelButton = ({
  variant = '',
  color = 'primary',
  size = 'medium',
  newTab = true,
  ...props
}) => {
  const { openPanel, loading, error, hasAccess, isAuthenticated } = usePanel();

  const handleClick = async () => {
    await openPanel({ newTab });
  };

  // Kullanıcı giriş yapmamışsa veya yetkisi yoksa butonu disable et
  const isDisabled = loading || !isAuthenticated || !hasAccess;

  // Tooltip mesajını belirle
  let tooltipMessage = '';
  if (!isAuthenticated) {
    tooltipMessage = 'You must be logged in to access the panel';
  } else if (!hasAccess) {
    tooltipMessage = 'You must have admin privileges to access the panel';
  } else if (error) {
    tooltipMessage = error;
  }

  return (
    <Tooltip title={tooltipMessage} placement="bottom" arrow disabled={!isDisabled}>
      <Button
        variant={variant}
        color={color}
        size={size}
        sx={{
          textTransform: 'capitalize',
          width: '100%',
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          '&:hover': {
            backgroundColor: 'rgba(25, 118, 210, 0.08)',
          },
        }}
        onClick={handleClick}
        disabled={isDisabled}
        startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
        {...props}
      >
        {loading ? 'Redirecting...' : 'Panel'}
      </Button>
    </Tooltip>
  );
};

export default PanelButton;
