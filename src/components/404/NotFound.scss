.not-found-page {
  display: flex;
  align-items: center;
  min-height: calc(100vh - 100px);
  
  .not-found-image {
    max-width: 100%;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.03);
    }
  }
  
  .not-found-title {
    font-size: 5rem;
    font-weight: 700;
    color: #21325B;
    margin-bottom: 1rem;
  }
  
  .not-found-text {
    color: #666;
    margin-bottom: 2rem;
  }
  
  .back-button {
    text-transform: none;
    font-weight: 500;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(33, 50, 91, 0.3);
    }
  }
  
  @media (max-width: 900px) {
    .not-found-content {
      text-align: center;
      margin-top: 2rem;
    }
    
    .not-found-title {
      font-size: 4rem;
    }
  }
}

@media (max-width: 960px) {
  .not-found-container {
    text-align: center;
    
    .not-found-image {
      margin-bottom: 2rem;
    }
  }
} 