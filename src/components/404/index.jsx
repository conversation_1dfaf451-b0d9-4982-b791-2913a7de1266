import React from 'react';
import { Box, Container, Grid, Typo<PERSON>, Button } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

// 404 SVG görseli import
import NotFoundSvg from '../../assets/images/404.svg';
import './NotFound.scss';

const NotFoundPage = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleGoBack = () => {
    navigate('/');
  };

  return (
    <Container className="not-found-page">
      <Grid 
        container 
        spacing={4} 
        sx={{ alignItems: 'center' }}
      >
        <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'center' }}>
          <Box
            component="img"
            src={NotFoundSvg}
            alt="404 Not Found"
            className="not-found-image"
          />
        </Grid>
        <Grid item xs={12} md={6} className="not-found-content">
          <Typography 
            variant="h1" 
            component="h1" 
            className="not-found-title"
          >
            404
          </Typography>
          <Typography 
            variant="h5" 
            component="p" 
            className="not-found-text"
          >
            {t('errors.pageNotFound', 'Sorry, the page you\'re looking for cannot be found.')}
          </Typography>
          <Button 
            variant="contained" 
            color="primary" 
            size="large"
            onClick={handleGoBack}
            className="back-button"
            sx={{ minWidth: 200 }}
          >
            {t('common.goBack', 'Go back to the App')}
          </Button>
        </Grid>
      </Grid>
    </Container>
  );
};

export default NotFoundPage;
