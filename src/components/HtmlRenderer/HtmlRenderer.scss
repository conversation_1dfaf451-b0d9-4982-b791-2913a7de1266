.html-renderer {
  // <PERSON><PERSON> stiller
  font-family: inherit;
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;

  // <PERSON><PERSON><PERSON><PERSON>k stilleri
  h1, h2, h3, h4, h5, h6 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.25;
    color: #505d68 !important;
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  h3 {
    font-size: 1.25rem;
  }

  // Paragraf stilleri
  p {
    margin-top: 0;
    margin-bottom: 1rem;
  }

  // Modern tablo stilleri
  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 20px 0;
    font-size: 14px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    background-color: #fff;
    border: 1px solid #e2e8f0;
    position: relative;
  }
  
  thead {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid #cbd5e1;

    th {
      padding: 16px;
      text-align: left;
      font-weight: 600;
      color: #334155;
      border-bottom: none;
      position: relative;
      white-space: nowrap;
      text-transform: uppercase;
      font-size: 13px;
      letter-spacing: 0.5px;
      
      &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 20%;
        height: 60%;
        width: 1px;
        background-color: #cbd5e1;
      }
    }

  }

  
  th {
    padding: 16px;
    text-align: left;
    font-weight: 600;
    border-bottom: none;
    position: relative;
    white-space: nowrap;
    text-transform: uppercase;
    font-size: 13px;
    letter-spacing: 0.5px;
    
    &:not(:last-child)::after {
      content: '';
      position: absolute;
      right: 0;
      top: 20%;
      height: 60%;
      width: 1px;
      background-color: rgba(255, 255, 255, 0.3);
    }
  } 

  
  td {
    padding: 14px 16px;
    vertical-align: top;
    border-bottom: 1px solid #edf2f7;
    transition: all 0.2s ease;
    
    strong {
      display: inline-block;
      margin-bottom: 6px;
      color: #2d3748;
      font-weight: 600;
    }
    
    ul {
      margin: 0;
      padding-left: 18px;
      
      li {
        margin-bottom: 4px;
        position: relative;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  
  tbody {
    tr {
      transition: all 0.3s ease;
      
      &:hover {
        background-color: #f7fafc;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      }
      
      &:nth-child(even) {
        background-color: #f8fafc;
      }
      
      &:nth-child(odd) {
        background-color: #ffffff;
      }
      
      &:last-child td {
        border-bottom: none;
      }
    }
  }
  
  // Responsive tablo
  @media (max-width: 768px) {
    table {
      display: block;
      overflow-x: auto;
      white-space: nowrap;
      border-radius: 8px;
    }
    
    tbody tr:hover {
      transform: none;
    }
  }

  // Section sınıfına sahip tablolar için özel stil
  table.section {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 20px 0;
    font-size: 14px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    background-color: #fff;
    border: 1px solid #e2e8f0;
    position: relative;
    
    thead {
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      position: sticky;
      top: 0;
      z-index: 100;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      border-bottom: 1px solid #cbd5e1;
      display: table-header-group;
    }

    
    td {
      padding: 14px 16px;
      vertical-align: top;
      border-bottom: 1px solid #edf2f7;
      transition: all 0.2s ease;
      
      strong {
        display: inline-block;
        margin-bottom: 6px;
        color: #2d3748;
        font-weight: 600;
      }
    }
    
    tbody tr {
      transition: all 0.3s ease;
      
      &:hover {
        background-color: #f7fafc;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      }
      
      &:nth-child(even) {
        background-color: #f8fafc;
      }
      
      &:nth-child(odd) {
        background-color: #ffffff;
      }
      
      &:last-child td {
        border-bottom: none;
      }
    }
  }

  // Section sınıfına sahip listeler için özel stil
  ul.section {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.07);
    background: #fff;
    position: relative;
    
    > li {
      margin: 0;
      padding: 0;
      border-bottom: 1px solid #edf2f7;
      position: relative;
      
      // Alt liste olmadığında içerik stillemesi
      &:not(:has(> ul)) {
        padding: 16px 20px;
        flex-wrap: wrap;
        align-items: flex-start;
        
        > strong {
          padding: 0;
          margin-right: 8px;
          background-color: transparent;
          flex: 0 0 auto;
          margin-bottom: 0;
          
          &:before {
            margin-right: 8px;
          }
        }
        
        > em {
          font-style: italic;
          color: #4b5563;
          padding-left: 4px;
        }
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      > strong {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        margin: 0;
        color: #1e293b;
        font-weight: 600;
        font-size: 15px;
        background-color: #f8fafc;
        position: relative;
        
        &:before {
          content: '';
          width: 10px;
          height: 10px;
          margin-right: 12px;
          background-color: #dbeafe;
          border-radius: 50%;
          border: 2px solid #3b82f6;
          flex-shrink: 0;
        }
      }
      
      > ul {
        background-color: #fff;
        margin: 0;
        padding: 12px 20px 16px;
        
        li {
          position: relative;
          padding: 8px 0 8px 22px;
          margin-bottom: 6px;
          font-size: 15px;
          line-height: 1.5;
          color: #475569;
          display: flex;
          align-items: flex-start;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 14px;
            width: 8px;
            height: 8px;
            background-color: #93c5fd;
            border-radius: 50%;
            flex-shrink: 0;
          }
          
          &:hover {
            color: #1e293b;
            
            &:before {
              background-color: #3b82f6;
            }
          }
        }
      }
      
      &:hover {
        > strong {
          color: #1e40af;
          background-color: #f0f7ff;
          
          &:before {
            background-color: #bfdbfe;
            border-color: #3b82f6;
          }
        }
      }
    }
    
    // Responsive stil
    @media (max-width: 768px) {
      border-radius: 8px;
      
      > li > strong {
        padding: 14px 16px;
        font-size: 14px;
      }
      
      > li > ul {
        padding: 10px 16px 14px;
      }
      
      > li:not(:has(> ul)) {
        padding: 14px 16px;
      }
    }
  }
  
  // Section sınıfına sahip sıralı listeler için özel stil
  ol.section {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.07);
    background: #fff;
    position: relative;
    counter-reset: item;
    
    > li {
      margin: 0;
      padding: 0;
      border-bottom: 1px solid #edf2f7;
      position: relative;
      counter-increment: item;
      
      // Alt liste olmadığında içerik stillemesi
      &:not(:has(> ul)) {
        padding: 16px 20px;
        flex-wrap: wrap;
        align-items: flex-start;
        
        > strong {
          padding: 0;
          margin-right: 8px;
          background-color: transparent;
          flex: 0 0 auto;
          margin-bottom: 0;
          
          &:before {
            content: counter(item) ". ";
            margin-right: 8px;
            color: #3b82f6;
            font-weight: 600;
          }
        }
        
        > p {
          margin: 8px 0 0 0;
          color: #475569;
          font-size: 15px;
          line-height: 1.5;
        }
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      > strong {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        margin: 0;
        color: #1e293b;
        font-weight: 600;
        font-size: 15px;
        background-color: #f8fafc;
        position: relative;
        
        &:before {
          content: counter(item) ". ";
          margin-right: 12px;
          color: #3b82f6;
          font-weight: 600;
        }
      }
      
      > ul {
        background-color: #fff;
        margin: 0;
        padding: 12px 20px 16px;
        
        li {
          position: relative;
          padding: 8px 0 8px 22px;
          margin-bottom: 6px;
          font-size: 15px;
          line-height: 1.5;
          color: #475569;
          display: flex;
          align-items: flex-start;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 14px;
            width: 8px;
            height: 8px;
            background-color: #93c5fd;
            border-radius: 50%;
            flex-shrink: 0;
          }
          
          &:hover {
            color: #1e293b;
            
            &:before {
              background-color: #3b82f6;
            }
          }
        }
      }
      
      &:hover {
        > strong {
          color: #1e40af;
          background-color: #f0f7ff;
          
          &:before {
            color: #2563eb;
          }
        }
      }
    }
    
    // Responsive stil
    @media (max-width: 768px) {
      border-radius: 8px;
      
      > li > strong {
        padding: 14px 16px;
        font-size: 14px;
      }
      
      > li > ul {
        padding: 10px 16px 14px;
      }
      
      > li:not(:has(> ul)) {
        padding: 14px 16px;
      }
    }
  }
  
  // Kod blok stilleri
  pre {
    margin: 1.5rem 0;
    padding: 0;
    overflow: hidden;
    background-color: #1e293b;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    position: relative;
    width: 100%;
    max-width: 100%;
    
    // HTML/XML tag'leri için özel stiller
    &.language-html, &.language-xml {
      code {
        .tag-open, .tag-close {
          color: #7ee787;
          font-weight: 500;
        }
        
        .tag-name {
          color: #7ee787;
          font-weight: 500;
        }
        
        .attr-name {
          color: #79c0ff;
          font-weight: normal;
        }
        
        .attr-value {
          color: #a5d6ff;
          font-weight: normal;
          
          &::before, &::after {
            color: #ff7b72; // Tırnak işaretleri için
            content: attr(data-quote);
          }
        }
        
        .script-tag {
          background-color: rgba(255, 255, 255, 0.03);
          border-left: 2px solid #ff7b72;
          margin-left: -3.5rem;
          padding-left: 3.5rem;
          display: inline-block;
          width: calc(100% + 3.5rem);
        }
        
        // HTML içindeki tagler için aktif highlight efekti
        .html-tag {
          display: inline-block;
          position: relative;
          transition: all 0.2s ease;
          
          &:hover {
            color: #9aeabc;
            text-shadow: 0 0 8px rgba(126, 231, 135, 0.4);
          }
          
          // Tag eşleşme efekti
          &:hover + .html-tag-pair, 
          &.html-tag-pair:hover {
            color: #9aeabc;
            text-shadow: 0 0 8px rgba(126, 231, 135, 0.4);
          }
        }
      }
    }
    
    &::before {
      content: '';
      position: absolute;
      top: 12px;
      left: 12px;
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #f87171;
      box-shadow: 18px 0 0 #fbbf24, 36px 0 0 #34d399;
      z-index: 5;
    }
    
    // Yatay kaydırma (scrollbar) stilini özelleştirme
    &::-webkit-scrollbar {
      height: 8px;
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 0 0 8px 8px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.3);
      }
    }
    
    code {
      display: block;
      padding: 2.5rem 1.25rem 1.25rem;
      margin: 0;
      background-color: transparent;
      border-radius: 0;
      overflow: auto;
      color: #e2e8f0;
      font-family: 'Fira Code', 'JetBrains Mono', SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
      font-size: 0.9rem;
      line-height: 1.6;
      font-weight: 400;
      text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 100%;
      overflow-x: auto;
      white-space: pre;
      word-break: keep-all;
      word-wrap: normal;
      tab-size: 2;
      
      // HTML etiketleri ve özel karakterler için renklendirme
      .tag { color: #7ee787; }
      .tag-bracket { color: #8b949e; }
      .attr-name { color: #79c0ff; }
      .attr-value { color: #a5d6ff; }
      .attr-quote { color: #ff7b72; }
      .entity { color: #79c0ff; }
      
      // Sözdizimi vurgulaması
      .string { color: #a5d6ff; }
      .number { color: #ff9580; }
      .boolean { color: #ff9580; }
      .null { color: #ff9580; }
      .key { color: #79c0ff; }
      .keyword { color: #ff7b72; }
      .function { color: #d2a8ff; }
      .method { color: #d2a8ff; }
      .selector { color: #7ee787; }
      .property { color: #79c0ff; }
      .variable { color: #ffa657; }
      .operator { color: #ff7b72; }
      .parameter { color: #ffa657; }
      .comment { color: #8b949e; font-style: italic; }
      .punctuation { color: #c9d1d9; }
      .doctype { color: #8b949e; font-style: italic; }
      
      // Satır numarası benzeri bir görünüm için sol taraf padding
      padding-left: 3.5rem;
      background-image: 
        linear-gradient(to right, rgba(255, 255, 255, 0.05) 3rem, transparent 3rem),
        linear-gradient(to bottom, transparent 0%, transparent 100%);
      background-size: 100% 1.6rem;
      background-position: 0 0.8rem;
      position: relative;
      
      // Satır numarası benzeri görünüm için kod bloğunun içeriğini görsel olarak satırlara bölme
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 2.5rem;
        bottom: 0;
        width: 3rem;
        border-right: 1px solid rgba(255, 255, 255, 0.1);
        z-index: 1;
      }
      
      // Her satır için vurgu ekleme (hover edildiğinde satırı vurgulama)
      &:hover {
        background-image: 
          linear-gradient(to right, rgba(255, 255, 255, 0.05) 3rem, transparent 3rem),
          linear-gradient(to bottom, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.02) 100%);
      }
      
      // Satırların tam metni kaplaması için özel stil
      @supports (width: stretch) {
        width: -webkit-fill-available;
        width: -moz-available;
        width: stretch;
      }
    }
  }
  
  // HTML sözdizimi için özel işleme
  pre.language-html, pre.language-xml {
    position: relative;
    
    // VSCode benzeri etiket vurgulama
    &::after {
      content: 'HTML';
      position: absolute;
      top: 12px;
      right: 12px;
      font-size: 10px;
      color: rgba(255, 255, 255, 0.4);
      background-color: rgba(255, 255, 255, 0.1);
      padding: 2px 6px;
      border-radius: 3px;
      text-transform: uppercase;
      letter-spacing: 1px;
      z-index: 5;
    }

    .token.tag {
      color: #7ee787;
    }
    
    .token.attr-name {
      color: #79c0ff;
    }
    
    .token.attr-value {
      color: #a5d6ff;
    }
    
    .token.punctuation {
      color: #8b949e;
    }
    
    .token.doctype {
      color: #8b949e; 
      font-style: italic;
    }
    
    // Script tag vurgulaması
    .token.script-tag {
      position: relative;
      margin-left: -3.5rem;
      padding-left: 3.5rem;
      width: calc(100% + 3.5rem);
      display: inline-block;
      background-color: rgba(255, 255, 255, 0.03);
      border-left: 2px solid #ff7b72;
    }
    
    .token.script {
      background-color: rgba(255, 255, 255, 0.03);
      display: inline-block;
      width: 100%;
      
      .token.keyword {
        color: #ff7b72;
      }
      
      .token.string {
        color: #a5d6ff;
      }
      
      .token.function {
        color: #d2a8ff;
      }
      
      .token.operator {
        color: #ff7b72;
      }
      
      .token.variable {
        color: #ffa657;
      }
      
      .token.comment {
        color: #8b949e;
        font-style: italic;
      }
    }
    
    // Özel sözdizimi vurgulaması
    // src ve href attr vurgulama
    .token.attr-name[data-value="src"],
    .token.attr-name[data-value="href"] {
      color: #79c0ff;
      font-weight: 500;
      
      & + .token.attr-value {
        text-decoration: underline;
        text-decoration-style: dotted;
        text-decoration-color: rgba(255, 255, 255, 0.3);
        text-underline-offset: 3px;
      }
    }
    
    // script tag içindeki src özelliği
    .token.tag[data-value="script"] {
      background-color: rgba(255, 255, 255, 0.03);
      
      .token.attr-name[data-value="src"] + .token.attr-value {
        font-style: italic;
      }
    }
  }
  
  // HTML tag formatı için özelleştirme - içerik korunarak
  .html-content {
    pre code.language-html, pre code.language-xml {
      .tag { display: inline; color: #7ee787; }
      .attr-name { display: inline; color: #79c0ff; }
      .attr-value { display: inline; color: #a5d6ff; }
      
      // Script tag için özel stil
      .script-tag {
        color: #ff7b72;
        font-weight: 500;
      }
      
      // src, href gibi özellikler için özel stil
      .url-attr {
        color: #a5d6ff;
        text-decoration: underline;
        text-decoration-style: dotted;
        text-decoration-color: rgba(255, 255, 255, 0.3);
        text-underline-offset: 3px;
      }
    }
  }

  code {
    position: relative;
    padding: 0.2rem 0.4rem;
    margin: 0 0.2rem;
    font-size: 0.85em;
    background-color: #f1f5f9;
    color: #0f172a;
    border-radius: 4px;
    font-family: 'Fira Code', 'JetBrains Mono', SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    vertical-align: text-bottom;
    letter-spacing: -0.3px;
    transition: all 0.2s ease;
    
    // Satır içi kod için, nowrap kaldırılarak uzun kodların görünmesi sağlandı
    white-space: pre-wrap;
    word-break: break-word;
    
    &:hover {
      background-color: #e2e8f0;
      border-color: #cbd5e1;
    }
  }

  // Bağlantı stilleri
  a {
    color: #0366d6;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }



  // Resim stilleri
  img {
    max-width: 100%;
    height: auto;
    margin: 1rem 0;
    border-radius: 4px;
  }

  // Blok alıntı stilleri
  blockquote {
    margin: 1rem 0;
    padding: 0 1rem;
    color: #6a737d;
    border-left: 0.25rem solid #dfe2e5;

    > :first-child {
      margin-top: 0;
    }

    > :last-child {
      margin-bottom: 0;
    }
  }

  // Yatay çizgi stili
  hr {
    height: 0.25rem;
    padding: 0;
    margin: 1.5rem 0;
    background-color: #e1e4e8;
    border: 0;
  }
}

// API yanıtları için özel sınıf
.api-response {
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

// Kod vurgulama (syntax highlighting) için
.code-block {
  position: relative;
  width: 100%;
  
  // Editör benzeri satır numaraları için
  .line-numbers {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3rem;
    padding: 2.5rem 0 1.25rem;
    font-family: 'Fira Code', 'JetBrains Mono', monospace;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.4);
    text-align: right;
    user-select: none;
    pointer-events: none;
    
    .line {
      padding: 0 0.75rem;
      height: 1.6rem;
      line-height: 1.6rem;
    }
  }
  
  .copy-button {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 20;
    opacity: 0;
    transform: translateY(-4px);
    transition: all 0.2s ease-in-out;
    background-color: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
    border: none;
    border-radius: 4px;
    padding: 6px 10px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    
    svg {
      width: 14px;
      height: 14px;
    }
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-4px) scale(1.05);
    }
    
    &.copied {
      background-color: #34d399;
      color: #022c22;
    }
  }
  
  &:hover .copy-button {
    opacity: 1;
    transform: translateY(0);
  }

  // Kod dili etiketi
  .code-language {
    position: absolute;
    top: 8px;
    right: 88px; // Kopyalama düğmesinin solunda
    background-color: rgba(255, 255, 255, 0.08);
    color: #cbd5e1;
    font-family: 'Fira Code', 'JetBrains Mono', monospace;
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 4px;
    z-index: 10;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    pointer-events: none; // Tıklamayı engelle
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
  }
}

// Markdown içerik için
.markdown-body {
  @extend .html-renderer;
}

// Inline code için yeni görünüm
code:not(pre code) {
  // ... existing code ...
  background-color: #f1f5f9;
  color: #0f172a;
  
  // Modern kodlama renklerini burada da kullanalım
  &.function { color: #7c3aed; }
  &.keyword { color: #ef4444; }
  &.string { color: #0284c7; }
  &.method { color: #7c3aed; }
  &.selector { color: #16a34a; }
  &.jquery { 
    color: #0284c7; 
    &::before {
      content: "$";
      color: #16a34a;
      font-weight: 600;
    }
  }
}

// jQuery sözdizimi için özel renklendirme
pre.language-javascript, pre.language-js, pre.language-jquery {
  code {
    .jquery-token { color: #7ee787; }
    .function-call { color: #d2a8ff; }
    
    // Dollar işareti ve jQuery selektörleri
    .jquery-selector { 
      color: #79c0ff;
      &::before {
        content: "$";
        color: #7ee787;
        font-weight: 600;
      }
    }
    
    // jQuery metotları
    .jquery-method {
      color: #d2a8ff;
      font-weight: 500;
    }
    
    // jQuery event işleyicileri
    .jquery-event {
      color: #ff7b72;
      font-weight: 500;
    }
    
    // jQuery içindeki fonksiyon tanımlamaları
    .function-definition {
      color: #d2a8ff;
    }
    
    // Yorum satırları için özel stil
    .comment {
      color: #8b949e; 
      font-style: italic;
      opacity: 0.8;
    }
  }
} 