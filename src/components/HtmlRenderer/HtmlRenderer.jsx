import PropTypes from 'prop-types';
import { useHtmlTransformer } from '@/middleware/htmlTransformer';
import './HtmlRenderer.scss';

/**
 * HTML içeriğini güvenli şekilde görüntülemek için kullanılan ortak bileşen
 * API yanıtlarındaki HTML içeriğini render etmek için kullanılır
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {string} props.content - Görüntülenecek HTML içeriği
 * @param {string} props.className - Ek CSS sınıfı
 * @param {boolean} props.sanitize - İçeriğin sanitize edilip edilmeyeceği (varsayılan: true)
 * @param {boolean} props.preserveHtmlTags - HTML etiketlerinin korunup korunmayacağı (varsayılan: true)
 * @param {boolean} props.formatCodeBlocks - Kod bloklarının formatlanıp formatlanmaya<PERSON>ğı (varsayılan: true)
 * @param {boolean} props.transformHtml - HTML içeriğinin transformHtmlContent fonksiyonuyla dönüştürülüp dönüştürülmeyeceği (varsayılan: false)
 * @returns {JSX.Element} HTML içeriğini render eden bileşen
 */
const HtmlRenderer = ({
  content,
  className = 'html-content',
  sanitize = true,
  preserveHtmlTags = true,
  formatCodeBlocks = true,
  transformHtml = false,
}) => {
  const { content: processedContent } = useHtmlTransformer(content, {
    sanitize,
    preserveHtmlTags,
    formatCodeBlocks,
    transformHtml,
    className,
  });

  return (
    <div
      className={`html-renderer ${className}`}
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
};

HtmlRenderer.propTypes = {
  content: PropTypes.string.isRequired,
  className: PropTypes.string,
  sanitize: PropTypes.bool,
  preserveHtmlTags: PropTypes.bool,
  formatCodeBlocks: PropTypes.bool,
  transformHtml: PropTypes.bool,
};

export default HtmlRenderer;
