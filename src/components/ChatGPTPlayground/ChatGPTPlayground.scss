@use 'sass:color';
@use '../../styles/abstracts/variables' as *;

// PromptModal.scss'den chat ile ilgili tüm stilleri buraya taşıyalım
.prompt-modal__chat {
  display: flex;
  flex-direction: column;
  background: url('@/assets/images/effect.png') no-repeat top -80px right -75px $bg-paper;
  height: auto;
  border: 1px solid $border-color;
  margin-top: $spacing-4;
  border-radius: $border-radius-md;
  position: relative;

  &-container {
    display: flex;
    flex: 1;
    gap: $spacing-4;
    padding: $spacing-4;
    position: relative;

    @media (max-width: $tablet) {
      flex-direction: column;
      padding: $spacing-3;
    }
    
    .settings-toggle-button {
      position: absolute;
      top: $spacing-2;
      right: $spacing-2;
      color: $text-secondary;
      background-color: rgba(#fff, 0.8);
      border: 1px solid $border-color;
      z-index: 10;
      overflow: hidden;
      transition: all 0.3s ease;
      
      // S<PERSON><PERSON>li ripple efekti
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at center, rgba(#79AC9B, 0) 0%, rgba(#79AC9B, 0.2) 50%, rgba(#79AC9B, 0) 100%);
        transform: scale(0);
        border-radius: 50%;
        animation: permanent-ripple 3s linear infinite;
      }
      
      @keyframes permanent-ripple {
        0% {
          transform: scale(0.3);
          opacity: 0;
        }
        35% {
          transform: scale(1);
          opacity: 0.3;
        }
        100% {
          transform: scale(2);
          opacity: 0;
        }
      }
      
      // Tıklama ripple efekti
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(#79AC9B, 0.5);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%, -50%);
        transform-origin: 50% 50%;
      }
      
      &:active::after {
        animation: ripple 1.5s ease-out;
      }
      
      &:focus::after {
        animation: ripple 1.5s ease-out;
      }
      
      @keyframes ripple {
        0% {
          transform: scale(0, 0);
          opacity: 0.8;
        }
        20% {
          transform: scale(25, 25);
          opacity: 0.6;
        }
        100% {
          opacity: 0;
          transform: scale(50, 50);
        }
      }
      
      &:hover {
        background-color: rgba(#79AC9B, 0.1);
        box-shadow: 0 0 8px rgba(#79AC9B, 0.5);
      }
      
      &.pulse {
        animation: pulse 1.5s infinite;
        box-shadow: 0 0 0 rgba(#79AC9B, 0.6);
        
        &:hover {
          animation: none;
          box-shadow: 0 0 8px rgba(#79AC9B, 0.5);
        }
      }
      
      @keyframes pulse {
        0% {
          box-shadow: 0 0 0 0 rgba(#79AC9B, 0.6);
        }
        70% {
          box-shadow: 0 0 0 15px rgba(#79AC9B, 0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba(#79AC9B, 0);
        }
      }
    }
  }

  &-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 500px;
    max-height: 700px;
    position: relative;
    &.promptLibrary{
      height: 500px !important;
    }

    @media (max-width: $tablet) {
      height: 400px;
    }
  }

  &-welcome {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;

    img {
      margin-bottom: $spacing-2;
    }

    .MuiTypography-h6 {
      font-weight: $font-weight-medium;
    }

    @media (max-width: $tablet) {
      img {
        width: 32px;
        height: 32px;
      }

      .MuiTypography-h6 {
        font-size: $font-size-lg;
      }
    }
  }

  &-input {
    margin-top: auto;
    position: relative;
    padding: $spacing-3;
    background-color: $bg-paper;
    border-top: 1px solid $border-color;

    .prompt-modal__chat-textfield {
      .MuiOutlinedInput-root {
        background-color: $bg-paper;
        border-radius: $border-radius-md;
        font-family: inherit;
        font-size: $font-size-md;
        line-height: 1.5;
        color: $text-primary;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        height: 56px;
        min-height: 56px;
        max-height: 56px;
        padding-right: 40px;
        
        &.Mui-focused .MuiOutlinedInput-notchedOutline {
          border-color: #79AC9B;
          border-width: 1px;
        }
        
        &:hover .MuiOutlinedInput-notchedOutline {
          border-color: #79AC9B;
        }
        
        .MuiOutlinedInput-input {
          padding: $spacing-2;
          height: 40px !important;
          max-height: 40px !important;
          overflow-y: auto !important;
          display: flex;
          align-items: center;
          font-size: 16px !important;
          font-weight: 400;
        }
        
        fieldset.MuiOutlinedInput-notchedOutline {
          border-width: 1px;
        }
        
        @media (max-width: $tablet) {
          font-size: $font-size-sm;
          height: 48px;
          min-height: 48px;
          max-height: 48px;
          
          .MuiOutlinedInput-input {
            padding: $spacing-1 $spacing-2;
            height: 32px !important;
            max-height: 32px !important;
            font-size: 15px !important;
          }
        }
      }
    }

    .prompt-modal__chat-send {
      color: #79AC9B;
      background-color: transparent !important;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      
      &:hover {
        background-color: rgba(#79AC9B, 0.1) !important;
      }
    }
  }

  &-settings {
    width: 200px;
    background-color: $bg-default;
    padding: $spacing-4;
    border-radius: $border-radius-md;
    border: 1px solid $border-color;
    transition: transform 0.5s ease, opacity 0.5s ease;
    transform-origin: top right;
    
    &.closing {
      transform: scale(0.8);
      opacity: 0;
    }
    
    .settings-close-button {
      color: $text-secondary;
      
      &:hover {
        background-color: rgba(#79AC9B, 0.1);
      }
    }

    .MuiTypography-h6 {
      margin-bottom: $spacing-4;
      color: $text-primary;
    }

    @media (max-width: $tablet) {
      width: 100%;
      padding: $spacing-3;
      margin-top: $spacing-3;

      .MuiTypography-h6 {
        font-size: $font-size-lg;
        margin-bottom: $spacing-3;
      }
    }
  }

  &-setting {
    margin-bottom: $spacing-3;

    .MuiTypography-root:not(.prompt-modal__setting-label) {
      color: $text-secondary;
      margin-bottom: $spacing-1;
      font-size: $font-size-sm;
    }

    .MuiIconButton-root {
      &:hover {
        background-color: rgba(#79AC9B, 0.1);
      }
    }

    .MuiSlider-root {
      color: #79AC9B;
    }

    .MuiSelect-select {
      background-color: $bg-paper !important;
    }

    .MuiOutlinedInput-root {
      background-color: $bg-paper;
      
      &:hover .MuiOutlinedInput-notchedOutline {
        border-color: #79AC9B;
      }
      
      &.Mui-focused .MuiOutlinedInput-notchedOutline {
        border-color: #79AC9B;
      }
    }

    @media (max-width: $tablet) {
      margin-bottom: $spacing-2;

      .MuiTypography-root:not(.prompt-modal__setting-label) {
        font-size: $font-size-xs;
      }

      .MuiSlider-root {
        margin-bottom: $spacing-1;
      }
    }
  }

  &-footer {
    padding: $spacing-4;
    border-top: 1px solid $border-color;
    text-align: center;

    @media (max-width: $tablet) {
      padding: $spacing-3;
      font-size: $font-size-xs;
    }
  }

  &-back {
    position: absolute;
    top: $spacing-4;
    left: $spacing-4;
    z-index: 1;

    &-button {
      color: $text-secondary !important;
      text-transform: none !important;
      background-color: #F5F5F5 !important;
      font-weight: $font-weight-medium !important;
      padding: $spacing-1 $spacing-2 !important;
      transition: all 0.3s ease !important;
      overflow: hidden !important;
      white-space: nowrap !important;

      .MuiButton-startIcon {
        margin-right: 0;
        transition: margin-right 0.3s ease;
      }

      .back-text {
        display: inline-block;
        transition: opacity 0.2s ease, transform 0.3s ease;
        margin-left: $spacing-1;
      }

      &.collapsed {
        min-width: 32px;
        .back-text {
          opacity: 0;
          transform: translateX(-20px);
          width: 0;
          margin-left: 0;
        }
      }

      &:hover {
        background-color: #f1f1f1 !important;

        &.collapsed {
          padding-right: $spacing-2 !important;

          .back-text {
            opacity: 1;
            transform: translateX(0);
            width: max-content;
            margin-left: $spacing-1;
          }

          .MuiButton-startIcon {
            margin-right: $spacing-1;
          }
        }
      }
    }

    @media (max-width: $tablet) {
      position: static;
      padding: $spacing-3 $spacing-3 0 $spacing-3;

      &-button {
        font-size: $font-size-xs;
      }
    }
  }

  &-messages {
    display: flex;
    flex-direction: column;
    gap: $spacing-2;
    flex: 1;
    overflow-y: auto;
    padding: $spacing-3;
    padding-right: $spacing-4;
    height: calc(100% - 120px);
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 3px;
    }

    // Code block için stiller
    .code-block {
      position: relative;
      margin-bottom: $spacing-3;
      
      .copy-button {
        position: absolute;
        top: $spacing-1;
        right: $spacing-1;
        z-index: 1;
        background-color: rgba(#fff, 0.1);
        color: #fff;
        
        &:hover {
          background-color: rgba(#fff, 0.2);
        }
      }
      
      pre {
        margin: 0 !important;
        border-radius: $border-radius-md;
        overflow: hidden;
      }
    }
  }

  &-message {
    display: flex;
    align-items: flex-start;
    padding: $spacing-3;
    border-radius: $border-radius-md;
    max-width: 85%;
    word-break: break-word;

    &.user {
      background-color: rgba(#79AC9B, 0.1);
      margin-left: auto;
    }

    &.assistant {
      background-color: $bg-default;
      margin-right: auto;
      border: 1px solid $border-color;
      display: flex;
      align-items: flex-start;
      gap: $spacing-2;
    }
  }

  .assistant-avatar {
    margin-top: 2px;
    border-radius: 4px;
    flex-shrink: 0;
  }
}

// Material UI ripple efekti için özel stiller
.settings-ripple-visible {
  opacity: 0.8 !important;
  animation-duration: 850ms !important;
}

.settings-ripple-child {
  background-color: rgba(#79AC9B, 0.7) !important;
}

.settings-ripple-child-leaving {
  opacity: 0 !important;
  animation-duration: 850ms !important;
}

.prompt-modal__setting-label {
  font-size: $font-size-xs !important;
  font-weight: $font-weight-medium !important;

  @media (max-width: $tablet) {
    font-size: $font-size-xs !important;
  }
} 