import { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  IconButton,
  Select,
  MenuItem,
  Slider,
  Tooltip,
  Button,
  CircularProgress,
  TextField,
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import OpenAIIcon from '../../assets/images/logos/openai-logo.svg';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SettingsIcon from '@mui/icons-material/Settings';
import CloseIcon from '@mui/icons-material/Close';
import { useTranslation } from 'react-i18next';
import { useUseAzureOpenAIConnectorsMutation } from '../../redux/services/connectors-api';
import { useUpdateAppTrackingMutation } from '../../redux/services/app-tracking-api';
import { useSelector } from 'react-redux';
import HtmlRenderer from '@components/HtmlRenderer';
import { toast } from 'react-toastify';
import './ChatGPTPlayground.scss';

const settingDescriptions = {
  temperature:
    'Controls randomness: Lowering results in more focused and deterministic responses, while increasing leads to more diverse and creative outputs. (0 = focused, 2 = creative)',
  topP: 'Controls diversity via nucleus sampling: Lower values keep responses more focused on highly probable tokens, while higher values allow for more diversity. (0 = focused, 1 = diverse)',
  frequencyPenalty:
    'Reduces repetition by lowering the likelihood of using tokens that have already appeared in the text. Higher values mean less repetition. (0 = no penalty, 2 = high penalty)',
  presencePenalty:
    'Encourages using new topics by penalizing tokens that appear in the whole text. Higher values encourage covering new topics. (0 = no penalty, 2 = high penalty)',
};

const ChatGPTPlayground = ({
  initialPrompt,
  initialTemperature = 1,
  initialTopP = 1,
  initialFrequencyPenalty = 0,
  initialPresencePenalty = 0,
  onBack,
  disableBack = false,
  onGenerate,
  onNotify,
  showNotification = true,
  notificationMessage = '',
  defaultSettingsOpen = false,
  library = false,
}) => {
  const { t } = useTranslation();
  const user = useSelector((state) => state.auth.user);
  const [message, setMessage] = useState(initialPrompt);
  const [selectedModel, setSelectedModel] = useState('Gpt-4o-2024-08-06');
  const [messages, setMessages] = useState([]);
  const [isTextareaFocused, setIsTextareaFocused] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showSettings, setShowSettings] = useState(defaultSettingsOpen);
  const [settingsClosing, setSettingsClosing] = useState(false);
  const [showSettingsHint, setShowSettingsHint] = useState(false);

  // Internal state management with initial values from props
  const [temperature, setTemperature] = useState(initialTemperature);
  const [topP, setTopP] = useState(initialTopP);
  const [frequencyPenalty, setFrequencyPenalty] = useState(initialFrequencyPenalty);
  const [presencePenalty, setPresencePenalty] = useState(initialPresencePenalty);

  // Update internal state when initial values change
  useEffect(() => {
    setTemperature(initialTemperature);
    setTopP(initialTopP);
    setFrequencyPenalty(initialFrequencyPenalty);
    setPresencePenalty(initialPresencePenalty);
  }, [initialTemperature, initialTopP, initialFrequencyPenalty, initialPresencePenalty]);

  const messagesRef = useRef(null);
  const abortControllerRef = useRef(null);
  const [generateText] = useUseAzureOpenAIConnectorsMutation();
  const [updateAppTracking] = useUpdateAppTrackingMutation();

  // TextUsecase'ten alınan yazma efekti için state'ler
  const [contentQueue, setContentQueue] = useState([]);
  const [typingSpeed] = useState(10);
  const [isTyping, setIsTyping] = useState(false);
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  const lastUserInteractionRef = useRef(0);
  const isNearBottomRef = useRef(true);

  const scrollToBottom = () => {
    if (messagesRef.current && !userHasScrolled) {
      const scrollHeight = messagesRef.current.scrollHeight;
      messagesRef.current.scrollTo({
        top: scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  // Kullanıcının scroll pozisyonunu kontrol eden fonksiyon
  const checkIfUserNearBottom = () => {
    if (messagesRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesRef.current;
      const isNearBottom = scrollHeight - (scrollTop + clientHeight) < 100;
      isNearBottomRef.current = isNearBottom;
      return isNearBottom;
    }
    return true;
  };

  // Kullanıcı scroll yaptıktan sonra belirli bir süre geçince otomatik scroll'u tekrar aktif et
  const resetUserScrollAfterDelay = () => {
    const now = Date.now();
    // Kullanıcının son etkileşiminden 8 saniye geçtiyse ve ekranın altına yakınsa
    if (now - lastUserInteractionRef.current > 8000 && checkIfUserNearBottom()) {
      setUserHasScrolled(false);
    }
  };

  // Yeni mesaj eklendiğinde scroll yapma kontrolü
  useEffect(() => {
    if (!userHasScrolled && messages.length > 0) {
      setTimeout(scrollToBottom, 100);
    }
  }, [messages.length, userHasScrolled]);

  useEffect(() => {
    if (isTextareaFocused && messages.length > 0 && !userHasScrolled) {
      scrollToBottom();
    }
  }, [isTextareaFocused, messages.length, userHasScrolled]);

  // Scroll olayını dinleyerek kullanıcının manuel scroll yapıp yapmadığını tespit et
  useEffect(() => {
    if (messagesRef.current) {
      const handleScroll = () => {
        const isNearBottom = checkIfUserNearBottom();

        // Scroll olayı kullanıcıdan geliyorsa
        if (!isNearBottom) {
          if (!userHasScrolled) {
            setUserHasScrolled(true);
          }
          lastUserInteractionRef.current = Date.now();
        } else if (userHasScrolled) {
          // Kullanıcı manuel olarak en alta geldiyse
          setUserHasScrolled(false);
        }
      };

      messagesRef.current.addEventListener('scroll', handleScroll);

      // Periyodik olarak kullanıcının scroll durumunu kontrol et (daha az sıklıkta)
      const intervalId = setInterval(resetUserScrollAfterDelay, 2000);

      return () => {
        if (messagesRef.current) {
          messagesRef.current.removeEventListener('scroll', handleScroll);
        }
        clearInterval(intervalId);
      };
    }
  }, [userHasScrolled]);

  // Eksik yanıtları tamamlamak için ekstra bir useEffect
  useEffect(() => {
    // contentQueue boşaldığında ve isTyping false olduğunda
    // yani yazma efekti tamamlandığında ve veri akışı bittiğinde
    // son bir kontrol yapalım
    if (contentQueue.length === 0 && !isTyping && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];

      // Eğer son mesaj assistant'tan geliyorsa ve cümle yarım kalmış olabilir
      if (lastMessage?.role === 'assistant') {
        const content = lastMessage.content;

        // Son mesajda biten cümlelerin ve paragrafların tipik işaretleri var mı diye kontrol et
        const hasProperEnding =
          content.endsWith('.') ||
          content.endsWith('!') ||
          content.endsWith('?') ||
          content.endsWith(':') ||
          content.endsWith(';') ||
          content.endsWith('\n\n');

        // Son paragraf/cümlenin son kelimesi tam mı kontrol et
        const endsWithIncompleteWord = /[a-zA-Z]$/.test(content) && !content.endsWith(' ');

        // Word kesme kontrolü - son kelimeler genellikle anlamlı olmalı
        const commonEndingWords = [
          'the',
          'and',
          'to',
          'of',
          'in',
          'for',
          'with',
          'on',
          'at',
          'by',
          'as',
        ];
        const lastWord = content.split(' ').pop();
        const endsWithCommonWord = commonEndingWords.includes(lastWord.toLowerCase());

        // Eğer düzgün bir şekilde bitmiyorsa ve içeriğin uzunluğu 30 karakterden fazlaysa
        // muhtemelen kesik bir yanıt vardır
        if (
          (!hasProperEnding || endsWithCommonWord || endsWithIncompleteWord) &&
          content.length > 30
        ) {
          // Bu durumda API'den yeni bir yanıt istemek yerine, mevcut yanıtın
          // tamamlanmış olduğunu bildiren bir not ekleyelim
          const updatedContent = content + ' [...]';

          setMessages((prevMessages) => [
            ...prevMessages.slice(0, -1),
            { ...lastMessage, content: updatedContent },
          ]);
        }
      }
    }
  }, [contentQueue, isTyping, messages]);

  // Toplam karakter sayısını hesaplayan yardımcı fonksiyon
  const getTotalQueueCharacterCount = (queue) => {
    return queue.reduce((sum, item) => sum + item.length, 0);
  };

  // Yazma efekti için eklenen useEffect - TextUsecase ile uyumlu hale getirildi
  useEffect(() => {
    if (contentQueue.length === 0) return;

    // Yazma efekti için optimizasyon: Büyük veri parçalarını daha küçük parçalara böl
    const processQueue = () => {
      const totalQueueChars = getTotalQueueCharacterCount(contentQueue);

      // Eğer kuyrukta çok fazla karakter varsa veya yazma işlemi bittiyse
      // daha hızlı işleme için büyük parçalar halinde ekle
      const shouldProcessFaster = totalQueueChars > 100 || isTyping === false;
      const chunkSize = shouldProcessFaster ? 50 : 15;

      if (contentQueue.length > 0) {
        const nextContent = contentQueue[0];

        // Eğer içerik çok uzunsa, parçalara böl
        if (nextContent.length > chunkSize && isTyping) {
          const firstChunk = nextContent.substring(0, chunkSize);
          const remainingChunk = nextContent.substring(chunkSize);

          // İlk parçayı ekle
          setMessages((prevMessages) => {
            const lastMessage = prevMessages[prevMessages.length - 1];
            if (lastMessage?.role === 'assistant') {
              return [
                ...prevMessages.slice(0, -1),
                { ...lastMessage, content: lastMessage.content + firstChunk },
              ];
            } else {
              return [...prevMessages, { role: 'assistant', content: firstChunk }];
            }
          });

          // Kalan parçayı kuyruğun başına ekle
          setContentQueue((prev) => [remainingChunk, ...prev.slice(1)]);
        } else {
          // Normal işleme
          setMessages((prevMessages) => {
            const lastMessage = prevMessages[prevMessages.length - 1];
            if (lastMessage?.role === 'assistant') {
              return [
                ...prevMessages.slice(0, -1),
                { ...lastMessage, content: lastMessage.content + nextContent },
              ];
            } else {
              return [...prevMessages, { role: 'assistant', content: nextContent }];
            }
          });

          // İşlenen içeriği kuyruktan çıkar
          setContentQueue((prev) => prev.slice(1));
        }

        // Kritik değişiklik: Kullanıcı scroll yapmadıysa ve en alttaysa
        if (!userHasScrolled && isNearBottomRef.current) {
          // Önce RAF ile browser'ın DOM güncellenmesini bekle, sonra scroll yap
          requestAnimationFrame(() => {
            if (messagesRef.current) {
              messagesRef.current.scrollTop = messagesRef.current.scrollHeight;
            }
          });
        }
      }
    };

    // Yazma hızını dinamik olarak ayarla
    const typingDelay = isTyping
      ? getTotalQueueCharacterCount(contentQueue) > 50
        ? 10
        : typingSpeed
      : 0;

    const timer = setTimeout(processQueue, typingDelay);
    return () => clearTimeout(timer);
  }, [contentQueue, typingSpeed, isTyping, userHasScrolled]);

  const handleStreamEnd = () => {
    // Stream bittiğinde, tam yanıtın düzgün bir şekilde gösterildiğinden emin olalım
    setIsTyping(false);

    // Kuyrukta kalan tüm içeriği hızlıca işle
    if (contentQueue.length > 0) {
      const remainingContent = contentQueue.join('');
      setContentQueue([]);

      setMessages((prevMessages) => {
        const lastMessage = prevMessages[prevMessages.length - 1];
        if (lastMessage?.role === 'assistant') {
          return [
            ...prevMessages.slice(0, -1),
            { ...lastMessage, content: lastMessage.content + remainingContent },
          ];
        } else {
          return [...prevMessages, { role: 'assistant', content: remainingContent }];
        }
      });
    }

    // Scroll pozisyonunu güncelle
    if (!userHasScrolled) {
      requestAnimationFrame(() => {
        if (messagesRef.current) {
          messagesRef.current.scrollTop = messagesRef.current.scrollHeight;
        }
      });
    }
  };

  const handleTextareaFocus = () => {
    setIsTextareaFocused(true);
  };

  const handleSend = async () => {
    if (!message.trim() || isLoading) return;

    try {
      setIsLoading(true);
      // Kullanıcı mesaj gönderdiğinde, otomatik scroll'u aktif et
      setUserHasScrolled(false);

      // Kullanıcı mesajını ekle
      const newMessages = [
        ...messages,
        {
          role: 'user',
          content: message,
        },
      ];
      setMessages(newMessages);

      // Yeni bir AbortController oluştur
      abortControllerRef.current = new AbortController();

      let isFirstResponse = true;
      setContentQueue([]); // Yazma efekti kuyruğunu temizle
      setIsTyping(false);

      const result = await generateText({
        prompt: message,
        temperature: temperature,
        frequency: frequencyPenalty,
        topP: topP,
        presence: presencePenalty,
        abortController: abortControllerRef.current,
        html: true,
        onUpdate: async (streamContent) => {
          try {
            if (!streamContent || streamContent.trim() === '') return;

            if (typeof streamContent === 'string' && streamContent.startsWith('data: ')) {
              const jsonData = streamContent.slice(6).trim();

              if (jsonData === '[DONE]') {
                setIsTyping(false);
                // Stream sonunda kalan içeriği işle
                handleStreamEnd();
                return;
              }

              try {
                const parsed = JSON.parse(jsonData);
                if (parsed.content) {
                  const content = parsed.content;

                  // İç içe JSON kontrolü
                  if (content.includes('{"content":')) {
                    return;
                  }

                  // Büyük veri parçalarını daha küçük parçalara böl
                  // Bu, daha doğal bir yazma efekti sağlar
                  const chunkSize = 15;
                  if (content.length > chunkSize) {
                    // Uzun içeriği parçalara böl
                    for (let i = 0; i < content.length; i += chunkSize) {
                      const chunk = content.substring(i, i + chunkSize);
                      setContentQueue((prev) => [...prev, chunk]);
                    }
                  } else {
                    // Kısa içeriği doğrudan ekle
                    setContentQueue((prev) => [...prev, content]);
                  }

                  setIsTyping(true);

                  // İlk yanıt geldiğinde app tracking güncellemesi yap
                  if (isFirstResponse) {
                    isFirstResponse = false;
                    const currentDate = new Date();
                    await updateAppTracking({
                      userId: user?._id,
                      appId: 'playgrounds/chatgpt',
                      appType: 'playground',
                      title: 'ChatGPT Playground',
                      translations: {
                        english: {
                          title: 'ChatGPT Playground',
                        },
                      },
                      year: currentDate.getFullYear().toString(),
                      month: (currentDate.getMonth() + 1).toString(),
                      day: currentDate.getDate().toString(),
                    });

                    // İlerleme ve tamamlama için onGenerate çağrısı yapılıyor
                    if (onGenerate) {
                      // block yerine mesaj içeriğinden bir benzersiz tanımlayıcı oluşturalım
                      const messageId = `msg_${Date.now()}`;
                      console.log('🚀 Calling onGenerate for message:', messageId);
                      onGenerate();
                    }

                    // Bildirim göster - İlk yanıt alındığında
                    if (showNotification) {
                      // Custom notification mesajı varsa onu kullan, yoksa varsayılan mesajı kullan
                      const message =
                        notificationMessage ||
                        t(
                          'playgrounds.chatGPT.completed',
                          'AI text creation completed successfully!'
                        );
                      console.log('🔔 Showing notification:', message);

                      toast.success(message, {
                        position: 'bottom-right',
                        autoClose: 5000,
                        hideProgressBar: false,
                        closeOnClick: true,
                        pauseOnHover: true,
                        draggable: true,
                      });
                    }

                    // Eğer bildirim için özel bir callback varsa çağır
                    if (onNotify) {
                      onNotify();
                    }
                  }
                }
              } catch (parseError) {
                console.debug('Stream data parsing error:', parseError);
                if (jsonData && jsonData.trim()) {
                  // İç içe JSON kontrolü
                  if (!jsonData.includes('{"content":')) {
                    // Büyük veri parçalarını daha küçük parçalara böl
                    const chunkSize = 15;
                    if (jsonData.length > chunkSize) {
                      // Uzun içeriği parçalara böl
                      for (let i = 0; i < jsonData.length; i += chunkSize) {
                        const chunk = jsonData.substring(i, i + chunkSize);
                        setContentQueue((prev) => [...prev, chunk]);
                      }
                    } else {
                      // Kısa içeriği doğrudan ekle
                      setContentQueue((prev) => [...prev, jsonData.trim()]);
                    }

                    setIsTyping(true);
                  }
                }
              }
            }
          } catch (error) {
            console.error('Stream data processing error:', error);
            setIsTyping(false);
            setMessages((prevMessages) => [
              ...prevMessages,
              {
                role: 'assistant',
                content: 'Error: ' + (error.message || 'Error processing response'),
              },
            ]);
          }
        },
      });

      if (result.error) {
        if (result.error.name === 'AbortError' || result.error.message === 'Response cancelled') {
          return;
        }

        throw new Error(result.error.data || 'API request failed');
      }

      setMessage(''); // Mesaj alanını temizle
      setIsTextareaFocused(false);
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          role: 'assistant',
          content: 'Error: ' + (error.message || 'Failed to send message. Please try again.'),
        },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const renderSettingLabel = (label, description) => (
    <Box display="flex" alignItems="center" gap={0.5}>
      <Typography className="prompt-modal__setting-label">{label}</Typography>
      <Tooltip title={description} arrow placement="top">
        <IconButton size="small" sx={{ padding: '2px' }}>
          <InfoOutlinedIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
        </IconButton>
      </Tooltip>
    </Box>
  );

  // İlk yüklemede settings ipucunu göster
  useEffect(() => {
    // Kısa bir süre sonra ipucu göster
    const timer = setTimeout(() => {
      setShowSettingsHint(true);

      // İpucunu 3 saniye sonra kapat
      setTimeout(() => {
        setShowSettingsHint(false);
      }, 3000);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <Box className="prompt-modal__chat">
      {!disableBack && (
        <Box className="prompt-modal__chat-back">
          <Button
            className={`prompt-modal__chat-back-button`}
            startIcon={<ArrowBackIcon />}
            onClick={onBack}
          >
            <span className="back-text">{t('common.back')}</span>
          </Button>
        </Box>
      )}

      <Box className="prompt-modal__chat-container">
        <Box className={`prompt-modal__chat-content ${library ? 'promptLibrary' : ''}`}>
          {messages.length === 0 ? (
            <Box className="prompt-modal__chat-welcome">
              <img src={OpenAIIcon} alt="OpenAI" width={48} height={48} />
              <Typography variant="h6">{t('playgrounds.chatGPT.welcome')}</Typography>
              <Typography variant="body2" color="textSecondary">
                {t('playgrounds.chatGPT.welcomeDescription')}
              </Typography>
            </Box>
          ) : (
            <Box className="prompt-modal__chat-messages" ref={messagesRef}>
              {messages.map((msg, index) => (
                <Box
                  key={index}
                  className={`prompt-modal__chat-message ${msg.role === 'user' ? 'user' : 'assistant'}`}
                >
                  {msg.role === 'assistant' && (
                    <img
                      src={OpenAIIcon}
                      alt="OpenAI"
                      className="assistant-avatar"
                      width={24}
                      height={24}
                    />
                  )}
                  <HtmlRenderer
                    content={msg.content}
                    className="markdown-body"
                    sanitize={true}
                    formatCodeBlocks={true}
                  />
                </Box>
              ))}
            </Box>
          )}

          <Box className="prompt-modal__chat-input">
            <TextField
              fullWidth
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder={t('playgrounds.common.typeMessage')}
              variant="outlined"
              size="small"
              rows={1}
              onFocus={handleTextareaFocus}
              onBlur={(e) => {
                if (!e.target.value.trim() && messages.length > 0) {
                  setIsTextareaFocused(false);
                }
              }}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSend();
                }
              }}
              disabled={isLoading}
              InputProps={{
                className: 'prompt-modal__chat-textfield',
                endAdornment: (
                  <IconButton
                    className="prompt-modal__chat-send"
                    onClick={handleSend}
                    title={t('playgrounds.common.sendMessage')}
                    disabled={isLoading}
                    size="small"
                  >
                    {isLoading ? <CircularProgress size={20} /> : <SendIcon />}
                  </IconButton>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  height: '56px',
                },
                '& .MuiInputBase-input': {
                  height: '40px',
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '16px !important',
                  fontWeight: '400',
                },
              }}
            />
          </Box>
        </Box>

        {showSettings ? (
          <Box className={`prompt-modal__chat-settings ${settingsClosing ? 'closing' : ''}`}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">{t('playgrounds.chatGPT.settings')}</Typography>
              <IconButton
                onClick={() => {
                  setSettingsClosing(true);
                  setTimeout(() => {
                    setShowSettings(false);
                    setSettingsClosing(false);
                  }, 500);
                }}
                size="small"
                className="settings-close-button"
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>

            <Box className="prompt-modal__chat-setting">
              <Typography className="prompt-modal__setting-label">
                {t('playgrounds.chatGPT.model')}
              </Typography>
              <Select
                value={selectedModel}
                onChange={(e) => setSelectedModel(e.target.value)}
                fullWidth
                size="small"
              >
                <MenuItem value="Gpt-4o-2024-08-06">GPT-4o</MenuItem>
              </Select>
            </Box>

            <Box className="prompt-modal__chat-setting">
              <Box display="flex" justifyContent="space-between" alignItems="center">
                {renderSettingLabel('Temperature', settingDescriptions.temperature)}
                <Typography>{temperature.toFixed(2)}</Typography>
              </Box>
              <Slider
                value={temperature}
                onChange={(event, newValue) => setTemperature(newValue)}
                min={0}
                max={2}
                step={0.01}
              />
            </Box>

            <Box className="prompt-modal__chat-setting">
              <Box display="flex" justifyContent="space-between" alignItems="center">
                {renderSettingLabel('Top P', settingDescriptions.topP)}
                <Typography>{topP.toFixed(2)}</Typography>
              </Box>
              <Slider
                value={topP}
                onChange={(event, newValue) => setTopP(newValue)}
                min={0}
                max={1}
                step={0.01}
              />
            </Box>

            <Box className="prompt-modal__chat-setting">
              <Box display="flex" justifyContent="space-between" alignItems="center">
                {renderSettingLabel('Frequency penalty', settingDescriptions.frequencyPenalty)}
                <Typography>{frequencyPenalty.toFixed(2)}</Typography>
              </Box>
              <Slider
                value={frequencyPenalty}
                onChange={(event, newValue) => setFrequencyPenalty(newValue)}
                min={0}
                max={2}
                step={0.01}
              />
            </Box>

            <Box className="prompt-modal__chat-setting">
              <Box display="flex" justifyContent="space-between" alignItems="center">
                {renderSettingLabel('Presence penalty', settingDescriptions.presencePenalty)}
                <Typography>{presencePenalty.toFixed(2)}</Typography>
              </Box>
              <Slider
                value={presencePenalty}
                onChange={(event, newValue) => setPresencePenalty(newValue)}
                min={0}
                max={2}
                step={0.01}
              />
            </Box>
          </Box>
        ) : (
          <Tooltip
            title={t(
              'playgrounds.chatGPT.settingsTooltip',
              'Buradan model, sıcaklık ve diğer AI parametrelerini ayarlayabilirsiniz'
            )}
            arrow
            placement="left"
          >
            <IconButton
              className={`settings-toggle-button ${showSettingsHint ? 'pulse' : ''}`}
              onClick={() => setShowSettings(true)}
              disableRipple={false}
              centerRipple
              TouchRippleProps={{
                classes: {
                  rippleVisible: 'settings-ripple-visible',
                  child: 'settings-ripple-child',
                  childLeaving: 'settings-ripple-child-leaving',
                },
              }}
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      <Box className="prompt-modal__chat-footer">
        <Typography variant="caption" color="textSecondary">
          {t('common.warnings.dataPrivacy')}
        </Typography>
      </Box>
    </Box>
  );
};

ChatGPTPlayground.propTypes = {
  initialPrompt: PropTypes.string,
  initialTemperature: PropTypes.number,
  initialTopP: PropTypes.number,
  initialFrequencyPenalty: PropTypes.number,
  initialPresencePenalty: PropTypes.number,
  onBack: PropTypes.func,
  disableBack: PropTypes.bool,
  onGenerate: PropTypes.func,
  onNotify: PropTypes.func,
  showNotification: PropTypes.bool,
  notificationMessage: PropTypes.string,
  defaultSettingsOpen: PropTypes.bool,
  library: PropTypes.bool,
};

export default ChatGPTPlayground;
