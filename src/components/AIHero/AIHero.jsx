import { Box, Grid, Typography } from '@mui/material';
import Button from '../Button/Button';
import ReactPlayer from 'react-player';
import { useState } from 'react';
import PropTypes from 'prop-types';
import parse from 'html-react-parser';
import './AIHero.scss';

const AIHero = ({ 
  title, 
  description, 
  buttonText, 
  onButtonClick, 
  videoUrl, 
  videoThumbnail,
  imageUrl,
  position = 'right',
  smallTitle = false
}) => {
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlayClick = () => {
    setIsPlaying(true);
  };

  return (
    <Grid 
      container 
      spacing={6} 
      className="hero-section"
      direction={position === 'left' ? 'row-reverse' : 'row'}
    >
      <Grid item xs={12} md={6}>
        <Box className={`hero-content ${smallTitle ? 'small' : ''}`}>
          <Typography variant="h1" className={`hero-title`}>
            {title}
          </Typography>
          <Box className="hero-description">
            {typeof description === 'string' ? (
              <>
                {parse(description)}
              </>
            ) : (
              description
            )}
          </Box>
          {buttonText && (
            <Button
              variant="contained"
              color="primary"
              size="medium"
              onClick={onButtonClick ? onButtonClick : undefined}
              className="try-button"
              disabled={!onButtonClick}
            >
              {buttonText}
            </Button>
          )}
        </Box>
      </Grid>
      <Grid item xs={12} md={6}>
        <Box className="video-container">
          {imageUrl ? (
            <Box className="image-wrapper">
              <img 
                src={imageUrl} 
                alt={title}
                className="hero-image"
              />
            </Box>
          ) : (
            <Box className="video-wrapper">
              <Box className="aspect-ratio-box">
                {!isPlaying && (
                  <>
                    <img 
                      src={videoThumbnail} 
                      alt="Video Thumbnail" 
                      className="video-thumbnail"
                    />
                    <Box className="play-button" onClick={handlePlayClick}>
                      <svg width="48" height="48" viewBox="0 0 24 24">
                        <path fill="#fff" d="M8 5v14l11-7z"/>
                      </svg>
                    </Box>
                  </>
                )}
                {isPlaying && (
                  <ReactPlayer
                    url={videoUrl}
                    width="100%"
                    height="100%"
                    playing={true}
                    controls={true}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0
                    }}
                    config={{
                      file: {
                        forceHLS: true,
                        hlsOptions: {
                          autoStartLoad: true,
                        }
                      }
                    }}
                  />
                )}
              </Box>
            </Box>
          )}
        </Box>
      </Grid>
    </Grid>
  );
};

AIHero.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.node
  ]).isRequired,
  buttonText: PropTypes.string,
  onButtonClick: PropTypes.func,
  videoUrl: PropTypes.string,
  videoThumbnail: PropTypes.string,
  imageUrl: PropTypes.string,
  position: PropTypes.oneOf(['left', 'right']),
  smallTitle: PropTypes.bool
};

export default AIHero; 