@use '../../styles/abstracts/variables' as *;

.hero-section {
    padding: $spacing-6 0 0 0;
  }
  
  .hero-content {
      display:flex;
      flex-direction: column;
      justify-content: center;
      height: 100%;
    .hero-title {
      font-size: calc($font-size-md * 2);
      font-weight: $font-weight-bold;
      color: $text-primary !important;
      margin-bottom: $spacing-3;
    }
  
    .hero-description {
      font-size: $font-size-md;
      line-height: 1.6;
      color: $text-secondary;
      p{
        color: $text-secondary !important;
      }
      ul{
        padding:0;
        margin:$spacing-3 0 $spacing-3 $spacing-3;
        color:$text-secondary;
        li{
            margin-bottom:$spacing-1;
        }
      }
    }
    .try-button {
      width: max-content;
      margin-top: 24px;
      padding-left:$spacing-4;
      padding-right:$spacing-4;
    }

    &.small {
      .hero-title{
        font-size: $font-size-xxl;
        margin-bottom: $spacing-1;
      }
      .hero-description{
        p{
          font-size: calc($font-size-md - 1px) !important;
        }
      }
    }
  }
  
  .video-container {
    display:flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    .image-wrapper {
      width: 100%;
      border-radius: $border-radius-lg;
      overflow: hidden;
      box-shadow: $shadow-sm;
  
      .hero-image {
        width: 100%;
        height: auto;
        display: block;
        object-fit: cover;
      }
    }
  
    .video-wrapper {
      position: relative;
      width: 100%;
      border-radius: $border-radius-lg;
      overflow: hidden;
      box-shadow: $shadow-sm;
  
      .aspect-ratio-box {
        position: relative;
        width: 100%;
        padding-top: 56.25%; // 16:9 aspect ratio (9/16 = 0.5625)
      }
      
      .video-player, .video-thumbnail {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
  
      .video-player {
        display: none;
        
        &.playing {
          display: block;
        }
      }
  
      .video-thumbnail {
        width: 100%;
        height: auto;
        display: block;
      }
  
      .play-button {
        position: absolute;
        top: 32px;
        right: 32px;
        box-shadow: $shadow-sm;
        width: 64px;
        height: 64px;
        background: $primary-color;
        border-radius: 50%;
        display: flex;
        box-shadow: $shadow-sm;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
  
        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }
  
  @media (max-width: $tablet) {
    .hero-section {
      padding: $spacing-4 0;
    }
    .hero-content {
      padding: $spacing-3 0;
  
      .hero-title {
        font-size: 24px;
      }
  
      .hero-description {
        font-size: $font-size-sm;
      }
    }
  
    .video-container {
      padding: $spacing-3 0;
    }
  }
  
  
  
  @media (max-width: $tablet) {
    .features-section {
      padding: $spacing-4 0;
    }
  }
 
  