import { useState, useEffect, useRef, useReducer, useImperativeHandle, forwardRef } from 'react';
import PropTypes from 'prop-types';
import { AdvancedCreatorTools } from '../../mockData/Forms';
import {
  Box,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  FormControlLabel,
  FormLabel,
  Radio,
  RadioGroup,
  Checkbox,
  FormGroup,
  Button,
  Paper,
  Divider,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import './FormRender.scss';

const FormRender = forwardRef(
  (
    {
      formTitle,
      formData,
      hideTitle = false,
      hideDescription = false,
      onChange,
      disabled = false,
      errors = {},
      values = {},
    },
    ref
  ) => {
    const { t } = useTranslation();
    const [formState, setFormState] = useState({ ...values });
    const [highlightedFields, setHighlightedFields] = useState({});
    const previousVisibility = useRef({});
    const [, forceUpdate] = useReducer((x) => x + 1, 0);

    // Form değişkenini useEffect'lerden önce tanımla
    const form =
      formData || AdvancedCreatorTools[0]?.data?.forms?.find((form) => form.title === formTitle);

    useEffect(() => {
      // Only update if values are actually different to avoid infinite loops
      if (JSON.stringify(formState) !== JSON.stringify(values)) {
        setFormState((prevState) => {
          // Eğer values boş bir obje ise, tüm form state'ini sıfırla
          if (Object.keys(values).length === 0) {
            return {};
          }
          // Aksi halde değerleri güncelle
          return { ...prevState, ...values };
        });

        // Değerlerin değiştiği zaman bir kez tetiklenecek, sonsuz döngüyü önler
        const timer = setTimeout(() => {
          // Sonsuz döngüyü önlemek için referans eşitliği kontrolü yapmak yerine
          // sadece başlangıçta bir kere çalışsın
        }, 50);

        // Cleanup function to clear the timeout
        return () => clearTimeout(timer);
      }
    }, [values]);

    // Form ilk yüklendiğinde required alanların kontrolünü yap
    useEffect(() => {
      if (form && form.fields && onChange) {
        // Form alanlarını kontrol et
        // İlk yüklemede kontrol etmiyoruz, bu nedenle bu kısmı yorum satırına alıyoruz
        /*
        const errors = {};
        let hasErrors = false;

        // Form fields kontrolü
        form.fields.forEach((field) => {
          if (field.required) {
            const fieldValue = values[field.name] || formState[field.name];
            if (checkIfEmpty(fieldValue)) {
              errors[field.name] = 'This field is required';
              hasErrors = true;
            }
          }
        });

        // Form topics kontrolü
        if (form.topics) {
          form.topics.forEach((topic) => {
            if (topic.fields) {
              topic.fields.forEach((field) => {
                if (field.required) {
                  const fieldValue = values[field.name] || formState[field.name];
                  if (checkIfEmpty(fieldValue)) {
                    errors[field.name] = 'This field is required';
                    hasErrors = true;
                  }
                }
              });
            }
          });
        }

        // Eğer hata varsa, onChange fonksiyonunu çağır
        if (hasErrors) {
          onChange({
            __errors: errors,
          });
        }
        */
      }
    }, [form, onChange, values, formState]);

    // Track field visibility changes for animations
    useEffect(() => {
      if (form && form.fields) {
        const newVisibility = {};
        form.fields.forEach((field) => {
          newVisibility[field.name] = checkCondition(field, formState);
        });

        // Find fields that have changed visibility status
        const fieldsToHighlight = {};
        Object.keys(newVisibility).forEach((fieldName) => {
          if (
            previousVisibility.current[fieldName] !== undefined &&
            previousVisibility.current[fieldName] !== newVisibility[fieldName] &&
            newVisibility[fieldName] === true
          ) {
            fieldsToHighlight[fieldName] = true;
          }
        });

        // Set highlighted fields
        if (Object.keys(fieldsToHighlight).length > 0) {
          setHighlightedFields(fieldsToHighlight);

          // Clear highlights after animation completes
          setTimeout(() => {
            setHighlightedFields({});
          }, 1500);
        }

        previousVisibility.current = newVisibility;
      }
    }, [formState, form]);

    const handleChange = (event, fieldName) => {
      const { name, value, type, checked } = event.target;
      const actualName = fieldName || name;

      // Event olmadan doğrudan çağrılırsa (feedback gibi durumlarda)
      const actualValue = event.target ? (type === 'checkbox' ? checked : value) : event;

      // Değeri önce yerel state'de güncelle
      const newState = {
        ...formState,
        [actualName]: actualValue,
      };
      setFormState(newState);

      // Sonra onChange callback'i çağır
      if (onChange) {
        // Değişikliği üst bileşene bildir
        onChange({
          [actualName]: actualValue,
          // Send complete form state to ensure all conditions are properly evaluated
          __formState: newState,
          // Kullanıcı etkileşimini belirtmek için flag ekle
          __userInteracted: true,
        });

        // Re-evaluate conditions: doğrudan state güncelleyerek sonsuz döngüyü önleyelim
        // Form değerlerinin değiştiğini internal olarak işaretlemek için yeni bir state kullanalım

        // Koşulların yeniden değerlendirilmesini tetiklemek için form durumunu doğrudan güncelleme
        // Forceupdate kullanmak daha güvenli olabilir
        forceUpdate();
      }

      // Form alanlarının görünürlüğü değişebileceğinden tüm hataları tekrar kontrol edelim
      // Özellikle şimdi görünür olmayan alan için hataları temizleyelim
      if (form && form.fields) {
        const updatedErrors = { ...errors };
        let errorsUpdated = false;

        form.fields.forEach((field) => {
          const isVisible = checkCondition(field, newState);
          const isFieldDisabled = !isVisible || disabled;

          // Eğer alan artık görünür değilse veya disabled ise ve bir hata varsa, hatayı temizle
          if ((isFieldDisabled || !isVisible) && updatedErrors[field.name]) {
            delete updatedErrors[field.name];
            errorsUpdated = true;
          }
        });

        // Eğer form'un topic'leri varsa onları da kontrol et
        if (form.topics) {
          form.topics.forEach((topic) => {
            if (topic.fields) {
              topic.fields.forEach((field) => {
                const isVisible = checkCondition(field, newState);
                const isFieldDisabled = !isVisible || disabled;

                // Eğer alan artık görünür değilse veya disabled ise ve bir hata varsa, hatayı temizle
                if ((isFieldDisabled || !isVisible) && updatedErrors[field.name]) {
                  delete updatedErrors[field.name];
                  errorsUpdated = true;
                }
              });
            }
          });
        }

        // Eğer hatalar güncellenirse, onChange ile bildir
        if (errorsUpdated && onChange) {
          onChange({
            __errors: updatedErrors,
            __userInteracted: true,
          });
        }
      }

      // Required alan kontrolü - SADECE değişen alan için kontrol yap, diğer alanları kontrol etme
      if (form && form.fields) {
        let hasErrors = false;
        const fieldErrors = {};

        // Sadece değişen alan için kontrol
        if (fieldName) {
          const field = form.fields.find((f) => f.name === fieldName);
          if (field) {
            // Önce alanın görünür olup olmadığını kontrol et
            const isVisible = checkCondition(field, newState);

            // Alan görünür ve required ise kontrol et
            if (isVisible && field.required) {
              const isEmpty = checkIfEmpty(actualValue);
              if (isEmpty) {
                fieldErrors[fieldName] = t('onboarding.form.required'); // Translation eklendi
                hasErrors = true;
              } else {
                // Eğer alan doluysa ve önceden bir hata varsa, temizle
                if (onChange) {
                  onChange({
                    __errors: { [fieldName]: null }, // Bu alandaki hatayı temizle
                    __userInteracted: true,
                  });
                }
              }
            } else if (!isVisible && onChange) {
              // Alan görünür değilse, hata varsa temizle
              onChange({
                __errors: { [fieldName]: null }, // Görünmeyen alandaki hatayı temizle
                __userInteracted: true,
              });
            }
          }
        }

        // Form'un topic'leri içindeki değişen alanı kontrol et
        if (form.topics && fieldName) {
          form.topics.forEach((topic) => {
            if (topic.fields) {
              const field = topic.fields.find((f) => f.name === fieldName);
              if (field) {
                // Önce alanın görünür olup olmadığını kontrol et
                const isVisible = checkCondition(field, newState);

                // Alan görünür ve required ise kontrol et
                if (isVisible && field.required) {
                  const isEmpty = checkIfEmpty(actualValue);
                  if (isEmpty) {
                    fieldErrors[fieldName] = t('onboarding.form.required'); // Translation eklendi
                    hasErrors = true;
                  } else {
                    // Eğer alan doluysa ve önceden bir hata varsa, temizle
                    if (onChange) {
                      onChange({
                        __errors: { [fieldName]: null }, // Bu alandaki hatayı temizle
                        __userInteracted: true,
                      });
                    }
                  }
                } else if (!isVisible && onChange) {
                  // Alan görünür değilse, hata varsa temizle
                  onChange({
                    __errors: { [fieldName]: null }, // Görünmeyen alandaki hatayı temizle
                    __userInteracted: true,
                  });
                }
              }
            }
          });
        }

        // Eğer değişen alanda hata varsa, sadece o alan için hata bildirimi yap
        if (hasErrors && onChange) {
          onChange({
            __errors: fieldErrors,
            __userInteracted: true, // Kullanıcı etkileşimini belirtmek için flag ekle
          });
        }
      }
    };

    // Boş alan kontrolü için yardımcı fonksiyon
    const checkIfEmpty = (value) => {
      if (value === undefined || value === null) {
        return true;
      }

      if (typeof value === 'string') {
        return value.trim() === '';
      }

      if (Array.isArray(value)) {
        return value.length === 0;
      }

      if (typeof value === 'object' && !Array.isArray(value)) {
        return Object.keys(value).length === 0;
      }

      return false;
    };

    // Add a function to check if a field should be displayed based on its conditional logic
    const checkCondition = (field, formData) => {
      // Temel alanları her zaman göster - business_benefit ve business_area
      if (field.name === 'business_benefit' || field.name === 'business_area') {
        return true;
      }

      if (!field.conditional_logic || !field.conditional_logic.enabled) {
        return true;
      }

      // Koşul gruplarını değerlendir (her grup OR ilişkisiyle değerlendirilir)
      return field.conditional_logic.rules.some((ruleGroup) => {
        // Her gruptaki kuralları değerlendir (her kural AND ilişkisiyle değerlendirilir)
        const groupResult = ruleGroup.every((rule) => {
          // Boş alan adı varsa, kuralı atla
          if (!rule.field) {
            return true;
          }

          const fieldValue = formData[rule.field];

          let ruleResult = false;
          switch (rule.operator) {
            case 'equals':
              ruleResult = fieldValue === rule.value;
              break;
            case '=':
              ruleResult = fieldValue === rule.value;
              break;
            case '==':
              ruleResult = fieldValue === rule.value;
              break;
            case 'not_equals':
              ruleResult = fieldValue !== rule.value;
              break;
            case '!=':
              // For empty value comparison, check if field has any value
              if (rule.value === '') {
                ruleResult = fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
              } else {
                ruleResult = fieldValue !== rule.value;
              }
              break;
            case 'has_any_value':
              ruleResult = fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
              break;
            case 'is_empty':
              ruleResult = fieldValue === undefined || fieldValue === null || fieldValue === '';
              break;
            case '>':
            case 'greater_than':
              ruleResult = parseFloat(fieldValue) > parseFloat(rule.value);
              break;
            case '<':
            case 'less_than':
              ruleResult = parseFloat(fieldValue) < parseFloat(rule.value);
              break;
            case 'contains':
              ruleResult = typeof fieldValue === 'string' && fieldValue.includes(rule.value);
              break;
            case 'starts_with':
              ruleResult = typeof fieldValue === 'string' && fieldValue.startsWith(rule.value);
              break;
            case 'ends_with':
              ruleResult = typeof fieldValue === 'string' && fieldValue.endsWith(rule.value);
              break;
            default:
              ruleResult = true;
              break;
          }

          return ruleResult;
        });

        return groupResult;
      });
    };

    // Form submit edildiğinde required alanların kontrolünü yapan fonksiyon
    const validateForm = () => {
      if (!form) return true;

      // Form alanlarını kontrol et
      const errors = {};
      let hasErrors = false;

      // Form fields kontrolü - daha detaylı hata mesajları ile
      if (form.fields) {
        form.fields.forEach((field) => {
          // Önce alanın görünür olup olmadığını kontrol et
          const isVisible = checkCondition(field, formState);

          // Alan görünür değilse veya disabled ise kontrol etme
          if (!isVisible || disabled) {
            return; // Bu alanı atla, continue yerine return kullanıyoruz çünkü forEach içindeyiz
          }

          // Alan görünür, disabled değil ve required ise kontrol et
          if (field.required) {
            const fieldValue = values[field.name] || formState[field.name];
            if (checkIfEmpty(fieldValue)) {
              errors[field.name] = t('onboarding.form.required'); // Translation eklendi
              hasErrors = true;
            }
          }
        });
      }

      // Form topics kontrolü - daha detaylı hata mesajları ile
      if (form.topics) {
        form.topics.forEach((topic) => {
          if (topic.fields) {
            topic.fields.forEach((field) => {
              // Önce alanın görünür olup olmadığını kontrol et
              const isVisible = checkCondition(field, formState);

              // Alan görünür değilse veya disabled ise kontrol etme
              if (!isVisible || disabled) {
                return; // Bu alanı atla
              }

              // Alan görünür, disabled değil ve required ise kontrol et
              if (field.required) {
                const fieldValue = values[field.name] || formState[field.name];
                if (checkIfEmpty(fieldValue)) {
                  errors[field.name] = t('onboarding.form.required'); // Translation eklendi
                  hasErrors = true;
                }
              }
            });
          }
        });
      }

      // Eğer hata varsa, onChange fonksiyonunu çağır ve false döndür
      if (hasErrors && onChange) {
        onChange({
          __errors: errors,
          __userInteracted: true, // Her validasyon kullanıcı etkileşimi olarak sayılsın
        });
        return false;
      }

      return true;
    };

    // Ref aracılığıyla validateForm fonksiyonunu dışarıya açıyoruz
    useImperativeHandle(ref, () => ({
      validateForm: () => {
        const result = validateForm();
        if (!result) {
          // Form alanlarını vurgula ve animasyon ekle
          setTimeout(() => {
            const errorFields = document.querySelectorAll('.Mui-error');
            errorFields.forEach((field) => {
              field.scrollIntoView({ behavior: 'smooth', block: 'center' });
            });
          }, 100);
        }
        return result;
      },
    }));

    if (!form) {
      return (
        <Box className="styled-box">
          <Typography variant="h6" color="text.secondary">
            Form not found
          </Typography>
        </Box>
      );
    }

    const renderField = (field) => {
      const isVisible = checkCondition(field, formState);

      const { type, label, name, options } = field;
      const hasError = errors[name] ? true : false;
      const isHighlighted = highlightedFields[name];

      // Etiket metninde asterisk var mı kontrol et
      // HTML etiketlerini temizleyerek ve daha kesin bir regex ile kontrol edelim
      const labelText = typeof label === 'string' ? label : '';
      const cleanLabel = labelText.replace(/<[^>]*>/g, ''); // HTML etiketlerini temizle
      const labelAlreadyHasAsterisk = /\*\s*$/.test(cleanLabel) || cleanLabel.includes(' *');

      // Values prop'undan değerleri oku, eğer yoksa formState'ten oku
      // values prop'unu öncelikli kullanıyoruz - dışarıdan gelen değerler her zaman öncelikli
      const fieldValue =
        values && values[name] !== undefined ? values[name] : formState[name] || '';

      // Alan görünür değilse, disabled olarak işaretle
      // Alan zaten disabled ise, disabled kalacak
      const isFieldDisabled = !isVisible || disabled;

      // Required özelliği sadece alan görünürse ve disabled değilse geçerli olacak
      const isDynamicRequired = isVisible && !isFieldDisabled ? field.required : false;

      const commonProps = {
        name,
        id: name,
        disabled: isFieldDisabled,
        value: fieldValue,
        onChange: (e) => handleChange(e, name),
        fullWidth: true,
        size: 'medium',
        error: hasError && isVisible, // Sadece görünür alanlarda hata göster
        required: isDynamicRequired, // Dinamik required kullan
      };

      const renderLabel = (labelText) => {
        // Dinamik required özelliğine göre asterisk göster veya gizle
        const isRequired = isDynamicRequired && !labelAlreadyHasAsterisk;

        return (
          <FormLabel className={`styled-form-label ${hasError && isVisible ? 'Mui-error' : ''}`}>
            {labelText}
            {isRequired && (
              <span className="required-marker" style={{ color: 'red' }}>
                {' '}
                *
              </span>
            )}
          </FormLabel>
        );
      };

      const renderErrorMessage = () => {
        if (!hasError || !isVisible) return null;
        return (
          <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
            {errors[name]}
          </Typography>
        );
      };

      // Render the field content based on type
      let fieldContent;

      switch (type) {
        case 'text':
        case 'email':
        case 'number':
          fieldContent = (
            <FormControl className="styled-form-control form-topic" error={hasError && isVisible}>
              {renderLabel(label)}
              <TextField {...commonProps} type={type} variant="outlined" />
              {renderErrorMessage()}
            </FormControl>
          );
          break;

        case 'textarea':
          fieldContent = (
            <FormControl className="styled-form-control form-topic" error={hasError && isVisible}>
              {renderLabel(label)}
              <TextField {...commonProps} multiline rows={4} variant="outlined" />
              {renderErrorMessage()}
            </FormControl>
          );
          break;

        case 'select':
          fieldContent = (
            <FormControl className="styled-form-control form-topic" error={hasError && isVisible}>
              {renderLabel(label)}
              <Select
                {...commonProps}
                displayEmpty
                onChange={(e) => {
                  // Boş değer seçildiğinde null olarak işle
                  const value = e.target.value === '' ? null : e.target.value;

                  // Önce yerel değişiklikleri yap
                  handleChange({ target: { name, value } }, name);

                  // Eğer zorunlu alan ve değer boşsa hata göster
                  if (field.required && (value === null || value === '')) {
                    onChange({
                      [name]: value,
                      __errors: { [name]: t('onboarding.form.required') }, // Translation eklendi
                    });
                  } else if (field.required) {
                    // Eğer değer seçildiyse ve önceden hata varsa, hata mesajını kaldır
                    onChange({
                      [name]: value,
                      __errors: { [name]: null }, // null değer, hatanın temizlenmesi gerektiğini gösterir
                    });
                  }

                  // Force immediate re-evaluation of conditions
                  forceUpdate();
                }}
                value={fieldValue === null || fieldValue === undefined ? '' : fieldValue}
                inputProps={{
                  'aria-required': field.required,
                  name: name,
                }}
                error={hasError && isVisible}
              >
                <MenuItem value="">
                  <em>{t('onboarding.form.selectOption')}</em>
                </MenuItem>
                {options.map((option) => (
                  <MenuItem key={option._id || `option-${option.value}`} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
              <Divider sx={{ marginTop: 2 }} />
              {renderErrorMessage()}
            </FormControl>
          );
          break;

        case 'radio':
          fieldContent = (
            <FormControl className="styled-form-control form-topic" error={hasError && isVisible}>
              {renderLabel(label)}
              <RadioGroup
                name={name}
                value={formState[name] || ''}
                onChange={(e) => handleChange(e, name)}
              >
                {options.map((option) => (
                  <FormControlLabel
                    key={option._id}
                    value={option.value}
                    control={<Radio disabled={disabled} />}
                    label={option.label}
                  />
                ))}
              </RadioGroup>
              {renderErrorMessage()}
            </FormControl>
          );
          break;

        case 'checkbox':
          fieldContent = (
            <FormControl className="styled-form-control form-topic" error={hasError && isVisible}>
              {renderLabel(label)}
              <FormGroup>
                {options &&
                  options.map((option) => (
                    <FormControlLabel
                      key={option.value}
                      control={
                        <Checkbox
                          checked={formState[name]?.includes(option.value) || false}
                          onChange={(e) => {
                            const currentValues = formState[name] || [];
                            const newValues = e.target.checked
                              ? [...currentValues, option.value]
                              : currentValues.filter((v) => v !== option.value);

                            handleChange({ target: { value: newValues } }, name);
                          }}
                        />
                      }
                      label={option.label}
                    />
                  ))}
              </FormGroup>
              {renderErrorMessage()}
            </FormControl>
          );
          break;

        case 'date':
          fieldContent = (
            <FormControl className="styled-form-control form-topic" error={hasError && isVisible}>
              {renderLabel(label)}
              <TextField
                {...commonProps}
                type="date"
                variant="outlined"
                InputLabelProps={{
                  shrink: true,
                }}
              />
              {renderErrorMessage()}
            </FormControl>
          );
          break;

        case 'feedback':
          fieldContent = (
            <FormControl className="styled-form-control form-topic">
              {renderLabel(label)}
              <Box sx={{ mt: 2, mb: 1 }}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    maxWidth: 500,
                    borderBottom: '1px solid #e0e0e0',
                  }}
                >
                  {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (
                    <Button
                      key={`${name}-rating-${rating}`}
                      variant={formState[name] === rating.toString() ? 'contained' : 'outlined'}
                      color={formState[name] === rating.toString() ? 'primary' : 'inherit'}
                      onClick={() => handleChange({ target: { value: rating.toString() } }, name)}
                      disabled={disabled}
                      sx={{
                        minWidth: '40px',
                        height: '40px',
                        borderRadius: '4px',
                        mx: 0.5,
                      }}
                    >
                      {rating}
                    </Button>
                  ))}
                </Box>
              </Box>
            </FormControl>
          );
          break;

        default:
          fieldContent = null;
      }

      // Wrap the field content in a Fade component and apply visibility logic
      return (
        <div
          className={`field-container ${isHighlighted ? 'field-highlight' : ''} ${
            !isVisible ? 'hidden-field' : ''
          }`}
          data-field-name={name}
          style={{ display: isVisible ? 'block' : 'none' }} // CSS ile gizle/göster
        >
          {fieldContent}
        </div>
      );
    };

    return (
      <Paper className="styled-paper">
        <Box className="form-details">
          {!hideTitle && form.title && (
            <Typography variant="h6" component="h6" gutterBottom className="form-title">
              {form.title}
            </Typography>
          )}
          {!hideDescription && form.description && (
            <Typography
              variant="body1"
              gutterBottom
              className="styled-description form-description"
            >
              {form.description}
            </Typography>
          )}
        </Box>
        <form>
          {form.fields &&
            form.fields.map((field) => (
              <div key={field._id || `field-${field.name}-${field.type}`}>{renderField(field)}</div>
            ))}

          {form.topics &&
            form.topics.map((topic) => {
              const isVisible = checkCondition(topic, formState);

              return (
                <Box key={topic._id || `topic-${topic.title}`}>
                  {!hideTitle && (
                    <Typography variant="h6" component="h6" gutterBottom className="topic-title">
                      {topic.title}
                    </Typography>
                  )}
                  {topic.fields &&
                    topic.fields.map((field) => (
                      <div key={field._id || `topic-field-${field.name}-${field.type}`}>
                        {renderField(field)}
                      </div>
                    ))}
                </Box>
              );
            })}
        </form>
      </Paper>
    );
  }
);

FormRender.displayName = 'FormRender';

FormRender.propTypes = {
  formTitle: PropTypes.string,
  formData: PropTypes.object,
  hideTitle: PropTypes.bool,
  hideDescription: PropTypes.bool,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  errors: PropTypes.object,
  values: PropTypes.object,
};

export default FormRender;
