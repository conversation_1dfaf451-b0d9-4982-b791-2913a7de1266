@use '../../styles/abstracts/variables' as *;



.styled-paper {
  max-width: 100%;
  margin: 0 auto !important;
  padding:0 !important;
  background-color: none !important;
  border-radius: var(--border-radius-md);
  box-shadow: none !important;
  .form-details {
    margin-bottom: $spacing-4 !important;
  }
  .form-render {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
  
    h2 {
      margin-bottom: 1rem;
      color: #333;
    }
  
    .form-description {
      margin-bottom: $spacing-4 !important;
      color: #666;
    }
  
    .form-field {
      margin-bottom: 1.5rem;
  
      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #333;
  
        .required {
          color: #dc3545;
          margin-left: 4px;
        }
      }
  
      .form-control {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
  
        &:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
      }
  
      .radio-group,
      .checkbox-group {
        .radio-item,
        .checkbox-item {
          margin-bottom: 0.5rem;
          display: flex;
          align-items: center;
  
          input[type="radio"],
          input[type="checkbox"] {
            margin-right: 0.5rem;
          }
  
          label {
            margin-bottom: 0;
            font-weight: normal;
          }
        }
      }
  
      .validation-message {
        display: block;
        margin-top: 0.25rem;
        color: #dc3545;
        font-size: 0.875rem;
      }
    }
  
    .submit-button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 1rem;
  
      &:hover {
        background-color: #0056b3;
      }
  
      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }
    }
  }
  
  .form-title {
    color: var(--text-primary);
    font-size: $font-size-md !important;
    font-weight: $font-weight-bold !important;
    margin-bottom: var(--spacing-4);
  }
  .form-description {
    color: var(--text-secondary) !important;
    font-size: $font-size-sm !important;
    font-weight: $font-weight-regular !important;
    margin-bottom: var(--spacing-4);
  }
  
  .form-container {
    width: 100%;
  }
  
  // Custom styles for MUI components overrides if needed
  .MuiTextField-root {
    margin-bottom: var(--spacing-3) !important;
  }
  .MuiFormControl-root{
    margin-bottom: $spacing-2 !important;
  }
  .MuiTypography-root {
    &.MuiTypography-h4 {
      color: var(--text-primary);
      font-weight: $font-weight-bold;
      margin-bottom: var(--spacing-4);
    }
  }
  
  .MuiFormLabel-root {
    display: block;
    margin-bottom: var(--spacing-2);
    color: var(--text-primary) !important;
    font-weight: $font-weight-medium !important;
    font-size: $font-size-sm !important;
    
    
    &.Mui-error {
      color: var(--error-color) !important;
    }
  }
  
  .MuiOutlinedInput-root {
    background-color: var(--bg-light);
    
    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      border-color: var(--primary-color) !important;
    }
    
    &.Mui-error .MuiOutlinedInput-notchedOutline {
      border-color: var(--error-color) !important;
    }
  }
  
  .MuiFormHelperText-root {
    margin-top: var(--spacing-1) !important;
    display:none !important;
    
    &.Mui-error {
      color: var(--error-color) !important;
    }
  }
  
  .MuiCheckbox-root, .MuiRadio-root {
    color: var(--primary-color) !important;
    padding: var(--spacing-1) !important;
    
    &.Mui-checked {
      color: var(--primary-color) !important;
    }
  }
  
  .MuiFormGroup-root {
    margin-top: var(--spacing-1);
  }
  
  .MuiFormControlLabel-root {
    margin-left: -var(--spacing-1);
    margin-right: var(--spacing-2);
    
    .MuiFormControlLabel-label {
      color: var(--text-primary);
      font-weight: $font-weight-regular;
    }
  }
.styled-form-control {
  margin-bottom: var(--spacing-4) !important;
  width: 100%;
}

.styled-button {
  background-color: var(--primary-color) !important;
  color: var(--bg-paper) !important;
  
  &:hover {
    background-color: var(--primary-color-dark);
  }
}
.styled-form-label {
  margin-bottom: var(--spacing-2 !important);
  font-weight: $font-weight-bold !important;
  font-size: $font-size-sm !important;
  color: var(--text-primary) !important;
  
  .required-marker {
    color: var(--error-color) !important;
    font-weight: bold;
  }
}

.styled-box {
  text-align: center;
  padding: var(--spacing-4) !important;
}

.form-topic {
  margin-bottom: $spacing-4;
  padding-bottom: $spacing-4;


  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }

  .topic-title {
    color: $text-primary;
    font-weight: $font-weight-semibold;
  }
}

.styled-form-control {
  margin-bottom: $spacing-3;
  width: 100%;

  &:last-child {
    margin-bottom: 0;
  }
}

.field-container {
  transition: all 0.3s ease-out;
}

.field-highlight {
  background-color: rgba(255, 0, 0, 0.1);
  animation: highlight-fade 1.5s ease-out forwards;
}

@keyframes highlight-fade {
  0% {
    background-color: rgba(255, 0, 0, 0.2);
  }
  100% {
    background-color: transparent;
  }
}

.conditional-field-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.conditional-field-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.conditional-field-exit {
  opacity: 1;
}

.conditional-field-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}

.hidden-field {
  opacity: 0.5;
  pointer-events: none;
}

}
