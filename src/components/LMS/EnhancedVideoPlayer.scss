@use '../../styles/abstracts/variables' as *;

.video-player-container {
  width: 100%;
  position: relative;
  
  &.fullscreen-video-container {
    position: fixed !important;
    top: 0;
    left: 0;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999;
    background-color: #000;
    display: flex;
    flex-direction: column;
    justify-content: center;
    
    .video-wrapper {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 0 !important;
    }
    
    .main-video {
      width: 100% !important;
      height: 100% !important;
      max-height: 100vh !important;
      object-fit: contain !important;
    }
    
    .video-controls {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 10000;
      border-radius: 0 !important;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 40%, rgba(0, 0, 0, 0.5) 70%, rgba(0, 0, 0, 0) 100%);
      transform: translateY(100%);
      
      &.visible {
        transform: translateY(0);
        opacity: 1;
      }
    }
  }
  
  &.attempting-fullscreen {
    user-select: none;
    pointer-events: none;
    
    * {
      user-select: none;
      pointer-events: none;
    }
  }

  .video-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: $bg-paper;
    z-index: 1;
  }

  .video-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1;
  }

  .video-unavailable {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  .video-wrapper {
    position: relative;
    width: 100%;
    border-radius: $border-radius-md;
    cursor: pointer;
    overflow: hidden;

    &:hover .video-controls {
      opacity: 1;
      transform: translateY(0);
    }
    
    .main-video {
      display: block;
      max-width: 100%;
      background-color: #000;
      
      &.fullscreen-video {
        width: 100% !important;
        height: 100% !important;
        max-height: 100vh !important;
        object-fit: contain !important;
      }
    }

    .center-play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 3;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      padding: $spacing-3;
      cursor: pointer;
      transition: all 0.3s ease;
      width: 42px;
      height: 42px;

      &.visible {
        display: flex;
      }

      .MuiSvgIcon-root {
        color: $bg-paper;
        font-size: 48px;
        transition: transform 0.3s ease;
      }

      &:hover {
        background-color: rgba(0, 0, 0, 0.8);
        .MuiSvgIcon-root {
          transform: scale(1.1);
        }
      }
    }
  }

  .video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 40%, rgba(0, 0, 0, 0.5) 70%, rgba(0, 0, 0, 0) 100%);
    padding: 16px;
    transition: opacity 0.3s ease, transform 0.3s ease;
    opacity: 0;
    transform: translateY(100%);

    &.visible, .video-wrapper:hover & {
      opacity: 1;
      transform: translateY(0);
    }

    .progress-slider {
      color: $bg-paper;
      height: 3px;

      .MuiSlider-thumb {
        width: 12px;
        height: 12px;
        transition: 0.2s;
        opacity: 0;

        &:hover, &.Mui-focusVisible {
          box-shadow: 0 0 0 8px rgba(255, 255, 255, 0.16);
        }
      }

      .MuiSlider-rail {
        opacity: 0.28;
      }

      &:hover {
        .MuiSlider-thumb {
          opacity: 1;
        }
      }
    }

    .controls-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;

      .time-display {
        color: $bg-paper;
        margin: 0 $spacing-2;
        min-width: 85px;
        font-size: 0.875rem;
        font-weight: 500;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        user-select: none;
      }

      .volume-slider {
        width: 100px;
        margin: 0 $spacing-2;
        color: $bg-paper;

        .MuiSlider-rail {
          opacity: 0.28;
        }
      }

      .control-button, .MuiIconButton-root {
        color: $bg-paper;
        padding: $spacing-1;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .MuiSvgIcon-root {
          color: $bg-paper;
        }
      }
    }
  }

  .time-display {
    color: white;
    margin: 0 8px;
  }

  .flex-grow {
    flex-grow: 1;
  }

  .playback-menu, .quality-menu {
    margin-top: $spacing-2;
    min-width: 120px;
    box-shadow: $shadow-md;
  }

  .menu-paper {
    margin-top: 8px;
    min-width: 120px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .menu-item {
    font-size: 0.95rem;
    padding-top: 8px;
    padding-bottom: 8px;

    &.selected {
      background-color: rgba(0, 0, 0, 0.04);

      &:hover {
        background-color: rgba(0, 0, 0, 0.08);
      }
    }
  }
} 

/* Tarayıcı-Spesifik Tam Ekran Stilleri */
:-webkit-full-screen .video-player-container {
  width: 100vw !important;
  height: 100vh !important;
}

:-moz-full-screen .video-player-container {
  width: 100vw !important;
  height: 100vh !important;
}

:-ms-fullscreen .video-player-container {
  width: 100vw !important;
  height: 100vh !important;
}

:fullscreen .video-player-container {
  width: 100vw !important;
  height: 100vh !important;
} 