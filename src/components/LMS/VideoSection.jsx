import React from 'react';
import { Typography, Alert, Box } from '@mui/material';
import EnhancedVideoPlayer from './EnhancedVideoPlayer';

const VideoSection = ({
  title,
  videoTitle,
  videoUrl,
  duration,
  isComplete,
  setIsComplete,
  courseId,
  chapterIndex,
  topicIndex,
  isSupplementary = false,
  customAlert = null,
  transcript = [],
  onTimeUpdate
}) => {
  return (
    <Box sx={{ 
      width: '100%',
      height: 'calc(100vh - 120px)',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Title Section */}
      {title && (
        <Box sx={{ 
          p: '12px 24px', 
          borderBottom: 1,
          borderColor: 'divider'
        }}>
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
          {videoTitle && (
            <Typography variant="subtitle1" color="text.secondary">
              {videoTitle}
            </Typography>
          )}
        </Box>
      )}
      
      <Box sx={{ 
        flex: '1 1 auto',
        minHeight: 0,
        position: 'relative',
        mb: 3,
        overflow: 'auto'
      }}>
        <EnhancedVideoPlayer
          courseId={courseId}
          chapterIndex={chapterIndex}
          topicIndex={topicIndex}
          videoUrl={videoUrl}
          isSupplementary={isSupplementary}
          onComplete={{ isComplete, setIsComplete }}
          transcript={transcript}
          onTimeUpdate={onTimeUpdate}
        />
      </Box>

      {/* Alert Section */}
      <Box sx={{
        p: '16px 24px',
        bgcolor: 'background.paper',
        borderTop: 1,
        borderColor: 'divider',
        mt: 2
      }}>
        {isComplete ? (
          <Alert 
            severity="success"
            sx={{
              '& .MuiAlert-message': {
                color: '#2e7d32',
                fontWeight: 500
              }
            }}
          >
            Great work! You've completed this video lesson.
          </Alert>
        ) : (
          <Alert 
            severity="error"
            sx={{
              '& .MuiAlert-message': {
                color: '#d32f2f',
                fontWeight: 500
              }
            }}
          >
            You must complete watching this video to proceed to the next chapter.
          </Alert>
        )}
      </Box>
    </Box>
  );
};

export default VideoSection; 