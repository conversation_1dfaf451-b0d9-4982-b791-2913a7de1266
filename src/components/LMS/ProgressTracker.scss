@use '../../styles/abstracts/variables' as *;
@use '../../styles/components/tooltip' as *;

.progress-tracker {
  padding: $spacing-3 !important;
  border-radius: $border-radius-md !important;
  background-color: $bg-paper !important;
  border: 1px solid $border-color !important;

  &__header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin-bottom: $spacing-2 !important;

    &-title {
      display: flex !important;
      align-items: center !important;
      gap: $spacing-1 !important;
      color: $text-primary !important;
      font-size: 1.1rem !important;
      font-weight: $font-weight-semibold !important;

      svg {
        font-size: 20px !important;
      }
    }
  }

  &__milestone {
    margin-bottom: $spacing-3 !important;
    animation: fadeIn 0.5s ease-in !important;

    &-message {
      font-weight: $font-weight-semibold !important;
      color: $success-color-dark !important;
    }

    &-reward {
      color: $success-color-dark !important;
    }

    .Mu<PERSON><PERSON>lert-message {
      display: flex !important;
      flex-direction: column !important;
      gap: 0.5 * $spacing-1 !important;
    }
  }

  &__progress-bar {
    margin-bottom: $spacing-3 !important;

    &-percentage {
      display: flex !important;
      justify-content: flex-end !important;
      margin-bottom: $spacing-1 !important;
      color: $primary-color !important;
      font-weight: $font-weight-semibold !important;
    }

    &-container {
      position: relative !important;
      height: 8px !important;
      border-radius: 4px !important;
      background-color: $bg-light-dark !important;
      overflow: hidden !important;
    }

    &-fill {
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      height: 100% !important;
      border-radius: 4px !important;
      background: linear-gradient(
        90deg, 
        $primary-color 0%,
        $secondary-color 50%,
        $primary-color 100%
      ) !important;
      background-size: 200% 100% !important;
      animation: shine 2s linear infinite !important;
    }

    &-shimmer {
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      height: 100% !important;
      border-radius: 4px !important;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent) !important;
      animation: shimmer 1.5s infinite !important;
    }
  }

  &__stats {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: $spacing-2 !important;

    &-completed {
      padding: $spacing-3 !important;
      background-color: #f0f7ff !important;
      border-radius: $border-radius-md !important;

      &-row {
        display: flex !important;
        justify-content: space-between !important;
      }

      &-label {
        color: $primary-color !important;
        font-size: calc($font-size-sm - 1px) !important;
        font-weight: $font-weight-medium !important;
      }

      &-value {
        color: $primary-color !important;
        font-size: calc($font-size-sm - 1px) !important;
        font-weight: $font-weight-semibold !important;
      }
    }

    &-time {
      padding: $spacing-2 !important;
      background-color: #fff7ed !important;
      border-radius: $border-radius-md !important;

      &-header {
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        gap: $spacing-2 !important;

        svg {
          color: #ea580c !important;
          font-size: 20px !important;
        }
 
      }

      &-value {
        font-weight: $font-weight-semibold !important;
        color: #ea580c !important;
        font-size: $font-size-md !important;
        font-weight: $font-weight-semibold !important;
      }
    }
  }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes shine {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
