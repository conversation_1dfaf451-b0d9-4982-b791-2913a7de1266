import React, { useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  CircularProgress,
  Alert,
  Stack,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  LinkedIn as LinkedInIcon,
  Download as DownloadIcon,
  Share as ShareIcon,
} from "@mui/icons-material";
import {
  fetchCertificate,
  shareOnLinkedIn,
} from "../../store/slices/certificateSlice";
import CertificateTemplateEditor from "../../pages/CertificateTemplateEditor";

const CertificateView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    currentCertificate: certificate,
    loading,
    error,
  } = useSelector((state) => state.certificate);
  const { isAuthenticated } = useSelector((state) => state.auth);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login", { state: { from: `/certificates/${id}` } });
      return;
    }

    if (id && id !== "template-editor") {
      dispatch(fetchCertificate(id));
    }
  }, [dispatch, id, isAuthenticated, navigate]);

  const handleShareLinkedIn = async () => {
    try {
      await dispatch(shareOnLinkedIn(id)).unwrap();
    } catch (err) {
      console.error("Failed to share on LinkedIn:", err);
    }
  };

  const handleDownload = () => {
    if (certificate?.certificateUrl) {
      window.open(certificate.certificateUrl, "_blank");
    }
  };

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="60vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container sx={{ mt: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  if (id === "template-editor") {
    return <CertificateTemplateEditor />;
  }

  if (!certificate) {
    return (
      <Container sx={{ mt: 4 }}>
        <Alert severity="error">Certificate not found</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 4 }}>
        <Box
          sx={{
            mb: 3,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h4">Course Completion Certificate</Typography>
          <Stack direction="row" spacing={1}>
            <Tooltip title="Share on LinkedIn">
              <IconButton
                color="primary"
                onClick={handleShareLinkedIn}
                disabled={certificate.status === "shared"}
              >
                <LinkedInIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Download Certificate">
              <IconButton
                color="primary"
                onClick={handleDownload}
                disabled={!certificate.certificateUrl}
              >
                <DownloadIcon />
              </IconButton>
            </Tooltip>
          </Stack>
        </Box>

        <Box sx={{ mb: 4, textAlign: "center" }}>
          {certificate.certificateUrl ? (
            <img
              src={certificate.certificateUrl}
              alt="Course Certificate"
              style={{
                maxWidth: "100%",
                height: "auto",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
              }}
            />
          ) : (
            <Alert severity="info">Certificate image not available</Alert>
          )}
        </Box>

        <Box sx={{ mt: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Course: {certificate.metadata?.courseTitle || "Course Certificate"}
          </Typography>
          <Typography variant="body1" gutterBottom>
            Certificate Number: {certificate.certificateNumber}
          </Typography>
          <Typography variant="body1" gutterBottom>
            Issue Date: {new Date(certificate.issueDate).toLocaleDateString()}
          </Typography>
          {certificate.metadata?.grade && (
            <Typography variant="body1" gutterBottom>
              Final Grade: {certificate.metadata.grade}%
            </Typography>
          )}
        </Box>

        <Box sx={{ mt: 4, display: "flex", justifyContent: "center" }}>
          <Button
            variant="outlined"
            startIcon={<ShareIcon />}
            onClick={() => {
              const verificationUrl = `${window.location.origin}/certificates/verify/${certificate.certificateNumber}`;
              navigator.clipboard.writeText(verificationUrl);
            }}
          >
            Copy Verification Link
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default CertificateView;
