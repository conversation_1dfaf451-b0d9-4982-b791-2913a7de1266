import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  CircularProgress,
  Alert,
  Divider,
} from "@mui/material";
import {
  Search as SearchIcon,
  VerifiedUser as VerifiedIcon,
  Cancel as InvalidIcon,
} from "@mui/icons-material";
import {
  verifyCertificate,
  clearVerificationResult,
} from "../../store/slices/certificateSlice";

const CertificateVerification = () => {
  const dispatch = useDispatch();
  const { loading, error, verificationResult } = useSelector(
    (state) => state.certificate
  );
  const [certificateNumber, setCertificateNumber] = useState("");

  const handleVerify = async (e) => {
    e.preventDefault();
    if (certificateNumber.trim()) {
      dispatch(clearVerificationResult());
      dispatch(verifyCertificate(certificateNumber.trim()));
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Typography variant="h4" gutterBottom align="center">
          Verify Certificate
        </Typography>
        <Typography
          variant="body1"
          color="text.secondary"
          paragraph
          align="center"
        >
          Enter the certificate ID to verify its authenticity
        </Typography>

        <Box
          component="form"
          onSubmit={handleVerify}
          sx={{
            mt: 4,
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
            gap: 2,
          }}
        >
          <TextField
            fullWidth
            label="Certificate ID"
            variant="outlined"
            value={certificateNumber}
            onChange={(e) => setCertificateNumber(e.target.value)}
            placeholder="e.g., CERT-2304-1234"
            disabled={loading}
          />
          <Button
            type="submit"
            variant="contained"
            startIcon={
              loading ? <CircularProgress size={20} /> : <SearchIcon />
            }
            disabled={!certificateNumber.trim() || loading}
            sx={{ minWidth: { sm: 200 } }}
          >
            {loading ? "Verifying..." : "Verify"}
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mt: 3 }}>
            {error}
          </Alert>
        )}

        {verificationResult && (
          <Box sx={{ mt: 4 }}>
            <Divider sx={{ mb: 4 }} />
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                mb: 3,
              }}
            >
              {verificationResult.isValid ? (
                <VerifiedIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
              ) : (
                <InvalidIcon color="error" sx={{ fontSize: 40, mr: 2 }} />
              )}
              <Typography
                variant="h5"
                color={
                  verificationResult.isValid ? "success.main" : "error.main"
                }
              >
                {verificationResult.isValid
                  ? "Valid Certificate"
                  : "Invalid Certificate"}
              </Typography>
            </Box>

            {verificationResult.isValid && verificationResult.certificate && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Student Name
                </Typography>
                <Typography variant="body1" paragraph>
                  {verificationResult.certificate.studentName}
                </Typography>

                <Typography variant="subtitle2" color="text.secondary">
                  Course Title
                </Typography>
                <Typography variant="body1" paragraph>
                  {verificationResult.certificate.courseTitle}
                </Typography>

                <Typography variant="subtitle2" color="text.secondary">
                  Issue Date
                </Typography>
                <Typography variant="body1" paragraph>
                  {new Date(
                    verificationResult.certificate.issueDate
                  ).toLocaleDateString()}
                </Typography>

                <Typography variant="subtitle2" color="text.secondary">
                  Certificate ID
                </Typography>
                <Typography variant="body1">
                  {verificationResult.certificate.certificateNumber}
                </Typography>
              </Box>
            )}
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default CertificateVerification;
