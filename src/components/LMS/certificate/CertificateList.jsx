import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  Container,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Box,
  CircularProgress,
  Alert,
  Paper,
} from "@mui/material";
import {
  Download as DownloadIcon,
  LinkedIn as LinkedInIcon,
  Visibility as ViewIcon,
} from "@mui/icons-material";
import {
  fetchUserCertificates,
  shareOnLinkedIn,
} from "../../store/slices/certificateSlice";

const CertificateList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    certificates = [],
    loading,
    error,
  } = useSelector(
    (state) =>
      state.certificate || { certificates: [], loading: false, error: null }
  );

  useEffect(() => {
    dispatch(fetchUserCertificates());
  }, [dispatch]);

  const handleShare = async (certificateId) => {
    try {
      await dispatch(shareOnLinkedIn(certificateId)).unwrap();
    } catch (error) {
      console.error("Error sharing certificate:", error);
    }
  };

  const handleDownload = (certificateUrl) => {
    window.open(certificateUrl, "_blank");
  };

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (!certificates || certificates.length === 0) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper sx={{ p: 4, textAlign: "center" }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No certificates yet
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Complete courses to earn certificates
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate("/courses")}
          >
            Browse Courses
          </Button>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        My Certificates
      </Typography>
      <Grid container spacing={4}>
        {certificates.map((certificate, index) => (
          <Grid item xs={12} md={6} key={certificate._id || `cert-${index}`}>
            <Card
              sx={{
                height: "100%",
                display: "flex",
                flexDirection: "column",
                "&:hover": {
                  transform: "translateY(-4px)",
                  boxShadow: 4,
                  transition: "all 0.3s ease",
                },
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant="h6" gutterBottom>
                  {certificate.metadata?.courseTitle || "Course Certificate"}
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Certificate ID: {certificate.certificateNumber}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Issued on:{" "}
                  {new Date(certificate.issueDate).toLocaleDateString()}
                </Typography>
                {certificate.metadata?.grade && (
                  <Typography variant="body2" color="text.secondary">
                    Grade: {certificate.metadata.grade}%
                  </Typography>
                )}
              </CardContent>
              <CardActions sx={{ p: 2, pt: 0 }}>
                <Button
                  size="small"
                  startIcon={<ViewIcon />}
                  onClick={() => navigate(`/certificates/${certificate._id}`)}
                >
                  View
                </Button>
                <Button
                  size="small"
                  startIcon={<DownloadIcon />}
                  onClick={() => handleDownload(certificate.certificateUrl)}
                >
                  Download
                </Button>
                <Button
                  size="small"
                  startIcon={<LinkedInIcon />}
                  onClick={() => handleShare(certificate._id)}
                  disabled={certificate.status === "shared"}
                >
                  {certificate.status === "shared" ? "Shared" : "Share"}
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
};

export default CertificateList;
