@use '../../styles/abstracts/variables' as *;

.quiz {
  padding: $spacing-4 !important;
  box-shadow: none !important;
  border-radius: $border-radius-md;
  font-size: $font-size-md !important;
  border: 1px solid $border-color !important;
  background-color: $bg-paper !important;
  width: calc(100% - $spacing-4 * 2) !important;
  margin: 0 !important;

  // Tüm MUI typography elemanları için genel font boyutu ayarı
  .MuiTypography-root {
    font-size: $font-size-md !important;
    &.MuiTypography-h6 {
      font-size: $font-size-lg !important; 
      font-weight: $font-weight-bold !important;
    }
    &.MuiTypography-subtitle1 {
      font-size: $font-size-md + 1px !important;
    }
  }
  
  &__main-title {
    font-size: $font-size-xxl !important;
    font-weight: $font-weight-bold !important;
    margin-bottom: $spacing-4 !important;
    padding-bottom: $spacing-3 !important;
    border-bottom: 2px solid rgba($primary-color, 0.2) !important;
    display: block !important;
    width: 100% !important;
  }

  &__title {
    margin-bottom: $spacing-3 !important;
    font-size: $font-size-xl !important;
    font-weight: $font-weight-bold !important;
  }

  &__alert {
    margin-bottom: $spacing-4 !important;
  }

  &__question {
    border:1px solid $border-color !important;
    border-radius: $border-radius-md !important;
    box-shadow:none !important;
    // Checkbox ve radio butonlar için font boyutu ayarları
    .MuiFormControlLabel-root {
      .MuiTypography-root {
        font-size: $font-size-md !important;
      }
    }
    
    // Box içindeki Typography elementleri için
    .MuiBox-root .MuiTypography-root {
      font-size: $font-size-md !important;
    }
  }
  &__question-matching{
    border:1px solid $border-color !important;
    border-radius: $border-radius-md !important;
    box-shadow:none !important;
    padding: $spacing-3 !important;
  }
  &__question-content{
    padding: $spacing-3 $spacing-3 $spacing-2 $spacing-3 !important;
  }

  &__matching-options {
    margin-bottom: 24px;
    
    &-container {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-top: 12px;
      padding: 16px;
      background-color: #f5f5f5;
      border-radius: 8px;
      min-height: 60px;
    }
  }

  &__matching-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #ffffff;
    cursor: grab;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      background-color: #f9f9f9;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    &:active {
      cursor: grabbing;
      background-color: #e0e0e0;
    }
  }

  &__matching-pair {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding: 8px;
    border-radius: 4px;
    background-color: #f9f9f9;
  }

  &__matching-left-item {
    flex: 1;
    padding: 12px;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  &__matching-dropzone-container {
    margin-top: 24px;
  }

  &__matching-drag-icon {
    color: #757575;
  }

  &__matching-dropzone {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    min-height: 50px;
    padding: 12px;
    border: 2px dashed #bdbdbd;
    border-radius: 4px;
    background-color: #fafafa;
    transition: all 0.2s ease;

    &:hover {
      border-color: #9e9e9e;
      background-color: #f5f5f5;
    }

    &--filled {
      border-style: solid;
      border-color: #2196f3;
      background-color: #e3f2fd;
    }
  }

  &__matching-placeholder {
    color: #9e9e9e;
    font-style: italic;
    width: 100%;
    text-align: center;
  }

  &__matching-clear-btn {
    opacity: 0.7;
    
    &:hover {
      opacity: 1;
    }
  }

  &__fail-alert {
    margin-right: $spacing-3;
    flex-basis: 100%;
    @media (min-width: $tablet) {
      flex-basis: auto;
    }
  }

  &__fill-input {
    margin-top: 8px;
  }

  &__matching {
    display: flex;
    gap: $spacing-5;
    margin-top: $spacing-3;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    

    @media (max-width: $tablet) {
      flex-direction: column;
      gap: $spacing-4;
    }

    &-column {
      flex: 1;
      min-width: 0; // Prevent flex item overflow
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      
      &--full-width {
        flex: 1 1 100%;
        width: 100%;
      }
    }

    &-container {
      border: 1px solid $border-color !important;
      padding: $spacing-3 !important;
    }

    &-card {
      background-color: $bg-paper !important;
      border: 1px solid $border-color !important;
      border-radius: $border-radius-md !important;
      transition: all 0.3s ease !important;

      &:hover {
        border-color: $primary-color !important;
        background-color: $bg-light !important;
        box-shadow: $shadow-sm !important;
      }

      &--matched {
        background-color: rgba($primary-color, 0.05) !important;
        border-color: $primary-color !important;
        color: $primary-color !important;
      }
    }

    &-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-3;
      padding: $spacing-3;
      min-height: 60px;
      .MuiTypography-root {
        flex: 1;
        margin-right: auto; 
      }

      .MuiSvgIcon-root {
        color: $text-secondary;
        margin: 0 $spacing-3;
      }

      &:last-child {
        padding-bottom: $spacing-3 !important;
      }
    }

    &-dropzone {
      width: 450px !important;
      height: 18px !important;
      border: 2px dashed $border-color !important;
      border-radius: $border-radius-sm !important;
      display: flex;
      font-size: calc($font-size-xl - 1px) !important;
      align-items: center;
      padding: 0 $spacing-3 !important;
      background-color: $bg-light !important;
      transition: all 0.3s ease !important;
      margin-left: auto;
      &:hover {
        border-color: $primary-color !important;
        color:$primary-color !important;
        background-color: rgba($primary-color, 0.05) !important;
        p{
            color:$primary-color !important;
        }
      }

      &--active {
        border-color: $primary-color !important;
        background-color: rgba($primary-color, 0.1) !important;
      }
    }

    &-answer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      gap: $spacing-1;

      .MuiTypography-root {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        color:$text-secondary !important;
        font-size: calc($font-size-xl - 2px) !important;
        white-space: nowrap;
      }

      .MuiIconButton-root {
        padding: $spacing-1 !important;
        max-width: 24px !important;
        max-height: 24px !important;
        margin-left: $spacing-1 !important;

        &:hover {
          background-color: $error-color !important;
          color: $error-color !important;
          .MuiSvgIcon-root {
            color:$bg-paper !important;
          }
        }
      }

      .MuiSvgIcon-root {
        font-size: $font-size-xl !important;
      }
    }

    &-answers {
      width: 280px;
      align-self: flex-start;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      opacity: 1;
      transform: translateX(0);
      will-change: opacity, transform, width, max-width;
      max-width: 280px;
      overflow: hidden;

      &--hidden {
        opacity: 0;
        transform: translateX(50px);
        max-width: 0;
        margin-left: 0;
        padding-left: 0;
        padding-right: 0;
        border: none !important;
      }

      @media (max-width: $tablet) {
        width: 100%;
      }

      &-title {
        margin-bottom: $spacing-3 !important;
        color: $text-secondary !important;
        font-size: calc($font-size-xl - 1px) !important;
        font-weight: $font-weight-medium !important;
        padding: 0 $spacing-2 !important;
      }

      &-item {
        padding: $spacing-2 $spacing-3 !important;
        display: flex;
        min-height: 36px !important;
        align-items: center;
        gap: $spacing-2 !important;
        cursor: grab !important;
        border-radius: $border-radius-sm !important;
        transition: all 0.2s ease !important;
        margin-bottom: $spacing-2;
        background-color: $bg-light;
        border:1px solid $border-color !important;
        
        &:hover {
          border-color: $primary-color !important;
          transform: translateY(-1px);
          .MuiSvgIcon-root {
            color: $primary-color !important;
          }
          p{
            color: $primary-color !important;
          }
        }

        &:active {
          cursor: grabbing;
          transform: scale(0.98);
        }

        .MuiSvgIcon-root {
          color: $text-secondary !important;
        }
        p{
            font-size: calc($font-size-xl - 1px) !important;
        }

        &--disabled {
          cursor: default;
          opacity: 0.7;
          pointer-events: none;

          &:hover {
            transform: none;
            background-color: $bg-light;
          }
        }
      }
    }

    &-container {
      display: flex;
      gap: 2rem;
      margin-top: 1rem;
    }

    &-items {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 1rem;

    }

    &-item {
      display: flex;
      width: 50%;
      height: 100%;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem;
      background-color: #f5f5f5;
      border-radius: 4px;
      cursor: move;
      user-select: none;

      &:hover {
        background-color: #eeeeee;
      }
    }

    &-drag-icon {
      color: #757575;
    }

    &-dropzones {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    &-dropzone {
      min-height: 3.5rem;
      padding: 0.75rem;
      width: 90% !important;
      border: 2px dashed #bdbdbd;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #ffffff;
      transition: all 0.2s ease;

      &--active {
        border-color: #2196f3;
        background-color: #e3f2fd;
      }

      &--filled {
        border-style: solid;
        border-color: #9e9e9e;
      }

      &:hover {
        .quiz__matching-clear-btn {
          opacity: 1;
        }
      }
    }

    &-placeholder {
      color: #9e9e9e;
      width: 100%;
      text-align: center;
    }

    &-clear-btn {
      opacity: 0;
      transition: opacity 0.2s ease;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }

  &__actions {
    display: flex;
    justify-content: space-between;
    margin-top: $spacing-4;
    padding-top: $spacing-4;
    border-top: 1px solid $border-color;

    &-submit {
      margin-left: auto;
    }
  }
}
