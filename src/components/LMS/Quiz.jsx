import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
  Alert,
  CircularProgress,
  TextField,
  Checkbox,
  FormGroup,
  IconButton,
  Card,
  CardContent,
} from '@mui/material';
import { CheckCircle, Cancel, DragIndicator, Clear, ArrowForward } from '@mui/icons-material';
import {
  useGetQuizProgressQuery,
  useSaveQuizProgressMutation,
} from '../../redux/services/quiz-progress-api';
import axios from 'axios';
import { API_URL } from '../../config-global';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import './quiz.scss';
import { useQuizzesQuery } from '../../redux/services/quiz-service';
import { toast } from 'react-toastify';
import { developmentLogs, developmentError, developmentWarn } from '../../utils/developmentLogs';

const Quiz = ({
  quiz,
  onComplete,
  courseId,
  chapterIndex: chapterId,
  topicIndex: topicId,
  onScoreChange,
  isCompleted,
  availableLanguages,
}) => {
  const { t, i18n } = useTranslation();
  const [answers, setAnswers] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [score, setScore] = useState(0);
  const [showResults, setShowResults] = useState(false);
  const [loading, setLoading] = useState(true);
  const [draggedItem, setDraggedItem] = useState(null);
  const [dragOver, setDragOver] = useState(null);
  const [quizError, setQuizError] = useState(null);
  const currentLanguage = i18n.language || 'en';

  // Quiz için kullanılacak dili belirle
  const quizLanguage = currentLanguage;

  const [correctAnswers, setCorrectAnswers] = useState({});
  const [quizData, setQuizData] = useState(null);
  const [shuffledMatchingOptions, setShuffledMatchingOptions] = useState({});

  // Retry durumunu takip etmek için ref ekleyelim
  const isRetrying = useRef(false);
  // Cevapların yüklenip yüklenmediğini takip eden ref
  const hasInitializedRef = useRef(false);
  // Fallback için İngilizce quiz verisi istenip istenmediğini takip eden state
  const [useFallbackLanguage, setUseFallbackLanguage] = useState(false);

  // Quiz verilerini RTK Query ile al
  const {
    data: quizResponse,
    isLoading: isQuizLoading,
    isError: isQuizError,
  } = useQuizzesQuery(
    {
      quizId: quiz?.quiz?.quiz_id || quiz?.quiz?._id || quiz?.quiz?.id,
      language: useFallbackLanguage ? 'en' : quizLanguage,
    },
    {
      skip: !quiz?.quiz?.quiz_id && !quiz?.quiz?._id && !quiz?.quiz?.id,
    }
  );

  // Eğer mevcut dilde quiz datası bulunamazsa, fallback olarak İngilizce dili kullan
  useEffect(() => {
    if (!isQuizLoading && isQuizError && !useFallbackLanguage && quizLanguage !== 'en') {
      developmentLogs('Quiz data not found in current language, falling back to English');
      setUseFallbackLanguage(true);
    }
  }, [isQuizLoading, isQuizError, useFallbackLanguage, quizLanguage]);

  // Belirli bir dilde quiz verisi boş gelirse İngilizce'ye fallback
  useEffect(() => {
    if (!isQuizLoading && quizResponse === null && !useFallbackLanguage && quizLanguage !== 'en') {
      developmentLogs('Quiz response is null, falling back to English');
      setUseFallbackLanguage(true);
    }
  }, [isQuizLoading, quizResponse, useFallbackLanguage, quizLanguage]);

  // Quiz verilerini yükle
  useEffect(() => {
    const initializeQuizData = () => {
      // Eğer quiz doğrudan contentBlock içinde geliyorsa
      if (quiz?.quiz?.questions) {
        setQuizData(quiz.quiz);
        setLoading(false);
        return;
      }

      // Eğer quiz zaten tam bir nesne ise
      if (quiz?.questions) {
        setQuizData(quiz);
        setLoading(false);
        return;
      }

      // API'den gelen quiz verisi varsa
      if (quizResponse) {
        setQuizData(quizResponse);
        setLoading(false);
        return;
      }

      // Hiçbir veri bulunamadıysa ve henüz fallback yapmadıysak
      if (!isQuizLoading && !quizResponse && !useFallbackLanguage && quizLanguage !== 'en') {
        // Fallback işlemi useEffect içinde yapılacak, burada sadece durumu belirtelim
        developmentLogs('No quiz data found, will try fallback to English');
        return;
      }

      // Fallback sonrası da veri bulunamadıysa
      if (!isQuizLoading && !quizResponse && (useFallbackLanguage || quizLanguage === 'en')) {
        setQuizError(t('quiz.errors.noValidDataFound', 'Quiz verisi bulunamadı.'));
        setLoading(false);
      }
    };

    initializeQuizData();
  }, [quiz, quizResponse, isQuizLoading, t, useFallbackLanguage, quizLanguage]);

  // Redux hooks
  const { data: quizProgress, isLoading: isProgressLoading } = useGetQuizProgressQuery(
    { courseId, chapterId, topicId },
    {
      skip: !courseId || !chapterId || !topicId,
      refetchOnMountOrArgChange: true,
    }
  );
  const [saveProgress, { isLoading: isSaving }] = useSaveQuizProgressMutation();

  // İlk yükleme ve progress kontrolü
  useEffect(() => {
    // Eğer quiz progress değişti ama henüz başlatılmadıysa veya retry modundaysa
    if (
      !quizProgress ||
      isProgressLoading ||
      isRetrying.current ||
      !quizData ||
      hasInitializedRef.current
    ) {
      return;
    }

    // İşlem başlıyor, işaretleyelim
    hasInitializedRef.current = true;

    // Async işlemi hemen çağırmak yerine bir fonksiyon içine alıyoruz
    const initializeQuiz = async () => {
      try {
        const answersLength = quizProgress?.answers ? Object.keys(quizProgress.answers).length : 0;

        if (answersLength > 0) {
          // Önce cevapları ayarla
          setAnswers(quizProgress.answers);
          // Sonra submitted ve showResults'ı true yap
          setSubmitted(true);
          setShowResults(true);

          // Sayfa ilk yüklendiğinde doğru cevapları hesapla
          if (quizData && quizData.questions) {
            const answersCorrectness = {};
            let userPoints = 0;
            const totalPoints = quizData.questions.reduce(
              (total, question) => total + (question.points || 10),
              0
            );

            quizData.questions.forEach((question, index) => {
              const questionId = question.id || question._id || index;
              let isCorrect = false;
              const questionPoints = question.points || 10;

              // Burada handleSubmit fonksiyonundaki aynı kontrolleri yapıyoruz
              switch (question.type) {
                case 'multiple-choice': {
                  // Çoklu seçim kontrolü
                  let allCorrectMultipleChoice = true;
                  let userSelectedSomeMultipleChoice = false;

                  if (question.options) {
                    question.options.forEach((option, i) => {
                      const isOptionSelected = quizProgress.answers[questionId]?.[i] || false;
                      const isOptionCorrect = option.correct || option.isCorrect || false;

                      if (isOptionSelected) userSelectedSomeMultipleChoice = true;

                      if (
                        (isOptionCorrect && !isOptionSelected) ||
                        (!isOptionCorrect && isOptionSelected)
                      ) {
                        allCorrectMultipleChoice = false;
                      }
                    });
                  }

                  isCorrect = allCorrectMultipleChoice && userSelectedSomeMultipleChoice;
                  if (isCorrect) userPoints += questionPoints;
                  break;
                }

                case 'single-choice': {
                  // Tek seçim kontrolü
                  const singleSelectedIndex = parseInt(quizProgress.answers[questionId], 10);
                  if (!isNaN(singleSelectedIndex) && question.options[singleSelectedIndex]) {
                    isCorrect =
                      question.options[singleSelectedIndex].correct ||
                      question.options[singleSelectedIndex].isCorrect ||
                      false;
                    if (isCorrect) userPoints += questionPoints;
                  }
                  break;
                }

                case 'true-false': {
                  // Kontrol et: kullanıcı doğru cevabı seçmiş mi? (true/false)
                  const boolAnswer =
                    typeof quizProgress.answers[questionId] === 'boolean'
                      ? quizProgress.answers[questionId]
                      : quizProgress.answers[questionId] === 'true';

                  // İlk option (index 0) her zaman True, ikinci option (index 1) her zaman False değerini temsil eder
                  const correctAnswer = question.options[0].isCorrect;

                  // Kullanıcının cevabı (true/false) ile ilk option'ın doğruluğunu karşılaştır
                  isCorrect = boolAnswer === correctAnswer;

                  if (isCorrect) userPoints += questionPoints;
                  answersCorrectness[questionId] = isCorrect;
                  break;
                }

                case 'fill-in-the-blank':
                  // Boşluk doldurma kontrolü
                  isCorrect =
                    quizProgress.answers[questionId]?.toLowerCase?.().trim() ===
                    question.correctAnswer?.toLowerCase?.().trim();
                  if (isCorrect) userPoints += questionPoints;
                  break;

                case 'multiple-selection': {
                  // Kontrol et: kullanıcı doğru seçenekleri seçmiş mi?
                  let allCorrect = true;
                  let userSelectedSome = false;

                  if (question.options) {
                    question.options.forEach((option, i) => {
                      const expected = option.correct || option.isCorrect || false;
                      const actual = quizProgress.answers[questionId]?.[i] || false;

                      if (expected && actual) userSelectedSome = true;
                      if (expected !== actual) allCorrect = false;
                    });
                  }

                  isCorrect = allCorrect && userSelectedSome;
                  if (isCorrect) userPoints += questionPoints;
                  answersCorrectness[questionId] = isCorrect;
                  break;
                }

                case 'matching': {
                  // Eğer pairs dizisi yoksa, options dizisinden oluştur
                  let pairs = [];

                  // Öncelikle çevirilerde pairs dizisi varsa kullan
                  if (question.pairs && question.pairs.length > 0) {
                    // Pairs dizisi varsa kullan
                    pairs = question.pairs;
                  } else if (question.options && question.options.length > 0) {
                    // Options dizisinden pairs dizisini oluştur
                    pairs = question.options.map((opt) => ({
                      left: opt.text || '',
                      right: opt.match || opt.displayMatch || '',
                    }));
                  }

                  // Eğer pairs dizisi hala boşsa, varsayılan değerler oluştur
                  if (pairs.length === 0) {
                    developmentWarn(`No pairs found for question: ${questionId}`);
                  }

                  // Eşleştirilmemiş öğeleri belirle
                  const matchedRightValues = Object.values(quizProgress.answers[questionId] || {});
                  const unmatchedOptions = pairs.filter(
                    (pair) => !matchedRightValues.includes(pair.right)
                  );

                  // Tüm eşleştirmelerin doğru olup olmadığını kontrol et
                  const allMatchesCorrect = pairs.every((pair, index) => {
                    const userMatch = quizProgress.answers[questionId]?.[index];
                    return userMatch === pair.right;
                  });

                  isCorrect = allMatchesCorrect;
                  if (isCorrect) userPoints += questionPoints;
                  answersCorrectness[questionId] = isCorrect;
                  break;
                }

                // Diğer soru tipleri için kontroller...
                // ...
              }

              // Doğru/yanlış bilgisini kaydet
              answersCorrectness[questionId] = isCorrect;
            });

            // correctAnswers state'ini güncelle
            setCorrectAnswers(answersCorrectness);

            // Eğer score yoksa veya score hesaplaması yanlışsa, doğru score'u hesapla
            const calculatedScore = Math.round((userPoints / totalPoints) * 100);

            setScore(calculatedScore);
            // Quiz geçiş notu kontrolü
            const passingScore = quizData.passingScore !== undefined ? quizData.passingScore : 70;

            // İlk yüklemede de isPassing değerini doğru hesaplayalım
            const isPassed = passingScore === 0 || calculatedScore >= passingScore;
            developmentLogs('Quiz initialization - isPassed check:', {
              calculatedScore,
              passingScore,
              isPassed,
            });

            if (onScoreChange && !isRetrying.current) {
              onScoreChange(calculatedScore, passingScore);
            }

            // Eğer quiz progress verileri quiz'in tamamlandığını gösteriyorsa
            // ve quizProgress.completed değeri true ise
            // onComplete fonksiyonunu çağır
            if (onComplete && !isRetrying.current && isPassed && quizProgress.completed === true) {
              developmentLogs(
                'Quiz initialization - calling onComplete for passed quiz with completed status=true'
              );

              // Bilerek toast veya bildirim göstermesin, sadece tamamlandı olarak işaretlensin
              // Sayfa yüklendiğinde otomatik toast göstermesini engellemek için
              // onComplete fonksiyonunu çağırmadan önce bir session değişkeni oluştur
              sessionStorage.setItem('skipQuizCompletionToast', 'true');

              await onComplete(calculatedScore, totalPoints, passingScore);
            }
          }
        }
      } catch (error) {
        developmentError('Quiz initialization error:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeQuiz();
  }, [quizProgress, isProgressLoading, quizData, onComplete, onScoreChange]);

  // Reset the initialization flag when quizData or retry changes
  useEffect(() => {
    if (isRetrying.current || !quizData) {
      hasInitializedRef.current = false;
    }
  }, [isRetrying.current, quizData]);

  // Sadece unmatchedOptions değiştiğinde shuffle işlemini yap
  useEffect(() => {
    if (quizData?.questions) {
      const newShuffledOptions = {};

      quizData.questions.forEach((question, index) => {
        if (question.type === 'matching') {
          const questionId = question.id || question._id || index;
          const pairs =
            question.pairs ||
            question.options?.map((opt) => ({
              left: opt.text || '',
              right: opt.match || opt.displayMatch || '',
            })) ||
            [];

          // İlk yüklemede seçenekleri karıştır
          const rightValues = pairs.map((pair) => pair.right);
          newShuffledOptions[questionId] = rightValues.sort(() => Math.random() - 0.5);
        }
      });

      setShuffledMatchingOptions(newShuffledOptions);
    }
  }, [quizData]); // Sadece quizData değiştiğinde çalışsın

  const handleAnswerChange = (questionId, value, type = '') => {
    if (submitted) return;
    if (type === 'multiple-selection') {
      setAnswers((prev) => ({
        ...prev,
        [questionId]: {
          ...prev[questionId],
          [value]: !prev[questionId]?.[value],
        },
      }));
    } else if (type === 'matching') {
      setAnswers((prev) => ({
        ...prev,
        [questionId]: value,
      }));
    } else if (type === 'single-choice') {
      setAnswers((prev) => ({
        ...prev,
        [questionId]: value,
      }));
    } else if (type === 'true-false') {
      // true-false tipi sorular için string değeri boolean'a çevir
      const processedValue = value === 'true';
      setAnswers((prev) => ({
        ...prev,
        [questionId]: processedValue,
      }));
    } else {
      // Diğer soru tipleri için (multiple-choice, fill-in-the-blank)
      setAnswers((prev) => ({
        ...prev,
        [questionId]: value,
      }));
    }
  };

  const handleDragStart = (e, item) => {
    setDraggedItem(item);
    e.dataTransfer.setData('text/plain', ''); // Required for Firefox
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    if (!dragOver) {
      setDragOver(e.currentTarget);
      e.currentTarget.classList.add('quiz__matching-dropzone--active');
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    if (dragOver === e.currentTarget) {
      setDragOver(null);
      e.currentTarget.classList.remove('quiz__matching-dropzone--active');
    }
  };

  const handleDrop = (e, questionId, targetIndex) => {
    e.preventDefault();
    if (!draggedItem) return;

    const newAnswers = { ...answers[questionId] } || {};
    newAnswers[targetIndex] = draggedItem;
    handleAnswerChange(questionId, newAnswers, 'matching');
    setDraggedItem(null);

    if (dragOver) {
      dragOver.classList.remove('quiz__matching-dropzone--active');
      setDragOver(null);
    }
  };

  const handleClearMatch = (questionId, pairIndex) => {
    const newAnswers = { ...answers[questionId] };
    delete newAnswers[pairIndex];
    handleAnswerChange(questionId, newAnswers, 'matching');
  };

  const handleSubmit = async () => {
    try {
      if (loading || submitted) return;

      setSubmitted(true);

      // Cevapları kontrol et ve puanı hesapla
      let totalPoints = 0;
      let userPoints = 0;
      let calculatedScore = 0;
      // Create a map to track which questions were answered correctly
      const answersCorrectness = {};

      if (quizData && quizData.questions) {
        quizData.questions.forEach((question, index) => {
          const questionId = question.id || question._id || index;
          const questionPoints = question.points || 10;
          totalPoints += questionPoints;
          let isCorrect = false;

          switch (question.type) {
            case 'multiple-choice':
              // Çoklu seçim için doğrulama yapılmalı (tüm doğru seçenekler seçilmiş ve yanlış seçenekler seçilmemiş olmalı)
              let allCorrectMultipleChoice = true;
              let userSelectedSomeMultipleChoice = false;

              if (question.options) {
                question.options.forEach((option, i) => {
                  const isOptionSelected = answers[questionId]?.[i] || false;
                  const isOptionCorrect = option.correct || option.isCorrect || false;

                  // Kullanıcı en az bir seçenek seçmişse
                  if (isOptionSelected) userSelectedSomeMultipleChoice = true;

                  // Eğer doğru seçenek seçilmemiş veya yanlış seçenek seçilmişse
                  if (
                    (isOptionCorrect && !isOptionSelected) ||
                    (!isOptionCorrect && isOptionSelected)
                  ) {
                    allCorrectMultipleChoice = false;
                  }
                });
              }

              // Hiçbir şey seçilmemişse
              if (!userSelectedSomeMultipleChoice) {
                allCorrectMultipleChoice = false;
              }

              isCorrect = allCorrectMultipleChoice;
              if (isCorrect) userPoints += questionPoints;
              answersCorrectness[questionId] = isCorrect;
              break;

            case 'single-choice':
              // Kontrol et: kullanıcı doğru seçeneği seçmiş mi?
              const singleSelectedIndex = parseInt(answers[questionId], 10);
              if (!isNaN(singleSelectedIndex) && question.options[singleSelectedIndex]) {
                isCorrect =
                  question.options[singleSelectedIndex].correct ||
                  question.options[singleSelectedIndex].isCorrect ||
                  false;
                if (isCorrect) userPoints += questionPoints;
              }
              answersCorrectness[questionId] = isCorrect;
              break;

            case 'true-false':
              // Kontrol et: kullanıcı doğru cevabı seçmiş mi? (true/false)
              const boolAnswer =
                typeof answers[questionId] === 'boolean'
                  ? answers[questionId]
                  : answers[questionId] === 'true';

              // First item is true always , event if the text changed
              const correctAnswer = question.options[0].isCorrect;

              isCorrect = boolAnswer === correctAnswer;

              if (isCorrect) userPoints += questionPoints;
              answersCorrectness[questionId] = isCorrect;
              break;

            case 'fill-in-the-blank':
              // Kontrol et: kullanıcının cevabı doğru mu?
              if (answers[questionId] && question.correctAnswer) {
                isCorrect =
                  answers[questionId].toLowerCase().trim() ===
                  question.correctAnswer.toLowerCase().trim();
                if (isCorrect) userPoints += questionPoints;
              }
              answersCorrectness[questionId] = isCorrect;
              break;

            case 'multiple-selection':
              // Kontrol et: kullanıcı doğru seçenekleri seçmiş mi?
              let allCorrect = true;
              let userSelectedSome = false;

              if (question.options) {
                question.options.forEach((option, i) => {
                  const expected = option.correct || option.isCorrect || false;
                  const actual = answers[questionId]?.[i] || false;

                  if (expected && actual) userSelectedSome = true;
                  if (expected !== actual) allCorrect = false;
                });
              }

              isCorrect = allCorrect && userSelectedSome;
              if (isCorrect) userPoints += questionPoints;
              answersCorrectness[questionId] = isCorrect;
              break;

            case 'matching':
              // Eğer pairs dizisi yoksa, options dizisinden oluştur
              let pairs = [];

              // Öncelikle çevirilerde pairs dizisi varsa kullan
              if (question.pairs && question.pairs.length > 0) {
                // Pairs dizisi varsa kullan
                pairs = question.pairs;
              } else if (question.options && question.options.length > 0) {
                // Options dizisinden pairs dizisini oluştur
                pairs = question.options.map((opt) => ({
                  left: opt.text || '',
                  right: opt.match || opt.displayMatch || '',
                }));
              }

              // Eğer pairs dizisi hala boşsa, varsayılan değerler oluştur
              if (pairs.length === 0) {
                developmentWarn(`No pairs found for question: ${questionId}`);
              }

              // Eşleştirilmemiş öğeleri belirle
              const matchedRightValues = Object.values(answers[questionId] || {});
              const unmatchedOptions = pairs.filter(
                (pair) => !matchedRightValues.includes(pair.right)
              );

              // Karıştırılmış değerleri state'ten al ve sadece eşleştirilmemiş olanları göster
              const shuffledRightValues = (shuffledMatchingOptions[questionId] || []).filter(
                (value) => !matchedRightValues.includes(value)
              );

              // Tüm eşleştirmelerin doğru olup olmadığını kontrol et
              const allMatchesCorrect = pairs.every((pair, index) => {
                const userMatch = answers[questionId]?.[index];
                return userMatch === pair.right;
              });

              isCorrect = allMatchesCorrect;
              if (isCorrect) userPoints += questionPoints;
              answersCorrectness[questionId] = isCorrect;
              break;

            default:
              developmentWarn(`Unknown question type: ${question.type}`);
              break;
          }
        });

        if (totalPoints > 0) {
          calculatedScore = Math.round((userPoints / totalPoints) * 100);
        }
      }

      // Store the correctness information
      setCorrectAnswers(answersCorrectness);

      try {
        // Quiz için geçer not kontrolü - varsayılan olarak 70, eğer passingScore tanımlanmışsa onu kullan
        const passingScore = quizData.passingScore !== undefined ? quizData.passingScore : 70;

        // Debug için console.log ekleyelim
        developmentLogs('Quiz DATA CHECK BEFORE:', {
          quizDataPassingScore: quizData.passingScore,
          passingScore,
          calculatedScore,
        });

        // isPassed hesaplaması - passingScore'u açıkça kontrol edelim
        let isPassed = false;
        if (passingScore === 0) {
          // passingScore 0 ise quiz her zaman geçilmiş sayılır
          isPassed = true;
          developmentLogs('Quiz passed because passingScore is 0');
        } else {
          // Normal puan karşılaştırması
          isPassed = calculatedScore >= passingScore;
          developmentLogs(
            'Quiz passing check:',
            calculatedScore,
            '>=',
            passingScore,
            '=',
            isPassed
          );
        }

        // Debug için detaylı log ekle
        developmentLogs('Quiz completion DEBUG:', {
          calculatedScore,
          passingScore,
          isPassed,
          quizPassingScore: quizData.passingScore,
          userPoints,
          totalPoints,
          percentage: totalPoints > 0 ? (userPoints / totalPoints) * 100 : 0,
        });

        // Kullanıcı arayüzünde gösterilecek veya işlenecek gerçek skor
        const displayScore = calculatedScore;

        // Quiz sonucunu backend'e kaydet
        await saveProgress({
          courseId,
          chapterId,
          topicId,
          completed: isPassed, // isPassed kontrolüne göre tamamlandı olarak işaretle
          score: displayScore,
          answers,
          isPassing: isPassed, // isPassing değerini açıkça gönder
          passingScore: passingScore, // passingScore değerini de gönder
        }).unwrap();

        setScore(displayScore);
        setSubmitted(true);
        setShowResults(true);

        if (onScoreChange) {
          // Quiz puanıyla birlikte passingScore değerini de iletiyoruz
          onScoreChange(displayScore, passingScore);

          // Debug için console.log ekleyelim
          developmentLogs(
            'Quiz completed with score:',
            displayScore,
            'Passing score:',
            passingScore,
            'isPassed:',
            isPassed
          );
        }

        // Quiz geçme veya başarısız olma mesajını göster
        if (!isPassed) {
          toast.error(
            `${t('quiz.sorryFailed', "Sorry, you didn't pass the quiz")}: ${displayScore.toFixed(0)}%`,
            {
              position: 'top-right',
              autoClose: 3000,
            }
          );
        }

        // isPassed true ise ve onComplete fonksiyonu tanımlanmışsa çağır
        if (isPassed && typeof onComplete === 'function') {
          developmentLogs('✅ Quiz PASSED - Calling onComplete with:', {
            score: displayScore,
            totalPoints,
            passingScore,
          });

          // onComplete fonksiyonunu doğru parametrelerle çağır
          try {
            // Quiz'in tamamlandığı bilgisini güncelle ve kaydet
            await saveProgress({
              courseId,
              chapterId,
              topicId,
              completed: true, // completed değerini true olarak işaretle
              score: displayScore,
              answers,
              isPassing: isPassed,
              passingScore: passingScore,
            }).unwrap();

            await onComplete(displayScore, totalPoints, passingScore);
            developmentLogs('✅ onComplete function executed successfully');
          } catch (error) {
            developmentError('❌ Error executing onComplete function:', error);
          }
        } else {
          developmentLogs('❌ Quiz submission complete - Not calling onComplete:', {
            isPassed,
            onCompleteIsDefined: typeof onComplete === 'function',
          });
        }
      } catch (error) {
        developmentError('Quiz completion error:', error);
        setShowResults(false);
        setSubmitted(false);
        throw new Error(
          t(
            'quiz.errors.completionError',
            'Quiz tamamlanırken bir hata oluştu. Lütfen tekrar deneyin.'
          )
        );
      }
    } catch (error) {
      setShowResults(false);
      setSubmitted(false);
      setScore(0);
      if (onScoreChange) {
        onScoreChange(0);
      }
    }
  };

  const handleRetry = async () => {
    isRetrying.current = true; // Retry işlemi başladı

    try {
      setSubmitted(false);
      setShowResults(false);
      setScore(0);
      setCorrectAnswers({});

      await saveProgress({
        courseId,
        chapterId,
        topicId,
        completed: false,
        score: 0,
        answers: {},
      }).unwrap();

      if (onScoreChange) {
        onScoreChange(0);
      }
    } catch (error) {
      setAnswers({});
      setSubmitted(false);
      setShowResults(false);
      setScore(0);
      setCorrectAnswers({});
    }

    // Retry işlemi tamamlandıktan sonra bir süre bekle ve sonra isRetrying'i false yap
    setTimeout(() => {
      isRetrying.current = false;
    }, 1000);
  };
  /**
  useEffect(() => {
  }, [submitted]);

  useEffect(() => { 
  }, [showResults]);
*/
  if (loading || isProgressLoading || isSaving) {
    return (
      <Box display="flex" justifyContent="center" p={3}>
        <CircularProgress />
      </Box>
    );
  }

  if (quizError) {
    return (
      <Box p={3}>
        <Alert severity="error">{quizError}</Alert>
      </Box>
    );
  }

  if (!quizData) {
    return (
      <Box p={3}>
        <Alert severity="warning">{t('quiz.notFound', 'Quiz bulunamadı.')}</Alert>
      </Box>
    );
  }

  /*
   * Render Question
   * @param {Object} question - The question object
   * @param {number} index - The index of the question
   * @returns {React.ReactNode} The rendered question
   * This function is used to render the question based on the question type
   * It also renders the quiz on page load
   */
  const renderQuestion = (question, index) => {
    const questionId = question.id || question._id || index;
    const userAnswer = answers[questionId];
    const isAnswerCorrect = showResults ? correctAnswers[questionId] : null;

    switch (question.type) {
      case 'multiple-choice':
        return (
          <FormGroup>
            {question.options.map((option, optionIndex) => {
              const isSelected = userAnswer?.[optionIndex] || false;
              const isCorrectOption = option.correct || option.isCorrect || false;
              const isCorrectButNotSelected = showResults && isCorrectOption && !isSelected;

              return (
                <FormControlLabel
                  key={optionIndex}
                  control={
                    <Checkbox
                      checked={isSelected}
                      onChange={() =>
                        handleAnswerChange(questionId, optionIndex, 'multiple-selection')
                      }
                      disabled={submitted}
                    />
                  }
                  label={
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        ...(isCorrectButNotSelected && {
                          border: '2px solid #f44336',
                          borderRadius: '4px',
                          padding: '4px 8px',
                          backgroundColor: 'rgba(244, 67, 54, 0.08)',
                        }),
                      }}
                    >
                      <Typography variant="body1" sx={{ fontSize: '16px' }}>
                        {option.option || option.text}
                      </Typography>
                      {showResults &&
                        (isCorrectOption ? (
                          <CheckCircle color="success" fontSize="small" />
                        ) : isSelected ? (
                          <Cancel color="error" fontSize="small" />
                        ) : null)}
                    </Box>
                  }
                />
              );
            })}
          </FormGroup>
        );

      case 'single-choice':
        return (
          <RadioGroup
            value={userAnswer ?? ''}
            onChange={(e) => handleAnswerChange(questionId, e.target.value, 'single-choice')}
          >
            {question.options.map((option, optionIndex) => {
              const isCorrectOption = option.correct || option.isCorrect || false;
              const isSelected = userAnswer === optionIndex.toString();

              return (
                <FormControlLabel
                  key={optionIndex}
                  value={optionIndex.toString()}
                  control={<Radio />}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body1" sx={{ fontSize: '16px' }}>
                        {option.option || option.text}
                      </Typography>
                      {showResults &&
                        (isCorrectOption ? (
                          <CheckCircle color="success" fontSize="small" />
                        ) : isSelected ? (
                          <Cancel color="error" fontSize="small" />
                        ) : null)}
                    </Box>
                  }
                  disabled={submitted}
                />
              );
            })}
          </RadioGroup>
        );

      case 'true-false':
        const correctAnswer = question.options[0].isCorrect;
        return (
          <RadioGroup
            value={userAnswer?.toString() ?? ''}
            onChange={(e) => handleAnswerChange(questionId, e.target.value, 'true-false')}
          >
            <FormControlLabel
              value="true"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body1" sx={{ fontSize: '16px' }}>
                    {t('common.true', 'True')}
                  </Typography>
                  {showResults &&
                    (correctAnswer === true ? (
                      <CheckCircle color="success" fontSize="small" />
                    ) : userAnswer === true ? (
                      <Cancel color="error" fontSize="small" />
                    ) : null)}
                </Box>
              }
              disabled={submitted}
            />
            <FormControlLabel
              value="false"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body1" sx={{ fontSize: '16px' }}>
                    {t('common.false', 'False')}
                  </Typography>
                  {showResults &&
                    (correctAnswer === false ? (
                      <CheckCircle color="success" fontSize="small" />
                    ) : userAnswer === false ? (
                      <Cancel color="error" fontSize="small" />
                    ) : null)}
                </Box>
              }
              disabled={submitted}
            />
          </RadioGroup>
        );

      case 'fill-in-the-blank':
        const fillCorrect =
          userAnswer?.toLowerCase?.().trim() === question.correctAnswer?.toLowerCase?.().trim();
        return (
          <TextField
            fullWidth
            value={userAnswer ?? ''}
            onChange={(e) => handleAnswerChange(questionId, e.target.value)}
            disabled={submitted}
            size="small"
            className="quiz__fill-input"
            helperText={
              showResults
                ? `${t('quiz.correctAnswer', 'Correct answer')}: ${question.correctAnswer}`
                : t('quiz.enterYourAnswer', 'Enter your answer')
            }
            error={showResults && !fillCorrect}
            InputProps={{
              endAdornment:
                showResults &&
                (fillCorrect ? (
                  <CheckCircle color="success" fontSize="small" sx={{ mr: 1 }} />
                ) : (
                  <Cancel color="error" fontSize="small" sx={{ mr: 1 }} />
                )),
            }}
          />
        );

      case 'multiple-selection':
        return (
          <FormGroup>
            {question.options.map((option, optionIndex) => {
              const isCorrectOption = option.correct || option.isCorrect || false;
              const isSelected = userAnswer?.[optionIndex] || false;
              const isCorrectButNotSelected = showResults && isCorrectOption && !isSelected;

              return (
                <FormControlLabel
                  key={optionIndex}
                  control={
                    <Checkbox
                      checked={isSelected}
                      onChange={() =>
                        handleAnswerChange(questionId, optionIndex, 'multiple-selection')
                      }
                      disabled={submitted}
                    />
                  }
                  label={
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        ...(isCorrectButNotSelected && {
                          border: '2px solid #f44336',
                          borderRadius: '4px',
                          padding: '4px 8px',
                          backgroundColor: 'rgba(244, 67, 54, 0.08)',
                        }),
                      }}
                    >
                      <Typography variant="body1" sx={{ fontSize: '16px' }}>
                        {option.option || option.text}
                      </Typography>
                      {showResults &&
                        (isCorrectOption ? (
                          <CheckCircle color="success" fontSize="small" />
                        ) : isSelected ? (
                          <Cancel color="error" fontSize="small" />
                        ) : null)}
                    </Box>
                  }
                />
              );
            })}
          </FormGroup>
        );

      case 'matching':
        // Eğer pairs dizisi yoksa, options dizisinden oluştur
        let pairs = [];

        // Öncelikle çevirilerde pairs dizisi varsa kullan
        if (question.pairs && question.pairs.length > 0) {
          // Pairs dizisi varsa kullan
          pairs = question.pairs;
        } else if (question.options && question.options.length > 0) {
          // Options dizisinden pairs dizisini oluştur
          pairs = question.options.map((opt) => ({
            left: opt.text || '',
            right: opt.match || opt.displayMatch || '',
          }));
        }

        // Eğer pairs dizisi hala boşsa, varsayılan değerler oluştur
        if (pairs.length === 0) {
          developmentWarn(`No pairs found for question: ${questionId}`);
        }

        // Eşleştirilmemiş öğeleri belirle
        const matchedRightValues = Object.values(answers[questionId] || {});
        const unmatchedOptions = pairs.filter((pair) => !matchedRightValues.includes(pair.right));

        // Karıştırılmış değerleri state'ten al ve sadece eşleştirilmemiş olanları göster

        let shuffledRightValues = (shuffledMatchingOptions[questionId] || []).filter(
          (value) => !matchedRightValues.includes(value)
        );
        if (quizData.shuffleQuestions !== true) {
          shuffledRightValues = unmatchedOptions.map((pair) => pair.right);
        }
        return (
          <Box key={questionId} className="quiz__question-matching">
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {t(
                'quiz.dragItemsInstructions',
                'Drag items from the options below and drop them onto the corresponding drop zones.'
              )}
            </Typography>

            {/* Question result indicator for matching type */}
            {showResults && (
              <Box sx={{ mb: 2 }}>
                <Typography
                  variant="body2"
                  color={isAnswerCorrect ? 'success.main' : 'error.main'}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    fontWeight: 'bold',
                  }}
                >
                  {isAnswerCorrect ? (
                    <>
                      <CheckCircle fontSize="small" />
                      {t('quiz.allMatchesCorrect', 'All matches are correct!')}
                    </>
                  ) : (
                    <>
                      <Cancel fontSize="small" />
                      {t('quiz.someMatchesIncorrect', 'Some matches are incorrect or missing.')}
                    </>
                  )}
                </Typography>
              </Box>
            )}

            {/* Sürüklenecek öğeler (üstte) */}
            <Box className="quiz__matching-options">
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                {t('quiz.availableOptionsToMatch', 'Available options to match:')}
              </Typography>
              <Box className="quiz__matching-options-container">
                {submitted ? (
                  <Typography variant="body2" color="text.secondary" fontStyle="italic">
                    {t(
                      'quiz.submittedResultsDisplayed',
                      'Quiz submitted. Results are displayed below.'
                    )}
                  </Typography>
                ) : unmatchedOptions.length === 0 ? (
                  <Typography variant="body2" color="text.secondary" fontStyle="italic">
                    {t(
                      'quiz.allOptionsMatched',
                      'All options have been matched. You can rearrange them if needed.'
                    )}
                  </Typography>
                ) : (
                  shuffledRightValues.map((pair, pairIndex) => (
                    <Box
                      key={`option-${pairIndex}`}
                      className="quiz__matching-option"
                      draggable={!submitted}
                      onDragStart={(e) => handleDragStart(e, pair)}
                    >
                      <DragIndicator className="quiz__matching-drag-icon" />
                      <Typography>{pair}</Typography>
                    </Box>
                  ))
                )}
              </Box>
            </Box>

            {/* Bırakılacak hedef alanlar (altta) */}
            <Box className="quiz__matching-dropzone-container" sx={{ mt: 3 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                {t('quiz.matchItemsInstructions', 'Match items with their correct descriptions:')}
              </Typography>
              {pairs.map((pair, pairIndex) => {
                const matchedAnswer = answers[questionId]?.[pairIndex];
                const isMatchCorrect = showResults && matchedAnswer === pair.right;
                const isMatchIncorrect =
                  showResults && matchedAnswer && matchedAnswer !== pair.right;

                return (
                  <Box className="quiz__matching-pair" key={`pair-${pairIndex}`}>
                    <Box className="quiz__matching-left-item">
                      <Typography>{pair.left || `Item ${pairIndex + 1}`}</Typography>
                    </Box>
                    <ArrowForward sx={{ mx: 2, color: 'text.secondary' }} />
                    <Box
                      className={`quiz__matching-dropzone ${
                        matchedAnswer ? 'quiz__matching-dropzone--filled' : ''
                      } ${isMatchCorrect ? 'quiz__matching-dropzone--correct' : ''} 
                        ${isMatchIncorrect ? 'quiz__matching-dropzone--incorrect' : ''}`}
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                      onDrop={(e) => handleDrop(e, questionId, pairIndex)}
                    >
                      {matchedAnswer ? (
                        <>
                          <Typography>{matchedAnswer}</Typography>
                          {!submitted ? (
                            <IconButton
                              size="small"
                              onClick={() => handleClearMatch(questionId, pairIndex)}
                              className="quiz__matching-clear-btn"
                              aria-label="Remove match"
                            >
                              <Clear />
                            </IconButton>
                          ) : (
                            showResults &&
                            (isMatchCorrect ? (
                              <CheckCircle color="success" fontSize="small" sx={{ ml: 1 }} />
                            ) : (
                              <Cancel color="error" fontSize="small" sx={{ ml: 1 }} />
                            ))
                          )}
                        </>
                      ) : (
                        <Typography className="quiz__matching-placeholder">
                          {submitted && pair.right
                            ? `${t('quiz.correctMatch', 'Correct match')}: ${pair.right}`
                            : t('quiz.dropAnswerHere', 'Drop answer here')}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                );
              })}
            </Box>

            <Box sx={{ mt: 2 }}>
              <Typography variant="caption" color="text.secondary">
                {t(
                  'quiz.dragTip',
                  'Tip: Drag options from the top and drop them into the matching drop zones next to each description.'
                )}
              </Typography>
            </Box>
          </Box>
        );

      default:
        return (
          <Typography color="error">
            {t('quiz.unknownQuestionType', 'Unknown question type')}: {question.type}
          </Typography>
        );
    }
  };
  return (
    <Box className="quiz">
      <Typography variant="h5" gutterBottom className="quiz__main-title">
        {quizData.title || t('quiz.title', 'Quiz')}
      </Typography>

      {quizData.description && (
        <Typography variant="body1" color="text.secondary" paragraph>
          {quizData.description}
        </Typography>
      )}

      {showResults && (
        <Box mb={3}>
          <Alert
            severity={
              score >= (quizData.passingScore ?? 70) ||
              quizData.passingScore === 0 ||
              quizProgress?.isPassing
                ? 'success'
                : 'error'
            }
            icon={
              score >= (quizData.passingScore ?? 70) ||
              quizData.passingScore === 0 ||
              quizProgress?.isPassing ? (
                <CheckCircle />
              ) : (
                <Cancel />
              )
            }
          >
            {(score >= (quizData.passingScore ?? 70) ||
              quizData.passingScore === 0 ||
              quizProgress?.isPassing) &&
            quizData.passingScore === 0
              ? t(
                  'quiz.thankYouForSubmitting',
                  'Thank you for submitting the quiz. Please review your responses before proceeding to the next step.'
                )
              : score >= (quizData.passingScore ?? 70) || quizProgress?.isPassing
                ? `${t('quiz.congratulationsPassed', 'Congratulations! You passed the quiz')}: ${score.toFixed(0)}%`
                : `${t('quiz.sorryFailed', "Sorry, you didn't pass the quiz")}: ${score.toFixed(0)}%. ${t('quiz.pleaseTryAgain', 'Please try again.')}`}
          </Alert>
        </Box>
      )}

      <Box className="quiz__questions">
        {quizData.questions.map((question, index) => {
          const questionId = question.id || question._id || index;
          const isQuestionCorrect = showResults ? correctAnswers[questionId] : null;

          return (
            <Card key={index} className="quiz__question" sx={{ mb: 3 }}>
              <CardContent className="quiz__question-content">
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{
                    fontSize: '18px',
                    fontWeight: 'bold',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                  id={questionId}
                >
                  {quizData.questions.length > 1 ? `${index + 1}. ` : ''}
                  {question.question}
                  {showResults && isQuestionCorrect && (
                    <CheckCircle color="success" fontSize="small" />
                  )}
                  {showResults && !isQuestionCorrect && <Cancel color="error" fontSize="small" />}
                </Typography>
                {renderQuestion(question, index)}
              </CardContent>
            </Card>
          );
        })}
      </Box>

      <Box display="flex" justifyContent="flex-end" mt={3}>
        {!submitted ? (
          <Button
            variant="contained"
            color="primary"
            sx={{ textTransform: 'capitalize' }}
            onClick={handleSubmit}
            disabled={Object.keys(answers).length !== quizData.questions.length}
          >
            {t('quiz.finish', 'Finish Quiz')}
          </Button>
        ) : (
          <Box display="flex" alignItems="center" gap={2} flexWrap="wrap" justifyContent="flex-end">
            {score < (quizData.passingScore ?? 70) &&
              quizData.passingScore !== 0 &&
              !quizProgress?.isPassing && (
                <Alert severity="error" sx={{ flexGrow: 1 }} className="quiz__fail-alert">
                  {`${t('quiz.sorryFailed', "Sorry, you didn't pass the quiz")}: ${score.toFixed(0)}%. ${t('quiz.pleaseTryAgain', 'Please try again.')}`}
                </Alert>
              )}
            <Button
              variant="outlined"
              color="primary"
              onClick={handleRetry}
              disabled={
                score >= (quizData.passingScore ?? 70) ||
                quizData.passingScore === 0 ||
                quizProgress?.isPassing
              }
            >
              {t('quiz.tryAgain', 'Try Again')}
            </Button>
          </Box>
        )}
      </Box>
    </Box>
  );
};

Quiz.propTypes = {
  quiz: PropTypes.object.isRequired,
  onComplete: PropTypes.func,
  courseId: PropTypes.string.isRequired,
  chapterIndex: PropTypes.string.isRequired,
  topicIndex: PropTypes.string.isRequired,
  onScoreChange: PropTypes.func,
  isCompleted: PropTypes.bool,
  availableLanguages: PropTypes.array,
};

export default Quiz;
