import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { CheckCircle as CheckCircleIcon } from '@mui/icons-material';

const CourseObjectives = ({ objectives = [] }) => {
  return (
    <Box sx={{ mb: 6 }}>
      <Typography
        variant="h5"
        gutterBottom
        sx={{
          fontWeight: 600,
          position: 'relative',
          '&:after': {
            content: '""',
            position: 'absolute',
            bottom: -8,
            left: 0,
            width: 60,
            height: 3,
            bgcolor: 'primary.main',
            borderRadius: 1.5,
          },
        }}
      >
        What you'll learn
      </Typography>

      <Grid
        container
        spacing={3}
        sx={{
          mt: 3,
          width: '100%',
          margin: '0',
          '& .MuiGrid-item': { paddingTop: '24px', paddingLeft: '24px' },
        }}
      >
        {objectives.map((objective, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Paper
              elevation={0}
              sx={{
                p: 2.5,
                height: '100%',
                background: 'rgba(0, 102, 255, 0.03)',
                border: '1px solid',
                borderColor: 'rgba(0, 102, 255, 0.1)',
                borderRadius: 2,
                transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                display: 'flex',
                alignItems: 'flex-start',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(0, 102, 255, 0.08)',
                },
              }}
            >
              <CheckCircleIcon
                sx={{
                  color: 'primary.main',
                  fontSize: 20,
                  mt: 0.3,
                  opacity: 0.9,
                  flexShrink: 0,
                  mr: 1.5,
                }}
              />
              <Typography
                variant="body2"
                sx={{ color: 'text.primary', lineHeight: 1.6, fontWeight: 500 }}
              >
                {objective}
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default CourseObjectives;
