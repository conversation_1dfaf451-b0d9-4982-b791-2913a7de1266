@use '../../../../styles/abstracts/variables' as *;

.program-course-content {
  .MuiAccordion-root {
    &:before {
      display: none;
    }
    box-shadow: none;
    border: 1px solid $border-color;
    margin-bottom: $spacing-2;

    .MuiAccordionSummary-root {
      background-color: $bg-paper;
      
      &:hover {
        background-color: rgba($bg-light, 1);
      }

      .MuiTypography-subtitle1 {
        font-weight: $font-weight-medium;
      }

      .MuiTypography-caption {
        color: $text-secondary;
      }
    }

    .MuiAccordionDetails-root {
      padding: 0;

      .MuiList-root {
        padding: 0;
        .MuiButtonBase-root{
          border-bottom: 1px solid $border-color !important;
          &:last-child{
            border-bottom: none !important;
          }
        .MuiListItem-root {
          padding-top: $spacing-2;
          padding-bottom: $spacing-2;
          color: $text-primary;
          transition: all 0.3s ease;
          &:last-child {
            border-bottom: none !important;
          }
          &:hover{
            color: $primary-color !important;
            .MuiListItemIcon-root {
              .MuiSvgIcon-root {
                border-color: $primary-color;
                background-color: rgba($primary-color, 0.05);
                color: $primary-color !important;
              }
            }
          }
          .MuiListItemIcon-root {
            min-width: 24px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 1px solid $border-color;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: $spacing-3;
            background-color: $bg-light;
            transition: all 0.3s ease;

            .MuiSvgIcon-root {
              color: rgba($text-secondary, 0.5) !important;
              font-size: 16px;
              transition: all 0.3s ease;
            }

            &:hover {
              border-color: $primary-color;
              background-color: rgba($primary-color, 0.05);

              .MuiSvgIcon-root {
                color: $primary-color !important;
                transform: scale(1.1);
              }
            }
          }

          .MuiListItemText-root {
            .MuiTypography-body2 {
              font-weight: $font-weight-medium;
            }
          }

          .MuiChip-root {
            margin-left: $spacing-2;
            border-color: $border-color;
            background-color: transparent;
            
            .MuiChip-label {
              color: $text-secondary;
              font-size: $font-size-xs;
              padding: $spacing-1 $spacing-2;
            }

            &:hover {
              background-color: rgba($bg-light-dark, 0.1);
            }
          }
        }
      }
      }
    }
  }
}
