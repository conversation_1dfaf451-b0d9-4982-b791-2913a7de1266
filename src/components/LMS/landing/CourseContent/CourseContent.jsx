import React from 'react';
import {
  Typography,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  ButtonBase,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  PlayCircle as PlayCircleIcon,
  CheckCircle as CheckCircleIcon,
  AccessTime as AccessTimeIcon,
  TrendingUp as TrendingUpIcon,
  AutoAwesome as AutoAwesomeIcon,
  TextFields as TextIcon,
  Image as ImageIcon,
  Apps as AppsIcon,
  SlowMotionVideo as SlowMotionVideoIcon,
} from '@mui/icons-material';
import { getTopicTypeInfo } from '../../../../utils/topicTypeUtils';
import { useTranslation } from 'react-i18next';
import './CourseContent.scss';

// iconName'e göre uygun ikonu döndüren yardımcı fonksiyon
const getIconByName = (iconName) => {
  switch (iconName) {
    case 'video':
      return <SlowMotionVideoIcon />;
    case 'text':
      return <TextIcon />;
    case 'image':
      return <ImageIcon />;
    case 'playground':
      return <AppsIcon />;
    default:
      return <AppsIcon />;
  }
};

const CourseContent = ({
  chapters = [],
  expandedChapter,
  onChapterChange,
  onTopicClick,
  currentLanguage = 'en',
}) => {
  const { t } = useTranslation();

  return (
    <Box className="program-course-content">
      {chapters?.map((chapter, index) => (
        <Accordion
          key={index}
          expanded={expandedChapter === `chapter-${index}`}
          onChange={onChapterChange(`chapter-${index}`)}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ width: '100%' }}>
              <Typography variant="subtitle1">
                {t('courseContent.chapter')} {index + 1}: {chapter.title}
              </Typography>
              <Typography variant="caption">{chapter.topics.length} {t('courseContent.topics')}</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              {chapter.topics.map((topic, topicIndex) => {
                const { iconName } = getTopicTypeInfo(topic, { t });
                const topicIcon = getIconByName(iconName);

                return (
                  <ButtonBase
                    key={topicIndex}
                    onClick={() => {
                      onTopicClick(chapter._id, topic._id);
                    }}
                    sx={{ width: '100%', textAlign: 'left' }}
                  >
                    <ListItem
                      sx={{
                        width: '100%',
                        '&:hover': {
                          backgroundColor: 'rgba(0, 0, 0, 0.04)',
                          borderRadius: '4px',
                        },
                      }}
                    >
                      <ListItemIcon>{topicIcon}</ListItemIcon>
                      <ListItemText
                        primary={topic.title}
                        primaryTypographyProps={{
                          variant: 'body2',
                        }}
                      />
                    </ListItem>
                  </ButtonBase>
                );
              })}
            </List>
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );
};

export default CourseContent;
