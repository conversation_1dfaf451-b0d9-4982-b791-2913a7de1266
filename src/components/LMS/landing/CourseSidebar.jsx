import React from 'react';
import { Card, CardContent, CardMedia, Stack, Alert, Button, Box, Typography } from '@mui/material';
import { EmojiEvents as CertificateIcon } from '@mui/icons-material';
import { useSelector } from 'react-redux';

const CourseSidebar = ({ course, onEnroll }) => {
  const { user } = useSelector((state) => state.auth);
  const isAdmin = user?.admin === true;

  return (
    <Card sx={{ position: 'sticky', top: 24 }}>
      <Box
        component="img"
        src={course.coverImage}
        alt="cover photo"
        sx={{
          width: '100%',
          height: 200,
          objectFit: 'cover',
          borderBottom: '1px solid',
          borderColor: 'divider',
        }}
      />
      <CardContent>
        <Stack spacing={2}>
          {course.certificateAvailable && (
            <Alert icon={<CertificateIcon />} severity="success">
              Complete this course to earn a certificate
            </Alert>
          )}
          <Button
            fullWidth
            variant="contained"
            size="large"
            onClick={onEnroll}
            disabled={!isAdmin && course.isEnrolled}
            sx={{ bgcolor: '#0066ff', '&:hover': { bgcolor: '#0052cc' } }}
          >
            {isAdmin ? 'View Course' : course.isEnrolled ? 'Already Enrolled' : 'Enroll Now'}
          </Button>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default CourseSidebar;
