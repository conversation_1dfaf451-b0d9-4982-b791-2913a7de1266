import React, { useState, useRef, useEffect, forwardRef } from 'react';
import Hls from 'hls.js';
import YouTube from 'react-youtube';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Slider,
  Menu,
  MenuItem,
  Tooltip,
  CircularProgress,
  Button,
} from '@mui/material';
import {
  PlayArrow,
  Pause,
  VolumeUp,
  VolumeOff,
  Speed,
  Fullscreen,
  FullscreenExit,
} from '@mui/icons-material';
import './EnhancedVideoPlayer.scss';

const PLAYBACK_SPEEDS = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];

const EnhancedVideoPlayer = forwardRef(
  (
    {
      videoUrl,
      thumbnailUrl,
      onComplete,
      onProgress,
      courseId,
      chapterIndex,
      topicIndex,
      onTimeUpdate,
      onEnded,
      autoPlay = false,
      preventAutoPlayAfterComplete = false,
    },
    ref
  ) => {
    const { t } = useTranslation();
    const videoRef = useRef(null);
    const hlsRef = useRef(null);
    const playerContainerRef = useRef(null);
    const videoWrapperRef = useRef(null);
    const [playing, setPlaying] = useState(autoPlay && !preventAutoPlayAfterComplete);
    const [volume, setVolume] = useState(1);
    const [muted, setMuted] = useState(false);
    const [played, setPlayed] = useState(0);
    const [seeking, setSeeking] = useState(false);
    const [playbackSpeed, setPlaybackSpeed] = useState(1);
    const [speedMenuAnchor, setSpeedMenuAnchor] = useState(null);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);
    const [progressReported, setProgressReported] = useState(false);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [showControls, setShowControls] = useState(true);
    const lastKnownPositionRef = useRef(0);
    const positionUpdateTimeoutRef = useRef(null);
    const videoPositionRestored = useRef(false);
    const fullscreenTimeout = useRef(null);
    const controlsTimeoutRef = useRef(null);

    // YouTube video ayarları
    const youtubeOpts = {
      height: '100%',
      width: '100%',
      playerVars: {
        autoplay: 0,
        controls: 1,
        modestbranding: 1,
        rel: 0,
        origin: window.location.origin,
        enablejsapi: 1,
        playsinline: 1,
        fs: 1,
      },
    };

    // YouTube event handlers
    const handleYouTubeReady = (event) => {
      setIsLoading(false);
      setDuration(event.target.getDuration());
    };

    const handleYouTubeStateChange = (event) => {
      switch (event.data) {
        case YouTube.PlayerState.ENDED:
          if (onComplete) onComplete(1);
          if (onEnded) onEnded();
          break;
        case YouTube.PlayerState.PLAYING:
          setPlaying(true);
          setIsLoading(false);
          break;
        case YouTube.PlayerState.PAUSED:
          setPlaying(false);
          break;
      }
    };

    const handleYouTubeError = (error) => {
      setHasError(true);
    };

    const getYouTubeVideoId = (url) => {
      return url.split('/embed/')[1]?.split('?')[0];
    };

    // HLS yapılandırması
    useEffect(() => {
      if (!videoUrl) return;

      const video = videoRef.current;
      if (!video) return;

      // Safari tarayıcısı tespiti
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
      
      const cleanup = () => {
        if (positionUpdateTimeoutRef.current) {
          clearTimeout(positionUpdateTimeoutRef.current);
        }

        if (video) {
          if (video.currentTime > 0) {
            lastKnownPositionRef.current = video.currentTime;
          }
        }

        if (hlsRef.current) {
          hlsRef.current.destroy();
          hlsRef.current = null;
        }
      };

      cleanup();

      videoPositionRestored.current = false;

      const isHLS = videoUrl.includes('.m3u8');

      // Safari'de setHasError false olarak ayarla
      if (isSafari) {
        setHasError(false);
      }

      if (isHLS) {
        if (Hls.isSupported()) {
          const hls = new Hls({
            enableWorker: false,
            debug: false,
            maxBufferLength: 30,
            maxMaxBufferLength: 60,
            backBufferLength: 30,
            startPosition: -1,
            fragLoadingTimeOut: 20000,
            manifestLoadingTimeOut: 20000,
            manifestLoadingMaxRetry: 4,
            levelLoadingTimeOut: 20000,
            fragLoadingMaxRetry: 4,
          });

          hls.loadSource(videoUrl);
          hls.attachMedia(video);

          const safeSetPosition = (position) => {
            if (!video || !position || position <= 0 || videoPositionRestored.current) return;

            try {
              if (position > 0 && position < video.duration) {
                video.currentTime = position;
                videoPositionRestored.current = true;
              }
            } catch (error) {
              // Log kaldırıldı
            }
          };

          hls.on(Hls.Events.MANIFEST_PARSED, () => {
            setIsLoading(false);
            setHasError(false); // Hata durumunu sıfırla

            positionUpdateTimeoutRef.current = setTimeout(() => {
              safeSetPosition(lastKnownPositionRef.current);

              if ((playing || autoPlay) && !preventAutoPlayAfterComplete) {
                video.play().catch(console.error);
              }
            }, 200);
          });

          hls.on(Hls.Events.MEDIA_ATTACHED, () => {
            // Log kaldırıldı
          });

          hls.on(Hls.Events.FRAG_LOADED, () => {
            if (!videoPositionRestored.current && lastKnownPositionRef.current > 0) {
              positionUpdateTimeoutRef.current = setTimeout(() => {
                safeSetPosition(lastKnownPositionRef.current);
              }, 100);
            }
          });

          hls.on(Hls.Events.LEVEL_LOADED, () => {
            if (!videoPositionRestored.current && lastKnownPositionRef.current > 0) {
              positionUpdateTimeoutRef.current = setTimeout(() => {
                safeSetPosition(lastKnownPositionRef.current);
              }, 100);
            }
          });

          hls.on(Hls.Events.FRAG_CHANGED, () => {
            if (!videoPositionRestored.current && lastKnownPositionRef.current > 0) {
              safeSetPosition(lastKnownPositionRef.current);
            }
          });

          hls.on(Hls.Events.ERROR, (event, data) => {
            // Log kaldırıldı

            if (video.currentTime > 0) {
              lastKnownPositionRef.current = video.currentTime;
            }

            if (data.fatal) {
              switch (data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                  // Log kaldırıldı
                  hls.startLoad();
                  break;
                case Hls.ErrorTypes.MEDIA_ERROR:
                  // Log kaldırıldı
                  hls.recoverMediaError();
                  break;
                default:
                  // Log kaldırıldı
                  setHasError(true);
                  break;
              }

              videoPositionRestored.current = false;
            }
          });

          hlsRef.current = hls;
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
          // Safari için native HLS desteği
          setIsLoading(true);
          setHasError(false);
          
          video.src = videoUrl;
          
          // Safari için özel yükleme olayı dinleyicisi
          const handleSafariCanPlay = () => {
            setIsLoading(false);
            setHasError(false);
            
            if (!videoPositionRestored.current && lastKnownPositionRef.current > 0) {
              try {
                video.currentTime = lastKnownPositionRef.current;
                videoPositionRestored.current = true;
              } catch (e) {
                // Safari'de zaman ayarlarken oluşan hatayı gizle
              }
            }
            
            if ((playing || autoPlay) && !preventAutoPlayAfterComplete) {
              const playPromise = video.play();
              if (playPromise !== undefined) {
                playPromise.catch(error => {
                  // Otomatik oynatma engellendiyse hata gösterme
                  if (error.name !== 'NotAllowedError') {
                    console.error('Safari video playback error:', error);
                  }
                });
              }
            }
          };
          
          // Safari'de hatalı video olayını bastırmak için
          const handleSafariError = (error) => {
            // Safari'de bazı HLS videoları için geçici hatalar verebilir
            // Bu hatalar genellikle kısa süreli ağ sorunlarından kaynaklanır
            // Bu durumda hasError'u true olarak ayarlamıyoruz
            console.warn('Safari video error event occurred, but will be ignored:', error);
            
            // Video hala oynatılabilir mi kontrol et
            if (video.error && video.error.code === 4) {
              // MEDIA_ERR_SRC_NOT_SUPPORTED durumunda hata göster
              setHasError(true);
            }
          };
          
          video.addEventListener('canplay', handleSafariCanPlay, { once: true });
          
          if (isSafari) {
            video.addEventListener('error', handleSafariError);
          }
          
          // Yükleme zaman aşımı için güvenlik tedbiri
          const safariLoadTimeout = setTimeout(() => {
            if (isLoading) {
              setIsLoading(false);
            }
          }, 5000);
          
          return () => {
            clearTimeout(safariLoadTimeout);
            video.removeEventListener('canplay', handleSafariCanPlay);
            if (isSafari) {
              video.removeEventListener('error', handleSafariError);
            }
          };
        }
      } else {
        video.src = videoUrl;

        if (lastKnownPositionRef.current > 0) {
          video.addEventListener(
            'canplay',
            () => {
              if (!videoPositionRestored.current) {
                try {
                  video.currentTime = lastKnownPositionRef.current;
                  videoPositionRestored.current = true;
                } catch (e) {
                  // Safari için hata yönetimi
                  console.warn('Error setting video position:', e);
                }
              }
            },
            { once: true }
          );
        }
      }

      return cleanup;
    }, [videoUrl, autoPlay, preventAutoPlayAfterComplete]);

    // Video olayları
    useEffect(() => {
      const video = videoRef.current;
      if (!video) return;

      const handleTimeUpdate = () => {
        const currentTime = video.currentTime;
        const duration = video.duration || 0;
        const played = duration ? currentTime / duration : 0;

        if (currentTime > 0) {
          setCurrentTime(currentTime);
          setPlayed(played);
          lastKnownPositionRef.current = currentTime;
        }

        if (onTimeUpdate) {
          onTimeUpdate(currentTime);
        }
      };

      const handleLoadedMetadata = () => {
        setDuration(video.duration);
        setIsLoading(false);
        setHasError(false);

        if (!videoPositionRestored.current && lastKnownPositionRef.current > 0) {
          positionUpdateTimeoutRef.current = setTimeout(() => {
            try {
              video.currentTime = lastKnownPositionRef.current;
              videoPositionRestored.current = true;
            } catch (e) {
              // Safari için hata önleme
            }
          }, 100);
        }
      };

      const handleCanPlay = () => {
        setIsLoading(false);
        setHasError(false);
        
        if (!videoPositionRestored.current && lastKnownPositionRef.current > 0) {
          try {
            video.currentTime = lastKnownPositionRef.current;
            videoPositionRestored.current = true;
          } catch (e) {
            // Safari için hata önleme
          }
        }
      };

      const handleEnded = () => {
        if (onComplete) {
          onComplete(1);
        }
        if (onEnded) {
          onEnded();
        }
        setPlaying(false);
      };

      const handleError = (error) => {
        console.error('Video error:', error);
        
        // Safari'de video.error kontrol et
        if (video.error) {
          console.error('Video error code:', video.error.code);
          
          // MEDIA_ERR_SRC_NOT_SUPPORTED (4) hatası olmadığı sürece hata gösterme
          // Safari bazen geçici hatalar veriyor ve sonra düzeliyor
          if (video.error.code === 4) {
            setHasError(true);
          }
        } else {
          setHasError(true);
        }
        
        setIsLoading(false);
      };

      const handleSeeking = () => {
        if (video.currentTime > 0) {
          lastKnownPositionRef.current = video.currentTime;
        }
      };
      
      // Safari'de ilerleme kaydedeceğini belirtmek için bir kez çalıştır
      const handleLoadStart = () => {
        setHasError(false);
      };
      
      video.addEventListener('loadstart', handleLoadStart);
      video.addEventListener('timeupdate', handleTimeUpdate);
      video.addEventListener('loadedmetadata', handleLoadedMetadata);
      video.addEventListener('canplay', handleCanPlay);
      video.addEventListener('ended', handleEnded);
      video.addEventListener('error', handleError);
      video.addEventListener('seeking', handleSeeking);

      return () => {
        video.removeEventListener('loadstart', handleLoadStart);
        video.removeEventListener('timeupdate', handleTimeUpdate);
        video.removeEventListener('loadedmetadata', handleLoadedMetadata);
        video.removeEventListener('canplay', handleCanPlay);
        video.removeEventListener('ended', handleEnded);
        video.removeEventListener('error', handleError);
        video.removeEventListener('seeking', handleSeeking);

        if (video.currentTime > 0) {
          lastKnownPositionRef.current = video.currentTime;
        }

        if (positionUpdateTimeoutRef.current) {
          clearTimeout(positionUpdateTimeoutRef.current);
        }
      };
    }, [onTimeUpdate, onComplete, onEnded]);

    // Oynatma kontrolü
    useEffect(() => {
      const video = videoRef.current;
      if (!video) return;

      if (playing) {
        if (
          lastKnownPositionRef.current > 0 &&
          video.currentTime === 0 &&
          !videoPositionRestored.current
        ) {
          video.currentTime = lastKnownPositionRef.current;
          videoPositionRestored.current = true;
        }

        const playPromise = video.play();
        if (playPromise !== undefined) {
          playPromise.catch((error) => {
            // Log kaldırıldı
          });
        }
      } else {
        if (video.currentTime > 0) {
          lastKnownPositionRef.current = video.currentTime;
        }
        video.pause();
      }
    }, [playing]);

    // Ses kontrolü
    useEffect(() => {
      const video = videoRef.current;
      if (!video) return;

      video.volume = volume;
      video.muted = muted;
    }, [volume, muted]);

    // Oynatma hızı kontrolü
    useEffect(() => {
      const video = videoRef.current;
      if (!video) return;

      video.playbackRate = playbackSpeed;
    }, [playbackSpeed]);

    // Tam ekran kontrolü
    useEffect(() => {
      const container = playerContainerRef.current;

      const handleFullscreenChange = () => {
        const isFullscreenNow =
          document.fullscreenElement === container ||
          document.webkitFullscreenElement === container ||
          document.mozFullScreenElement === container ||
          document.msFullscreenElement === container;

        setIsFullscreen(isFullscreenNow);

        // Tarayıcı tam ekran API'lerinin yeniden hazır olması için kısa bir gecikme
        if (!isFullscreenNow) {
          clearTimeout(fullscreenTimeout.current);
          fullscreenTimeout.current = setTimeout(() => {
            // Tam ekrandan çıkıldığında video konteyneri boyutlarını düzelt
            if (container) {
              container.classList.remove('fullscreen-video-container');
            }
          }, 300);
        } else if (container) {
          container.classList.add('fullscreen-video-container');
        }
      };

      document.addEventListener('fullscreenchange', handleFullscreenChange);
      document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.addEventListener('mozfullscreenchange', handleFullscreenChange);
      document.addEventListener('MSFullscreenChange', handleFullscreenChange);

      return () => {
        document.removeEventListener('fullscreenchange', handleFullscreenChange);
        document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
        document.removeEventListener('MSFullscreenChange', handleFullscreenChange);

        if (fullscreenTimeout.current) {
          clearTimeout(fullscreenTimeout.current);
        }
      };
    }, []);

    // Kontrolleri otomatik gizleme
    useEffect(() => {
      const startControlsTimeout = () => {
        clearTimeout(controlsTimeoutRef.current);

        if (playing && !seeking) {
          controlsTimeoutRef.current = setTimeout(() => {
            setShowControls(false);
          }, 1000);
        }
      };

      if (playing) {
        startControlsTimeout();
      } else {
        setShowControls(true);
      }

      const handleMouseMove = () => {
        setShowControls(true);
        startControlsTimeout();
      };

      const videoWrapper = videoWrapperRef.current;
      if (videoWrapper) {
        videoWrapper.addEventListener('mousemove', handleMouseMove);
      }

      return () => {
        clearTimeout(controlsTimeoutRef.current);
        if (videoWrapper) {
          videoWrapper.removeEventListener('mousemove', handleMouseMove);
        }
      };
    }, [playing, seeking]);

    const handlePlayPause = () => {
      if (videoRef.current && videoRef.current.currentTime > 0) {
        lastKnownPositionRef.current = videoRef.current.currentTime;
      }
      setPlaying(!playing);
    };

    const handleVolumeChange = (event, newValue) => {
      setVolume(newValue);
      setMuted(newValue === 0);
    };

    const handleSeekChange = (event, newValue) => {
      const video = videoRef.current;
      if (!video) return;

      setSeeking(true);

      const newTime = newValue * duration;

      video.currentTime = newTime;
      lastKnownPositionRef.current = newTime;
      videoPositionRestored.current = true;

      setCurrentTime(newTime);
      setPlayed(newValue);
    };

    const handleSeekMouseDown = (event) => {
      setSeeking(true);
    };

    const handleSeekMouseUp = (event, newValue) => {
      setSeeking(false);

      if (!playing) {
        setPlaying(true);
      }
    };

    const handleSpeedChange = (speed) => {
      setPlaybackSpeed(speed);
      setSpeedMenuAnchor(null);
    };

    const toggleFullscreen = () => {
      if (!playerContainerRef.current) return;

      const container = playerContainerRef.current;

      try {
        if (!isFullscreen) {
          // Herhangi bir seçim yapılmasını veya tarayıcı davranışını önle
          container.classList.add('attempting-fullscreen');

          // Tam ekrana alma
          if (container.requestFullscreen) {
            container.requestFullscreen();
          } else if (container.webkitRequestFullscreen) {
            container.webkitRequestFullscreen();
          } else if (container.mozRequestFullScreen) {
            container.mozRequestFullScreen();
          } else if (container.msRequestFullscreen) {
            container.msRequestFullscreen();
          }
        } else {
          // Tam ekrandan çıkma
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
          }

          // Tam ekrandan çıkıldıktan sonra sınıfı kaldır
          setTimeout(() => {
            container.classList.remove('fullscreen-video-container');
            container.classList.remove('attempting-fullscreen');
          }, 100);
        }
      } catch (error) {
        // Log kaldırıldı
        container.classList.remove('attempting-fullscreen');
        container.classList.remove('fullscreen-video-container');
      }
    };

    const formatTime = (time) => {
      const minutes = Math.floor(time / 60);
      const seconds = Math.floor(time % 60);
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    };

    const handleVideoAreaClick = (event) => {
      // Video alanına tıklama - oynatma/durdurma toggle
      if (!seeking && videoRef.current) {
        handlePlayPause();
      }
    };

    const handleControlsClick = (event) => {
      // Kontrol çubuğuna tıklama olayının video alanına iletilmesini engelle
      event.stopPropagation();
    };

    return (
      <Box
        sx={{ width: '100%', position: 'relative' }}
        ref={playerContainerRef}
        className={`video-player-container ${isFullscreen ? 'fullscreen-video-container' : ''}`}
      >
        {!videoUrl ? (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              minHeight: '400px',
              bgcolor: '#f5f5f5',
              borderRadius: 1,
              p: 4,
              textAlign: 'center',
            }}
          >
            <Typography variant="body1" color="text.secondary">
              {t('videoPlayer.videoUnavailable')}
            </Typography>
          </Box>
        ) : videoUrl.includes('youtube.com/embed') ? (
          <Box sx={{ position: 'relative', width: '100%', paddingTop: '56.25%', bgcolor: '#000' }}>
            {isLoading && (
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'rgba(0, 0, 0, 0.7)',
                  zIndex: 2,
                }}
              >
                <CircularProgress color="primary" />
              </Box>
            )}
            {hasError ? (
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'rgba(0, 0, 0, 0.7)',
                  color: 'white',
                  zIndex: 2,
                  p: 3,
                  textAlign: 'center',
                }}
              >
                <Typography variant="h6" gutterBottom>
                  {t('videoPlayer.videoNotFound')}
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {t('videoPlayer.videoNotPlayable')}
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  href={`https://www.youtube.com/watch?v=${getYouTubeVideoId(videoUrl)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {t('videoPlayer.watchOnYoutube')}
                </Button>
              </Box>
            ) : (
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                }}
              >
                <YouTube
                  videoId={getYouTubeVideoId(videoUrl)}
                  opts={youtubeOpts}
                  onReady={handleYouTubeReady}
                  onStateChange={handleYouTubeStateChange}
                  onError={handleYouTubeError}
                  className="youtube-player"
                />
              </Box>
            )}
          </Box>
        ) : (
          <Box
            sx={{ position: 'relative', bgcolor: '#000', borderRadius: 1 }}
            ref={videoWrapperRef}
            className="video-wrapper"
            onMouseEnter={() => setShowControls(true)}
            onMouseMove={() => setShowControls(true)}
            onClick={handleVideoAreaClick}
          >
            {isLoading && (
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'rgba(0, 0, 0, 0.7)',
                  zIndex: 1,
                }}
              >
                <CircularProgress color="primary" />
              </Box>
            )}

            <video
              ref={videoRef}
              className={`main-video ${isFullscreen ? 'fullscreen-video' : ''}`}
              style={{
                width: '100%',
                height: isFullscreen ? '100vh' : 'auto',
                aspectRatio: isFullscreen ? 'unset' : '16/9',
                objectFit: isFullscreen ? 'contain' : 'fill',
              }}
              poster={thumbnailUrl}
              playsInline
              crossOrigin={videoUrl && videoUrl.includes('.m3u8') ? undefined : "anonymous"}
            />

            {hasError && (
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'rgba(0, 0, 0, 0.7)',
                  color: 'white',
                  zIndex: 1,
                }}
              >
                <Typography variant="body1">{t('videoPlayer.videoError')}</Typography>
              </Box>
            )}

            {/* Kontroller */}
            <Paper
              sx={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                bgcolor: 'transparent',
                p: 1.5,
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
                transition: 'opacity 0.2s',
                opacity: 0.8,
                '&:hover': { opacity: 1 },
                zIndex: 2,
              }}
              className={`video-controls ${showControls ? 'visible' : ''}`}
              onClick={handleControlsClick}
            >
              {/* İlerleme Çubuğu */}
              <Slider
                value={played}
                onChange={handleSeekChange}
                onMouseDown={handleSeekMouseDown}
                onChangeCommitted={handleSeekMouseUp}
                min={0}
                max={1}
                step={0.001}
                onClick={(e) => e.stopPropagation()}
                sx={{
                  color: '#fff',
                  height: 3,
                  '& .MuiSlider-thumb': {
                    width: 12,
                    height: 12,
                    transition: '0.2s',
                    '&:hover, &.Mui-focusVisible': {
                      boxShadow: '0 0 0 8px rgba(255, 255, 255, 0.16)',
                    },
                  },
                  '& .MuiSlider-rail': { opacity: 0.28 },
                }}
                className="progress-slider"
              />

              {/* Kontrol Butonları */}
              <Box
                sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                className="controls-wrapper"
              >
                <IconButton
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePlayPause();
                  }}
                  sx={{ color: 'white' }}
                  className="control-button"
                >
                  {playing ? <Pause /> : <PlayArrow />}
                </IconButton>

                <Box sx={{ width: 100, mx: 1 }} onClick={(e) => e.stopPropagation()}>
                  <Slider
                    value={volume}
                    onChange={handleVolumeChange}
                    min={0}
                    max={1}
                    step={0.1}
                    onClick={(e) => e.stopPropagation()}
                    sx={{ color: 'white', '& .MuiSlider-rail': { opacity: 0.28 } }}
                    className="volume-slider"
                  />
                </Box>

                <IconButton
                  onClick={(e) => {
                    e.stopPropagation();
                    setMuted(!muted);
                  }}
                  sx={{ color: 'white' }}
                  className="control-button"
                >
                  {muted ? <VolumeOff /> : <VolumeUp />}
                </IconButton>

                <Typography variant="body2" sx={{ color: 'white', mx: 1 }} className="time-display">
                  {formatTime(currentTime)} / {formatTime(duration)}
                </Typography>

                <Box sx={{ flexGrow: 1 }} className="flex-grow" />

                {/* Oynatma Hızı */}
                <Tooltip title={t('videoPlayer.playbackSpeed')}>
                  <IconButton
                    onClick={(e) => {
                      e.stopPropagation();
                      setSpeedMenuAnchor(e.currentTarget);
                    }}
                    sx={{ color: 'white' }}
                    className="control-button"
                  >
                    <Speed />
                  </IconButton>
                </Tooltip>

                {/* Tam Ekran Butonu */}
                <Tooltip
                  title={
                    isFullscreen ? t('videoPlayer.exitFullscreen') : t('videoPlayer.fullscreen')
                  }
                >
                  <IconButton
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleFullscreen();
                    }}
                    sx={{ color: 'white' }}
                    className="control-button"
                  >
                    {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
                  </IconButton>
                </Tooltip>
              </Box>
            </Paper>
          </Box>
        )}

        {/* Oynatma Hızı Menüsü */}
        <Menu
          anchorEl={speedMenuAnchor}
          open={Boolean(speedMenuAnchor)}
          onClose={() => setSpeedMenuAnchor(null)}
          PaperProps={{ sx: { mt: 1, minWidth: 120, boxShadow: '0 2px 10px rgba(0,0,0,0.1)' } }}
          className="playback-menu"
        >
          {PLAYBACK_SPEEDS.map((speed) => (
            <MenuItem
              key={speed}
              onClick={() => handleSpeedChange(speed)}
              selected={speed === playbackSpeed}
              sx={{
                fontSize: '0.95rem',
                py: 1,
                '&.Mui-selected': {
                  bgcolor: 'rgba(0, 0, 0, 0.04)',
                  '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.08)' },
                },
              }}
              className={`menu-item ${speed === playbackSpeed ? 'selected' : ''}`}
            >
              {speed}x
            </MenuItem>
          ))}
        </Menu>
      </Box>
    );
  }
);

EnhancedVideoPlayer.displayName = 'EnhancedVideoPlayer';

export default EnhancedVideoPlayer;
