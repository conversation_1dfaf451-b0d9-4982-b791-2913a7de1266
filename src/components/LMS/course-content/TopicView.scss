@use '../../../styles/abstracts/variables' as *;

.topic-view {
  display: grid;
  grid-template-columns: 1fr;
  width: 100%;
  background-color: $bg-light;
  transition: all 0.3s ease;

  @media (min-width: $tablet) {
    grid-template-columns: 330px 1fr;
    grid-template-areas: "sidebar main";
  }

  @media (min-width: $desktop) {
    grid-template-columns: 330px 1fr;
    grid-template-areas: "sidebar main";
  }

  &__main {
    width: 100%;
    background-color: $bg-paper;
    display: flex;
    flex-direction: column;
    grid-area: main;
    min-width: 0;
    transition: all 0.3s ease;
  }

  &__title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-3 $spacing-4;
    margin-bottom: $spacing-4;
    border-bottom: 1px solid $border-color;
    position: sticky;
    top: 0;
    background-color: $bg-light;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    @media (max-width: $tablet) {
      flex-wrap: wrap;
    }

    @media (min-width: $tablet) {
      margin-top: 0;
      flex-wrap: nowrap;
    }

    .topic-view__completion-message {
      margin: 0 !important;
      padding: 0 !important;
      background-color: transparent !important;
      border: none !important;
      box-shadow: none !important;
      display: flex;
      align-items: center;
      gap: $spacing-2;
      transition: all 0.3s ease;
      flex-shrink: 0;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .completion-message__icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: $success-color;
        color: $text-light;
        font-size: $font-size-md;
        flex-shrink: 0;
      }

      .completion-message__text {
        color: $success-color !important;
        font-weight: $font-weight-medium !important;
        font-size: $font-size-sm !important;
        white-space: nowrap;
      }
    }
  }

  &__title-wrapper {
    display: flex;
    align-items: center;
    gap: $spacing-2;
  }

  &__actions {
    display: flex;
    gap: $spacing-2;
    align-items: center;
    position: relative !important;
    padding-right: 0;
  }

  .navigation-footer__button {
    min-width: 100px;
    
    &--complete {
      min-width: 180px;
      text-transform: uppercase;
      font-weight: 600;
    }
  }

  &__title {
    font-size: $font-size-lg !important;
    font-weight: $font-weight-bold !important;
    margin: 0 !important;
  }

  &__content {
    flex: 1;
    width: calc(100% - $spacing-4 * 2);
    display: flex;
    flex-direction: column;
    min-height: 0;
    padding: 0 $spacing-4 $spacing-4 $spacing-4;
    &-scroll {
      flex: 1;
      overflow: auto;
      padding-bottom: $spacing-4;
      max-width: 1200px;
      margin: 0 auto;
      width: 100%;

      img, video, iframe {
        max-width: 100% !important;
        height: auto;
      }
    }
  }

  &__completion-message {
    margin: 0 !important;
    padding: $spacing-2 $spacing-3 !important;
    background-color: rgba($success-color, 0.1) !important;
    border: 1px solid $success-color !important;
    border-radius: $border-radius-md;
    display: flex;
    align-items: center;
    gap: $spacing-2;
    transition: all 0.3s ease;
    flex-shrink: 0;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .completion-message__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: $success-color;
      color: $text-light;
      font-size: $font-size-md;
      flex-shrink: 0;
    }

    .completion-message__text {
      color: $success-color !important;
      font-weight: $font-weight-medium !important;
      font-size: $font-size-sm !important;
      white-space: nowrap;
    }
  }

  &__loading {
    padding: $spacing-4;
    display: flex;
    justify-content: center;
  }

  &__error {
    padding: $spacing-4;
  }

  &__not-found {
    padding: $spacing-4;
  }
  
}
