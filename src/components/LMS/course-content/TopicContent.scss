@use '../../../styles/abstracts/variables' as *;

.topic-content {
  display: flex;
  flex-direction: column;
  &__data-container {
    box-shadow:none !important;
    border:none !important;
    padding: 0 !important;
    margin: 0 !important;
    --Paper-shadow:none !important;
    & > * {
      --Paper-shadow:none !important;
    }
  }
  &__video-container {
    margin-bottom: $spacing-4;
    width: 100%;
    position: relative;

    .youtube-wrapper {
      position: relative;
      width: 100%;
      padding-top: 56.25%; // 16:9 aspect ratio

      > div {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
  }

  &__quiz-container {
    margin-bottom: $spacing-4;
    border:none !important;
    box-shadow: none !important;
  }

  &__loading {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__usecase-container {
    display: flex;
    flex-direction: column;
  }

  &__single-app-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  // PDF container styles
  .pdf-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 141.42%; // A4 aspect ratio (1:1.4142)
    margin: 20px 0;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    
    iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }
  }
} 