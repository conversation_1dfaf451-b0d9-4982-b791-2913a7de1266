import React from 'react';
import { Box, Typography, IconButton, Breadcrumbs, Link } from '@mui/material';
import { NavigateBefore, NavigateNext } from '@mui/icons-material';

const TopicHeader = ({ topic, chapter, onPrevious, onNext, hasPrevious, hasNext }) => {
  return (
    <Box sx={{ mb: 3 }}>
      <Breadcrumbs sx={{ mb: 1 }}>
        <Link
          color="inherit"
          href="#"
          onClick={(e) => {
            e.preventDefault();
          }}
        >
          {chapter?.title || 'Chapter'}
        </Link>
        <Typography color="text.primary">{topic?.title || 'Topic'}</Typography>
      </Breadcrumbs>

      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <IconButton onClick={onPrevious} disabled={!hasPrevious} size="small">
          <NavigateBefore />
        </IconButton>

        <Typography variant="h5" component="h1" sx={{ flex: 1 }}>
          {topic?.title}
        </Typography>

        <IconButton onClick={onNext} disabled={!hasNext} size="small">
          <NavigateNext />
        </IconButton>
      </Box>

      {topic?.description && (
        <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
          {topic.description}
        </Typography>
      )}
    </Box>
  );
};

export default TopicHeader;
