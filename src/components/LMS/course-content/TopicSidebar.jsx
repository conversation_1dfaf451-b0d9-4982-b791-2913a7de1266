import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Box, Typography, Collapse } from '@mui/material';

import {
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  PlayCircle as PlayCircleIcon,
  SlowMotionVideo as SlowMotionVideoIcon,
  Image as ImageIcon,
  Apps as AppsIcon,
  PictureAsPdf as PictureAsPdfIcon,
  Quiz,
  ModelTraining,
  Checklist,
} from '@mui/icons-material';
import { Screenshot } from '@mui/icons-material';
import TextIcon from '@mui/icons-material/TextFormat';

import useCourseProgress from '../../../hooks/useCourseProgress';
import PropTypes from 'prop-types';
import './TopicSidebar.scss';

const getTopicIcon = (iconName) => {
  switch (iconName) {
    case 'video':
      return <SlowMotionVideoIcon className="topic-sidebar__topic-type-icon" />;
    case 'text':
      return <TextIcon className="topic-sidebar__topic-type-icon" />;
    case 'image':
      return <ImageIcon className="topic-sidebar__topic-type-icon" />;
    case 'playground':
      return <Screenshot className="topic-sidebar__topic-type-icon" />;
    case 'pdf':
      return <PictureAsPdfIcon className="topic-sidebar__topic-type-icon" />;
    case 'form':
      return <Checklist className="topic-sidebar__topic-type-icon" />;
    case 'quiz':
      return <Quiz className="topic-sidebar__topic-type-icon" />;
    case 'usecase':
      return <ModelTraining className="topic-sidebar__topic-type-icon" />;
    default:
      return <AppsIcon className="topic-sidebar__topic-type-icon" />;
  }
};

const TopicSidebar = ({
  course,
  currentChapterIndex,
  currentTopicIndex,
  progress: passedProgress,
  currentProgress: passedCurrentProgress,
  onTopicClick,
  sidebarKey,
}) => {
  const [expandedChapter, setExpandedChapter] = useState(currentChapterIndex);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const user = useSelector((state) => state.auth.user);

  // useCourseProgress'ten gelen değerleri kullan fakat dışarıdan sağlanan güncel progress verilerini de değerlendir
  const { completed, total, isChapterCompleted, isTopicCompleted, displayProgress, displayCourse } =
    useCourseProgress(course, user?._id, passedCurrentProgress);

  useEffect(() => {
    setExpandedChapter(currentChapterIndex);
  }, [currentChapterIndex]);

  // Eğer dışarıdan gelen ilerleme değeri varsa, onu kullan
  const actualProgress = passedProgress !== undefined ? passedProgress : displayProgress;

  const handleChapterClick = (chapterId) => {
    setExpandedChapter(expandedChapter === chapterId ? null : chapterId);
  };

  const handleCourseTitleClick = () => {
    if (displayCourse?._id) {
      navigate(`/course/${displayCourse._id}`);
    }
  };

  return (
    <Box className="topic-sidebar" key={sidebarKey}>
      <Box className="topic-sidebar__chapters">
        <Box className="topic-sidebar__course-info">
          <Typography
            variant="h4"
            className="topic-sidebar__course-name"
            onClick={handleCourseTitleClick}
            sx={{ cursor: 'pointer' }}
          >
            {displayCourse?.title}
          </Typography>

          <Box className="topic-sidebar__progress-section">
            <Box className="topic-sidebar__progress-bar">
              <Box className="topic-sidebar__progress-bar-container">
                <Box
                  className="topic-sidebar__progress-bar-fill"
                  sx={{ width: `${actualProgress}%` }}
                />
              </Box>
            </Box>

            <Box className="topic-sidebar__progress-info">
              <Typography variant="body2" className="topic-sidebar__progress-complete">
                {actualProgress}% {t('course.complete', 'Complete')}
              </Typography>
              <Typography variant="body2" className="topic-sidebar__progress-topics">
                {completed || 0} / {total || 0} {t('course.topics', 'topics')}
              </Typography>
            </Box>
          </Box>
        </Box>

        <Box>
          {displayCourse?.chapters?.map((chapter) => (
            <Box key={chapter._id} className="topic-sidebar__chapter">
              <Box
                className={`topic-sidebar__chapter-title ${
                  expandedChapter === chapter._id ? 'topic-sidebar__chapter-title--active' : ''
                }`}
                onClick={() => handleChapterClick(chapter._id)}
              >
                {isChapterCompleted(chapter._id) ? (
                  <CheckCircleIcon className="topic-sidebar__chapter-icon topic-sidebar__chapter-icon--completed" />
                ) : (
                  <RadioButtonUncheckedIcon className="topic-sidebar__chapter-icon" />
                )}
                <Typography className="topic-sidebar__chapter-text">{chapter.title}</Typography>
                {expandedChapter === chapter._id ? (
                  <ExpandLessIcon className="icon" />
                ) : (
                  <ExpandMoreIcon className="icon" />
                )}
              </Box>

              <Collapse in={expandedChapter === chapter._id} timeout="auto">
                <Box className="topic-sidebar__topics">
                  {chapter.topics?.map((topic) => {
                    const isActive = topic._id === currentTopicIndex;
                    const isCompleted = isTopicCompleted(chapter._id, topic._id);
                    const topicTypeIcon = getTopicIcon(topic.icon);

                    return (
                      <Box
                        key={topic._id}
                        className={`topic-sidebar__topic ${
                          isActive ? 'topic-sidebar__topic--active' : ''
                        }`}
                        onClick={() => {
                          // Yan menüden tıklandığı bilgisini session storage'a kaydet
                          sessionStorage.setItem('wasSidebarNavigation', 'true');

                          // Eğer topic tamamlandıysa, form ve quiz durumlarını sıfırlamadan topicClick işle
                          // Bu, topic'e tıklandığında checkFormAndQuizCompletion'ın doğru çalışmasını sağlar
                          if (isCompleted) {
                            sessionStorage.setItem('topicAlreadyCompleted', 'true');
                          } else {
                            sessionStorage.removeItem('topicAlreadyCompleted');
                          }

                          onTopicClick(chapter._id, topic._id);
                        }}
                      >
                        <Box className="topic-sidebar__topic-header">
                          {isCompleted ? (
                            <CheckCircleIcon className="topic-sidebar__topic-icon topic-sidebar__topic-icon--completed" />
                          ) : isActive ? (
                            <PlayCircleIcon className="topic-sidebar__topic-icon topic-sidebar__topic-icon--active" />
                          ) : (
                            <RadioButtonUncheckedIcon className="topic-sidebar__topic-icon" />
                          )}
                          <Typography className="topic-sidebar__topic-title">
                            {topic.title}
                          </Typography>
                        </Box>
                        <Box className="topic-sidebar__topic-content">
                          <Box className="topic-sidebar__topic-meta">
                            <Box className="topic-sidebar__topic-type">
                              <Box className="topic-sidebar__topic-type-icon">{topicTypeIcon}</Box>
                              <Typography
                                variant="caption"
                                className="topic-sidebar__topic-type-text"
                              >
                                {topic.type}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      </Box>
                    );
                  })}
                </Box>
              </Collapse>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

TopicSidebar.propTypes = {
  course: PropTypes.object,
  currentChapterIndex: PropTypes.string,
  currentTopicIndex: PropTypes.string,
  progress: PropTypes.number,
  currentProgress: PropTypes.object,
  onTopicClick: PropTypes.func,
  sidebarKey: PropTypes.string,
};

export default TopicSidebar;
