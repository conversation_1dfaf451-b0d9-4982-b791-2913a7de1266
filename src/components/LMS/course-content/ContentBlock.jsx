import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  RadioGroup,
  Radio,
  FormControlLabel,
  Checkbox,
  FormGroup,
  Card,
  CardContent,
  Grid,
  IconButton,
  LinearProgress,
  FormHelperText,
  Paper,
  Chip,
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import DeleteIcon from '@mui/icons-material/Delete';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

import Quiz from '../Quiz';
import EnhancedVideoPlayer from '../EnhancedVideoPlayer';
// VideoPlayer alias tanımlayalım
const VideoPlayer = EnhancedVideoPlayer;
import YouTube from 'react-youtube';
import axios from 'axios';
import SingleApp from '../../../pages/SingleApp';
import ImageUsecase from '../../../domains/apps/components/usecases/ImageUsecase';
import DallEPlayground from '../../../components/DallEPlayground/DallEPlayground';
import ChatGPTPlayground from '../../../components/ChatGPTPlayground/ChatGPTPlayground';
import HeygenVideoCreator from '../../../components/HeygenVideoCreator';
import DownloadIcon from '@mui/icons-material/Download';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import './ContentBlock.scss';
import { CDS_API_URL, CDS_API_KEY } from '../../../config-global';
import { useSubmitFormResponseMutation } from '../../../redux/services/form-response';
import { useFormsQuery } from '../../../redux/services/form-service';

// PDF.js importları
import * as pdfjsLib from 'pdfjs-dist';

// PDF worker ayarları
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`;

// PDF görüntüleyici bileşeni
const PdfViewer = ({ pdfUrl, title, description }) => {
  const [numPages, setNumPages] = useState(null);
  const [pdfError, setPdfError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [pdfDoc, setPdfDoc] = useState(null);
  const [scale, setScale] = useState(1.0);
  const [currentPage, setCurrentPage] = useState(1);
  const canvasContainerRef = useRef(null);
  const pdfContainerRef = useRef(null);
  const { t } = useTranslation();

  // PDF yükle
  const loadPDF = async (url) => {
    try {
      setIsLoading(true);

      // PDF yükleme görevi oluştur
      const loadingTask = pdfjsLib.getDocument({
        url: url,
        cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
        cMapPacked: true,
      });

      // Yükleme ilerleme durumu
      loadingTask.onProgress = function (progress) {
        return (
          <LinearProgress value={(progress.loaded / progress.total) * 100} variant="determinate" />
        );
      };

      const pdf = await loadingTask.promise;

      // PDF yüklendi, state'leri güncelle
      setPdfDoc(pdf);
      setNumPages(pdf.numPages);
      setCurrentPage(1);
      setIsLoading(false);
      setPdfError(null);
    } catch (err) {
      console.error('PDF loading error:', err.message);
      setPdfError(err);
      setIsLoading(false);
    }
  };

  // PDF sayfalarını render et
  const renderAllPages = async () => {
    if (!pdfDoc || !canvasContainerRef.current) return;

    try {
      // İçeriği temizle
      canvasContainerRef.current.innerHTML = '';

      // Her sayfayı render et
      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        const page = await pdfDoc.getPage(pageNum);

        // Sayfa container'ı oluştur
        const pageContainer = document.createElement('div');
        pageContainer.className = 'pdf-page-container';
        pageContainer.setAttribute('data-page-number', pageNum);

        // Sayfa numarasını oluştur
        const pageNumberElement = document.createElement('div');
        pageNumberElement.className = 'pdf-page-number';
        pageNumberElement.textContent = t('courseContent.pdfPageOf', { pageNum, numPages });
        pageContainer.appendChild(pageNumberElement);

        // Canvas oluştur
        const canvas = document.createElement('canvas');
        canvas.className = 'pdf-canvas';
        pageContainer.appendChild(canvas);

        // Container'ı ekle
        canvasContainerRef.current.appendChild(pageContainer);

        // PDF sayfasını render et
        const context = canvas.getContext('2d', { alpha: false });
        const viewport = page.getViewport({
          scale: scale,
          rotation: 0,
          offsetX: 0,
          offsetY: 0,
        });

        // Canvas boyutunu viewport'a göre ayarla - yüksek çözünürlük için 2x kullan
        const pixelRatio = window.devicePixelRatio || 1;
        canvas.width = viewport.width * pixelRatio;
        canvas.height = viewport.height * pixelRatio;

        // Canvas stilini scale değerine göre dinamik olarak ayarla - tam genişlik yerine gerçek boyut
        canvas.style.width = `${viewport.width}px`;
        canvas.style.height = `${viewport.height}px`;
        canvas.style.maxWidth = 'none';

        // Yüksek çözünürlük için context'i scale et
        context.scale(pixelRatio, pixelRatio);

        // Sayfayı render et
        await page.render({
          canvasContext: context,
          viewport: viewport,
          enableWebGL: true, // WebGL hızlandırma
          annotationMode: 2, // ENABLE_FORMS = 2
          intent: 'print',
        }).promise;
      }
    } catch (err) {
      console.error('PDF pages rendering error:', err);
      setPdfError(err);
    }
  };

  // Zoom fonksiyonu
  const handleZoom = (delta) => {
    setScale((prevScale) => {
      // Zoom adımı
      const zoomStep = 0.25;

      // Zoom değişimi ve sınırları ayarla
      let newScale;
      if (delta < 0) {
        // Zoom out - 0.25 azalt, minimum 0.5
        newScale = Math.max(0.5, prevScale - zoomStep);
      } else {
        // Zoom in - 0.25 artır, maximum 4.0
        newScale = Math.min(4.0, prevScale + zoomStep);
      }

      // Zoom değişimini tamamla
      return Math.round(newScale * 100) / 100; // İki ondalık basamağa yuvarla
    });
  };

  // Tam ekran fonksiyonu
  const handleFullscreen = () => {
    if (pdfContainerRef.current) {
      if (
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      ) {
        // Farklı tarayıcılar için uyumlu exitFullscreen çağrısı
        const exitMethod =
          document.exitFullscreen ||
          document.webkitExitFullscreen ||
          document.mozCancelFullScreen ||
          document.msExitFullscreen;

        if (exitMethod) {
          exitMethod.call(document).catch((err) => console.error('Fullscreen exit error:', err));
        }
      } else {
        // Farklı tarayıcılar için uyumlu requestFullscreen çağrısı
        const element = pdfContainerRef.current;
        const requestMethod =
          element.requestFullscreen ||
          element.webkitRequestFullscreen ||
          element.mozRequestFullScreen ||
          element.msRequestFullscreen;

        if (requestMethod) {
          requestMethod.call(element).catch((err) => console.error('Fullscreen error:', err));
        } else {
          console.warn('Browser does not support fullscreen mode');
        }
      }
    }
  };

  // Önceki sayfaya git
  const handlePrevPage = () => {
    if (currentPage > 1) {
      const newPage = currentPage - 1;
      setCurrentPage(newPage);
      scrollToPage(newPage);
    }
  };

  // Sonraki sayfaya git
  const handleNextPage = () => {
    if (currentPage < numPages) {
      const newPage = currentPage + 1;
      setCurrentPage(newPage);
      scrollToPage(newPage);
    }
  };

  // Belirli bir sayfaya scroll yapar
  const scrollToPage = (pageNum) => {
    const pageElem = canvasContainerRef.current?.querySelector(`[data-page-number="${pageNum}"]`);
    if (pageElem) {
      pageElem.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // PDF URL'si değiştiğinde PDF'i yükle
  useEffect(() => {
    if (pdfUrl) {
      loadPDF(pdfUrl);
    }
  }, [pdfUrl]);

  // PDF yüklendiğinde ve scale değiştiğinde sayfaları render et
  useEffect(() => {
    if (pdfDoc) {
      renderAllPages();
    }
  }, [pdfDoc, scale]);

  // Scroll olayını dinle
  useEffect(() => {
    const handleScroll = () => {
      if (!canvasContainerRef.current) return;

      // Ekranda görünen sayfayı tespit et
      const containerRect = canvasContainerRef.current.getBoundingClientRect();
      const pageElements = canvasContainerRef.current.querySelectorAll('.pdf-page-container');

      // En çok görünen sayfayı bul
      let mostVisiblePage = 1;
      let maxVisibleArea = 0;

      pageElements.forEach((pageElem) => {
        const pageRect = pageElem.getBoundingClientRect();
        const pageNum = parseInt(pageElem.getAttribute('data-page-number'));

        // Sayfa ve container arasındaki kesişim alanını hesapla
        const visibleTop = Math.max(pageRect.top, containerRect.top);
        const visibleBottom = Math.min(pageRect.bottom, containerRect.bottom);
        const visibleHeight = Math.max(0, visibleBottom - visibleTop);

        if (visibleHeight > maxVisibleArea) {
          maxVisibleArea = visibleHeight;
          mostVisiblePage = pageNum;
        }
      });

      if (mostVisiblePage !== currentPage) {
        setCurrentPage(mostVisiblePage);
      }
    };

    const container = canvasContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [currentPage]);

  return (
    <Box sx={{ p: 2 }}>
      {title && (
        <Typography variant="h5" gutterBottom>
          {title}
        </Typography>
      )}
      {description && (
        <Typography variant="body1" color="text.secondary" gutterBottom>
          {description}
        </Typography>
      )}

      {pdfUrl ? (
        <Box
          ref={pdfContainerRef}
          sx={{
            mt: 3,
            width: '100%',
            height: '80vh',
            border: '1px solid #ddd',
            borderRadius: 1,
            overflow: 'hidden',
            position: 'relative',
            bgcolor: 'background.paper',
            '.pdf-controls': {
              display: 'flex',
              alignItems: 'center',
              padding: '8px 16px',
              borderBottom: '1px solid #ddd',
              backgroundColor: '#f5f5f5',
            },
            '.pdf-control-button': {
              mx: 0.5,
            },
            '.page-info': {
              mx: 2,
              fontSize: '0.875rem',
            },
            '.zoom-info': {
              mx: 2,
              fontSize: '0.875rem',
            },
            '.controls-divider': {
              height: '24px',
              width: '1px',
              backgroundColor: '#ddd',
              mx: 2,
            },
            '.canvas-container': {
              height: 'calc(100% - 48px)',
              overflowY: 'auto',
              padding: '16px',
            },
            '.pdf-page-container': {
              position: 'relative',
              marginBottom: '24px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            },
            '.pdf-page-number': {
              fontSize: '0.75rem',
              color: '#666',
              marginBottom: '8px',
            },
            '.pdf-canvas': {
              border: '1px solid #ddd',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            },
          }}
        >
          {isLoading ? (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
              }}
            >
              <CircularProgress />
            </Box>
          ) : pdfError ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                p: 3,
                textAlign: 'center',
              }}
            >
              <Typography color="error" gutterBottom>
                {t('courseContent.pdf.errorLoading')}
              </Typography>
              <Button
                variant="outlined"
                color="primary"
                onClick={() => window.open(pdfUrl, '_blank')}
              >
                {t('courseContent.pdf.openInNewTab')}
              </Button>
            </Box>
          ) : (
            <>
              <div className="pdf-controls">
                <IconButton
                  onClick={handlePrevPage}
                  className="pdf-control-button"
                  disabled={currentPage <= 1}
                >
                  <ArrowBackIcon />
                </IconButton>

                <Typography className="page-info">
                  {currentPage} / {numPages}
                </Typography>

                <IconButton
                  onClick={handleNextPage}
                  className="pdf-control-button"
                  disabled={currentPage >= numPages}
                >
                  <ArrowForwardIcon />
                </IconButton>

                <div className="controls-divider"></div>

                <IconButton onClick={() => handleZoom(-1)} className="pdf-control-button">
                  <ZoomOutIcon />
                </IconButton>

                <Typography className="zoom-info">{Math.round(scale * 100)}%</Typography>

                <IconButton onClick={() => handleZoom(1)} className="pdf-control-button">
                  <ZoomInIcon />
                </IconButton>

                <IconButton onClick={handleFullscreen} className="pdf-control-button">
                  <FullscreenIcon />
                </IconButton>

                <Box sx={{ marginLeft: 'auto' }}>
                  <Button
                    variant="outlined"
                    color="primary"
                    startIcon={<DownloadIcon />}
                    onClick={() => window.open(pdfUrl, '_blank')}
                  >
                    {t('courseContent.pdf.download')}
                  </Button>
                </Box>
              </div>

              <div className="canvas-container" ref={canvasContainerRef}></div>
            </>
          )}
        </Box>
      ) : (
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: '1px dashed',
            borderColor: 'divider',
            borderRadius: 1,
            textAlign: 'center',
          }}
        >
          <Typography variant="body1" color="text.secondary">
            {t('courseContent.pdf.notFound')}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

const ContentBlock = ({
  block,
  courseId,
  chapterIndex,
  topicIndex,
  onQuizComplete,
  onQuizScoreChange,
  isTopicCompleted,
  onVideoEnded,
  onTimeUpdate,
  onUsecaseGenerate,
  onContentCompleted,
  onFormSubmit,
}) => {
  const [useCaseLoading, setUseCaseLoading] = useState(false);
  const [useCaseError, setUseCaseError] = useState(null);
  const [useCase, setUseCase] = useState(null);
  const [pdfError, setPdfError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [pdfBase64, setPdfBase64] = useState(null);
  const [isContentCompleted, setIsContentCompleted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isProcessing = useRef(false); // İşlem durumunu takip etmek için useRef ekledik

  // Çeviri için i18n hook'unu ekle
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || 'en';
  const convertedLanguage =
    currentLanguage === 'en' ? 'english' : currentLanguage === 'de' ? 'german' : '';

  // Form state'leri
  const [formValues, setFormValues] = useState({});
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  const [formId, setFormId] = useState(null);

  // File upload state'leri
  const [uploadedFiles, setUploadedFiles] = useState({});
  const [fileUploadErrors, setFileUploadErrors] = useState({});

  // Add Form Response Mutation'ı tanımla
  const [submitFormResponse, { isLoading: isSubmittingForm }] = useSubmitFormResponseMutation();

  // Form verilerini almak için query hook'u
  const { data: formData } = useFormsQuery(
    { formId, language: currentLanguage },
    { skip: !formId }
  );

  // Form ID'sini güncelle
  useEffect(() => {
    const localizedBlock = getLocalizedBlock();
    if (localizedBlock?.form_id) {
      setFormId(localizedBlock.form_id);
    }
  }, [block, currentLanguage]);

  useEffect(() => {
    setIsSubmitting(isSubmittingForm);
  }, [isSubmittingForm]);

  // Blok verilerini çevrilmiş haline güncelle
  const getLocalizedBlock = () => {
    if (!block) return block;

    // Eğer block'ta çeviri yoksa orijinal block'u döndür
    if (!block.translations) return block;

    // İlgili dildeki çeviriyi bul veya varsayılan olarak İngilizce çeviriyi kullan
    const translation = block.translations[currentLanguage] || block.translations.en || {};

    // Temel özellikleri çevirilerle birleştir
    const localizedBlock = {
      ...block,
      title: translation.title || block.title,
      description: translation.description || block.description,
    };

    // İçerik türüne göre çevirileri uygula
    if (block.type === 'text' && block.textContent && translation.textContent) {
      localizedBlock.textContent = {
        ...block.textContent,
        content: translation.textContent.content || block.textContent.content,
      };
    } else if (block.type === 'quiz' && block.quiz && translation.quiz) {
      // Quiz sorularını çevir
      if (block.quiz.questions && translation.quiz && translation.quiz.questions) {
        localizedBlock.quiz = {
          ...block.quiz,
          questions: block.quiz.questions.map((question, index) => {
            const translatedQuestion =
              translation.quiz.questions.length > index ? translation.quiz.questions[index] : {};
            return {
              ...question,
              question: translatedQuestion.question || question.question,
              options:
                (question.options &&
                  question.options.map((option, optIndex) => {
                    const translatedOption =
                      translatedQuestion.options && translatedQuestion.options.length > optIndex
                        ? translatedQuestion.options[optIndex]
                        : {};
                    return {
                      ...option,
                      option: translatedOption.option || option.option,
                    };
                  })) ||
                question.options,
            };
          }),
        };
      }
    } else if (block.type === 'form' && block.form_data && translation.form_data) {
      // Form verilerini çevir - burada doğrudan translations->chapters->topics->contentBlocks içinden
      // form verilerini almalıyız, block.form_data yerine

      // Öncelikle, form_data'dan gelen çevrilmiş başlık ve açıklama ile temel yapıyı oluştur
      localizedBlock.form_data = {
        ...(block.form_data || {}), // Temel yapıyı orijinalden al
        title: translation.form_data.title || (block.form_data ? block.form_data.title : ''),
        description:
          translation.form_data.description || (block.form_data ? block.form_data.description : ''),
      };

      // Eğer çeviride form_data.topics varsa, orayı kullan
      if (translation.form_data.topics && Array.isArray(translation.form_data.topics)) {
        // Konuları ve alanları ile birlikte doğrudan çevirilerden al
        localizedBlock.form_data.topics = translation.form_data.topics.map(
          (translatedTopic, topicIndex) => {
            // Orjinal form datasını bul (varsa)
            const originalTopic = block.form_data?.topics?.[topicIndex];

            // Her konu için tüm çevrilmiş bilgileri doğru şekilde al
            const result = {
              ...translatedTopic,
              _id:
                translatedTopic._id || (originalTopic ? originalTopic._id : `topic-${topicIndex}`),
              // Fields alanını daha detaylı işle - eksik alanları (name, type) orijinalden al
              fields: Array.isArray(translatedTopic.fields)
                ? translatedTopic.fields.map((translatedField, fieldIndex) => {
                    // Orjinal fieldi bul
                    const originalField = originalTopic?.fields?.[fieldIndex];

                    // Field değerlerini birleştir - eksik değerler için orijinali kullan
                    return {
                      ...originalField, // Önce orijinal değerleri al
                      ...translatedField, // Sonra çeviriden gelen değerleri ekle
                      // Kritik alanlar yoksa varsayılan değerler belirle
                      name:
                        translatedField.name ||
                        (originalField ? originalField.name : `field-${fieldIndex}`),
                      type: translatedField.type || (originalField ? originalField.type : 'select'),
                      required:
                        translatedField.required ||
                        (originalField ? originalField.required : false),
                      _id:
                        translatedField._id ||
                        (originalField ? originalField._id : `field-${topicIndex}-${fieldIndex}`),
                      options: Array.isArray(translatedField.options)
                        ? translatedField.options.map((translatedOption, optionIndex) => {
                            const originalOption = originalField?.options?.[optionIndex];
                            return {
                              ...originalOption,
                              ...translatedOption,
                              value:
                                translatedOption.value ||
                                (originalOption ? originalOption.value : `${optionIndex + 1}`),
                              _id:
                                translatedOption._id ||
                                (originalOption
                                  ? originalOption._id
                                  : `option-${topicIndex}-${fieldIndex}-${optionIndex}`),
                            };
                          })
                        : originalField?.options || [],
                    };
                  })
                : originalTopic?.fields || [],
            };
            return result;
          }
        );
      }
      // Eğer çeviride topics yoksa ancak orijinalde varsa, orijinali kullan
      else if (!localizedBlock.form_data.topics && block.form_data && block.form_data.topics) {
        localizedBlock.form_data.topics = block.form_data.topics;
      }
    }

    // Video içeriklerinin çevirilerini işle
    if (block.videoContent && translation.videoContent) {
      // Video içeriğini çevir - özellikle videoContent.hlsData.streamingUrls yolları
      localizedBlock.videoContent = {
        ...block.videoContent,
        title: translation.videoContent.title || block.videoContent.title,
        description: translation.videoContent.description || block.videoContent.description,
      };

      // HLS verilerini çevir
      if (translation.videoContent.hlsData) {
        localizedBlock.videoContent.hlsData = {
          ...block.videoContent.hlsData,
        };

        // StreamingUrls'i çevir
        if (translation.videoContent.hlsData.streamingUrls) {
          localizedBlock.videoContent.hlsData.streamingUrls =
            translation.videoContent.hlsData.streamingUrls;
        }
      }
    }

    // PDF içeriklerinin çevirilerini işle
    if (block.pdfContent && translation.pdfContent) {
      localizedBlock.pdfContent = {
        ...block.pdfContent,
        url: translation.pdfContent.url || block.pdfContent.url,
        title: translation.pdfContent.title || block.pdfContent.title,
        description: translation.pdfContent.description || block.pdfContent.description,
      };
    }

    // Usecase türünü çevir
    if (
      (block.type === 'usecase' || block.usecase_slug || block.usecase_type) &&
      translation.usecase_slug
    ) {
      localizedBlock.usecase_slug = translation.usecase_slug;
    }

    // Playground içeriklerinin çevirilerini işle
    if (block.playground_type && translation.playground_type) {
      localizedBlock.playground_type = translation.playground_type;

      if (translation.initialPrompt) {
        localizedBlock.initialPrompt = translation.initialPrompt;
      }
    }

    return localizedBlock;
  };

  // Form değerlerini değiştirme işleyicisi
  const handleFormChange = (fieldName, value) => {
    setFormValues((prev) => ({
      ...prev,
      [fieldName]: value,
    }));
  };

  // File upload işleyicisi
  const handleFileUpload = (fieldName, files) => {
    const fileList = Array.from(files);

    // Dosya boyutu ve türü validasyonu
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain', 'text/csv'
    ];

    const validFiles = [];
    const errors = [];

    fileList.forEach((file, index) => {
      if (file.size > maxSize) {
        errors.push(`${file.name}: ${t('courseContent.fileTooLarge')}`);
      } else if (!allowedTypes.includes(file.type)) {
        errors.push(`${file.name}: ${t('courseContent.fileTypeNotAllowed')}`);
      } else {
        validFiles.push(file);
      }
    });

    if (errors.length > 0) {
      setFileUploadErrors(prev => ({
        ...prev,
        [fieldName]: errors
      }));
      return;
    }

    // Hataları temizle
    setFileUploadErrors(prev => ({
      ...prev,
      [fieldName]: null
    }));

    // Dosyaları state'e kaydet
    setUploadedFiles(prev => ({
      ...prev,
      [fieldName]: validFiles
    }));

    // Form değerlerini güncelle (dosya isimlerini string olarak)
    const fileNames = validFiles.map(file => file.name).join(', ');
    handleFormChange(fieldName, fileNames);
  };

  // Dosya silme işleyicisi
  const handleFileRemove = (fieldName, fileIndex) => {
    setUploadedFiles(prev => {
      const currentFiles = prev[fieldName] || [];
      const newFiles = currentFiles.filter((_, index) => index !== fileIndex);

      if (newFiles.length === 0) {
        const { [fieldName]: removed, ...rest } = prev;
        return rest;
      }

      return {
        ...prev,
        [fieldName]: newFiles
      };
    });

    // Form değerlerini güncelle
    const currentFiles = uploadedFiles[fieldName] || [];
    const newFiles = currentFiles.filter((_, index) => index !== fileIndex);
    const fileNames = newFiles.map(file => file.name).join(', ');
    handleFormChange(fieldName, fileNames);
  };

  // Form gönderme işleyicisi
  const handleFormSubmit = async (e) => {
    e.preventDefault();
    setFormSubmitting(true);
    setFormErrors({});

    try {
      // Boş cevapları kontrol et
      const errors = {};

      // Tüm form alanlarını topla
      const allFields = [];
      formData?.topics?.forEach((topic) => {
        if (topic.fields && Array.isArray(topic.fields)) {
          allFields.push(...topic.fields);
        }
      });

      // Tüm alanları kontrol et, sadece gerekli olanları değil
      allFields.forEach((field) => {
        const value = formValues[field.name];

        // Dosya alanları için özel validasyon
        if (field.type === 'file') {
          const files = uploadedFiles[field.name] || [];
          const fileErrors = fileUploadErrors[field.name] || [];

          // Dosya upload hatası varsa
          if (fileErrors.length > 0) {
            errors[field.name] = fileErrors.join(', ');
          }
          // Gerekli alan ama dosya yüklenmemişse
          else if (field.required && files.length === 0) {
            errors[field.name] = t('courseContent.fileUpload.required', 'Dosya yüklenmesi zorunludur');
          }
        } else {
          // Diğer alan türleri için normal validasyon
          const isEmpty =
            value === undefined ||
            value === null ||
            value === '' ||
            (Array.isArray(value) && value.length === 0) ||
            (typeof value === 'string' && value.trim() === '');

          // Gerekli alanlar için mutlaka hata göster
          if (field.required && isEmpty) {
            errors[field.name] = t('common.fieldRequired');
          }
          // Gerekli olmayan ama boş gönderilemeyecek alanlar için kontrol
          else if (isEmpty && !field.allowEmpty) {
            errors[field.name] = t('common.fieldCannotBeEmpty');
          }
        }
      });

      // Eğer hata varsa formu gönderme
      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        toast.error(t('common.pleaseCompleteRequiredFields'), {
          position: 'bottom-right',
          autoClose: 3000,
        });
        setFormSubmitting(false);
        return;
      }

      // Form tipi dönüşüm fonksiyonu
      const getFormType = (type) => {
        switch (type?.toLowerCase()) {
          case 'ideation':
            return 'ideation';
          case 'feedback':
            return 'feedback';
          case 'help':
            return 'help';
          case 'assessment':
            return 'assessment';
          default:
            return 'feedback';
        }
      };

      // Yanıt tipi dönüşüm fonksiyonu
      const getResponseType = (type) => {
        switch (type?.toLowerCase()) {
          case 'text':
            return 'text';
          case 'number':
            return 'number';
          case 'email':
            return 'email';
          case 'textarea':
            return 'textarea';
          case 'select':
            return 'select';
          case 'radio':
            return 'radio';
          case 'checkbox':
            return 'checkbox';
          case 'date':
            return 'date';
          case 'file':
            return 'file';
          case 'feedback':
            return 'radio';
          default:
            return 'text';
        }
      };

      // Form yanıtlarını hazırlama fonksiyonu
      const prepareFormResponses = () => {
        return (formData?.topics || []).flatMap((topic) =>
          (topic.fields || []).map((field) => {
            const value = formValues[field.name];
            let safeValue = value === undefined || value === null ? '' : value;

            // Dosya alanları için özel işlem
            if (field.type === 'file') {
              const files = uploadedFiles[field.name] || [];
              if (files.length > 0) {
                // Dosya bilgilerini JSON string olarak kaydet
                safeValue = JSON.stringify(files.map(file => ({
                  name: file.name,
                  size: file.size,
                  type: file.type,
                  lastModified: file.lastModified
                })));
              } else {
                safeValue = '';
              }
            }

            return {
              fieldId: field._id,
              name: field.name,
              type: getResponseType(field.type),
              value: safeValue,
            };
          })
        );
      };

      // Tüm form yanıtlarını hazırla
      const responses = prepareFormResponses();

      await submitFormResponse({
        formId: block._id,
        formType: getFormType(formData?.type),
        title: formData?.title || 'Form Response',
        description: formData?.description || 'Form submission from course content',
        responses,
      }).then((response) => {
        setFormSubmitted(true);

        // Form başarıyla gönderildikten sonra onFormSubmit fonksiyonunu çağır
        if (onFormSubmit) {
          onFormSubmit(block._id);
        }
      });
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setFormSubmitting(false);
    }
  };

  // Form alanını render etme fonksiyonu
  const renderField = (field) => {
    if (!field || !field.type) {
      console.error('Geçersiz veya eksik field verisi:', field);
      return null;
    }

    const fieldId = `field-${field._id || 'unknown'}`;
    const isRequired = field.required || false;
    const fieldName = field.name || `field-${field._id || 'unknown'}`;
    const fieldError = formErrors[fieldName];

    // Eksik veri kontrolü
    if (!fieldName) {
      console.error('Field için name değeri bulunamadı:', field);
      return null;
    }

    // field.name değerini benzersiz tanımlayıcı olarak kullan
    const fieldValue = fieldName in formValues ? formValues[fieldName] : '';

    // Seçenekler kontrolü
    const hasValidOptions =
      field.options && Array.isArray(field.options) && field.options.length > 0;

    switch (field.type) {
      case 'text':
      case 'email':
      case 'number':
        return (
          <TextField
            key={fieldId}
            id={fieldId}
            label={field.label}
            type={field.type}
            value={fieldValue}
            onChange={(e) => handleFormChange(fieldName, e.target.value)}
            fullWidth
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
            error={!!fieldError}
            helperText={fieldError}
          />
        );

      case 'textarea':
        return (
          <TextField
            key={fieldId}
            id={fieldId}
            label={field.label}
            multiline
            rows={4}
            value={fieldValue}
            onChange={(e) => handleFormChange(fieldName, e.target.value)}
            fullWidth
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
            error={!!fieldError}
            helperText={fieldError}
          />
        );

      case 'select':
        if (!hasValidOptions) {
          console.error('Select field için geçerli seçenekler bulunamadı:', field);
          return (
            <Typography color="error" variant="body2" gutterBottom>
              {t('courseContent.noOptionsFound')}
            </Typography>
          );
        }

        return (
          <FormControl
            key={fieldId}
            fullWidth
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
            error={!!fieldError}
          >
            <InputLabel id={`${fieldId}-label`}>{field.label}</InputLabel>
            <Select
              labelId={`${fieldId}-label`}
              id={fieldId}
              value={fieldValue}
              label={field.label}
              onChange={(e) => handleFormChange(fieldName, e.target.value)}
            >
              {field.options &&
                field.options.map((option) => (
                  <MenuItem
                    key={option._id || option.value || `option-${option.label}`}
                    value={option.value || `value-${option.label}`}
                  >
                    {option.label}
                  </MenuItem>
                ))}
            </Select>
            {fieldError && <FormHelperText error>{fieldError}</FormHelperText>}
          </FormControl>
        );

      case 'radio':
        return (
          <FormControl
            key={fieldId}
            component="fieldset"
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
            error={!!fieldError}
          >
            <Typography className="content-block__form-feedback-title" variant="subtitle1">
              {field.label}
            </Typography>
            <RadioGroup
              value={fieldValue}
              onChange={(e) => handleFormChange(fieldName, e.target.value)}
            >
              {field.options &&
                field.options.map((option) => (
                  <FormControlLabel
                    key={option._id || option.value}
                    value={option.value}
                    control={<Radio />}
                    label={option.label}
                    className="content-block__form-control-label"
                  />
                ))}
            </RadioGroup>
            {fieldError && <FormHelperText error>{fieldError}</FormHelperText>}
          </FormControl>
        );

      case 'checkbox':
        // Checkbox için değerleri array olarak tutuyoruz
        const checkboxValues = Array.isArray(fieldValue) ? fieldValue : [];

        return (
          <FormControl
            key={fieldId}
            component="fieldset"
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
          >
            <Typography className="content-block__form-feedback-title" variant="subtitle1">
              {field.label}
            </Typography>
            <FormGroup>
              {field.options &&
                field.options.map((option) => (
                  <FormControlLabel
                    key={option._id || option.value}
                    control={
                      <Checkbox
                        checked={checkboxValues.includes(option.value)}
                        onChange={(e) => {
                          const newValues = e.target.checked
                            ? [...checkboxValues, option.value]
                            : checkboxValues.filter((v) => v !== option.value);
                          handleFormChange(fieldName, newValues);
                        }}
                      />
                    }
                    label={option.label}
                    className="content-block__form-control-label"
                  />
                ))}
            </FormGroup>
            {/* {isRequired && <FormHelperText>Bu alan zorunludur</FormHelperText>} */}
          </FormControl>
        );

      case 'feedback':
        const feedbackValue = fieldValue !== '' ? Number(fieldValue) : '';

        return (
          <FormControl
            key={fieldId}
            component="fieldset"
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
            className="content-block__form-feedback"
          >
            <Typography className="content-block__form-feedback-title" variant="subtitle1">
              {field.label}
            </Typography>
            <Box sx={{ mt: 2, mb: 1 }}>
              <Grid container alignItems="center">
                {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((value) => (
                  <Grid item key={value}>
                    <Button
                      variant={feedbackValue === value ? 'contained' : 'outlined'}
                      color={feedbackValue === value ? 'primary' : 'inherit'}
                      onClick={() => handleFormChange(fieldName, value)}
                      sx={{
                        borderRadius: '4px',
                      }}
                    >
                      {value}
                    </Button>
                  </Grid>
                ))}
              </Grid>
            </Box>
            {/* {isRequired && <FormHelperText>Bu alan zorunludur</FormHelperText>} */}
          </FormControl>
        );

      case 'file':
        const currentFiles = uploadedFiles[fieldName] || [];
        const fileErrors = fileUploadErrors[fieldName] || [];

        return (
          <FormControl
            key={fieldId}
            fullWidth
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
            error={!!fieldError || fileErrors.length > 0}
          >
            <Typography variant="subtitle1" gutterBottom>
              {field.label}
              {isRequired && <span style={{ color: 'red' }}> *</span>}
            </Typography>

            {field.description && (
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {field.description}
              </Typography>
            )}

            {/* File Upload Area */}
            <Box
              sx={{
                border: '2px dashed',
                borderColor: fileErrors.length > 0 ? 'error.main' : 'divider',
                borderRadius: 1,
                p: 3,
                textAlign: 'center',
                backgroundColor: formSubmitted ? 'action.disabledBackground' : 'background.paper',
                cursor: formSubmitted ? 'not-allowed' : 'pointer',
                transition: 'border-color 0.3s ease',
                '&:hover': {
                  borderColor: formSubmitted ? 'divider' : 'primary.main',
                },
              }}
              onClick={() => {
                if (!formSubmitted) {
                  document.getElementById(`file-input-${fieldId}`).click();
                }
              }}
            >
              <input
                id={`file-input-${fieldId}`}
                type="file"
                multiple
                style={{ display: 'none' }}
                onChange={(e) => {
                  if (e.target.files && e.target.files.length > 0) {
                    handleFileUpload(fieldName, e.target.files);
                  }
                }}
                disabled={formSubmitted}
                accept="image/*,.pdf,.doc,.docx,.txt,.csv"
              />

              <CloudUploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                {t('courseContent.fileUpload.clickToUpload', 'Click to upload')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('courseContent.fileUpload.supportedFormats', 'Supported formats: JPG, PNG, PDF, DOC, TXT, CSV')}
              </Typography>
              <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                {t('courseContent.fileUpload.maxSize', 'Max file size: 10MB')}
              </Typography>
            </Box>

            {/* Uploaded Files List */}
            {currentFiles.length > 0 && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  {t('courseContent.fileUpload.uploadedFiles', 'Uploaded Files')}:
                </Typography>
                {currentFiles.map((file, index) => (
                  <Chip
                    key={index}
                    icon={<AttachFileIcon />}
                    label={`${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`}
                    onDelete={formSubmitted ? undefined : () => handleFileRemove(fieldName, index)}
                    deleteIcon={<DeleteIcon />}
                    variant="outlined"
                    sx={{ mr: 1, mb: 1 }}
                    disabled={formSubmitted}
                  />
                ))}
              </Box>
            )}

            {/* Error Messages */}
            {(fieldError || fileErrors.length > 0) && (
              <FormHelperText error>
                {fieldError || fileErrors.join(', ')}
              </FormHelperText>
            )}
          </FormControl>
        );

      default:
        return null;
    }
  };

  // Usecase verilerini çek
  useEffect(() => {
    const fetchUseCase = async () => {
      // Önce block.type === 'usecase' kontrolü yap
      if (block.type === 'usecase' || block.usecase_slug || block.usecase_type) {
        const usecaseSlug =
          block.usecase_slug ||
          (block.type === 'usecase' && block.usecase_slug ? block.usecase_slug : null) ||
          (block.usecase_type ? block.usecase_type : null);

        if (usecaseSlug) {
          setUseCaseLoading(true);
          setUseCaseError(null);
          try {
            // API çağrısını güncelle - doğru URL'yi kullan
            const response = await axios.get(`${CDS_API_URL}/usecase/slug/${usecaseSlug}`, {
              headers: {
                'Content-Type': 'application/json',
                'x-api-key': CDS_API_KEY,
                // Eğer authentication header'ı gerekiyorsa ekleyin
                // 'Authorization': `Bearer ${token}`
              },
            });

            if (response.data && response.data.data) {
              setUseCase(response.data.data);
            } else if (response.data && response.data.usecase) {
              // Alternatif veri yapısı kontrolü
              setUseCase(response.data.usecase);
            } else if (response.data && typeof response.data === 'object' && !response.data.error) {
              // Doğrudan response.data'yı kullan
              setUseCase(response.data);
            } else {
              setUseCaseError('Invalid usecase data format');
            }
          } catch (error) {
            setUseCaseError(
              error.response?.data?.message || error.message || 'Failed to load usecase'
            );
          } finally {
            setUseCaseLoading(false);
          }
        } else {
          setUseCaseError('Missing usecase_slug for usecase block');
        }
      }
    };

    fetchUseCase();
  }, [block.usecase_slug, block.type, block.usecase_type]);

  // YouTube video ID'sini çıkaran yardımcı fonksiyon
  const getYouTubeVideoId = (url) => {
    if (!url) return null;
    const patterns = [
      /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i,
      /^[a-zA-Z0-9_-]{11}$/,
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }

    return null;
  };

  // Video URL'sini çıkaran yardımcı fonksiyon - çeviriler için güncellenmiş
  const getVideoUrl = (block) => {
    if (!block.videoContent) {
      return null;
    }

    // Blok'un çevrilmiş halini kullan
    const localizedBlock = getLocalizedBlock();

    // YouTube video için
    if (localizedBlock.videoContent.type === 'youtube') {
      return localizedBlock.videoContent.youtubeUrl;
    }

    // HLS video için
    if (localizedBlock.videoContent.type === 'hls') {
      if (
        !localizedBlock.videoContent.hlsData ||
        !localizedBlock.videoContent.hlsData.streamingUrls
      ) {
        return null;
      }

      const streamingUrls = localizedBlock.videoContent.hlsData.streamingUrls;

      // Durum 1: streamingUrls bir dizi ise
      if (Array.isArray(streamingUrls) && streamingUrls.length > 0) {
        const firstUrl = streamingUrls[0];

        // Durum 1.1: İlk eleman bir nesne ve paths özelliği varsa
        if (typeof firstUrl === 'object' && firstUrl !== null && firstUrl.paths) {
          if (Array.isArray(firstUrl.paths) && firstUrl.paths.length > 0) {
            return firstUrl.paths[0];
          } else if (typeof firstUrl.paths === 'string') {
            return firstUrl.paths;
          }
        }
        // Durum 1.2: İlk eleman doğrudan bir string ise
        else if (typeof firstUrl === 'string') {
          return firstUrl;
        }
      }
      // Durum 2: streamingUrls bir nesne ise
      else if (typeof streamingUrls === 'object' && streamingUrls !== null) {
        // Durum 2.1: paths özelliği bir dizi ise
        if (Array.isArray(streamingUrls.paths) && streamingUrls.paths.length > 0) {
          return streamingUrls.paths[0];
        }
        // Durum 2.2: paths özelliği bir string ise
        else if (typeof streamingUrls.paths === 'string') {
          return streamingUrls.paths;
        }
        // Durum 2.3: _id özelliği varsa (özel durum)
        else if (
          streamingUrls._id &&
          Array.isArray(streamingUrls.paths) &&
          streamingUrls.paths.length > 0
        ) {
          return streamingUrls.paths[0];
        }
      }
      // Durum 3: streamingUrls bir string ise
      else if (typeof streamingUrls === 'string') {
        return streamingUrls;
      }
    }

    // Eski veri yapısı kontrolü
    if (localizedBlock.videoContent.url) {
      return localizedBlock.videoContent.url;
    }

    return null;
  };

  const handleComplete = async () => {
    console.log('🔄 Content completed called for block:', block._id, {
      type: block.type,
      playground_type: block.playground_type,
      displayType: block.displayType,
      isContentCompleted,
      isProcessing: isProcessing.current,
      isTopicCompleted,
    });

    // İşlem zaten yapılıyorsa, tamamlanma işlemini atla
    if (isProcessing.current) {
      console.log('⚠️ Already processing. Skipping completion.');
      return;
    }

    // Playground türünü belirle
    const isGptPlayground =
      block.playground_type === 'gpt' ||
      block.displayType === 'playground-gpt' ||
      block.type === 'playground-gpt';

    // Eğer zaten tamamlanmışsa ve playground türü değilse işlemi atla
    // Not: ChatGPT Playground için her zaman işleme devam et
    if (!isGptPlayground && (isContentCompleted || isTopicCompleted)) {
      console.log('⚠️ Content already completed. Skipping completion.');
      return;
    }

    try {
      isProcessing.current = true;
      console.log('✅ İşlem başladı - isProcessing true yapıldı');

      // İçeriği tamamlanmış olarak işaretle
      setIsContentCompleted(true);
      console.log('🏁 İçerik tamamlandı olarak işaretlendi');

      // Ebeveyn komponente bildirimi sadece bir kez gönder
      // NOT: İnteraktif içerik türünü belirtmek için onContentCompleted'e block ID'yi gönderiyoruz
      if (onContentCompleted) {
        if (isGptPlayground) {
          console.log('📢 Calling onContentCompleted with interactive block ID:', block._id);
          // Playground tamamlandığını belirtmek için onContentCompleted'e block ID gönder
          onContentCompleted('interactive', block._id);
        } else {
          console.log('📢 Calling onContentCompleted (generic)');
          onContentCompleted();
        }
      }

      // Eğer bu bir usecase ise, onUsecaseGenerate de çağrılmalı
      if (
        onUsecaseGenerate &&
        (block.type === 'usecase' || block.usecase_slug || block.usecase_type)
      ) {
        console.log('🔍 Calling onUsecaseGenerate with block ID:', block._id);
        onUsecaseGenerate(block._id);
      }
    } catch (error) {
      console.error('❌ Error in handleComplete:', error);
      toast.error(t('courseContent.errorCompletingContent', 'Error completing content!'), {
        position: 'bottom-right',
        autoClose: 3000,
      });
    } finally {
      // İşlemin bittiğini belirt - biraz gecikme ile
      setTimeout(() => {
        isProcessing.current = false;
        console.log('✅ Processing completed for block:', block._id);
      }, 500);
    }
  };

  // Heygen Video Creator için postMessage listener'ı ekle
  useEffect(() => {
    const handleMessage = (event) => {
      // HeygenVideoCreator'dan gelen mesajları kontrol et
      if (event.data && event.data.type === 'HEYGEN_VIDEO_COMPLETED') {
        // HandleComplete'i çağır
        handleComplete();
      }
    };

    // Event listener'ı ekle
    window.addEventListener('message', handleMessage);

    // Cleanup
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []); // handleComplete fonksiyonu component içinde oluşturulduğu için bağımlılık olarak eklemeye gerek yok, eslint-disable-line react-hooks/exhaustive-deps

  // PDF URL'sini işleme fonksiyonu
  const processPdfUrl = (url) => {
    if (url && url.includes('aibsassets.blob.core.windows.net')) {
      return url.split('?')[0];
    }

    return url;
  };

  // HTML içeriğini decode et ve PDF içeriğini düzelt
  const decodeAndFixPdfContent = (content) => {
    if (!content) return '';

    // HTML karakterlerini decode et
    const decodedContent = content
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#039;/g, "'");

    // PDF URL'lerini düzelt
    const fixedContent = decodedContent.replace(
      /(href=["'])(.*?\.pdf)(["'])/gi,
      (match, p1, p2, p3) => {
        const fixedUrl = processPdfUrl(p2);
        return `${p1}${fixedUrl}${p3}`;
      }
    );

    return fixedContent;
  };

  // PDF'i base64 olarak yükle
  const loadPdfAsBase64 = async (url) => {
    try {
      setIsLoading(true);
      const response = await fetch(url, {
        method: 'GET',
        mode: 'cors',
        headers: {
          Accept: 'application/pdf',
          Origin: window.location.origin,
        },
        credentials: 'include',
      });

      if (!response.ok) {
        // PDF'i proxy üzerinden indirmeyi dene
        const proxyUrl = `${CDS_API_URL}/proxy/pdf?url=${encodeURIComponent(url)}`;
        const proxyResponse = await fetch(proxyUrl, {
          headers: {
            'x-api-key': CDS_API_KEY,
          },
        });

        if (!proxyResponse.ok) {
          throw new Error('PDF not loaded');
        }

        const blob = await proxyResponse.blob();
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            const base64data = reader.result;
            setPdfBase64(base64data);
            setIsLoading(false);
            resolve(base64data);
          };
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });
      }

      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64data = reader.result;
          setPdfBase64(base64data);
          setIsLoading(false);
          resolve(base64data);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('Error loading PDF:', error);
      setPdfError(error);
      setIsLoading(false);
      throw error;
    }
  };

  // PDF sayfası değiştiğinde yeniden render et
  useEffect(() => {
    if (
      block.type === 'pdf' ||
      (block.textContent?.content && block.textContent.content.includes('.pdf'))
    ) {
      const hrefMatch = block.textContent?.content?.match(/href=["']([^"']+)["']/);
      if (hrefMatch && hrefMatch[1]) {
        const pdfUrl = processPdfUrl(hrefMatch[1]);
        loadPdfAsBase64(pdfUrl);
      }
    }
  }, [block]);

  const renderContent = () => {
    // Blok içeriğini çevirilerle birlikte almak için getLocalizedBlock fonksiyonunu kullan
    const localizedBlock = getLocalizedBlock();

    // Usecase içeriği için usecase_slug veya usecase_type alanını kontrol et
    if (
      localizedBlock.type === 'usecase' ||
      localizedBlock.usecase_slug ||
      localizedBlock.usecase_type
    ) {
      // Usecase slug'ı belirle
      const usecaseSlug =
        localizedBlock.usecase_slug ||
        (localizedBlock.type === 'usecase' && localizedBlock.usecase_slug
          ? localizedBlock.usecase_slug
          : null) ||
        (localizedBlock.usecase_type ? localizedBlock.usecase_type : null);

      if (!usecaseSlug) {
        return (
          <Box sx={{ p: 2, textAlign: 'center', color: 'error.main' }}>
            <Typography variant="body1">{t('courseContent.usecaseSlugNotFound')}</Typography>
            <Typography variant="body2">
              Usecase content: {JSON.stringify(localizedBlock, null, 2)}
            </Typography>
          </Box>
        );
      }

      return (
        <Box sx={{ p: 2 }}>
          <>
            {useCaseLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : useCaseError ? (
              <Box sx={{ color: 'error.main', p: 2 }}>
                <Typography color="error" gutterBottom>
                  {t('courseContent.errorLoadingUsecase')}
                </Typography>
                <Typography variant="body2">{useCaseError}</Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  Usecase slug: {usecaseSlug}
                </Typography>
                <Button
                  variant="outlined"
                  color="primary"
                  size="small"
                  sx={{ mt: 2 }}
                  onClick={() => {
                    // API'yi tekrar çağır
                    const fetchUseCase = async () => {
                      setUseCaseLoading(true);
                      setUseCaseError(null);
                      try {
                        const response = await axios.get(
                          `${CDS_API_URL}/usecase/slug/${usecaseSlug}`,
                          {
                            headers: {
                              'Content-Type': 'application/json',
                              'x-api-key': CDS_API_KEY,
                            },
                          }
                        );
                        if (response.data && response.data.data) {
                          setUseCase(response.data.data);
                        } else if (response.data && response.data.usecase) {
                          setUseCase(response.data.usecase);
                        } else if (
                          response.data &&
                          typeof response.data === 'object' &&
                          !response.data.error
                        ) {
                          setUseCase(response.data);
                        } else {
                          setUseCaseError('Invalid usecase data format');
                        }
                      } catch (error) {
                        setUseCaseError(
                          error.response?.data?.message || error.message || 'Failed to load usecase'
                        );
                      } finally {
                        setUseCaseLoading(false);
                      }
                    };
                    fetchUseCase();
                  }}
                >
                  {t('common.tryAgain')}
                </Button>
              </Box>
            ) : useCase ? (
              <>
                {localizedBlock.usecase_type === 'image' || localizedBlock?.type === 'image' ? (
                  <ImageUsecase
                    data={{
                      ...localizedBlock,
                      api_type: 'dalle',
                      'dall-e_settings':
                        useCase?.translations?.[convertedLanguage]?.['dall-e_settings'] ||
                        useCase?.['dall-e_settings'],
                      translations: useCase?.translations || {},
                    }}
                    id={localizedBlock?._id}
                    disableBack={true}
                    onlyShowUsecase={false}
                    initialPrompt={
                      useCase?.translations?.[convertedLanguage]?.['dall-e_settings']
                        ?.form_fields?.[0]?.default_value ||
                      (useCase?.translations?.[convertedLanguage]?.['dall-e_settings']
                        ?.form_fields &&
                      useCase?.translations?.[convertedLanguage]?.['dall-e_settings']?.form_fields
                        .length > 0
                        ? useCase?.translations?.[convertedLanguage]?.['dall-e_settings']
                            ?.form_fields[0]?.default_value
                        : '') ||
                      ''
                    }
                    onGenerate={() => {
                      handleComplete();
                    }}
                  />
                ) : (
                  <SingleApp
                    slug={usecaseSlug}
                    onGenerate={(completed = true) => {
                      console.log('SingleApp: onGenerate tetiklendi', completed);
                      console.log(
                        'isTopicCompleted:',
                        isTopicCompleted,
                        'isContentCompleted:',
                        isContentCompleted
                      );
                      console.log(
                        'SingleApp: handleComplete doğrudan çağrılıyor (Topic durumundan bağımsız)'
                      );
                      handleComplete();
                    }}
                    disableAutoComplete={true}
                    isCompleted={isTopicCompleted || isContentCompleted}
                    useCase={localizedBlock}
                    onlyShowUsecase={false}
                    isLoading={useCaseLoading}
                    skipInitialLoad={false}
                  />
                )}
              </>
            ) : (
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <Typography color="text.secondary">
                  {t('courseContent.noUsecaseDataFor')} {usecaseSlug}
                </Typography>
                <Typography variant="body2">Usecase slug: {usecaseSlug}</Typography>
                <Button
                  variant="outlined"
                  color="primary"
                  size="small"
                  sx={{ mt: 2 }}
                  onClick={() => {
                    // API'yi tekrar çağır
                    const fetchUseCase = async () => {
                      setUseCaseLoading(true);
                      setUseCaseError(null);
                      try {
                        const response = await axios.get(`${CDS_API_URL}/usecase/${usecaseSlug}`, {
                          headers: {
                            'Content-Type': 'application/json',
                            'x-api-key': CDS_API_KEY,
                          },
                        });
                        if (response.data && response.data.data) {
                          setUseCase(response.data.data);
                        } else if (response.data && response.data.usecase) {
                          setUseCase(response.data.usecase);
                        } else if (
                          response.data &&
                          typeof response.data === 'object' &&
                          !response.data.error
                        ) {
                          setUseCase(response.data);
                        } else {
                          setUseCaseError('Invalid usecase data format');
                        }
                      } catch (error) {
                        setUseCaseError(
                          error.response?.data?.message || error.message || 'Failed to load usecase'
                        );
                      } finally {
                        setUseCaseLoading(false);
                      }
                    };
                    fetchUseCase();
                  }}
                >
                  {t('common.tryAgain')}
                </Button>
              </Box>
            )}
          </>
        </Box>
      );
    }

    // Handle playground_type for content blocks that should display as playground
    if (localizedBlock.playground_type) {
      return (
        <Box sx={{ p: 2 }}>
          {localizedBlock.description && (
            <Typography variant="body1" color="text.secondary" gutterBottom>
              {localizedBlock.description}
            </Typography>
          )}

          <Box>
            {localizedBlock.playground_type === 'gpt' ? (
              <ChatGPTPlayground
                initialPrompt={localizedBlock.initialPrompt || ''}
                onGenerate={() => {
                  // İlerleyişi kaydetmek için düz çağrı - koşulsuz olarak çağırıyoruz
                  console.log('💥 ChatGPT Playground tamamlandı - handleComplete çağrılıyor');
                  handleComplete();
                }}
                disableBack={true}
                temperature={localizedBlock.temperature || 0.7}
                topP={localizedBlock.topP || 1}
                frequencyPenalty={localizedBlock.frequencyPenalty || 0}
                presencePenalty={localizedBlock.presencePenalty || 0}
                showNotification={true}
                defaultSettingsOpen={true}
                notificationMessage={t('courseContent.playground.completed')}
              />
            ) : localizedBlock.playground_type === 'dalle' ? (
              <DallEPlayground
                initialPrompt={localizedBlock.initialPrompt || ''}
                onGenerate={isTopicCompleted || isContentCompleted ? undefined : handleComplete}
                disableBack={true}
              />
            ) : localizedBlock.playground_type === 'heygen' ? (
              <HeygenVideoCreator
                id={localizedBlock.heygen_id || ''}
                onGenerate={() => handleComplete()}
              />
            ) : (
              <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body1">{t('courseContent.playgroundNotSupported')}</Typography>
              </Box>
            )}
          </Box>
        </Box>
      );
    }

    // Handle displayType for playground content blocks
    if (
      localizedBlock.displayType === 'playground-gpt' ||
      localizedBlock.displayType === 'playground-dalle' ||
      localizedBlock.displayType === 'playground-heygen'
    ) {
      return (
        <Box sx={{ p: 2 }}>
          <Box>
            {localizedBlock.displayType === 'playground-gpt' ? (
              <ChatGPTPlayground
                initialPrompt={localizedBlock.initialPrompt || ''}
                onGenerate={() => {
                  // İlerleyişi kaydetmek için düz çağrı - koşulsuz olarak çağırıyoruz
                  console.log('💥 ChatGPT Playground tamamlandı - handleComplete çağrılıyor');
                  handleComplete();
                }}
                disableBack={true}
                temperature={localizedBlock.temperature || 0.7}
                topP={localizedBlock.topP || 1}
                frequencyPenalty={localizedBlock.frequencyPenalty || 0}
                presencePenalty={localizedBlock.presencePenalty || 0}
                showNotification={true}
                notificationMessage={t('courseContent.playground.completed')}
              />
            ) : localizedBlock.displayType === 'playground-dalle' ? (
              <DallEPlayground
                initialPrompt={localizedBlock.initialPrompt || ''}
                onGenerate={isTopicCompleted || isContentCompleted ? undefined : handleComplete}
                disableBack={true}
              />
            ) : localizedBlock.displayType === 'playground-heygen' ? (
              <HeygenVideoCreator
                id={localizedBlock.heygen_id || ''}
                onGenerate={() => handleComplete()}
              />
            ) : (
              <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body1">{t('courseContent.playgroundNotSupported')}</Typography>
              </Box>
            )}
          </Box>
        </Box>
      );
    }

    // Handle type for playground content blocks
    if (
      localizedBlock.type === 'playground-gpt' ||
      localizedBlock.type === 'playground-dalle' ||
      localizedBlock.type === 'playground-heygen'
    ) {
      return (
        <Box sx={{ p: 2 }}>
          <Box>
            {localizedBlock.type === 'playground-gpt' ? (
              <ChatGPTPlayground
                initialPrompt={localizedBlock.initialPrompt || ''}
                onGenerate={() => {
                  // İlerleyişi kaydetmek için düz çağrı - koşulsuz olarak çağırıyoruz
                  console.log('💥 ChatGPT Playground tamamlandı - handleComplete çağrılıyor');
                  handleComplete();
                }}
                disableBack={true}
                temperature={localizedBlock.temperature || 0.7}
                topP={localizedBlock.topP || 1}
                frequencyPenalty={localizedBlock.frequencyPenalty || 0}
                presencePenalty={localizedBlock.presencePenalty || 0}
                showNotification={true}
                notificationMessage={t('courseContent.playground.completed')}
              />
            ) : localizedBlock.type === 'playground-dalle' ? (
              <DallEPlayground
                initialPrompt={localizedBlock.initialPrompt || ''}
                onGenerate={isTopicCompleted || isContentCompleted ? undefined : handleComplete}
                disableBack={true}
              />
            ) : localizedBlock.type === 'playground-heygen' ? (
              <HeygenVideoCreator
                id={localizedBlock.heygen_id || ''}
                onGenerate={() => handleComplete()}
              />
            ) : (
              <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body1">{t('courseContent.playgroundNotSupported')}</Typography>
              </Box>
            )}
          </Box>
        </Box>
      );
    }

    switch (localizedBlock.type) {
      case 'text':
        return (
          <Box sx={{ p: 2 }}>
            {localizedBlock.textContent?.content ? (
              <Box
                sx={{
                  '& img': { maxWidth: '100%', height: 'auto' },
                  '& p': { margin: '1em 0' },
                  '& .pdf-container': {
                    position: 'relative',
                    height: '80vh',
                    width: '100%',
                    overflow: 'hidden',
                    '& object, & iframe': {
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      border: 'none',
                    },
                  },
                }}
                dangerouslySetInnerHTML={{
                  __html: decodeAndFixPdfContent(localizedBlock.textContent.content),
                }}
              />
            ) : (
              <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body1">İçerik bulunamadı</Typography>
              </Box>
            )}
          </Box>
        );

      case 'video':
        let videoUrl = getVideoUrl(localizedBlock);
        const isYouTubeVideo = localizedBlock.videoContent?.type === 'youtube';

        // HLS video URL'sini doğrudan kontrol et
        let finalVideoUrl = videoUrl;
        if (
          localizedBlock.videoContent?.type === 'hls' &&
          localizedBlock.videoContent?.hlsData?.streamingUrls
        ) {
          const streamingUrls = localizedBlock.videoContent.hlsData.streamingUrls;

          // Durum 1: streamingUrls bir dizi ise
          if (Array.isArray(streamingUrls) && streamingUrls.length > 0) {
            const firstUrl = streamingUrls[0];

            // Durum 1.1: İlk eleman bir nesne ve paths özelliği varsa
            if (typeof firstUrl === 'object' && firstUrl !== null && firstUrl.paths) {
              if (Array.isArray(firstUrl.paths) && firstUrl.paths.length > 0) {
                finalVideoUrl = firstUrl.paths[0];
              } else if (typeof firstUrl.paths === 'string') {
                finalVideoUrl = firstUrl.paths;
              }
            }
            // Durum 1.2: İlk eleman doğrudan bir string ise
            else if (typeof firstUrl === 'string') {
              finalVideoUrl = firstUrl;
            }
          }
          // Durum 2: streamingUrls bir nesne ise
          else if (typeof streamingUrls === 'object' && streamingUrls !== null) {
            // Durum 2.1: paths özelliği bir dizi ise
            if (Array.isArray(streamingUrls.paths) && streamingUrls.paths.length > 0) {
              finalVideoUrl = streamingUrls.paths[0];
            }
            // Durum 2.2: paths özelliği bir string ise
            else if (typeof streamingUrls.paths === 'string') {
              finalVideoUrl = streamingUrls.paths;
            }
            // Durum 2.3: _id özelliği varsa (özel durum)
            else if (
              streamingUrls._id &&
              Array.isArray(streamingUrls.paths) &&
              streamingUrls.paths.length > 0
            ) {
              finalVideoUrl = streamingUrls.paths[0];
            }
          }
          // Durum 3: streamingUrls bir string ise
          else if (typeof streamingUrls === 'string') {
            finalVideoUrl = streamingUrls;
          }
        }

        return (
          <Box sx={{ p: 2 }}>
            {finalVideoUrl ? (
              <Box className="topic-content__video-container">
                {isYouTubeVideo ? (
                  <Box className="youtube-wrapper">
                    <YouTube
                      videoId={getYouTubeVideoId(finalVideoUrl)}
                      opts={{
                        height: '100%',
                        width: '100%',
                        playerVars: {
                          autoplay: 0,
                          controls: 1,
                          modestbranding: 1,
                          rel: 0,
                          origin: window.location.origin,
                          enablejsapi: 1,
                        },
                      }}
                      onEnd={handleVideoEnd}
                      onStateChange={(event) => {
                        if (event.data === YouTube.PlayerState.ENDED) {
                          handleVideoEnd();
                        }
                      }}
                      onError={(error) => {
                        // YouTube hatası durumunda sessizce devam et
                        console.error('YouTube error:', error);
                      }}
                    />
                  </Box>
                ) : (
                  <EnhancedVideoPlayer
                    videoUrl={finalVideoUrl}
                    thumbnailUrl={localizedBlock.videoContent?.thumbnail || ''}
                    onComplete={handleVideoEnd}
                    courseId={courseId}
                    chapterIndex={chapterIndex}
                    topicIndex={topicIndex}
                    onTimeUpdate={onTimeUpdate}
                  />
                )}
              </Box>
            ) : (
              <Box sx={{ p: 2, textAlign: 'center', color: 'error.main' }}>
                <Typography variant="body1">{t('courseContent.videoUrlNotFound')}</Typography>
                <Typography variant="body2">
                  Video content: {JSON.stringify(localizedBlock.videoContent, null, 2)}
                </Typography>
              </Box>
            )}
          </Box>
        );

      case 'quiz':
        return (
          <Box sx={{ p: 3 }} className="content-block__quiz-container">
            <Quiz
              quiz={localizedBlock}
              onComplete={handleQuizComplete}
              courseId={courseId}
              chapterIndex={chapterIndex}
              topicIndex={topicIndex}
              onScoreChange={handleQuizScoreChange}
              isCompleted={isTopicCompleted}
              availableLanguages={Object.keys(block?.translations || {})}
            />
          </Box>
        );

      case 'pdf':
        let pdfUrl = null;
        const translatedBlock = useMemo(() => {
          // Önce mevcut dilde çeviri var mı kontrol et
          const currentLangTranslation = localizedBlock?.translations?.[currentLanguage];
          if (currentLangTranslation) return currentLangTranslation;

          // Yoksa İngilizce çeviriye geri dön
          return localizedBlock?.translations?.['en'] || localizedBlock;
        }, [localizedBlock, currentLanguage]);

        if (translatedBlock.pdfContent?.url) {
          pdfUrl = processPdfUrl(translatedBlock.pdfContent.url);
        } else if (translatedBlock.textContent?.content) {
          const hrefMatch = translatedBlock.textContent.content.match(/href=["']([^"']+)["']/);
          if (hrefMatch && hrefMatch[1]) {
            pdfUrl = processPdfUrl(hrefMatch[1]);
          }
        } else {
          if (localizedBlock.pdfContent?.url) {
            pdfUrl = processPdfUrl(localizedBlock.pdfContent.url);
          } else if (localizedBlock.textContent?.content) {
            const hrefMatch = localizedBlock.textContent.content.match(/href=["']([^"']+)["']/);
            if (hrefMatch && hrefMatch[1]) {
              pdfUrl = processPdfUrl(hrefMatch[1]);
            }
          }
        }

        return (
          <PdfViewer
            pdfUrl={pdfUrl}
            title={localizedBlock.title}
            description={localizedBlock.description}
          />
        );

      case 'form':
        return (
          <Box sx={{ p: 3 }}>
            {formSubmitted ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="h6" color="primary" gutterBottom>
                  {t('common.thankYou')}
                </Typography>
                <Typography variant="body1">{t('courseContent.formSubmittedSuccess')}</Typography>
              </Box>
            ) : (
              /* Form İçeriği */
              <Box component="form" className="content-block__form" onSubmit={handleFormSubmit}>
                {Array.isArray(formData?.topics) && formData?.topics?.length > 0 ? (
                  formData.topics.map((topic, topicIndex) => (
                    <Box key={topic._id || topicIndex}>
                      <Typography
                        variant="h6"
                        sx={{ mb: Array.isArray(topic.fields) && topic.fields.length > 0 ? 2 : 0 }}
                      >
                        {topic.title}
                      </Typography>
                      {topic.description && (
                        <Typography variant="body2" sx={{ mb: 2 }}>
                          {topic.description}
                        </Typography>
                      )}
                      {Array.isArray(topic.fields) &&
                        topic.fields.length > 0 &&
                        topic.fields.map((field, fieldIndex) => renderField(field))}
                    </Box>
                  ))
                ) : (
                  <Typography color="text.secondary">{t('courseContent.noFieldsFound')}</Typography>
                )}

                {/* Gönder Butonu */}
                <Button
                  type="submit"
                  onClick={handleFormSubmit}
                  variant="contained"
                  className="content-block__form-button"
                  color="primary"
                  sx={{ textTransform: 'capitalize' }}
                  disabled={formSubmitting || formSubmitted}
                >
                  {formSubmitting ? (
                    <>
                      <CircularProgress size={24} sx={{ mr: 1 }} />
                      {t('common.submitting')}
                    </>
                  ) : (
                    t('common.submit')
                  )}
                </Button>
              </Box>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  // Video tamamlandığında çağrılacak fonksiyon
  const handleVideoEnd = () => {
    if (onVideoEnded) {
      try {
        onVideoEnded(block._id);
      } catch (error) {
        console.error('Error in handleVideoEnd:', error);
      }
    }
  };

  // Quiz tamamlandığında çağrılacak fonksiyon
  const handleQuizComplete = (score, totalPoints, passingScore) => {
    if (onQuizComplete) {
      try {
        onQuizComplete(block._id, score, totalPoints, passingScore);
      } catch (error) {
        console.error('Error in handleQuizComplete:', error);
      }
    }
  };

  // Quiz skorunu değiştirdiğimizde çağrılacak fonksiyon
  const handleQuizScoreChange = (score, passingScore) => {
    if (onQuizScoreChange) {
      try {
        onQuizScoreChange(block._id, score, passingScore);
      } catch (error) {
        console.error('Error in handleQuizScoreChange:', error);
      }
    }
  };

  // Interactive içerik tamamlandığında çağrılacak fonksiyon
  const handleInteractiveComplete = () => {
    if (onContentCompleted) {
      try {
        onContentCompleted();
        console.log('Interactive content completed for block:', block._id);
      } catch (error) {
        console.error('Error in handleInteractiveComplete:', error);
      }
    }
  };

  return <>{renderContent()}</>;
};

ContentBlock.propTypes = {
  block: PropTypes.object.isRequired,
  courseId: PropTypes.string.isRequired,
  chapterIndex: PropTypes.string,
  topicIndex: PropTypes.string,
  onQuizComplete: PropTypes.func,
  onQuizScoreChange: PropTypes.func,
  isTopicCompleted: PropTypes.bool,
  onVideoEnded: PropTypes.func,
  onTimeUpdate: PropTypes.func,
  onUsecaseGenerate: PropTypes.func,
  onContentCompleted: PropTypes.func,
  onFormSubmit: PropTypes.func,
};

export default ContentBlock;
