@use '../../../styles/abstracts/variables' as *;

/* 
 * Topic Sidebar Ana Bileşeni
 * -------------------------- 
 */
.topic-sidebar {
  width: 330px;
  min-width: 330px;
  background-color: #f8f9fa;
  box-shadow: inset -36px 0 40px -32px rgba(0, 0, 0, 0.03);
  transition: margin-left 0.3s ease-in-out;
  border-right: 1px solid rgba(0, 0, 0, 0.04);
  display: none;
  position: sticky;
  top: 0;
  min-height: 100vh;
  
  // Tablet ve üzeri ekranlarda göster
  @media (min-width: $tablet) {
    display: block;
  }
  
  /* 
   * <PERSON><PERSON>il<PERSON><PERSON>
   */
  &__course-info {
    padding: $spacing-3;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  }
  
  &__course-name {
    font-size: $font-size-xxl !important;
    font-weight: $font-weight-semibold !important;
    color: #2c3e50 !important;
    padding: $spacing-2 !important;
    margin-bottom: $spacing-3 !important;
  }
  
  /* 
   * <PERSON><PERSON><PERSON><PERSON>
   */
  &__progress-section {
    margin: 0 0 $spacing-2;
    background-color: #f5f8fa;
    border-radius: 6px;
    padding: $spacing-2;
  }
  
  &__progress-bar {
    margin-bottom: 0.5rem;
    
    &-container {
      position: relative;
      height: 4px;
      border-radius: 2px;
      background-color: #e9ecf1;
      overflow: hidden;
    }
    
    &-fill {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      border-radius: 2px;
      background-color: $success-color;
      transition: width 0.3s ease;
    }
  }
  
  &__progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: $spacing-1;
  }
  
  &__progress-complete {
    font-size: $font-size-xs !important;
    font-weight: 500 !important;
    color: #516273 !important;
  }
  
  &__progress-topics {
    font-size: $font-size-xs !important;
    font-weight: 500 !important;
    color: #516273 !important;
  }
  
  /* 
   * Bölümler (Chapters) Alanı
   */
  &__chapters {
    height: 100%;
    overflow-y: auto;
  }
  
  &__chapter {
    &-title {
      display: flex;
      align-items: center;
      padding: $spacing-3 $spacing-4;
      cursor: pointer;
      color: #5a6a7e !important;
      transition: all 0.2s ease;
      gap: $spacing-2;
      border-bottom: 1px solid rgba(0, 0, 0, 0.03);
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.02);
      }
      
      /* Aktif Bölüm Başlığı */
      &--active {
        background-color: #e6eef5;
        
        &:hover {
          background-color: #e6eef5;
        }
        
        /* Aktif Bölüm İkonları ve Metni */
        .topic-sidebar__chapter-icon {
          color: #4a6583 !important;
        }
        
        .topic-sidebar__chapter-text {
          color: #2c3e50 !important;
        }
        
        .icon {
          color: #4a6583 !important;
        }
      }
    }
    
    &-icon {
      font-size: $font-size-xl !important;
      color: #8c99a5 !important;
      
      /* Tamamlanmış Bölüm İkonu */
      &--completed {
        color: #68b984 !important;
      }
      
      /* Aktif Bölüm İkonu */
      &--active {
        color: #4a6583 !important;
      }
    }
    
    &-text {
      flex: 1;
      color: #5a6a7e !important;
      font-size: calc($font-size-md - 1px) !important;
      font-weight: $font-weight-medium !important;
    }
    
    .icon {
      font-size: $font-size-xl !important;
      color: #8c99a5 !important;
    }
  }
  
  /* 
   * Konular (Topics) Alanı
   */
  &__topics {
    padding: $spacing-2 0;
    background-color: rgba(0, 0, 0, 0.01);
  }
  
  &__topic {
    padding: $spacing-2 $spacing-4 $spacing-2 $spacing-6;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    gap: $spacing-1;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }
    
    /* Aktif Konu */
    &--active {
      background-color: #e6eef5 !important;
      
      /* Aktif Konu İçindeki Tüm Öğeler */
      .topic-sidebar__topic-icon {
        color: #4a6583 !important;
      }
      
      .topic-sidebar__topic-title {
        color: #2c3e50 !important;
        font-weight: $font-weight-medium !important;
      }
      
      .topic-sidebar__topic-type-icon,
      .topic-sidebar__topic-type-text {
        color: #5a6a7e !important;
      }
    }
    
    /* Konu Başlık Satırı */
    &-header {
      display: flex;
      align-items: center;
      gap: $spacing-2;
    }
    
    /* Konu İkonu */
    &-icon {
      font-size: $font-size-lg !important;
      color: #8c99a5 !important;
      
      &--active {
        color: #4a6583 !important;
      }
      
      &--completed {
        color: #68b984 !important;
      }
    }
    
    /* Konu Başlığı */
    &-title {
      flex: 1;
      color: #5a6a7e !important;
      font-size: $font-size-sm !important;
      font-weight: $font-weight-regular !important;
    }
    
    /* Konu İçeriği */
    &-content {
      margin-left: calc($spacing-4 + $spacing-1);
    }
    
    /* Konu Meta Bilgileri */
    &-meta {
      display: flex;
      gap: $spacing-3;
    }
    
    /* Konu Türü */
    &-type {
      display: flex;
      align-items: center;
      gap: $spacing-1;
      
      &-icon {
        font-size: $font-size-md !important;
        color: #a0aab5 !important;
      }
      
      &-text {
        color: #a0aab5 !important;
        font-size: $font-size-xs !important;
      }
    }
  }
} 