import { useEffect, useState, useMemo, useCallback, useRef } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Box, CircularProgress, Alert, Paper, Typography, Container, Button } from '@mui/material';
import PropTypes from 'prop-types';
import {
  fetchCourseDetails,
  updateProgress,
  fetchProgress,
  selectCurrentCourse,
} from '../../../redux/features/courses/courseSlice';

// Components
import TopicSidebar from './TopicSidebar';
import TopicContent from './TopicContent';
import NavigationFooter from '../NavigationFooter';
import { toast } from 'react-toastify';
import './TopicView.scss';
import useCourseProgress from '../../../hooks/useCourseProgress';

const TopicView = ({ courseId, chapterId, topicId, onCourseComplete }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || 'en';
  const user = useSelector((state) => state.auth.user);

  // Doğrudan state.courses'dan veriyi çekmek yerine selector kullanıyoruz
  const course = useSelector(selectCurrentCourse);
  const { loading, error, currentProgress } = useSelector((state) => state.courses);

  // useCourseProgress hook'unu kullanarak ilerleme hesaplamalarını yap
  const {
    percentage: progress,
    isLastTopic: isLastTopicFn,
    isTopicCompleted: hookIsTopicCompleted,
    updateProgress: updateProgressHook,
  } = useCourseProgress(course, user?._id, currentProgress);

  const [currentTopic, setCurrentTopic] = useState(null);
  const [localizedCourse, setLocalizedCourse] = useState(null);
  const [transcript, setTranscript] = useState([]);
  const [duration, setDuration] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [slideDirection, setSlideDirection] = useState('left');
  const [isCourseCompleteModalOpen, setIsCourseCompleteModalOpen] = useState(false);
  const [isUpdatingProgress, setIsUpdatingProgress] = useState(false);
  const [quizScore, setQuizScore] = useState(null);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [isContentCompleted, setIsContentCompleted] = useState(false);
  // New state: Checks if there is an interactive playground in the content
  const [hasInteractivePlayground, setHasInteractivePlayground] = useState(false);
  // State to track video completion status
  const [completedVideos, setCompletedVideos] = useState({});
  const [videoPlayerKey, setVideoPlayerKey] = useState(0);
  // Video position tracking
  const [currentTime, setCurrentTime] = useState(0);
  // Add a new state to check if the topic contains a form
  const [hasForm, setHasForm] = useState(false);
  // 1. Önce DALL-E usecase içeren topicleri tespit etmek için bir state ekleyelim
  const [hasDallEUsecase, setHasDallEUsecase] = useState(false);
  // 1. Yeni bir state ekleyelim
  const [isDallECompleted, setIsDallECompleted] = useState(false);
  // Quizin geçiş notu için state ekle
  const [quizPassingScore, setQuizPassingScore] = useState(70);
  // Video ve quiz tamamlanma durumlarını izlemek için yeni state'ler ekle
  const [isVideoCompleted, setIsVideoCompleted] = useState(false);
  const [isQuizCompleted, setIsQuizCompleted] = useState(false);

  // İçerik bloklarının tamamlanma durumlarını izlemek için daha esnek bir yapı
  const [completedBlocks, setCompletedBlocks] = useState({
    // Her içerik türü için bir ID->boolean/object map'i tutuyoruz
    video: {}, // { videoId1: true, videoId2: true }
    quiz: {}, // { quizId1: {score: 80, isPassing: true}, quizId2: {score: 60, isPassing: false} }
    form: {}, // { formId1: true, formId2: true }
    usecase: {}, // { usecaseId1: true }
    interactive: {}, // { interactiveId1: true }
    text: {}, // { textId1: true } Text içeriği otomatik tamamlanır
    pdf: {}, // { pdfId1: true } PDF içeriği otomatik tamamlanır
  });

  // TopicSidebar'ı yeniden render etmek için kullanılan key
  const [sidebarKey, setSidebarKey] = useState(0);

  // Reference variable to load course only once
  const hasLoadedCourseRef = useRef(false);
  const prevCourseIdRef = useRef(courseId);

  // Quiz completion flag
  const quizCompletionProcessingRef = useRef(false);

  // İçerik tamamlanma durumunu kontrol eden yardımcı fonksiyon
  const checkContentCompletion = useCallback(() => {
    if (!currentTopic) return false;

    // İçerik bloklarını türlerine göre grupla
    const contentBlocksByType = {};

    // İçerik blokları yoksa veya boşsa false döndür
    if (!currentTopic.contentBlocks || currentTopic.contentBlocks.length === 0) {
      return false;
    }

    // Tüm içerik bloklarını türlerine göre grupla
    (currentTopic.contentBlocks || []).forEach((block) => {
      // Block tipini normalize et (bazı bloklar usecase_slug veya playground_type gibi özel alanlar kullanıyor)
      let blockType = block.type;

      // Usecase türü kontrolü
      if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
        blockType = 'usecase';
      }

      // Interactive türü kontrolü
      if (
        block.playground_type &&
        block.playground_type !== 'none' &&
        blockType !== 'interactive'
      ) {
        blockType = 'interactive';
      }

      if (!contentBlocksByType[blockType]) {
        contentBlocksByType[blockType] = [];
      }

      contentBlocksByType[blockType].push(block);
    });

    // Eğer sadece Text ve PDF içeriği varsa otomatik olarak tamamlanmış kabul et
    const blockTypes = Object.keys(contentBlocksByType);
    const hasOnlyTextAndPdf =
      blockTypes.length > 0 && blockTypes.every((type) => type === 'text' || type === 'pdf');

    if (hasOnlyTextAndPdf) {
      return true;
    }

    // Her grup için tamamlanma kontrolü yap
    for (const [blockType, blocks] of Object.entries(contentBlocksByType)) {
      if (blocks.length === 0) continue;

      // Text ve PDF içeriklerini atla
      if (blockType === 'text' || blockType === 'pdf') {
        continue; // Text ve PDF içerikleri için control yapmıyoruz
      }

      // Her blok türü için ayrı kontrol
      switch (blockType) {
        case 'video':
          // Tüm videolar tamamlanmış mı?
          const allVideosCompleted = blocks.every(
            (block) => completedBlocks.video[block._id] === true
          );
          if (!allVideosCompleted) return false;
          break;

        case 'quiz':
          // Tüm quizler tamamlanmış mı?
          const allQuizzesCompleted = blocks.every((block) => {
            // Quiz passingScore kontrolü
            const passingScore =
              block.quiz?.passingScore !== undefined ? block.quiz.passingScore : 70;

            // passingScore 0 ise her zaman geçer
            if (passingScore === 0) return true;

            // Quiz tamamlandı mı kontrol et
            const quizStatus = completedBlocks.quiz[block._id];
            const isScorePassing = quizStatus?.score >= passingScore;
            const isQuizPassing = quizStatus?.isPassing === true || isScorePassing;

            return isQuizPassing;
          });
          if (!allQuizzesCompleted) return false;
          break;

        case 'form':
          // Tüm formlar tamamlanmış mı?
          const allFormsCompleted = blocks.every(
            (block) => completedBlocks.form[block._id] === true
          );
          if (!allFormsCompleted) return false;
          break;

        case 'usecase':
          // Tüm usecase'ler tamamlanmış mı?
          const allUsecasesCompleted = blocks.every(
            (block) => completedBlocks.usecase[block._id] === true
          );
          if (!allUsecasesCompleted) return false;
          break;

        case 'interactive':
          // Tüm interactive içerikler tamamlanmış mı?
          const allInteractivesCompleted = blocks.every(
            (block) => completedBlocks.interactive[block._id] === true
          );
          if (!allInteractivesCompleted) return false;
          break;

        default:
          // Tanınmayan içerik türleri için kontrol ekleme noktası (gelecekte yeni türler eklenebilir)
          console.warn(`Tanınmayan içerik türü: ${blockType}`);
          return false;
      }
    }

    // Tüm kontroller başarılıysa içerik tamamlanmıştır
    return true;
  }, [currentTopic, completedBlocks]);

  // İçerik bloklarında sadece quiz olup olmadığını kontrol eden fonksiyon
  const checkOnlyHasQuiz = useCallback(() => {
    if (!currentTopic || !currentTopic.contentBlocks || currentTopic.contentBlocks.length === 0) {
      return false;
    }

    // İçerik bloklarını türlerine göre sayalım
    const contentTypeCounts = {
      quiz: 0,
      video: 0,
      text: 0,
      pdf: 0,
      form: 0,
      usecase: 0,
      interactive: 0,
      other: 0,
    };

    // Her içerik bloğunu kontrol et ve türüne göre say
    currentTopic.contentBlocks.forEach((block) => {
      let blockType = block.type;

      // Usecase türü kontrolü
      if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
        blockType = 'usecase';
      }

      // Interactive türü kontrolü
      if (
        block.playground_type &&
        block.playground_type !== 'none' &&
        blockType !== 'interactive'
      ) {
        blockType = 'interactive';
      }

      // Blok türünü sayıma ekle
      if (contentTypeCounts[blockType] !== undefined) {
        contentTypeCounts[blockType]++;
      } else {
        contentTypeCounts.other++;
      }
    });

    // Sadece quiz var mı kontrol et
    // Text ve PDF içerikleri interaktif olmadıkları için yok sayılabilir
    const quizOnly =
      contentTypeCounts.quiz > 0 &&
      contentTypeCounts.video === 0 &&
      contentTypeCounts.form === 0 &&
      contentTypeCounts.usecase === 0 &&
      contentTypeCounts.interactive === 0 &&
      contentTypeCounts.other === 0;

    return quizOnly;
  }, [currentTopic]);

  // İçerik bloklarında sadece form olup olmadığını kontrol eden fonksiyon
  const checkOnlyHasForm = useCallback(() => {
    if (!currentTopic || !currentTopic.contentBlocks || currentTopic.contentBlocks.length === 0) {
      return false;
    }

    // İçerik bloklarını türlerine göre sayalım
    const contentTypeCounts = {
      quiz: 0,
      video: 0,
      text: 0,
      pdf: 0,
      form: 0,
      usecase: 0,
      interactive: 0,
      other: 0,
    };

    // Her içerik bloğunu kontrol et ve türüne göre say
    currentTopic.contentBlocks.forEach((block) => {
      let blockType = block.type;

      // Usecase türü kontrolü
      if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
        blockType = 'usecase';
      }

      // Interactive türü kontrolü
      if (
        block.playground_type &&
        block.playground_type !== 'none' &&
        blockType !== 'interactive'
      ) {
        blockType = 'interactive';
      }

      // Blok türünü sayıma ekle
      if (contentTypeCounts[blockType] !== undefined) {
        contentTypeCounts[blockType]++;
      } else {
        contentTypeCounts.other++;
      }
    });

    // Sadece form var mı kontrol et
    // Text ve PDF içerikleri interaktif olmadıkları için yok sayılabilir
    const formOnly =
      contentTypeCounts.form > 0 &&
      contentTypeCounts.quiz === 0 &&
      contentTypeCounts.video === 0 &&
      contentTypeCounts.usecase === 0 &&
      contentTypeCounts.interactive === 0 &&
      contentTypeCounts.other === 0;

    return formOnly;
  }, [currentTopic]);

  // Yardımcı fonksiyon: URL oluşturma (artık cardId eklemiyoruz)
  const getTopicUrl = useCallback((courseId, chapterId, topicId) => {
    return `/course/learn/${courseId}/${chapterId}/${topicId}`;
  }, []);

  // Kurs verilerinde çevirileri işleyip, mevcut dile uygun çevirileri uygulayan yardımcı fonksiyon
  const processTranslations = useCallback(
    (courseData) => {
      if (!courseData || !courseData.translations) return courseData;

      // İlk olarak mevcut dil için çeviri var mı kontrol ediyoruz
      const translatedContent =
        courseData.translations[currentLanguage] || courseData.translations.en || {};

      // Tamamen çeviriden gelen tüm verileri kullan - ID dahil hiçbir veri orijinalden alınmayacak
      return {
        ...translatedContent,
      };
    },
    [currentLanguage]
  );

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // İlk yükleme kontrolü

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Kurs verisi değiştiğinde veya dil değiştiğinde çevirileri işle
  useEffect(() => {
    if (course) {
      const processedCourse = processTranslations(course);
      setLocalizedCourse(processedCourse);
    }
  }, [course, processTranslations, currentLanguage]);

  // Topic tamamlanma durumunu memoize ediyoruz
  const isTopicCompleted = useMemo(() => {
    return hookIsTopicCompleted(chapterId, topicId);
  }, [hookIsTopicCompleted, chapterId, topicId, currentProgress]); // currentProgress'i bağımlılık olarak ekliyoruz

  useEffect(() => {
    if (localizedCourse && currentTopic) {
      try {
        let currentTranscript = [];

        if (
          currentTopic.video &&
          localizedCourse.transcripts &&
          Array.isArray(localizedCourse.transcripts)
        ) {
          const transcriptData = localizedCourse.transcripts.find(
            (t) => t.chapterId === chapterId && t.topicId === topicId
          );
          if (transcriptData && transcriptData.transcript) {
            currentTranscript = transcriptData.transcript;
          }
        }

        setTranscript(currentTranscript);
      } catch (error) {}
    }
  }, [localizedCourse, currentTopic, chapterId, topicId]);

  useEffect(() => {
    setIsUpdatingProgress(false);
    setIsContentCompleted(false);
    setQuizScore(null);
    setQuizPassingScore(70);
    setHasInteractivePlayground(false);

    // Tüm içerik bloklarının tamamlanma durumlarını sıfırla
    const initialCompletedBlocks = {
      video: {},
      quiz: {},
      form: {},
      usecase: {},
      interactive: {},
      text: {},
      pdf: {},
    };

    // Eğer topic yoksa, içeriği zaten tamamlanmış say
    if (!currentTopic || !currentTopic.contentBlocks) {
      setCompletedBlocks(initialCompletedBlocks);
      return;
    }

    // İçerik bloklarını türlerine göre sınıflandır
    const contentTypes = new Set();

    // Text ve PDF içeriklerini artık otomatik tamamlanmış olarak işaretleme
    currentTopic.contentBlocks.forEach((block) => {
      // Block tipini normalize et (bazı bloklar usecase_slug veya playground_type gibi özel alanlar kullanıyor)
      let blockType = block.type;

      // Usecase türü kontrolü
      if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
        blockType = 'usecase';
        contentTypes.add('usecase');
      }

      // Interactive türü kontrolü
      if (
        block.playground_type &&
        block.playground_type !== 'none' &&
        blockType !== 'interactive'
      ) {
        blockType = 'interactive';
        contentTypes.add('interactive');
        setHasInteractivePlayground(true);
      }

      // Form içeriği kontrolü
      if (blockType === 'form') {
        contentTypes.add('form');
        setHasForm(true);
      }

      // Quiz içeriği kontrolü
      if (blockType === 'quiz') {
        contentTypes.add('quiz');
      }

      // Video içeriği kontrolü
      if (blockType === 'video') {
        contentTypes.add('video');
      }

      // Text ve PDF içeriklerini içerik türü olarak kaydet ama otomatik tamamlanmış olarak işaretleme
      if (blockType === 'text') {
        contentTypes.add('text');
        initialCompletedBlocks.text[block._id] = true; // Text içeriği otomatik tamamla
      }

      if (blockType === 'pdf') {
        contentTypes.add('pdf');
        initialCompletedBlocks.pdf[block._id] = true; // PDF içeriği otomatik tamamla
      }
    });

    // Sadece text ve PDF içeriği varsa, otomatik olarak içeriği tamamlanmış say
    const hasOnlyTextAndPdf =
      contentTypes.size > 0 && [...contentTypes].every((type) => type === 'text' || type === 'pdf');

    if (hasOnlyTextAndPdf) {
      setIsContentCompleted(true);
    }

    // State'leri güncelle
    setCompletedBlocks(initialCompletedBlocks);

    // İçerik türlerini kontrol et - artık hiçbir içerik otomatik tamamlanmayacak
    setIsContentCompleted(false);

    // Video player key'i sıfırla
    const timestamp = new Date().getTime();
    setVideoPlayerKey(timestamp);
  }, [topicId, currentTopic]);

  useEffect(() => {
    if (prevCourseIdRef.current !== courseId || !hasLoadedCourseRef.current) {
      prevCourseIdRef.current = courseId;

      hasLoadedCourseRef.current = false;

      const loadCourseData = async () => {
        try {
          try {
            await dispatch(fetchCourseDetails(courseId)).unwrap();
            hasLoadedCourseRef.current = true;
          } catch (error) {}

          try {
            await dispatch(fetchProgress(courseId)).unwrap();
          } catch (error) {}
        } catch (error) {}
      };

      loadCourseData();
    }
  }, [courseId, dispatch]);

  useEffect(() => {
    if (!localizedCourse?.chapters || !chapterId || !topicId) {
      return;
    }

    const chapter = localizedCourse.chapters.find((ch) => ch._id === chapterId) || null;

    if (!chapter) {
      // Önce kurs verisini yeniden yüklemeyi dene
      dispatch(fetchCourseDetails(courseId))
        .unwrap()
        .then(() => {})
        .catch((error) => {
          console.error('Failed to reload course data:', error);
          toast.error('Course data could not be loaded. Redirecting to course page...');
          navigate(`/course/${courseId}`);
        });

      return;
    }

    const topic = chapter?.topics?.find((t) => t._id === topicId);

    if (!topic) {
      navigate(`/course/${courseId}`);
      return;
    }

    setCurrentTopic(topic);
    setDuration(topic.video?.duration || 0);
  }, [localizedCourse, chapterId, topicId, courseId, navigate, dispatch]);

  // useEffect to reset completion status when topicId changes
  useEffect(() => {
    if (!currentTopic) return; // Eğer topic yüklenmemişse işlem yapma

    // Önce tüm state'leri sıfırla
    setCompletedVideos({});
    setIsContentCompleted(false);
    setIsVideoCompleted(false);
    setIsQuizCompleted(false);

    // Tüm içerik bloklarının tamamlanma durumlarını sıfırla
    const initialCompletedBlocks = {
      video: {},
      quiz: {},
      form: {},
      usecase: {},
      interactive: {},
      text: {},
      pdf: {},
    };

    // İçerik blokları kontrolü
    if (currentTopic.contentBlocks && currentTopic.contentBlocks.length > 0) {
      // İçerik bloklarını türlerine göre sınıflandır
      const contentTypes = new Set();

      // Text ve PDF içeriklerini toplama
      currentTopic.contentBlocks.forEach((block) => {
        // Block tipini normalize et (bazı bloklar usecase_slug veya playground_type gibi özel alanlar kullanıyor)
        let blockType = block.type;

        // Usecase türü kontrolü
        if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
          blockType = 'usecase';
          contentTypes.add('usecase');
        }

        // Interactive türü kontrolü
        if (
          block.playground_type &&
          block.playground_type !== 'none' &&
          blockType !== 'interactive'
        ) {
          blockType = 'interactive';
          contentTypes.add('interactive');
          setHasInteractivePlayground(true);
        }

        // Form içeriği kontrolü
        if (blockType === 'form') {
          contentTypes.add('form');
          setHasForm(true);
        }

        // Quiz içeriği kontrolü
        if (blockType === 'quiz') {
          contentTypes.add('quiz');
        }

        // Video içeriği kontrolü
        if (blockType === 'video') {
          contentTypes.add('video');
        }

        // Text içeriklerini her zaman otomatik olarak tamamlanmış olarak işaretle
        if (blockType === 'text') {
          contentTypes.add('text');
          initialCompletedBlocks.text[block._id] = true; // Text içeriği otomatik tamamla
        }

        // PDF'leri içerik türleri setine ekle, tamamlanmayı aşağıda kontrol edeceğiz
        if (blockType === 'pdf') {
          contentTypes.add('pdf');
        }
      });

      // İçerik türleri listesini bir array'e dönüştür
      const contentTypesArray = [...contentTypes];

      // Sadece text ve PDF içeriği varsa, PDF'leri otomatik olarak tamamlanmış işaretle
      // Eğer başka içerik türleri (video, quiz vb.) varsa PDF'ler otomatik tamamlanmaz
      const hasOnlyTextAndPdf =
        contentTypesArray.length > 0 &&
        contentTypesArray.every((type) => type === 'text' || type === 'pdf');

      if (hasOnlyTextAndPdf) {
        setIsContentCompleted(true);
      } else {
        // Karışık içerik türlerinde PDF'leri otomatik tamamlama
      }
    }

    // State'leri güncelle
    setCompletedBlocks(initialCompletedBlocks);

    // Video player key'i sıfırla
    const timestamp = new Date().getTime();
    setVideoPlayerKey(timestamp);
  }, [chapterId, topicId, currentTopic]);

  // useEffect to monitor topic content completion status
  useEffect(() => {
    // Topic zaten tamamlanmış ise veya güncelleme yapılıyorsa işlem yapma
    if (isTopicCompleted || isUpdatingProgress || !currentTopic) return;

    // İçeriğin tamamlanıp tamamlanmadığını kontrol et
    const allContentCompleted = checkContentCompletion();

    // İçerik tamamlandı mı kontrol et ve daha önce tamamlanmamışsa işaretle
    if (allContentCompleted && !isContentCompleted) {
      setIsContentCompleted(true);
      // İçerik tamamlandı olarak işaretlendi, diğer useEffect bunu algılayıp
      // otomatik olarak updateProgress API çağrısını yapacak
    } else if (!allContentCompleted && isContentCompleted) {
      // Eğer içerik tamamlanmamışsa ama isContentCompleted true ise (hatalı bir durum)
      // state'i güncelle
      setIsContentCompleted(false);
    }
  }, [
    currentTopic,
    topicId,
    chapterId,
    isTopicCompleted,
    isUpdatingProgress,
    completedBlocks,
    isContentCompleted,
    checkContentCompletion,
  ]);

  // currentProgress değiştiğinde TopicSidebar'ı yeniden render et
  useEffect(() => {
    if (currentProgress) {
      // Sidebar'ı yeniden render etmek için key'i artır
      setSidebarKey((prevKey) => prevKey + 1);
    }
  }, [currentProgress]);

  // useEffect to automatically update progress when content is completed
  useEffect(() => {
    // İçerik tamamlandığında ve henüz topic işaretlenmemişse
    if (isContentCompleted && !isTopicCompleted && !isUpdatingProgress) {
      // ÖNEMLİ DEĞİŞİKLİK: Next butonuna tıklamayı beklemeden otomatik ilerleme güncelleme

      // Son bir kontrol yapalım - eğer yan menüden gelinen bir sayfaysa otomatik tamamlanmasın
      const wasMenuNavigation = sessionStorage.getItem('wasSidebarNavigation') === 'true';

      if (wasMenuNavigation) {
        console.log('Yan menüden gelindiği için otomatik topic tamamlanmasını engelliyoruz.');
        // Otomatik tamamlanmayı engelle ve session storage'ı temizle
        sessionStorage.removeItem('wasSidebarNavigation');
        return;
      }

      // Sayfa yüklendiğinde veya başka bir sayfadan dönüldüğünde toast göstermeyelim
      const skipToast = sessionStorage.getItem('skipQuizCompletionToast') === 'true';

      // handleNextClick içindeki API çağrısını burada tekrar ediyoruz
      const updateTopicProgress = async () => {
        try {
          setIsUpdatingProgress(true);

          const progressData = {
            chapterId,
            topicId,
            completed: true,
            progress: 1,
            courseComplateStatus: 'completed',
          };

          // Redux thunk ile ilerlemeyi güncelle
          const result = await dispatch(updateProgress({ courseId, progressData })).unwrap();

          // İlerleme verilerini yenile - bu kurs içeriğini değiştirmeden sadece progress verilerini günceller
          await dispatch(fetchProgress(courseId)).unwrap();

          // Sadece toast göstermeyi kontrol et, direkt kullanıcı etkileşimi varsa göster
          if (!skipToast) {
            /* toast.success(t('courseContent.topicCompleted', 'Topic completed successfully!'), {
              position: 'bottom-right',
              autoClose: 3000,
            }); */
          }

          // Session değişkenini temizle
          sessionStorage.removeItem('skipQuizCompletionToast');
        } catch (error) {
          console.error('❌ Error in automatic progress update:', error);
          toast.error(
            t('courseContent.errorSavingProgress', 'An error occurred while saving your progress!'),
            {
              position: 'bottom-right',
              autoClose: 3000,
            }
          );
        } finally {
          setIsUpdatingProgress(false);
        }
      };

      // API çağrısını yap
      updateTopicProgress();
    }
  }, [
    isContentCompleted,
    isTopicCompleted,
    isUpdatingProgress,
    chapterId,
    topicId,
    courseId,
    dispatch,
    t,
  ]);

  // isContentCompleted değeri değiştiğinde logla
  useEffect(() => {}, [
    isContentCompleted,
    isTopicCompleted,
    completedBlocks,
    hasInteractivePlayground,
  ]);

  const handleNextClick = async () => {
    // İçerik türlerini kontrol ederek sadece PDF ve Text içeriği olup olmadığını tespit et
    const contentTypes = new Set();
    let hasOnlyTextAndPdf = false;

    if (currentTopic?.contentBlocks) {
      currentTopic.contentBlocks.forEach((block) => {
        let blockType = block.type;

        // Usecase türü kontrolü
        if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
          blockType = 'usecase';
          contentTypes.add('usecase');
        }

        // Interactive türü kontrolü
        if (
          block.playground_type &&
          block.playground_type !== 'none' &&
          blockType !== 'interactive'
        ) {
          blockType = 'interactive';
          contentTypes.add('interactive');
        }

        if (blockType === 'form') contentTypes.add('form');
        if (blockType === 'quiz') contentTypes.add('quiz');
        if (blockType === 'video') contentTypes.add('video');
        if (blockType === 'text') contentTypes.add('text');
        if (blockType === 'pdf') contentTypes.add('pdf');
      });

      // Sadece text ve/veya pdf içeriği varsa ve başka içerik türü yoksa
      hasOnlyTextAndPdf =
        contentTypes.size > 0 &&
        [...contentTypes].every((type) => type === 'text' || type === 'pdf');
    }

    // Update progress if content is completed but topic is not yet marked as completed
    // PDF/Text içerikli topicler için isContentCompleted kontrolünü bypass ediyoruz

    // Topic tamamlanmışsa ve içerik tamamlanmışsa VEYA sadece Text ve PDF içeriği varsa
    if (!isTopicCompleted && (isContentCompleted || hasOnlyTextAndPdf)) {
      try {
        setIsUpdatingProgress(true);

        const progressData = {
          chapterId,
          topicId,
          completed: true,
          progress: 1,
          courseComplateStatus: 'completed',
        };

        // Redux thunk ile ilerlemeyi güncelle
        const result = await dispatch(updateProgress({ courseId, progressData })).unwrap();

        // İlerleme verilerini yenile - bu kurs içeriğini değiştirmeden sadece progress verilerini günceller
        await dispatch(fetchProgress(courseId)).unwrap();

        // Başarı mesajı göster
        /* toast.success(t('courseContent.topicCompleted', 'Topic completed successfully!'), {
          position: 'bottom-right',
          autoClose: 3000,
        }); */
      } catch (error) {
        console.error('❌ Error in handleNextClick:', error);
        toast.error(
          t('courseContent.errorSavingProgress', 'An error occurred while saving your progress!'),
          {
            position: 'bottom-right',
            autoClose: 3000,
          }
        );
      } finally {
        setIsUpdatingProgress(false);
      }
    }

    // Check if this is the last topic in the course
    const isLastTopicInCourse = isLastTopicFn?.(chapterId, topicId) || false;

    // If it's the last topic, trigger the course completion handler from props
    if (isLastTopicInCourse) {
      if (onCourseComplete) {
        onCourseComplete();
      }
    } else {
      // State'leri temizle - sonraki topic'e geçmeden önce
      setCompletedVideos({});
      setIsContentCompleted(false);
      setIsVideoCompleted(false);
      setIsQuizCompleted(false);
      setCompletedBlocks({
        video: {},
        quiz: {},
        form: {},
        usecase: {},
        interactive: {},
        text: {},
        pdf: {},
      });

      // Önce state'leri temizle, sonra navigate işlemini yap
      // Bu, state'lerin taşınmasını engelleyecek
      setTimeout(() => {
        navigateToNextContent();
      }, 50);
    }
  };

  // Son konuda hem form hem quiz tamamlandığında direkt olarak topic'i tamamlayacak fonksiyon
  const checkFormAndQuizCompletion = useCallback(() => {
    // Sadece son topic için çalışsın
    const isLastTopicInCourse = isLastTopicFn?.(chapterId, topicId) || false;
    if (!isLastTopicInCourse || isUpdatingProgress || !currentTopic) return;

    // İçerik türlerini kontrol et
    const contentTypes = new Set();
    const completedContentTypes = new Set();

    if (currentTopic?.contentBlocks) {
      // İçerik türlerini ve tamamlanma durumlarını tespit et
      currentTopic.contentBlocks.forEach((block) => {
        let blockType = block.type;

        // Form içeriği kontrolü
        if (blockType === 'form') {
          contentTypes.add('form');
          if (completedBlocks.form[block._id]) completedContentTypes.add('form');
        }

        // Quiz içeriği kontrolü
        if (blockType === 'quiz') {
          contentTypes.add('quiz');
          // Quiz passingScore kontrolü
          const passingScore =
            block.quiz?.passingScore !== undefined ? block.quiz.passingScore : 70;
          // Quiz geçtiyse
          if (completedBlocks.quiz[block._id]?.isPassing || passingScore === 0) {
            completedContentTypes.add('quiz');
          }
        }
      });
    }

    // Form ve quiz kontolü
    const hasForm = contentTypes.has('form');
    const hasQuiz = contentTypes.has('quiz');
    const isFormCompleted = hasForm ? completedContentTypes.has('form') : true;
    const isQuizCompleted = hasQuiz ? completedContentTypes.has('quiz') : true;

    // Debug için log ekleyelim
    console.log('Form and Quiz Completion Check:', {
      hasForm,
      hasQuiz,
      isFormCompleted,
      isQuizCompleted,
      contentTypes: Array.from(contentTypes),
      completedContentTypes: Array.from(completedContentTypes),
      isTopicCompleted,
      isLastTopicInCourse,
    });

    // Her ikisi de tamamlanmışsa topic'i otomatik olarak tamamla
    if ((!hasForm || isFormCompleted) && (!hasQuiz || isQuizCompleted)) {
      // Topic'i otomatik olarak tamamla
      const updateLastTopicProgress = async () => {
        try {
          setIsUpdatingProgress(true);

          const progressData = {
            chapterId,
            topicId,
            completed: true,
            progress: 1,
            courseComplateStatus: 'completed',
          };

          // Redux thunk ile ilerlemeyi güncelle
          const result = await dispatch(updateProgress({ courseId, progressData })).unwrap();

          // İlerleme verilerini yenile
          await dispatch(fetchProgress(courseId)).unwrap();

          // Başarı mesajı göster
          toast.success(
            t(
              'courseContent.courseReadyToComplete',
              'Course ready to complete! Click Complete Course button.'
            ),
            {
              position: 'bottom-right',
              autoClose: 5000,
            }
          );
        } catch (error) {
          console.error('Error in automatic last topic update:', error);
        } finally {
          setIsUpdatingProgress(false);
        }
      };

      // Topic'i tamamla
      updateLastTopicProgress();
    }
  }, [
    isLastTopicFn,
    chapterId,
    topicId,
    isUpdatingProgress,
    currentTopic,
    completedBlocks,
    courseId,
    dispatch,
    t,
  ]);

  // Form submit edildiğinde çağrılacak fonksiyon
  const handleFormSubmit = (formId) => {
    // Form zaten tamamlandı mı kontrol et
    if (completedBlocks.form[formId]) {
      return;
    }

    // Form tamamlandı toast mesajını göster
    toast.success(t('courseContent.formSubmitted', 'Form submitted successfully!'), {
      position: 'bottom-right',
      autoClose: 3000,
    });

    // completedBlocks state'ini güncelle - tamamlanan formları kaydet
    setCompletedBlocks((prev) => {
      const updatedState = {
        ...prev,
        form: {
          ...prev.form,
          [formId]: true,
        },
      };

      return updatedState;
    });

    // State güncellemesinin tamamlanmasını bekleyip içerik tamamlama durumunu kontrol et
    setTimeout(() => {
      // İçerik tamamlanma durumunu yeniden kontrol et
      const isAllContentCompleted = checkContentCompletion();

      if (isAllContentCompleted) {
        setIsContentCompleted(true);

        // Sadece form varsa ve form tamamlandıysa topic'i otomatik olarak tamamla
        const onlyHasForm = checkOnlyHasForm();

        if (onlyHasForm && !isTopicCompleted && !isUpdatingProgress) {
          // Topic'i otomatik olarak tamamla
          const updateTopicProgress = async () => {
            try {
              setIsUpdatingProgress(true);

              const progressData = {
                chapterId,
                topicId,
                completed: true,
                progress: 1,
                courseComplateStatus: 'completed',
              };

              // Redux thunk ile ilerlemeyi güncelle
              await dispatch(updateProgress({ courseId, progressData })).unwrap();

              // İlerleme verilerini yenile
              await dispatch(fetchProgress(courseId)).unwrap();

              // Topic tamamlandı bildirimi göster - form tamamlandı bildirimi zaten gösterildi
              // Bu nedenle burada ekstra bildirim göstermiyoruz
            } catch (error) {
              console.error('❌ Error in automatic form completion update:', error);
            } finally {
              setIsUpdatingProgress(false);
            }
          };

          // Topic'i tamamla
          updateTopicProgress();
        }
      }

      // ÖNEMLİ: Son konudaki form+quiz kontrolünü yap
      // Her zaman çalıştır - sidebar navigasyonu için de gerekli
      checkFormAndQuizCompletion();
    }, 300); // İlgili state'lerin güncellenebilmesi için süreyi arttırdık
  };

  // Quiz tamamlandığında çağrılacak handler fonksiyonu
  const handleQuizComplete = useCallback(
    async (quizId, score, totalPoints, passingScore = 70) => {
      try {
        if (isUpdatingProgress || !quizId) return;

        // isPassing hesaplaması - açık koşullar
        let isPassing = false;

        // passingScore 0 ise her zaman geçer
        if (passingScore === 0) {
          isPassing = true;
        } else {
          // Aksi halde skor passingScore'dan büyük veya eşit mi kontrol et
          isPassing = score >= passingScore;
        }

        // Quiz skorunu güncelle
        setQuizScore(score);
        setQuizPassingScore(passingScore);

        // Tamamlanma durumunu completedBlocks state'ine kaydet
        setCompletedBlocks((prev) => {
          // Önce güncellenmiş state'i hazırla
          const updatedState = {
            ...prev,
            quiz: {
              ...prev.quiz,
              [quizId]: {
                score,
                isPassing,
                totalPoints,
                passingScore, // Passing score'u da kaydedelim
                completedAt: new Date().toISOString(), // Tamamlanma zamanını kaydedelim
              },
            },
          };

          return updatedState;
        });

        // Quiz geçtiyse ilerleme durumunu kontrol et
        if (isPassing) {
          // Session değişkenini kontrol et - eğer sayfa yüklendiğinde geldiyse toast gösterme
          const skipToast = sessionStorage.getItem('skipQuizCompletionToast') === 'true';

          if (!skipToast) {
            // Bildirim göster
            toast.success(t('courseContent.quizPassed', 'Quiz passed successfully!'), {
              position: 'bottom-right',
              autoClose: 3000,
            });
          }

          // Temizle - bir sonraki için hazır olsun
          sessionStorage.removeItem('skipQuizCompletionToast');

          // State güncellemesinin tamamlanmasını bekleyip içerik tamamlama durumunu kontrol et
          setTimeout(() => {
            // İçerik tamamlanma durumunu yeniden kontrol et
            const isAllContentCompleted = checkContentCompletion();
            if (isAllContentCompleted) {
              setIsContentCompleted(true);

              // Tüm içerik tamamlandığında bildirim göster - eğer otomatik yükleme değilse
              if (!skipToast) {
                toast.success(
                  t('courseContent.contentCompleted', 'All content completed successfully!'),
                  {
                    position: 'bottom-right',
                    autoClose: 3000,
                  }
                );
              }
            }

            // ÖNEMLİ: Son konudaki form+quiz kontrolünü yap
            checkFormAndQuizCompletion();

            // YENİ: Content bloklarında sadece quiz varsa ve quiz başarılı tamamlandıysa
            // Topic'i otomatik olarak tamamlanmış olarak işaretle
            const onlyHasQuiz = checkOnlyHasQuiz();

            if (onlyHasQuiz && isPassing && !isTopicCompleted && !isUpdatingProgress) {
              // Topici otomatik olarak tamamla
              const updateTopicProgress = async () => {
                try {
                  setIsUpdatingProgress(true);

                  const progressData = {
                    chapterId,
                    topicId,
                    completed: true,
                    progress: 1,
                    courseComplateStatus: 'completed',
                  };

                  // Redux thunk ile ilerlemeyi güncelle
                  await dispatch(updateProgress({ courseId, progressData })).unwrap();

                  // İlerleme verilerini yenile
                  await dispatch(fetchProgress(courseId)).unwrap();

                  // Başarı mesajı göster - eğer otomatik yükleme değilse
                  if (!skipToast) {
                    /* toast.success(
                      t('courseContent.topicCompleted', 'Topic completed successfully!'),
                      {
                        position: 'bottom-right',
                        autoClose: 3000,
                      }
                    ); */
                  }
                } catch (error) {
                  console.error('❌ Error in automatic quiz completion update:', error);
                } finally {
                  setIsUpdatingProgress(false);
                }
              };

              // Topic'i tamamla
              updateTopicProgress();
            }
          }, 500); // 500ms bekletme - state güncellemesinin uygulanması için yeterli süre
        } else {
          // Topic daha önceden tamamlanmışsa uyarıyı gösterme
          const isTopicPreviouslyCompleted =
            isTopicCompleted ||
            (progress && topicId && progress.completedTopics?.includes(topicId));

          if (!isTopicPreviouslyCompleted) {
            // Quiz geçilmediyse ve topic daha önce tamamlanmamışsa bildirim göster
            toast.warning(t('courseContent.quizFailed', 'You did not pass the quiz. Try again!'), {
              position: 'bottom-right',
              autoClose: 3000,
            });
          }
        }
      } catch (error) {
        console.error('Error in handleQuizComplete:', error);
        toast.error(
          t('courseContent.errorSavingProgress', 'An error occurred while saving your progress!'),
          {
            position: 'bottom-right',
            autoClose: 3000,
          }
        );
      }
    },
    [
      t,
      isUpdatingProgress,
      checkContentCompletion,
      checkFormAndQuizCompletion,
      currentProgress,
      chapterId,
      courseId,
      dispatch,
      progress,
      topicId,
      isTopicCompleted,
    ]
  );

  const navigateToNextContent = async () => {
    if (!localizedCourse || !chapterId || !topicId) return;

    const currentChapterIndex = localizedCourse.chapters.findIndex((ch) => ch._id === chapterId);
    if (currentChapterIndex === -1) return;

    const currentChapter = localizedCourse.chapters[currentChapterIndex];
    if (!currentChapter || !currentChapter.topics) return;

    const currentTopicIndex = currentChapter.topics.findIndex((t) => t._id === topicId);
    if (currentTopicIndex === -1) return;

    let nextUrl = '';

    if (currentTopicIndex < currentChapter.topics.length - 1) {
      // Sonraki konuya git
      const nextTopic = currentChapter.topics[currentTopicIndex + 1];
      nextUrl = getTopicUrl(courseId, chapterId, nextTopic._id);
    } else if (currentChapterIndex < localizedCourse.chapters.length - 1) {
      // Sonraki bölümün ilk konusuna git
      const nextChapter = localizedCourse.chapters[currentChapterIndex + 1];
      if (nextChapter.topics && nextChapter.topics.length > 0) {
        const firstTopic = nextChapter.topics[0];
        nextUrl = getTopicUrl(courseId, nextChapter._id, firstTopic._id);
      } else {
        nextUrl = getTopicUrl(courseId, courseId, courseId);
      }
    }
    const isLastChapter = currentChapterIndex === localizedCourse.chapters.length - 1;
    const isLastTopicInChapter = currentTopicIndex === currentChapter.topics.length - 1;

    // URL oluşturulduysa, yönlendir
    if (nextUrl) {
      navigate(nextUrl);
    }
  };

  const navigateToPreviousContent = async () => {
    try {
      if (!localizedCourse || !chapterId || !topicId) {
        return;
      }

      // Chapters dizisi var mı kontrol et - localizedCourse kullanıyoruz
      if (
        !localizedCourse.chapters ||
        !Array.isArray(localizedCourse.chapters) ||
        localizedCourse.chapters.length === 0
      ) {
        return;
      }

      const currentChapterIndex = localizedCourse.chapters.findIndex(
        (ch) => ch && ch._id === chapterId
      );
      if (currentChapterIndex === -1) {
        return;
      }

      const currentChapter = localizedCourse.chapters[currentChapterIndex];
      if (!currentChapter || !currentChapter.topics || !Array.isArray(currentChapter.topics)) {
        return;
      }

      const currentTopicIndex = currentChapter.topics.findIndex((t) => t && t._id === topicId);
      if (currentTopicIndex === -1) {
        return;
      }

      let prevUrl = '';

      if (currentTopicIndex > 0) {
        // Önceki konuya git
        const previousTopic = currentChapter.topics[currentTopicIndex - 1];
        if (previousTopic && previousTopic._id) {
          prevUrl = getTopicUrl(courseId, chapterId, previousTopic._id);
        }
      } else if (currentChapterIndex > 0) {
        // Önceki bölümün son konusuna git
        const previousChapter = localizedCourse.chapters[currentChapterIndex - 1];
        if (
          previousChapter &&
          previousChapter.topics &&
          Array.isArray(previousChapter.topics) &&
          previousChapter.topics.length > 0
        ) {
          const lastTopic = previousChapter.topics[previousChapter.topics.length - 1];
          if (lastTopic && lastTopic._id) {
            prevUrl = getTopicUrl(courseId, previousChapter._id, lastTopic._id);
          }
        } else {
          prevUrl = getTopicUrl(courseId, courseId, courseId);
        }
      }

      // URL oluşturulduysa, yönlendir
      if (prevUrl) {
        navigate(prevUrl);
      } else {
        console.warn('Could not generate URL for previous content');
      }
    } catch (error) {
      console.error('Error in navigateToPreviousContent:', error);
    }
  };

  // Usecase generate edildiğinde çağrılacak fonksiyon
  const handleImageGenerate = useCallback(
    async (usecaseId) => {
      try {
        // usecase tamamlanma durumunu güncelle
        setCompletedBlocks((prev) => ({
          ...prev,
          usecase: {
            ...prev.usecase,
            [usecaseId]: true,
          },
        }));

        // İçerik tamamlanma durumunu kontrol et
        const isAllContentCompleted = checkContentCompletion();
        if (isAllContentCompleted) {
          setIsContentCompleted(true);

          // Tüm içerik tamamlandığında genel bir bildirim göster
          toast.success(
            t('courseContent.contentCompleted', 'All content completed successfully!'),
            {
              position: 'bottom-right',
              autoClose: 3000,
            }
          );
        } else {
          // UseCase için bildirim göster - içerik tamamen tamamlanmamış olsa bile
          toast.success(t('courseContent.usecaseCompleted', 'Use case completed successfully!'), {
            position: 'bottom-right',
            autoClose: 3000,
          });
        }
      } catch (error) {
        console.error('Error in handleImageGenerate:', error);
        toast.error(
          t('courseContent.errorSavingProgress', 'An error occurred while saving your progress!'),
          {
            position: 'bottom-right',
            autoClose: 3000,
          }
        );
      }
    },
    [t, checkContentCompletion]
  );

  // Interactive içerik tamamlandığında çağrılacak fonksiyon
  const handleInteractiveContentComplete = useCallback(
    (interactiveId) => {
      // interactive tamamlanma durumunu güncelle
      setCompletedBlocks((prev) => ({
        ...prev,
        interactive: {
          ...prev.interactive,
          [interactiveId]: true,
        },
      }));

      // İçerik tamamlanma durumunu kontrol et
      const isAllContentCompleted = checkContentCompletion();
      if (isAllContentCompleted) {
        setIsContentCompleted(true);

        // Tüm içerik tamamlandığında genel bir bildirim göster
        toast.success(t('courseContent.contentCompleted', 'All content completed successfully!'), {
          position: 'bottom-right',
          autoClose: 3000,
        });
      } else {
        // Tüm içerik tamamlanmadıysa, sadece interactive için bildirim göster
        toast.success(
          t('courseContent.interactiveCompleted', 'Interactive exercise completed successfully!'),
          {
            position: 'bottom-right',
            autoClose: 3000,
          }
        );
      }
    },
    [t, checkContentCompletion]
  );

  // Video tamamlandığında çağrılacak fonksiyon
  const handleVideoEnd = useCallback(
    (videoId) => {
      // Video tamamlanma durumunu güncelle
      setCompletedBlocks((prev) => ({
        ...prev,
        video: {
          ...prev.video,
          [videoId]: true,
        },
      }));

      // İçerik tamamlanma durumunu kontrol et
      const isAllContentCompleted = checkContentCompletion();
      if (isAllContentCompleted) {
        setIsContentCompleted(true);

        // Tüm içerik tamamlandığında genel bir bildirim göster
        toast.success(t('courseContent.contentCompleted', 'All content completed successfully!'), {
          position: 'bottom-right',
          autoClose: 3000,
        });
      } else {
        // Video için bildirim göster
        toast.success(t('courseContent.videoCompleted', 'Video completed successfully!'), {
          position: 'bottom-right',
          autoClose: 3000,
        });
      }
    },
    [t, checkContentCompletion]
  );

  // Location değişimini izleyen ve state'leri sıfırlayan yeni useEffect
  useEffect(() => {
    // State'leri sıfırla
    setCompletedVideos({});
    setIsContentCompleted(false);
    setIsVideoCompleted(false);
    setIsQuizCompleted(false);
    setHasInteractivePlayground(false);
    setHasForm(false);
    setHasDallEUsecase(false);
    setIsDallECompleted(false);
    setQuizScore(null);

    // Tüm içerik bloklarının tamamlanma durumlarını sıfırla
    const cleanCompletedBlocks = {
      video: {},
      quiz: {},
      form: {},
      usecase: {},
      interactive: {},
      text: {},
      pdf: {},
    };

    setCompletedBlocks(cleanCompletedBlocks);

    // Video player key'i sıfırla
    const timestamp = new Date().getTime();
    setVideoPlayerKey(timestamp);

    // ÖNEMLİ: Topic'in server tarafında tamamlanma durumunu kontrol et
    // Bu sayede yan menüden gidilince koşulsuz tamamlanma durumunu engelliyoruz
    setTimeout(() => {
      // Server'da topic tamamlanmışsa hookIsTopicCompleted true olacak
      const isCompletedOnServer = hookIsTopicCompleted && hookIsTopicCompleted(chapterId, topicId);

      if (isCompletedOnServer) {
        // Eğer topic zaten server tarafında tamamlanmışsa,
        // topicin içeriğini tamamlanmış olarak gösterme ama isContentCompleted'i false olarak tut
        console.log(
          'Topic zaten server tarafında tamamlanmış. Client tamamlanma durumunu FALSE tutuyoruz.'
        );
      } else {
        // Topic tamamlanmamışsa, İçerik türlerini kontrol et
        if (currentTopic && currentTopic.contentBlocks) {
          // Text ve PDF içeriği kontrolü yap - bunlar genellikle otomatik tamamlanır
          const contentTypes = new Set();

          // İçerik türlerini topla
          currentTopic.contentBlocks.forEach((block) => {
            let blockType = block.type;
            contentTypes.add(blockType);
          });

          // Sadece text ve PDF içeriği varsa
          const hasOnlyTextAndPdf =
            contentTypes.size > 0 &&
            [...contentTypes].every((type) => type === 'text' || type === 'pdf');

          // Eğer sadece text ve PDF içeriği varsa bile otomatik tamamlanmayı engelle
          if (hasOnlyTextAndPdf) {
            console.log(
              'Topic sadece Text ve PDF içeriyor, ancak otomatik tamamlanmasını engelliyoruz.'
            );
            setIsContentCompleted(false);
          }
        }
      }
    }, 100);
  }, [location.key, hookIsTopicCompleted, chapterId, topicId, currentTopic]); // location.key her rota değişiminde değişir

  // Eğer topic sidebar'dan yüklendiyse, ilgili state'leri doğru şekilde kuruyoruz
  useEffect(() => {
    if (currentTopic) {
      // Sidebar navigasyonundan kontrol et
      const wasMenuNavigation = sessionStorage.getItem('wasSidebarNavigation') === 'true';
      // Topic'in zaten tamamlanmış olup olmadığını kontrol et
      const wasTopicCompleted = sessionStorage.getItem('topicAlreadyCompleted') === 'true';

      // Session storage'ı temizle
      sessionStorage.removeItem('wasSidebarNavigation');
      sessionStorage.removeItem('topicAlreadyCompleted');

      // Eğer bu topic sidebar'dan açıldıysa ve tamamlandıysa
      // o zaman tüm blokları tamamlanmış olarak işaretleyelim
      if (wasMenuNavigation && wasTopicCompleted && currentTopic.contentBlocks) {
        console.log('SideBar tamamlanmış topic için blokları tamamlanmış olarak işaretleme');

        // Geçici tamamlanmış bloklar objesini hazırla
        const newCompletedBlocks = { ...completedBlocks };

        // Her içerik bloğu için geçerli durumu ayarla
        currentTopic.contentBlocks.forEach((block) => {
          switch (block.type) {
            case 'video':
              newCompletedBlocks.video[block._id] = true;
              break;
            case 'quiz':
              const passingScore = block.quiz?.passingScore || 70;
              newCompletedBlocks.quiz[block._id] = {
                score: 100, // Varsayılan olarak tam puan
                isPassing: true,
                totalPoints: 100,
                passingScore,
                completedAt: new Date().toISOString(),
              };
              break;
            case 'form':
              newCompletedBlocks.form[block._id] = true;
              break;
            case 'usecase':
              newCompletedBlocks.usecase[block._id] = true;
              break;
            case 'interactive':
              newCompletedBlocks.interactive[block._id] = true;
              break;
            default:
              // Text, PDF gibi diğer içerik tipleri otomatik tamamlanmış sayılır
              break;
          }
        });

        // CompletedBlocks state'ini güncelle
        setCompletedBlocks(newCompletedBlocks);

        // İçeriğin tamamlandığını işaretle
        setIsContentCompleted(true);

        // Son topic ise ve hem quiz hem form varsa, Course Complete butonunu aktifleştir
        if (isLastTopicFn?.(chapterId, topicId)) {
          // Topic tamamlanmasından 500ms sonra form ve quiz kontrolünü yap
          setTimeout(() => {
            checkFormAndQuizCompletion();
          }, 500);
        }
      }
    }
  }, [
    currentTopic,
    chapterId,
    topicId,
    completedBlocks,
    isLastTopicFn,
    checkFormAndQuizCompletion,
  ]);

  if (loading && !course) {
    return (
      <Box className="topic-view__loading">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box className="topic-view__error">
        <Alert severity="error">
          {t('course.errors.loading', 'Error loading course')}: {error}
        </Alert>
      </Box>
    );
  }

  if (!currentTopic) {
    return (
      <Container maxWidth="sm" sx={{ mt: 12 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {t('course.errors.loading')}
        </Alert>
        <Button variant="contained" onClick={() => window.location.reload()}>
          {t('course.actions.tryAgain')}
        </Button>
      </Container>
    );
  }

  const currentChapter = course?.chapters?.find((ch) => ch._id === chapterId);
  const currentChapterIndex = course?.chapters?.findIndex((ch) => ch._id === chapterId) ?? -1;
  const currentTopicIndex = currentChapter?.topics?.findIndex((t) => t._id === topicId) ?? -1;

  // Navigation Footer button text and style
  const getNextButtonProps = () => {
    // Check if this is the last topic using hook's function
    const isLastTopicInCourse = isLastTopicFn?.(chapterId, topicId) || false;

    // İşlem sırasında (progress güncellenirken) butonu devre dışı bırak
    if (isUpdatingProgress) {
      return {
        text: t('common.updating', 'Updating...'),
        variant: 'contained',
        color: 'primary',
        disabled: true,
        tooltip: t('courseContent.pleaseWait', 'Please wait while we update your progress...'),
        sx: { minWidth: '100px' },
      };
    }

    // Topic tamamlanmışsa direkt geçiş izni ver
    if (isTopicCompleted) {
      return {
        text: isLastTopicInCourse
          ? t('course.navigation.completeCourse', 'COMPLETE COURSE')
          : t('course.navigation.next', 'Next'),
        variant: 'contained',
        color: 'primary',
        disabled: false,
        tooltip: '',
        sx: isLastTopicInCourse
          ? {
              minWidth: '180px',
              textTransform: 'uppercase',
              fontWeight: 600,
            }
          : { minWidth: '100px' },
      };
    }

    // ÖNEMLİ: Son konuda (isLastTopicInCourse) form ve quiz kontrolü yap
    // Form ve quizlerin tamamlanma durumunu kontrol et - son topicte daha esnek davranacağız
    if (isLastTopicInCourse) {
      // İçerik türlerini kontrol et
      const contentTypes = new Set();
      const completedContentTypes = new Set();

      if (currentTopic?.contentBlocks) {
        // İçerik türlerini ve tamamlanma durumlarını tespit et
        currentTopic.contentBlocks.forEach((block) => {
          let blockType = block.type;

          // Usecase türü kontrolü
          if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
            blockType = 'usecase';
            contentTypes.add('usecase');
            if (completedBlocks.usecase[block._id]) completedContentTypes.add('usecase');
          }

          // Interactive türü kontrolü
          if (
            block.playground_type &&
            block.playground_type !== 'none' &&
            blockType !== 'interactive'
          ) {
            blockType = 'interactive';
            contentTypes.add('interactive');
            if (completedBlocks.interactive[block._id]) completedContentTypes.add('interactive');
          }

          // Form içeriği kontrolü
          if (blockType === 'form') {
            contentTypes.add('form');
            if (completedBlocks.form[block._id]) completedContentTypes.add('form');
          }

          // Quiz içeriği kontrolü
          if (blockType === 'quiz') {
            contentTypes.add('quiz');
            // Quiz passingScore kontrolü
            const passingScore =
              block.quiz?.passingScore !== undefined ? block.quiz.passingScore : 70;
            // Quiz geçtiyse
            if (completedBlocks.quiz[block._id]?.isPassing || passingScore === 0) {
              completedContentTypes.add('quiz');
            }
          }

          if (blockType === 'video') {
            contentTypes.add('video');
            if (completedBlocks.video[block._id]) completedContentTypes.add('video');
          }

          if (blockType === 'text') contentTypes.add('text');
          if (blockType === 'pdf') contentTypes.add('pdf');
        });
      }

      // En önemli içerik türlerini kontrol et (form ve quiz)
      const hasForm = contentTypes.has('form');
      const hasQuiz = contentTypes.has('quiz');
      const isFormCompleted = hasForm ? completedContentTypes.has('form') : true;
      const isQuizCompleted = hasQuiz ? completedContentTypes.has('quiz') : true;

      // Eğer form ve quiz yoksa veya varsa ve tamamlanmışsa, butonu etkinleştir
      if ((!hasForm || isFormCompleted) && (!hasQuiz || isQuizCompleted)) {
        return {
          text: t('course.navigation.completeCourse', 'COMPLETE COURSE'),
          variant: 'contained',
          color: 'primary',
          disabled: false,
          tooltip: '',
          sx: {
            minWidth: '180px',
            textTransform: 'uppercase',
            fontWeight: 600,
          },
        };
      }
    }

    // İçerik tamamlandı mı kontrol et - isContentCompleted TRUE ise kullanıcı Next butonuna tıklayabilir
    if (isContentCompleted) {
      return {
        text: isLastTopicInCourse
          ? t('course.navigation.completeCourse', 'COMPLETE COURSE')
          : t('course.navigation.next', 'Next'),
        variant: 'contained',
        color: 'primary',
        disabled: false,
        tooltip: '',
        sx: isLastTopicInCourse
          ? {
              minWidth: '180px',
              textTransform: 'uppercase',
              fontWeight: 600,
            }
          : { minWidth: '100px' },
      };
    }

    // Özel durum kontrolü: Sadece PDF ve Text içeriği varsa butonu aktif et
    // İçerik bloklarını türlerine göre grupla
    const contentTypes = new Set();

    if (currentTopic?.contentBlocks) {
      currentTopic.contentBlocks.forEach((block) => {
        let blockType = block.type;

        // Usecase türü kontrolü
        if ((block.usecase_slug || block.usecase_type) && blockType !== 'usecase') {
          blockType = 'usecase';
          contentTypes.add('usecase');
        }

        // Interactive türü kontrolü
        if (
          block.playground_type &&
          block.playground_type !== 'none' &&
          blockType !== 'interactive'
        ) {
          blockType = 'interactive';
          contentTypes.add('interactive');
        }

        if (blockType === 'form') contentTypes.add('form');
        if (blockType === 'quiz') contentTypes.add('quiz');
        if (blockType === 'video') contentTypes.add('video');
        if (blockType === 'text') contentTypes.add('text');
        if (blockType === 'pdf') contentTypes.add('pdf');
      });
    }

    // Özel durum: Sadece text ve/veya pdf içeriği varsa ve başka içerik türü yoksa
    const hasOnlyTextAndPdf =
      contentTypes.size > 0 && [...contentTypes].every((type) => type === 'text' || type === 'pdf');

    if (hasOnlyTextAndPdf) {
      return {
        text: isLastTopicInCourse
          ? t('course.navigation.completeCourse', 'COMPLETE COURSE')
          : t('course.navigation.next', 'Next'),
        variant: 'contained',
        color: 'primary',
        disabled: false,
        tooltip: '',
        sx: isLastTopicInCourse
          ? {
              minWidth: '180px',
              textTransform: 'uppercase',
              fontWeight: 600,
            }
          : { minWidth: '100px' },
      };
    }

    // Tooltip message için tamamlanmamış içerik durumunu kontrol et
    let tooltipMessage = t(
      'course.navigation.tooltips.completeAllContent',
      'You must complete all required content to proceed.'
    );

    return {
      text: isLastTopicInCourse
        ? t('course.navigation.completeCourse', 'COMPLETE COURSE')
        : t('course.navigation.next', 'Next'),
      variant: 'contained',
      color: 'primary',
      disabled: true,
      tooltip: tooltipMessage,
      sx: isLastTopicInCourse
        ? {
            minWidth: '180px',
            textTransform: 'uppercase',
            fontWeight: 600,
          }
        : { minWidth: '100px' },
    };
  };

  return (
    <Box className={`topic-view ${isTransitioning ? `sliding-${slideDirection}` : ''}`}>
      <Box className="topic-view__sidebar">
        <TopicSidebar
          key={sidebarKey}
          course={course}
          currentChapterIndex={chapterId}
          currentTopicIndex={topicId}
          progress={progress}
          currentProgress={currentProgress}
          onTopicClick={async (chIdx, tIdx) => {
            let chapter = course.translations[currentLanguage].chapters.find(
              (ch) => ch._id === chIdx
            );
            let topic = chapter?.topics.find((t) => t._id === tIdx);

            if (!chapter || !topic) {
              try {
                await dispatch(fetchCourseDetails(courseId)).unwrap();
                // Güncellenmiş course datasını tekrar al
                const refreshedCourse = selectCurrentCourse(store.getState());
                const refreshedChapters =
                  refreshedCourse.translations?.[currentLanguage]?.chapters || [];
                chapter = refreshedChapters.find((ch) => ch._id === chIdx);
                topic = chapter?.topics.find((t) => t._id === tIdx);
                if (!chapter || !topic) {
                  return;
                }
                const nextUrl = getTopicUrl(courseId, chapter._id, topic._id);
                navigate(nextUrl);
              } catch (err) {
                console.error('Failed to reload course data:', err);
                return;
              }
              return;
            }
            const nextUrl = getTopicUrl(courseId, chapter._id, topic._id);
            navigate(nextUrl);
          }}
        />
      </Box>

      <Box className="topic-view__main">
        <Box className="topic-view__title-container">
          <Box className="topic-view__title-wrapper">
            {isTopicCompleted && (
              <Paper className="topic-view__completion-message">
                <span className="completion-message__icon">✓</span>
              </Paper>
            )}
            <Typography variant="h4" className="topic-view__title">
              {currentTopic?.title}
            </Typography>
          </Box>

          <Box className="topic-view__actions">
            <Box className="topic-view__actions-wrapper"></Box>
            <NavigationFooter
              hasPrevious={currentTopicIndex > 0 || currentChapterIndex > 0}
              hasNext={true}
              onPrevious={navigateToPreviousContent}
              onNext={handleNextClick}
              courseProgress={progress}
              isContentRequired={hasInteractivePlayground && !isTopicCompleted}
              isContentCompleted={isContentCompleted}
              quizScore={quizScore}
              quizPassingScore={quizPassingScore}
              nextButtonProps={{
                text: getNextButtonProps().text,
                variant: getNextButtonProps().variant,
                color: getNextButtonProps().color,
                disabled: getNextButtonProps().disabled,
                tooltip: getNextButtonProps().tooltip,
                className: `navigation-footer__button ${getNextButtonProps().text === t('course.navigation.completeCourse', 'COMPLETE COURSE') ? 'navigation-footer__button--complete' : ''}`,
              }}
            />
          </Box>
        </Box>

        <Box className="topic-view__content">
          <TopicContent
            key={videoPlayerKey}
            topic={currentTopic}
            courseId={courseId}
            chapterIndex={chapterId}
            topicIndex={topicId}
            currentTime={currentTime}
            onVideoProgress={null}
            onVideoEnded={handleVideoEnd}
            onQuizComplete={handleQuizComplete}
            onTimeUpdate={(time) => setCurrentTime(time)}
            onQuizScoreChange={(quizId, score, passingScore) => {
              setQuizScore(score);
              setQuizPassingScore(passingScore);
            }}
            isTopicCompleted={isTopicCompleted}
            onUsecaseGenerate={handleImageGenerate}
            onContentCompleted={(blockType, blockId) => {
              // İçeriği tamamlandı olarak işaretle
              setIsContentCompleted(true);

              // Eğer blockType ve blockId parametreleri geldiyse, ilgili bloğu tamamlanmış olarak işaretle
              if (blockType && blockId) {
                // Belirli blok türü için completedBlocks'u güncelle
                setCompletedBlocks((prev) => {
                  // Eğer belirtilen blok türü için bir state varsa onu güncelle
                  if (prev[blockType]) {
                    return {
                      ...prev,
                      [blockType]: {
                        ...prev[blockType],
                        [blockId]: true,
                      },
                    };
                  }

                  // Belirtilen blok türü için state yoksa, varsayılan state'i döndür
                  return prev;
                });
              }
            }}
            onFormSubmit={handleFormSubmit}
            onInteractiveComplete={handleInteractiveContentComplete}
          />
        </Box>
      </Box>
    </Box>
  );
};

TopicView.propTypes = {
  courseId: PropTypes.string.isRequired,
  chapterId: PropTypes.string.isRequired,
  topicId: PropTypes.string.isRequired,
  onCourseComplete: PropTypes.func,
};

export default TopicView;
