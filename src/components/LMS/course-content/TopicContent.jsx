import React from 'react';
import { Box } from '@mui/material';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import './TopicContent.scss';
import ContentBlock from './ContentBlock';

const TopicContent = ({
  topic,
  courseId,
  chapterIndex,
  topicIndex,
  onQuizComplete,
  onTimeUpdate,
  onVideoEnded,
  onUsecaseGenerate,
  onQuizScoreChange,
  isTopicCompleted,
  onContentCompleted,
  onFormSubmit,
  onInteractiveComplete,
}) => {
  // i18n hook'unu ekle
  const { i18n } = useTranslation();

  const currentLanguage = i18n.language || 'en';

  // Video tamamlandığında çağrılacak fonksiyon
  const handleVideoEnd = (videoId) => {
    if (onVideoEnded) {
      try {
        onVideoEnded(videoId);
      } catch (error) {
        console.error('Error in handleVideoEnd:', error);
      }
    }
  };

  // Quiz tamamlandığında çağrılacak fonksiyon
  const handleQuizComplete = (quizId, score, totalPoints, passingScore) => {
    if (onQuizComplete) {
      try {
        onQuizComplete(quizId, score, totalPoints, passingScore);
      } catch (error) {
        console.error('Error in handleQuizComplete:', error);
      }
    }
  };

  // Form gönderildiğinde çağrılacak fonksiyon
  const handleFormSubmit = (formId) => {
    if (onFormSubmit) {
      try {
        onFormSubmit(formId);
      } catch (error) {
        console.error('Error in handleFormSubmit:', error);
      }
    }
  };

  // Usecase generate edildiğinde çağrılacak fonksiyon
  const handleUsecaseGenerate = (usecaseId) => {
    if (onUsecaseGenerate) {
      try {
        onUsecaseGenerate(usecaseId);
      } catch (error) {
        console.error('Error in handleUsecaseGenerate:', error);
      }
    }
  };

  // Interactive content tamamlandığında çağrılacak fonksiyon
  const handleInteractiveComplete = (interactiveId) => {
    if (onInteractiveComplete) {
      try {
        onInteractiveComplete(interactiveId);
      } catch (error) {
        console.error('Error in handleInteractiveComplete:', error);
      }
    }
  };

  // Helper function to get video URL from topic
  const getVideoUrl = (topic) => {
    if (!topic?.contentBlocks) return null;

    // İlk video bloğunu bul
    const videoBlock = topic.contentBlocks.find((block) => block.type === 'video');
    if (!videoBlock || !videoBlock.videoContent) return null;

    // YouTube video kontrolü
    if (videoBlock.videoContent.type === 'youtube') {
      return videoBlock.videoContent.youtubeUrl;
    }

    if (videoBlock.videoContent.type === 'hls') {
      if (!videoBlock.videoContent.hlsData || !videoBlock.videoContent.hlsData.streamingUrls) {
        return null;
      }

      const streamingUrls = videoBlock.videoContent.hlsData.streamingUrls;

      if (Array.isArray(streamingUrls)) {
        if (streamingUrls.length === 0) {
          return null;
        }

        const firstUrl = streamingUrls[0];

        if (typeof firstUrl === 'object' && firstUrl !== null && firstUrl.paths) {
          if (Array.isArray(firstUrl.paths) && firstUrl.paths.length > 0) {
            return firstUrl.paths[0];
          } else if (typeof firstUrl.paths === 'string') {
            return firstUrl.paths;
          }
        } else if (typeof firstUrl === 'string') {
          return firstUrl;
        }
      } else if (typeof streamingUrls === 'object' && streamingUrls !== null) {
        if (Array.isArray(streamingUrls.paths) && streamingUrls.paths.length > 0) {
          return streamingUrls.paths[0];
        } else if (typeof streamingUrls.paths === 'string') {
          return streamingUrls.paths;
        } else if (
          streamingUrls._id &&
          Array.isArray(streamingUrls.paths) &&
          streamingUrls.paths.length > 0
        ) {
          return streamingUrls.paths[0];
        }
      } else if (typeof streamingUrls === 'string') {
        return streamingUrls;
      }
    }

    if (videoBlock.videoContent.url) {
      return videoBlock.videoContent.url;
    }

    return null;
  };

  const videoUrl = getVideoUrl(topic);

  // Her bir content block için çeviri bilgisini kontrol et ve ekle
  const ensureTranslations = (block) => {
    // Block null veya tanımsız ise, boş bir nesne döndür
    if (!block) return {};

    // Eğer block zaten translations içeriyorsa ve güncel dil varsa doğrudan kullan
    if (block.translations && block.translations[currentLanguage]) {
      return block;
    }

    // Eğer topic'de translations varsa ve ilgili content block için çeviri bilgisi varsa
    if (
      topic &&
      topic.translations &&
      topic.translations[currentLanguage] &&
      topic.translations[currentLanguage].contentBlocks
    ) {
      // Topic içindeki contentBlocks dizisinde bu bloğa karşılık gelen indeksi bul
      const blockIndex = topic.contentBlocks
        ? topic.contentBlocks.findIndex((b) => b === block)
        : -1;

      if (
        blockIndex !== -1 &&
        topic.translations[currentLanguage].contentBlocks.length > blockIndex
      ) {
        // Çeviriyi içeren bloku al
        const translatedBlock = topic.translations[currentLanguage].contentBlocks[blockIndex];

        // Çeviri bilgisini block içine ekle
        return {
          ...block,
          translations: {
            ...(block.translations || {}),
            [currentLanguage]: translatedBlock,
          },
        };
      }
    }

    return block;
  };

  const renderContentBlocks = () => {
    if (
      !topic ||
      !topic.translations ||
      !topic.translations[currentLanguage] ||
      !topic.translations[currentLanguage].contentBlocks ||
      topic.translations[currentLanguage].contentBlocks.length === 0
    ) {
      // Topic içinde contentBlocks yoksa, orijinal contentBlocks'u kullan
      if (topic && topic.contentBlocks && topic.contentBlocks.length > 0) {
        return (
          <Box className="topic-content__blocks">
            {topic.contentBlocks.map((block, index) => (
              <ContentBlock
                key={index}
                block={block}
                courseId={courseId}
                chapterIndex={chapterIndex}
                topicIndex={topicIndex}
                onQuizComplete={handleQuizComplete}
                onQuizScoreChange={onQuizScoreChange}
                isTopicCompleted={isTopicCompleted}
                onVideoEnded={handleVideoEnd}
                onTimeUpdate={onTimeUpdate}
                onUsecaseGenerate={handleUsecaseGenerate}
                onContentCompleted={onContentCompleted}
                onFormSubmit={handleFormSubmit}
                onInteractiveComplete={handleInteractiveComplete}
              />
            ))}
          </Box>
        );
      }

      return null;
    }

    return (
      <Box className="topic-content__blocks">
        {topic.translations[currentLanguage].contentBlocks.map((block, index) => {
          // Her blok için çeviri bilgisini kontrol et ve gerekirse ekle
          const blockWithTranslations = ensureTranslations(block);

          return (
            <ContentBlock
              key={index}
              block={blockWithTranslations}
              courseId={courseId}
              chapterIndex={chapterIndex}
              topicIndex={topicIndex}
              onQuizComplete={handleQuizComplete}
              onQuizScoreChange={onQuizScoreChange}
              isTopicCompleted={isTopicCompleted}
              onVideoEnded={handleVideoEnd}
              onTimeUpdate={onTimeUpdate}
              onUsecaseGenerate={handleUsecaseGenerate}
              onContentCompleted={onContentCompleted}
              onFormSubmit={handleFormSubmit}
              onInteractiveComplete={handleInteractiveComplete}
            />
          );
        })}
      </Box>
    );
  };

  // Doğrudan içerik bloklarını render et
  return (
    <Box className="topic-content">
      {/* <Typography variant="h5" gutterBottom>
        {topic.title}ddd
      </Typography> */}

      <Box className="topic-content__textBlock">
        {renderContentBlocks()}
      </Box>
    </Box>
  );
};

TopicContent.propTypes = {
  topic: PropTypes.object.isRequired,
  courseId: PropTypes.string.isRequired,
  chapterIndex: PropTypes.string,
  topicIndex: PropTypes.string,
  onVideoProgress: PropTypes.func,
  onQuizComplete: PropTypes.func,
  onTimeUpdate: PropTypes.func,
  onVideoEnded: PropTypes.func,
  onImageGenerate: PropTypes.func,
  onUsecaseGenerate: PropTypes.func,
  onQuizScoreChange: PropTypes.func,
  isTopicCompleted: PropTypes.bool,
  onContentCompleted: PropTypes.func,
  onFormSubmit: PropTypes.func,
  onInteractiveComplete: PropTypes.func,
};

export default TopicContent;
