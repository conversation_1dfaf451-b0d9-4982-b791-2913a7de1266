// Import variables
@use '@/styles/abstracts/_variables' as *;

// <PERSON><PERSON> stiller (değişkenler olmadan)
.favorite-button {
  color: rgba(0, 0, 0, 0.6) !important;
  font-weight: 500;
  text-transform: inherit !important;
  font-size: 0.875rem;
  
  &:hover {
    background-color: rgba(37, 99, 235, 0.05);
  }
}

.favorite-icon-button {
  color: rgba(0, 0, 0, 0.6);
  
  &:hover {
    background-color: rgba(37, 99, 235, 0.05);
    color: #f44336; // Favori butonuna hover yapıldığında kırmızı renk
  }
} 