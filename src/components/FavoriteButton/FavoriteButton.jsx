import PropTypes from 'prop-types';
import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Button, IconButton, CircularProgress } from '@mui/material';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import FavoriteIcon from '@mui/icons-material/Favorite';
import './FavoriteButton.scss';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '../../redux/features/auth/authSlice';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useTranslation } from 'react-i18next';
import {
  useGetUserShortcutsQuery,
  useAddShortcutMutation,
  useRemoveShortcutMutation,
  prefetchUserShortcuts
} from '../../redux/services/shortcuts-api';

/**
 * Favorite button - A reusable component for the entire application
 * @param {Object} props - Component props
 * @param {function} props.onClick - Click event handler (optional)
 * @param {string} props.text - Button text (default: "Add to Favorites")
 * @param {string} props.className - Additional CSS class
 * @param {boolean} props.iconOnly - Show only icon? (default: false)
 * @param {string} props.shortcutID - ID of the item to be added to favorites
 * @param {string} props.shortcutType - Type of the item to be added to favorites (default: "usecase")
 * @returns {React.Component} FavoriteButton component
 */
const FavoriteButton = ({
  onClick,
  text,
  className = '',
  iconOnly = false,
  shortcutID,
  shortcutType = 'usecase',
}) => {
  const { t } = useTranslation();
  
  // Initialize initial state as false for faster loading
  // but still indicate with null that it's unknown
  const [isFavorite, setIsFavorite] = useState(false);
  const [isInitialDataLoaded, setIsInitialDataLoaded] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Refs for process and click management
  const processingRef = useRef(false);
  const requestInProgressRef = useRef(false);
  const lastActionTimeRef = useRef(0);
  const actionTimeoutRef = useRef(null);
  
  // Shorter debounce time (faster response)
  const DEBOUNCE_TIME = 300; // 0.3 seconds
  
  // Ref to store the current favorite record's ID
  const currentShortcutRef = useRef(null);
  
  // Store the DB _id value of the most recently added or processed shortcut
  const lastShortcutDBIdRef = useRef(null);

  // Get logged-in user information from Redux
  const currentUser = useSelector(selectCurrentUser);
  const userId = currentUser?._id || '';

  // RTK Query Hooks - adding optimization settings
  const { 
    data: shortcuts, 
    isLoading: isLoadingShortcuts,
    isFetching: isFetchingShortcuts
  } = useGetUserShortcutsQuery(
    { userId, shortcutType },
    { 
      skip: !shortcutID || !userId, // Skip query if shortcutID or userId is missing
      refetchOnMountOrArgChange: false, // Don't query automatically when component mounts
      refetchOnFocus: false, // Don't query when page regains focus
      refetchOnReconnect: false, // Don't query when reconnecting after disconnection
    }
  );

  const [addShortcut, { isLoading: isAddingShortcut }] = useAddShortcutMutation();
  const [removeShortcut, { isLoading: isRemovingShortcut }] = useRemoveShortcutMutation();

  // Determine if operation is in progress (adding or removing favorite)
  // Don't show loading spinner if isInitialDataLoaded is true
  const isLoading = (isLoadingShortcuts && !isInitialDataLoaded) || 
                    isAddingShortcut || 
                    isRemovingShortcut || 
                    isProcessing;

  // Find current favorite record
  const findCurrentShortcut = useCallback(() => {
    if (!shortcuts || !shortcutID) return null;
    
    // Düzgün dizi al
    const shortcutsArray = Array.isArray(shortcuts) ? shortcuts : shortcuts?.data || [];
    
    // Eşleşen shortcut'ı bul
    return shortcutsArray.find(item => item.shortcutID === shortcutID);
  }, [shortcuts, shortcutID]);

  // Prefetch data when mounted
  useEffect(() => {
    if (userId && shortcutID) {
      // Attempt to prefetch data
      prefetchUserShortcuts(userId, shortcutType);
    }
  }, [userId, shortcutID, shortcutType]);

  // Check favorite status - optimized
  useEffect(() => {
    // Skip updating state if an operation is in progress (prevent race conditions)
    if (processingRef.current) return;
    
    if (shortcuts) {
      // Mark initial data as loaded
      setIsInitialDataLoaded(true);
      
      if (shortcutID) {
        // Check shortcutID match
        const shortcut = findCurrentShortcut();
        const isFav = Boolean(shortcut);
        
        // Store current favorite record in ref
        currentShortcutRef.current = shortcut;
        
        // If it's a favorite, also store the DB ID
        if (shortcut) {
          lastShortcutDBIdRef.current = shortcut._id;
        }
        
        setIsFavorite(isFav);
      } else {
        // If favorites are empty
        setIsFavorite(false);
        currentShortcutRef.current = null;
      }
    } else if (!isLoadingShortcuts && !isFetchingShortcuts) {
      // API query completed and no data, not in favorites
      setIsFavorite(false);
      setIsInitialDataLoaded(true);
    }
  }, [shortcuts, shortcutID, findCurrentShortcut, isLoadingShortcuts, isFetchingShortcuts]);

  // Cleanup operations
  useEffect(() => {
    return () => {
      // Clear timeout when component is unmounted
      if (actionTimeoutRef.current) {
        clearTimeout(actionTimeoutRef.current);
      }
    };
  }, []);

  // Add/remove favorite function - optimized
  const toggleFavorite = useCallback(async (e) => {
    // Prevent event propagation (don't bubble to parent elements)
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }

    // Exit if an operation is already in progress
    if (processingRef.current || requestInProgressRef.current || isLoading) {
      console.log('Operation already in progress, multiple requests blocked');
      return;
    }
    
    // Debounce mechanism: block if less than DEBOUNCE_TIME has passed since last action
    const now = Date.now();
    if (now - lastActionTimeRef.current < DEBOUNCE_TIME) {
      console.log('Rapid clicking detected, operation blocked');
      return;
    }
    lastActionTimeRef.current = now;
    
    // Show warning and exit if user is not logged in
    if (!userId) {
      toast.error(t('common.pleaseLogin'));
      return;
    }

    // Exit if shortcutID is missing
    if (!shortcutID) {
      console.error('shortcutID not specified');
      return;
    }

    // Operation started
    processingRef.current = true;
    requestInProgressRef.current = true;
    setIsProcessing(true);

    // Flag to track operation status
    let apiCallSuccess = false;

    try {
      if (isFavorite) {
        // Get current favorite record
        // First check currentShortcutRef, then lastShortcutDBIdRef, then search in shortcuts
        const shortcut = currentShortcutRef.current || findCurrentShortcut();
        
        // Check DB Shortcut ID
        const dbId = shortcut?._id || lastShortcutDBIdRef.current;
        
        if (!dbId) {
          throw new Error(t('common.shortcutNotFound'));
        }

        // Update UI immediately (optimistic)
        setIsFavorite(false);
        
        // Send record deletion request to API
        await removeShortcut({
          userId,
          _id: dbId,
          shortcutID // Needed for cache update
        }).unwrap();
        
        // Clear relevant refs
        lastShortcutDBIdRef.current = null;
        currentShortcutRef.current = null;
        
        apiCallSuccess = true;
        toast.success(t('apply.favorites.removedFromFavorites'));
      } else {
        // Update UI immediately (optimistic)
        setIsFavorite(true);
        
        // Send new record addition request to API
        const result = await addShortcut({
          userId,
          shortcutType,
          shortcutID
        }).unwrap();
        
        // Get _id from API response and store it
        if (result && result.data && result.data._id) {
          lastShortcutDBIdRef.current = result.data._id;
          // Also store new shortcut object (for subsequent delete operation)
          currentShortcutRef.current = result.data;
        } else if (result && result._id) {
          lastShortcutDBIdRef.current = result._id;
          // Also store new shortcut object (for subsequent delete operation)
          currentShortcutRef.current = result;
        }
        
        apiCallSuccess = true;
        toast.success(t('apply.favorites.addedToFavorites'));
      }

      // Call callback
      if (onClick && apiCallSuccess) {
        onClick(!isFavorite);
      }
    } catch (error) {
      console.error('Error changing favorite status:', error);
      
      // Revert UI if error occurs
      setIsFavorite(isFavorite);
      
      // Show error message
      toast.error(`${t('common.error')}: ${error.message || t('common.unknownError')}`);
    } finally {
      // We don't end the operation immediately, wait for debounce time to prevent multiple requests
      actionTimeoutRef.current = setTimeout(() => {
        setIsProcessing(false);
        processingRef.current = false;
        requestInProgressRef.current = false;
        actionTimeoutRef.current = null;
      }, DEBOUNCE_TIME);
    }
  }, [
    userId, 
    isFavorite, 
    shortcutID, 
    shortcutType, 
    addShortcut, 
    removeShortcut, 
    onClick, 
    t, 
    isLoading, 
    findCurrentShortcut
  ]);

  // Calculate button text
  const buttonText = useMemo(() => {
    if (text) return text;
    
    return isFavorite 
      ? t('apply.favorites.myFavorite') 
      : t('apply.favorites.addToFavorites');
  }, [text, isFavorite, t]);

  // Optimize loading spinner - don't show for very short periods
  const shouldShowSpinner = isLoading;

  // Fast button render
  if (iconOnly === true) {
    return (
      <IconButton
        onClick={toggleFavorite}
        className={`favorite-icon-button ${className} ${isFavorite ? 'is-favorite' : ''}`}
        aria-label={buttonText}
        title={buttonText}
        disabled={isLoading || !userId || !shortcutID}
        size="medium"
      >
        {shouldShowSpinner ? (
          <CircularProgress size={16} color="inherit" thickness={4} /> // Smaller spinner
        ) : (
          isFavorite ? <FavoriteIcon color="error" /> : <FavoriteBorderIcon />
        )}
      </IconButton>
    );
  }

  return (
    <Button
      startIcon={
        shouldShowSpinner ? (
          <CircularProgress size={16} color="inherit" thickness={4} /> // Smaller spinner
        ) : (
          isFavorite ? <FavoriteIcon color="error" /> : <FavoriteBorderIcon />
        )
      }
      onClick={toggleFavorite}
      className={`favorite-button ${className} ${isFavorite ? 'is-favorite' : ''}`}
      disabled={isLoading || !userId || !shortcutID}
    >
      {buttonText}
    </Button>
  );
};

FavoriteButton.propTypes = {
  onClick: PropTypes.func,
  text: PropTypes.string,
  className: PropTypes.string,
  iconOnly: PropTypes.bool,
  shortcutID: PropTypes.string,
  shortcutType: PropTypes.string,
};

export default FavoriteButton;
