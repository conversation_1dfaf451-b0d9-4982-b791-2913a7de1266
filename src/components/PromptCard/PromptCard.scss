@use '../../styles/abstracts/variables' as *;

// <PERSON>r<PERSON>n renkleri için stiller
.product-text {
  &--word {
    font-size: 13px;
    font-weight: 400;
    color: #185ABD;
  }
  
  &--excel {
    font-size: 13px;
    font-weight: 400;
    color: #217346;
  }
  
  &--powerpoint {
    font-size: 13px;
    font-weight: 400;
    color: #B7472A;
  }
  
  &--onenote {
    font-size: 13px;
    font-weight: 400;
    color: #7719AA;
  }
}

// App ikonları için flex container
.app-icons-container {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.prompt-card {
  padding: $spacing-3;
  height: calc(100% - $spacing-3 * 2);
  display: flex;
  flex-direction: column;
  background-color: $bg-paper !important;
  border: 1px solid $border-color !important;
  border-radius: $border-radius-md !important;

  &:hover {
    border-color: $primary-color !important;
    cursor: pointer;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-2;
  }

  &__provider-icon {
    width: 20px;
    height: 20px;
    object-fit: contain;
  }

  &__favorite {
    color: rgba($text-secondary, 0.5);
    padding: 4px !important;

    &:hover {
      color: $error-color;
      background-color: rgba($error-color, 0.04) !important;
    }
  }

  &__title {
    flex-grow: 1;
    margin-bottom: $spacing-3 !important;
    color: $text-primary;
    font-size: $font-size-md;
    font-weight: $font-weight-regular !important;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;

    &-highlight {
      background: linear-gradient(90deg, #ff75cd, #8449c0, #09e0b5);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: bold;
    }
  }

  &__footer {
    margin-top: auto;
  }

  &__try-in-word {
    display: inline-flex;
    align-items: center;
    gap: $spacing-1;
    padding: 4px 8px;
    border-radius: $border-radius-sm;
    background-color: rgba(#185ABD, 0.04);

    &:hover {
      background-color: rgba(#185ABD, 0.08);
    }
  }
  
  &__try-in-app {
    display: inline-flex;
    align-items: center;
    gap: $spacing-1;
    padding: 4px 8px;
    border-radius: $border-radius-sm;
    
    &--word {
      background-color: rgba(#185ABD, 0.04);
      &:hover {
        background-color: rgba(#185ABD, 0.08);
      }
      
      .product-text--word {
        color: #185ABD;
      }
    }
    
    &--excel {
      background-color: rgba(#217346, 0.04);
      &:hover {
        background-color: rgba(#217346, 0.08);
      }
      
      .product-text--excel {
        color: #217346;
      }
    }
    
    &--powerpoint {
      background-color: rgba(#B7472A, 0.04);
      &:hover {
        background-color: rgba(#B7472A, 0.08);
      }
      
      .product-text--powerpoint {
        color: #B7472A;
      }
    }
    
    &--onenote {
      background-color: rgba(#7719AA, 0.04);
      &:hover {
        background-color: rgba(#7719AA, 0.08);
      }
      
      .product-text--onenote {
        color: #7719AA;
      }
    }
  }

  &__app-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }
} 