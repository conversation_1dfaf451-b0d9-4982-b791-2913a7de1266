@use '../../styles/abstracts/variables' as *;

.app-card {
  background: $bg-paper;
  border: 1px solid $divider-color;
  border-radius: $border-radius-md;
  padding: $spacing-4;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: $spacing-3;
  height: calc(100% - ($spacing-4 * 2));

  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-md;
  }

  &__icon-wrapper {
    width: 48px;
    height: 48px;
    min-width: 48px;
    min-height: 48px;
    background: rgba($primary-color, 0.1);
    border-radius: $border-radius-sm;
    display: flex;
    align-items: center;
    justify-content: center;
    & > *{
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    svg {
      width: 24px;
      height: 24px;
      fill: $primary-color;
      path {
        fill: $primary-color;
      }
      & > g{
        fill: $primary-color !important;
        & >path {
          fill: $primary-color !important;
        }
      }
    }

    .MuiSvgIcon-root {
      color: $primary-color;
      font-size: 24px;
    }
    
    .app-card__svg-wrapper {
      width: 24px;
      height: 24px;
      
      .app-card__svg-image {
        filter: brightness(0) saturate(100%) invert(46%) sepia(45%) 
               saturate(7181%) hue-rotate(215deg) brightness(97%) contrast(98%);
      }
    }
  }

  &__title {
    font-size: $font-size-md !important;
    font-weight: $font-weight-medium;
    letter-spacing: -0.1px !important;
    color: $text-primary;
    margin: 0;
    padding:0 16px 0 0;
  }

  &__favorite {
    position: absolute;
    top: $spacing-3;
    right: $spacing-3;
    z-index: 10;
    
    .favorite-icon-button {
      padding: 4px !important;
      
      &:hover {
        background-color: rgba($primary-color, 0.1) !important;
      }
      
      &.is-favorite {
        color: $error-color;
      }
    }
    
    .MuiSvgIcon-root {
      color: $primary-color;
      font-size: 20px;
    }
  }

  &.used {
    border:1px solid rgba(#2e7d32, 0.1) !important;
    .app-card__icon-wrapper {
      background: rgba(#2e7d32, 0.1);

      .MuiSvgIcon-root {
        color: #2e7d32;
      }
      
      .app-card__svg-wrapper {
        .app-card__svg-image {
          filter: brightness(0) saturate(100%) invert(45%) sepia(26%) 
                 saturate(1410%) hue-rotate(89deg) brightness(91%) contrast(96%);
        }
      }
    }

    .app-card__favorite {
      .MuiSvgIcon-root {
        color: #2e7d32;
      }
      
      .favorite-icon-button {
        &.is-favorite {
          .MuiSvgIcon-root {
            color: $error-color !important;
          }
        }
      }
    }
  }

  &__svg-wrapper {
    width: 24px;
    height: 24px;
    
    .app-card__svg-icon {
      width: 100%;
      height: 100%;
      
      ::deep svg {
        path, circle, rect {
          fill: currentColor;
          stroke: currentColor;
        }
      }
    }
  }
}

.MuiTooltip-popper {
  .app-card-tooltip {
    background-color: #2e7d32;
    font-size: $font-size-xs;
    padding: $spacing-2 $spacing-3;
    border-radius: $border-radius-sm;
    font-weight: $font-weight-regular;
    
    &.MuiTooltip-tooltipPlacementTop {
      .MuiTooltip-arrow {
        color: #2e7d32;
      }
    }
    
    &.MuiTooltip-tooltipPlacementBottom {
      .MuiTooltip-arrow {
        color: #2e7d32;
      }
    }
  }
} 