import PropTypes from 'prop-types';
import { Box, Typography, Tooltip } from '@mui/material';
import GridViewIcon from '@mui/icons-material/GridView';
import { memo } from 'react';
import './AppCard.scss';
import FavoriteButton from '../FavoriteButton/FavoriteButton';

// Optimize edilmiş SVG icon bileşeni
const SvgIcon = memo(({ url }) => {
  if (!url) {
    return <GridViewIcon />;
  }

  // SVG içinde image element kullanarak external SVG referansı veriyoruz
  // Bu şekilde her SVG için ayrı fetch isteği yapmaya gerek kalmaz
  return (
    <svg width="24" height="24" viewBox="0 0 200 200" className="app-card__svg-wrapper">
      <image 
        width="200" 
        height="200" 
        xlinkHref={url}
        className="app-card__svg-image"
      />
    </svg>
  );
});

SvgIcon.displayName = 'SvgIcon';

// Performans için AppCard bileşenini memo ile sarıyoruz
const AppCard = memo(({ title, usecase_icon_url, onClick, usedCount = 0, appType, id }) => {
  const tooltipTitle =
    usedCount > 0 ? `You've tried this use case ${usedCount} time${usedCount > 1 ? 's' : ''}.` : '';

  // Handle card click but exclude the favorite button area
  const handleCardClick = (e) => {
    // Only trigger onClick if the click didn't come from favorite button
    if (!e.defaultPrevented && onClick) {
      onClick();
    }
  };

  const card = (
    <Box className={`app-card ${usedCount > 0 ? 'used' : ''}`} onClick={handleCardClick}>
      <Box className="app-card__icon-wrapper">
        <SvgIcon url={usecase_icon_url} />
      </Box>
      <Typography className="app-card__title">{title}</Typography>
      <Box className="app-card__favorite">
        <FavoriteButton 
          iconOnly={true} 
          shortcutID={id} 
          shortcutType={appType || "usecase"} 
        />
      </Box>
    </Box>
  );

  return usedCount > 0 ? (
    <Tooltip
      title={tooltipTitle}
      arrow
      placement="top"
      componentsProps={{
        tooltip: {
          className: 'app-card-tooltip',
        },
      }}
    >
      {card}
    </Tooltip>
  ) : (
    card
  );
});

AppCard.displayName = 'AppCard';

AppCard.propTypes = {
  title: PropTypes.string.isRequired,
  usecase_icon_url: PropTypes.string,
  onClick: PropTypes.func,
  usedCount: PropTypes.number,
  appType: PropTypes.string,
  id: PropTypes.string
};

export default AppCard;
