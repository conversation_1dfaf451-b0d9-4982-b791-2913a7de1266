import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Box,
  DialogActions,
  Button,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import './ConfirmationModal.scss';

const ConfirmationModal = ({
  open,
  onClose,
  title,
  content,
  onConfirm,
  maxWidth = 'md',
  fullWidth = true,
  showCloseButton = true,
}) => {
  const { t } = useTranslation();

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      className="ai-modal"
      scroll="paper"
    >
      <DialogTitle>
        <Box display="flex" alignItems="center">
          {title}
          {showCloseButton && (
            <IconButton onClick={onClose} className="close-button" size="small">
              <CloseIcon />
            </IconButton>
          )}
        </Box>
      </DialogTitle>
      <DialogContent>{content}</DialogContent>
      <DialogActions>
        <Button onClick={onClose}>{t('modal.cancel')}</Button>
        <Button onClick={onConfirm}>{t('modal.confirm')}</Button>
      </DialogActions>
    </Dialog>
  );
};

ConfirmationModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  content: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.node,
    PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.node])),
  ]).isRequired,
  maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
  fullWidth: PropTypes.bool,
  showCloseButton: PropTypes.bool,
  customContent: PropTypes.bool,
};

export default ConfirmationModal;
