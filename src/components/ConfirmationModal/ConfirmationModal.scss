@use '../../styles/abstracts/variables' as *;

.ai-modal {
  .MuiDialog-paper {
    border-radius: $border-radius-md;
    overflow: hidden; // Border radius için içeriğin taşmasını engelle
    max-height: 90vh; // Ekranın %90'ını geçmesin
    
    .MuiDialogTitle-root {
      padding: $spacing-2 $spacing-3;
      background-color: $bg-paper;
      border-bottom: 1px solid $divider-color;
      
      .close-button {
        position: absolute;
        right: $spacing-2;
        top: $spacing-2;
        color: $text-secondary;
        padding: $spacing-1;
        
        &:hover {
          color: $text-primary;
          background-color: $bg-light;
        }
      }
    }
    
    .MuiDialogContent-root {
      padding: $spacing-3;
      
      // Video stil
      video {
        width: 100%;
        border-radius: $border-radius-sm;
        background-color: $text-primary;
        aspect-ratio: 16 / 9;
      }

    }

      // Navigation buttons container
      .modal-navigation {
        margin-top: auto; // İçeriğin altına yapıştır
        border-top: 1px solid $divider-color;
        padding: $spacing-2;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: $spacing-2;

        // İlk step'te next butonu sağa yaslı olsun
        &:has(> :only-child) {
          justify-content: flex-end;
        }

        // Button wrapper for tooltip
        > span {
          display: inline-flex;
        }
      }
  }
} 