import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import Box from '@mui/material/Box';
import MuiContainer from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import './WhiteContainer.scss';
import { Typography } from '@mui/material';
import Button from '@mui/material/Button';
import { useTranslation } from 'react-i18next';

const WhiteContainer = ({
  title,
  subtitle,
  children,
  containerProps,
  showNavigation = false,
  selectLevel = false,
  selectedLevel = 'beginner',
  onLevelChange,
  variant = 'default',
  showMore = false,
  showMoreText = 'Show more',
  onShowMoreClick,
  id,
}) => {
  const [swiperInstance, setSwiperInstance] = useState(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const [shouldShowNavigation, setShouldShowNavigation] = useState(false);

  // Ekran genişliğine göre slidesPerView değerini hesapla
  const getMaxSlidesPerView = (carouselProps) => {
    const width = window.innerWidth;

    // Carousel'ın breakpoints'lerini kontrol et
    if (carouselProps?.breakpoints) {
      const breakpoints = Object.entries(carouselProps.breakpoints).sort(
        (a, b) => Number(b[0]) - Number(a[0])
      ); // Büyükten küçüğe sırala

      for (const [breakpoint, settings] of breakpoints) {
        if (width >= Number(breakpoint)) {
          return settings.slidesPerView;
        }
      }
    }

    // Eğer slidesPerView 'auto' değilse ve direkt bir sayı olarak belirtilmişse
    if (carouselProps?.slidesPerView && carouselProps.slidesPerView !== 'auto') {
      return carouselProps.slidesPerView;
    }

    // Varsayılan değerler
    if (width >= 1440) return 4;
    if (width >= 1024) return 3;
    if (width >= 768) return 2;
    return 1;
  };

  // Children içindeki item sayısını kontrol et
  useEffect(() => {
    let itemCount = 0;
    let hasCarousel = false;
    let carouselProps = null;

    React.Children.forEach(children, (child) => {
      if (
        React.isValidElement(child) &&
        (child.type.name === 'Carousel' || child.type.displayName === 'Carousel')
      ) {
        hasCarousel = true;
        carouselProps = {
          ...child.props?.swiperProps,
          ...(child.props?.breakpoints && { breakpoints: child.props.breakpoints }),
        };
        const childItems = React.Children.toArray(child.props?.children || []);
        itemCount = childItems.length;
      }
    });

    const maxSlidesPerView = getMaxSlidesPerView(carouselProps);
    setShouldShowNavigation(hasCarousel && itemCount > maxSlidesPerView);
  }, [children]);

  // Ekran boyutu değişikliklerini dinle
  useEffect(() => {
    const handleResize = () => {
      let itemCount = 0;
      let hasCarousel = false;
      let carouselProps = null;

      React.Children.forEach(children, (child) => {
        if (
          React.isValidElement(child) &&
          (child.type.name === 'Carousel' || child.type.displayName === 'Carousel')
        ) {
          hasCarousel = true;
          carouselProps = {
            ...child.props?.swiperProps,
            ...(child.props?.breakpoints && { breakpoints: child.props.breakpoints }),
          };
          const childItems = React.Children.toArray(child.props?.children || []);
          itemCount = childItems.length;
        }
      });

      const maxSlidesPerView = getMaxSlidesPerView(carouselProps);
      setShouldShowNavigation(hasCarousel && itemCount > maxSlidesPerView);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [children]);

  useEffect(() => {
    if (swiperInstance) {
      const updateNavigation = () => {
        setIsBeginning(swiperInstance.isBeginning);
        setIsEnd(swiperInstance.isEnd);
      };

      swiperInstance.on('slideChange', updateNavigation);
      swiperInstance.on('snapGridLengthChange', updateNavigation);

      // Initial state
      updateNavigation();

      return () => {
        swiperInstance.off('slideChange', updateNavigation);
        swiperInstance.off('snapGridLengthChange', updateNavigation);
      };
    }
  }, [swiperInstance]);

  const handlePrevClick = () => {
    if (swiperInstance && !isBeginning) {
      swiperInstance.slidePrev();
    }
  };

  const handleNextClick = () => {
    if (swiperInstance && !isEnd) {
      swiperInstance.slideNext();
    }
  };

  const handleSwiperInit = (swiper) => {
    setSwiperInstance(swiper);
    setIsBeginning(swiper.isBeginning);
    setIsEnd(swiper.isEnd);
  };

  const enhancedChildren = React.Children.map(children, (child) => {
    if (
      React.isValidElement(child) &&
      (child.type.name === 'Carousel' || child.type.displayName === 'Carousel')
    ) {
      return React.cloneElement(child, {
        onSwiper: handleSwiperInit,
        navigation: false,
        swiperProps: {
          navigation: false,
          allowTouchMove: true,
          observer: true,
          observeParents: true,
        },
      });
    }
    return child;
  });

  const { t } = useTranslation();

  return (
    <Box className={`white-container ${variant ? variant : ''}`} {...containerProps} id={id}>
      <MuiContainer sx={{ py: 0, pr: 0 }} maxWidth="lg">
        <Grid container spacing={0}>
          <Grid item xs={12}>
            <Grid container alignItems="center" justifyContent="space-between">
              {(title || subtitle) && (
                <Grid className="section-title-wrapper" item>
                  {title && (
                    <Typography variant="h2" className="section-title">
                      {title}
                    </Typography>
                  )}
                  {subtitle && (
                    <Typography variant="body1" className="section-subtitle">
                      {subtitle}
                    </Typography>
                  )}
                </Grid>
              )}
              {(selectLevel || shouldShowNavigation) && (
                <Grid item>
                  <Box className="actions-wrapper">
                    {selectLevel && (
                      <Box className="level-selector">
                        <Typography variant="body1" className="level-label">
                          {t('home.selectLevel')}:
                        </Typography>
                        <FormControl size="small">
                          <Select
                            value={selectedLevel}
                            onChange={onLevelChange}
                            className="level-select"
                            variant="outlined"
                            MenuProps={{
                              anchorOrigin: {
                                vertical: 'bottom',
                                horizontal: 'left',
                              },
                              transformOrigin: {
                                vertical: 'top',
                                horizontal: 'left',
                              },
                              PaperProps: {
                                elevation: 0,
                                sx: {
                                  backgroundColor: 'white',
                                  mt: 1,
                                  '& .MuiMenuItem-root': {
                                    fontSize: '14px',
                                    minHeight: '36px',
                                  },
                                },
                              },
                            }}
                          >
                            <MenuItem value="beginner">{t('home.levels.beginner')}</MenuItem>
                            <MenuItem value="expert">{t('home.levels.expert')}</MenuItem>
                            <MenuItem value="master">{t('home.levels.master')}</MenuItem>
                          </Select>
                        </FormControl>
                      </Box>
                    )}
                    {shouldShowNavigation && (
                      <Box className="custom-navigation">
                        <IconButton
                          className="nav-button prev"
                          onClick={handlePrevClick}
                          aria-label="Previous slide"
                          disabled={isBeginning}
                        >
                          <ArrowBackIcon />
                        </IconButton>
                        <IconButton
                          className="nav-button next"
                          onClick={handleNextClick}
                          aria-label="Next slide"
                          disabled={isEnd}
                        >
                          <ArrowForwardIcon />
                        </IconButton>
                      </Box>
                    )}
                  </Box>
                </Grid>
              )}
            </Grid>
          </Grid>
          <Grid item xs={12}>
            {enhancedChildren}
          </Grid>

          {showMore && (
            <Grid item xs={12}>
              <Box className="show-more-wrapper">
                <Button
                  variant="text"
                  onClick={onShowMoreClick}
                  className="show-more-button"
                  endIcon={<ArrowForwardIcon className="arrow-icon" />}
                >
                  {showMoreText}
                </Button>
              </Box>
            </Grid>
          )}
        </Grid>
      </MuiContainer>
    </Box>
  );
};

WhiteContainer.propTypes = {
  title: PropTypes.string,
  subtitle: PropTypes.string,
  children: PropTypes.node.isRequired,
  containerProps: PropTypes.object,
  showNavigation: PropTypes.bool,
  onPrevClick: PropTypes.func,
  onNextClick: PropTypes.func,
  selectLevel: PropTypes.bool,
  selectedLevel: PropTypes.string,
  onLevelChange: PropTypes.func,
  variant: PropTypes.oneOf(['default', 'transparent', 'backButtonTrue']),
  showMore: PropTypes.bool,
  showMoreText: PropTypes.string,
  onShowMoreClick: PropTypes.func,
  id: PropTypes.string,
};

export default WhiteContainer;
