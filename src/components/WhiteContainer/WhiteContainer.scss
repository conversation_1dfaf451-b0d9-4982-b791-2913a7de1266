@use '../../styles/abstracts/variables' as *;

.white-container {
  position: relative !important;
  // Ana stiller
  & {
    background: $bg-paper;
    border-radius: $border-radius-lg;
    padding: $spacing-4;
  }

  // Default varyant için stiller
  &.default  {
    padding: $spacing-5 !important;
    background-color: $bg-paper;
    border-radius: $border-radius-md;
    border: 1px solid $border-color;
  }
  &.backButtonTrue {
    padding: $spacing-5 !important;
    background-color: $bg-paper;
    border-radius: $border-radius-md;
    border-top-left-radius: 0 !important;
    border: 1px solid $border-color;
  }

  // Transparent varyant için stiller
  &.transparent {
    padding: 0 !important;
    background-color: transparent;
    border: none;
    margin: 0 !important;
    .section-title-wrapper {
      margin: 0 0 $spacing-1 0 !important;
    }
  }

  // Ortak stiller
  .MuiContainer-root {
    @media (min-width: $tablet) {
      padding: 0;
    }
    @media screen and (max-width: 768px) {
      padding: 0;
    }
  }

  .title-container {
    display: flex;
    flex-direction: row;
    & > .MuiGrid-item {
      width: 50%;
    }
  }

  .section-title {
    font-size: 20px;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin: 0;
    padding: 0;
  }

  .section-subtitle {
    color: $primary-text-color;
    font-size: $font-size-sm;
    font-weight: $font-weight-regular;
    padding: 0;
    margin: 6px 0 0 0;
  }

  .section-title-wrapper {
    margin: 0 0 $spacing-3 0;
  }

  .actions-wrapper {
    display: flex;
    align-items: center;
    gap: $spacing-4;
  }

  .level-selector {
    display: flex;
    align-items: center;
    gap: $spacing-3;

    .level-label {
      color: $text-secondary;
      font-size: $font-size-xs;
      font-weight: $font-weight-regular;
      white-space: nowrap;
    }

    .level-select {
      min-width: 130px;
      height: 36px;
      
      .MuiSelect-select {
        font-size: $font-size-sm;
        font-weight: $font-weight-regular;
        padding: $spacing-2 14px;
        background-color: $bg-paper;
        color: $text-primary;
      }

      .MuiOutlinedInput-notchedOutline {
        border-color: $border-color;
        border-radius: 6px;
      }
      
      &:hover .MuiOutlinedInput-notchedOutline {
        border-color: $primary-color;
      }
      
      &.Mui-focused .MuiOutlinedInput-notchedOutline {
        border-color: $primary-color;
        border-width: 1px;
      }

      .MuiMenu-paper {
        margin-top: $spacing-1;
        border-radius: 6px;
        border: 1px solid $border-color;
        box-shadow: $shadow-sm;
      }

      .MuiMenuItem-root {
        font-size: $font-size-sm;
        padding: $spacing-2 14px;
        color: $text-primary;
        
        &:hover {
          background-color: rgba($primary-color, 0.04);
        }
        
        &.Mui-selected {
          background-color: rgba($primary-color, 0.08);
          font-weight: $font-weight-medium;
          
          &:hover {
            background-color: rgba($primary-color, 0.12);
          }
        }
      }
    }
  }

  .custom-navigation {
    display: flex;
    gap: $spacing-2;

    .nav-button {
      width: 32px;
      height: 32px;
      background: $bg-paper;
      border-radius: 50%;
      border: 1px solid $border-color;
      color: $primary-color;
      transition: all 0.2s ease;
      padding: $spacing-1;

      &:hover {
        background: $bg-light;
        border: 1px solid $primary-color;
        transform: translateY(-2px);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .MuiSvgIcon-root {
        font-size: 20px;
      }
    }
  }

  .show-more-wrapper {
    display: flex;
    justify-content: flex-start;
    margin-top: $spacing-2;
    
    .show-more-button {
      color: $primary-color;
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      text-transform: none;
      letter-spacing: -0.3px;
      padding: 0;
      margin: $spacing-3 0 0 0;
      
      .arrow-icon {
        font-size: 18px;
        margin-left: -4px;
      }
      
      &:hover {
        background-color: transparent;
        text-decoration: underline;
      }
    }
  }

  // Medya sorguları
  @media (max-width: $tablet) {
    padding: $spacing-3;
    
    .actions-wrapper {
      gap: $spacing-3;
      flex-wrap: wrap;
    }
    
    .level-selector {
      .level-select {
        min-width: 100px;
      }
    }
  }
} 