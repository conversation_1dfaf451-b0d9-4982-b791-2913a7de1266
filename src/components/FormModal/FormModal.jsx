import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Box,
  Stepper,
  Step,
  StepLabel,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import Button from '../Button/Button';
import './FormModal.scss';
import { useTranslation } from 'react-i18next';

const FormModal = ({
  open,
  onClose,
  title,
  steps = [],
  children,
  showReview = false,
  onSubmit,
  maxWidth = 'md',
  fullWidth = true,
  showCloseButton = true,
  width,
  formErrors = {},
  onNext,
  loading = false,
  activeStep: externalActiveStep,
  values = {},
  onChange,
}) => {
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState(0);
  const [validationError, setValidationError] = useState(false);
  const [visitedSteps, setVisitedSteps] = useState([]);
  const [stepData, setStepData] = useState({});
  const formRenderRef = useRef(null);
  const hasSteps = steps && steps.length > 0;
  const totalSteps = hasSteps ? steps.length : 0;
  const isLastStep = activeStep === totalSteps - 1;
  const isReviewStep = showReview && activeStep === totalSteps;
  const shouldShowSubmit = !hasSteps || isReviewStep || (isLastStep && !showReview);

  useEffect(() => {
    if (open) {
      setVisitedSteps([0]);
      setValidationError(false);
      setStepData({});
    }
  }, [open]);

  useEffect(() => {
    if (externalActiveStep !== undefined && externalActiveStep !== activeStep) {
      setActiveStep(externalActiveStep);
      if (!visitedSteps.includes(externalActiveStep)) {
        setVisitedSteps([...visitedSteps, externalActiveStep]);
      }
    }
  }, [externalActiveStep]);

  const dialogStyle = width
    ? {
        '& .MuiDialog-paper': {
          width: width,
          maxWidth: width,
        },
      }
    : {};

  const resetForm = () => {
    setActiveStep(0);
    setVisitedSteps([0]);
    setValidationError(false);
    setStepData({});
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleStepDataChange = (data) => {
    if (!data || typeof data !== 'object') {
      return;
    }

    if (data && Object.keys(data).filter((key) => !key.startsWith('__')).length > 0) {
      const newData = { ...stepData };

      Object.keys(data).forEach((key) => {
        if (!key.startsWith('__')) {
          newData[key] = data[key];
        }
      });

      setStepData(newData);

      if (onChange) {
        onChange(data);
      }
    }

    if (steps && steps[activeStep] && steps[activeStep].onChange) {
      steps[activeStep].onChange(data);
    }
  };

  const evaluateFieldCondition = (field, formValues) => {
    if (!field.conditional_logic || !field.conditional_logic.enabled) {
      return true;
    }

    return field.conditional_logic.rules.some((ruleGroup) => {
      return ruleGroup.every((rule) => {
        if (!rule.field) {
          return true;
        }

        const fieldValue = formValues[rule.field];

        switch (rule.operator) {
          case 'equals':
          case '=':
          case '==':
            return fieldValue === rule.value;
          case 'not_equals':
          case '!=':
            if (rule.value === '') {
              return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
            }
            return fieldValue !== rule.value;
          case 'has_any_value':
            return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
          case 'is_empty':
            return fieldValue === undefined || fieldValue === null || fieldValue === '';
          case '>':
          case 'greater_than':
            return parseFloat(fieldValue) > parseFloat(rule.value);
          case '<':
          case 'less_than':
            return parseFloat(fieldValue) < parseFloat(rule.value);
          case 'contains':
            return typeof fieldValue === 'string' && fieldValue.includes(rule.value);
          case 'starts_with':
            return typeof fieldValue === 'string' && fieldValue.startsWith(rule.value);
          case 'ends_with':
            return typeof fieldValue === 'string' && fieldValue.endsWith(rule.value);
          default:
            return true;
        }
      });
    });
  };

  const validateRequiredFields = () => {
    if (isReviewStep) {
      return true;
    }

    if (formRenderRef.current && formRenderRef.current.validateForm) {
      console.log('Using FormRender validation...');
      const isValid = formRenderRef.current.validateForm();

      if (!isValid) {
        setValidationError(true);
        return false;
      }

      setValidationError(false);
      return true;
    }

    if (Object.keys(formErrors).length > 0) {
      const currentStep = activeStep;
      if (
        steps &&
        steps[currentStep] &&
        steps[currentStep].content &&
        steps[currentStep].content.props
      ) {
        const stepContent = steps[currentStep].content;

        if (stepContent.props.formData && stepContent.props.formData.fields) {
          const fields = stepContent.props.formData.fields;

          const visibleErrors = Object.keys(formErrors).filter((fieldName) => {
            const field = fields.find((f) => f.name === fieldName);
            if (!field) return true;

            if (!field.conditional_logic) return true;

            const isVisible = evaluateFieldCondition(field, values);
            console.log(`Field "${field.name}" visibility for error filtering: ${isVisible}`);

            return isVisible;
          });

          if (visibleErrors.length > 0) {
            setValidationError(true);
            return false;
          }
        }
      }

      console.log('Form errors found but no visible validation errors:', formErrors);
      setValidationError(false);
      return true;
    }

    setValidationError(false);
    return true;
  };

  const handleNext = async () => {
    if (!hasSteps || isReviewStep || (isLastStep && !showReview)) {
      if (!validateRequiredFields()) {
        return;
      }

      try {
        const result = onSubmit();

        if (result && typeof result.then === 'function') {
          const success = await result;
          if (success !== false) {
            handleClose();
          }
        } else if (result !== false) {
          handleClose();
        }
      } catch (error) {
        console.error('Form submission failed:', error);
      }
      return;
    }

    if (isLastStep && showReview) {
      if (!validateRequiredFields()) {
        return;
      }

      setActiveStep(totalSteps);
      if (!visitedSteps.includes(totalSteps)) {
        setVisitedSteps([...visitedSteps, totalSteps]);
      }
      return;
    }

    if (onNext) {
      let shouldProceed;
      try {
        shouldProceed = onNext();
      } catch (error) {
        console.error('Error in onNext function:', error);
        shouldProceed = false;
      }

      if (shouldProceed === false) {
        setValidationError(true);
        return;
      }
    }

    if (!validateRequiredFields()) {
      return;
    }

    const nextStep = activeStep + 1;
    setActiveStep(nextStep);

    if (!visitedSteps.includes(nextStep)) {
      setVisitedSteps([...visitedSteps, nextStep]);
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  const renderStepContent = (stepIndex) => {
    if (!hasSteps) {
      return React.Children.map(children, (child) =>
        React.cloneElement(child, {
          onChange: handleStepDataChange,
          errors: formErrors,
          ref: formRenderRef,
          values: stepData,
        })
      );
    }

    if (isReviewStep) {
      return (
        <Box className="form-modal__review">
          {steps.map((step, index) => (
            <Box key={index} className="form-modal__review-step">
              {React.cloneElement(step.content, {
                disabled: true,
                onChange: () => {},
                errors: formErrors,
                values: stepData,
              })}
            </Box>
          ))}
        </Box>
      );
    }

    const step = steps[stepIndex];

    return React.cloneElement(step.content, {
      onChange: handleStepDataChange,
      errors: formErrors,
      ref: formRenderRef,
      values: stepData,
    });
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth={width ? false : maxWidth}
      fullWidth={width ? false : fullWidth}
      className="form-modal"
      scroll="paper"
      sx={dialogStyle}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            {title}
            {hasSteps && (
              <Box className="form-modal__step-subtitle">
                {isReviewStep ? t('modal.reviewYourSelections') : steps[activeStep]?.title}
              </Box>
            )}
          </Box>
          {showCloseButton && (
            <IconButton onClick={handleClose} className="close-button" size="small">
              <CloseIcon />
            </IconButton>
          )}
        </Box>
      </DialogTitle>

      <Box className={`form-modal__content ${!hasSteps ? 'form-modal__content--no-steps' : ''}`}>
        {hasSteps && (
          <Box className="form-modal__stepper">
            <Stepper activeStep={activeStep} orientation="vertical">
              {steps.map((step, index) => (
                <Step key={index}>
                  <StepLabel>{step.label || `Step ${index + 1}`}</StepLabel>
                </Step>
              ))}
              {showReview && (
                <Step>
                  <StepLabel>{t('modal.review')}</StepLabel>
                </Step>
              )}
            </Stepper>
          </Box>
        )}

        <Box className="form-modal__form-content">
          <DialogContent className={validationError ? 'validation-error' : ''}>
            {renderStepContent(activeStep)}
          </DialogContent>

          <DialogActions
            className={`form-modal__actions ${!hasSteps ? 'form-modal__actions--right' : ''}`}
          >
            {hasSteps && (
              <Button
                variant="outlined"
                onClick={handleBack}
                disabled={activeStep === 0 || loading}
              >
                {t('modal.back')}
              </Button>
            )}
            <Button variant="contained" onClick={handleNext} color="primary" disabled={loading}>
              {shouldShowSubmit ? t('modal.submit') : t('modal.continue')}
            </Button>
          </DialogActions>
        </Box>
      </Box>
    </Dialog>
  );
};

FormModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  steps: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      label: PropTypes.string,
      content: PropTypes.node.isRequired,
    })
  ),
  children: PropTypes.node,
  showReview: PropTypes.bool,
  onSubmit: PropTypes.func.isRequired,
  maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
  fullWidth: PropTypes.bool,
  showCloseButton: PropTypes.bool,
  width: PropTypes.string,
  formErrors: PropTypes.object,
  onNext: PropTypes.func,
  loading: PropTypes.bool,
  activeStep: PropTypes.number,
  values: PropTypes.object,
  onChange: PropTypes.func,
};

export default FormModal;
