@use '../../styles/abstracts/variables' as *;

.form-modal {
  .MuiDialog-paper {
    border-radius: $border-radius-md;
    overflow: hidden;
    max-height: 90vh;
    
    .MuiDialogTitle-root {
      padding: $spacing-3 $spacing-3 !important;
      background-color: $bg-paper;
      border-bottom: 1px solid $divider-color;
      font-weight: $font-weight-semibold;
      font-size: $font-size-lg;
      
      .close-button {
        position: absolute;
        right: $spacing-3 !important;
        top: $spacing-3 !important;
        color: $text-secondary;
        padding: $spacing-1;
        
        &:hover {
          color: $text-primary;
          background-color: $bg-light;
        }
      }
    }

    .form-modal__step-subtitle {
      font-size: $font-size-sm;
      color: $text-secondary;
      margin-top: $spacing-1;
    }

    .form-modal__content {
      display: flex;
      min-height: 400px;
    }
    
    .form-modal__stepper {
      width: max-content !important;
      padding: $spacing-5;
      background-color: $bg-paper;
      display:flex;
      flex-direction: column;
      align-items: center;
      border-right: 1px solid $divider-color;

      .MuiStepLabel-label {
        font-size: $font-size-sm !important;
        
        &.Mui-active {
          color: $primary-color;
          font-weight: $font-weight-semibold;
        }
      }

      .MuiStepIcon-root {
        &.Mui-active {
          color: $primary-color;
        }
        &.Mui-completed {
          color: $success-color;
        }
      }

      .MuiStepper-vertical {
        .MuiStep-root {
          &:not(:last-child) {
            margin-bottom: $spacing-2;
          }
        }
      }
    }

    .form-modal__form-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .MuiDialogContent-root {
        padding: $spacing-3 !important;
        flex: 1;
        overflow-y: auto;

        .form-field {
          &.required {
            label::after {
              content: " *";
              color: $error-color;
            }
          }

          &.error {
            .MuiInputBase-root {
              border-color: $error-color;
              background-color: rgba($error-color, 0.02);
            }

            .error-message {
              color: $error-color;
              font-size: $font-size-sm;
              margin-top: $spacing-1;
            }
          }
        }
      }
    }

    .form-modal__review {
      .form-modal__review-step {
        margin-bottom: $spacing-4;
        border-bottom: 1px solid $divider-color;

        &:last-child {
          margin-bottom: 0;
          padding-bottom: 0;
          border-bottom: none;
        }

        .form-modal__review-step-title {
          color: $text-primary;
          margin-bottom: $spacing-3;
          font-weight: $font-weight-semibold;
        }

        // Disabled input styles
        .MuiInputBase-root.Mui-disabled {
          background-color: $bg-light;
          -webkit-text-fill-color: $text-secondary;
          
          .MuiOutlinedInput-notchedOutline {
            border-color: $divider-color;
          }
        }

        .MuiFormControl-root {
          margin-bottom: $spacing-3;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .MuiFormLabel-root.Mui-disabled {
          color: $text-secondary;
        }

        .MuiSelect-select.Mui-disabled,
        .MuiRadio-root.Mui-disabled,
        .MuiCheckbox-root.Mui-disabled {
          color: $text-secondary;
        }
      }
    }

    .form-modal__actions {
      padding: $spacing-3;
      border-top: 1px solid $divider-color;
      display: flex;
      justify-content: space-between;
      gap: $spacing-2;

      &--right {
        justify-content: flex-end;
      }
    }

    .validation-error {
      .styled-form-control {
        .MuiFormLabel-root.Mui-error,
        .MuiTextField-root .Mui-error {
          color: #d32f2f;
        }

        .MuiOutlinedInput-root.Mui-error fieldset,
        .MuiTextField-root .Mui-error fieldset {
          border-color: #d32f2f !important;
          border-width: 2px;
        }

        .required-marker {
          color: #d32f2f !important;
        }
      }

      .field-container {
        animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
      }
    }

    @keyframes shake {
      10%, 90% {
        transform: translate3d(-1px, 0, 0);
      }
      
      20%, 80% {
        transform: translate3d(2px, 0, 0);
      }
    
      30%, 50%, 70% {
        transform: translate3d(-4px, 0, 0);
      }
    
      40%, 60% {
        transform: translate3d(4px, 0, 0);
      }
    }
  }
}
 