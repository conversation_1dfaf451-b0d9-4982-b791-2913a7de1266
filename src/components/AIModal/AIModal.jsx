import { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Box,
  Tooltip,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import parse from 'html-react-parser';
import Button from '@/components/Button/Button.jsx';
import { useSelector } from 'react-redux';
import {
  useUpdateJourneyTrackingMutation,
  useGetJourneyTrackingQuery,
} from '../../redux/services/journey-api';
import './AIModal.scss';
import { useTranslation } from 'react-i18next';
import EnhancedVideoPlayer from '../LMS/EnhancedVideoPlayer';

const AIModal = ({
  open,
  onClose,
  title,
  content,
  maxWidth = 'md',
  fullWidth = true,
  showCloseButton = true,
  cardId = null,
  isCompleted = false,
  className = '',
  footer = null,
}) => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [isVideoCompleted, setIsVideoCompleted] = useState(isCompleted);
  const [completedVideoSteps, setCompletedVideoSteps] = useState(new Set());
  const [shouldAutoPlay, setShouldAutoPlay] = useState(true);
  const videoRef = useRef(null);
  const contentArray = Array.isArray(content) ? content : [content];
  const isLastStep = currentStep === contentArray.length - 1;
  const [showTooltip, setShowTooltip] = useState(false);

  const user = useSelector((state) => state.auth.user);
  const [updateJourneyTracking] = useUpdateJourneyTrackingMutation();

  // Kullanıcının mevcut journey tracking verilerini çekelim
  const { data: trackingData } = useGetJourneyTrackingQuery(user?._id, {
    skip: !user?._id || !open,
  });

  const isCurrentContentVideo = (content) => {
    return typeof content === 'string' && content.includes('<video') && content.includes('.m3u8');
  };

  useEffect(() => {
    if (open) {
      setIsVideoCompleted(isCompleted);
      setShouldAutoPlay(true);
    }
  }, [open, isCompleted]);

  const handleVideoEnded = async () => {
    setIsVideoCompleted(true);
    setCompletedVideoSteps((prev) => new Set([...prev, currentStep]));
    setShowTooltip(false);

    if (!isVideoCompleted && cardId && user?._id && user?.journeyLevel?.name) {
      try {
        // Kullanıcının journey level'ını alalım
        const userJourneyLevel = user.journeyLevel.name.toLowerCase();

        // Eğer trackingData varsa, mevcut verileri alıp güncelleyelim
        if (trackingData) {
          // Mevcut seviyedeki kartları alalım veya boş bir dizi oluşturalım
          const currentLevelCards = trackingData[userJourneyLevel] || [];

          // Eğer kart ID'si zaten listede varsa, tekrar eklemeyelim
          if (!currentLevelCards.includes(cardId)) {
            // Yeni kart ID'sini ekleyelim
            const updatedCards = [...currentLevelCards, cardId];

            // Journey tracking verilerini güncelleyelim
            await updateJourneyTracking({
              userId: user._id,
              journeyTracking: {
                [userJourneyLevel]: updatedCards,
              },
            }).unwrap();
          }
        } else {
          // Tracking verisi yoksa, sadece yeni card ID'sini gönderelim
          await updateJourneyTracking({
            userId: user._id,
            journeyTracking: {
              [userJourneyLevel]: [cardId],
            },
          }).unwrap();
        }

        // Video tamamlandıktan 2 saniye sonra modalı kapatalım
        setTimeout(() => {
          onClose({ completed: true, cardId });
        }, 2000);
      } catch (error) {
        console.error('Journey tracking update failed:', error);
      }
    }
  };

  const handleNext = () => {
    const currentContent = contentArray[currentStep];

    if (
      !isCurrentContentVideo(currentContent) ||
      isVideoCompleted ||
      completedVideoSteps.has(currentStep)
    ) {
      if (currentStep < contentArray.length - 1) {
        setCurrentStep((prev) => prev + 1);
        setIsVideoCompleted(false);
        setShouldAutoPlay(true);
      }
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
      setIsVideoCompleted(completedVideoSteps.has(currentStep - 1));
      setShouldAutoPlay(false);
    }
  };

  const handleComplete = async () => {
    setCurrentStep(0);
    setIsVideoCompleted(false);
    setCompletedVideoSteps(new Set());
    setShouldAutoPlay(true);

    if (cardId && user?._id && user?.journeyLevel?.name) {
      try {
        // Kullanıcının journey level'ını alalım
        const userJourneyLevel = user.journeyLevel.name.toLowerCase();

        // Journey tracking verilerini güncelleyelim
        await updateJourneyTracking({
          userId: user._id,
          journeyTracking: {
            [userJourneyLevel]: [cardId],
          },
        }).unwrap();
      } catch (error) {
        console.error('Journey tracking update failed:', error);
      }
    }

    // Modal kapanırken completed bilgisini gönder
    await onClose({ completed: true, cardId });
  };

  const hasText = (html) => {
    return (
      html.includes('<p') ||
      html.includes('<h1') ||
      html.includes('<h2') ||
      html.includes('<h3') ||
      html.includes('<h4') ||
      html.includes('<h5') ||
      html.includes('<h6')
    );
  };

  const isVideoContent = (html) => {
    return html.includes('.mp4') || html.includes('.m3u8') || html.includes('video');
  };

  const processContent = (html) => {
    // İçerikte video yoksa doğrudan parse et
    if (!isVideoContent(html)) {
      return parse(html);
    }

    console.log('Processing video content:', html);

    // M3U8 URL'sini regex ile doğrudan çıkar
    const m3u8Regex = /(https?:\/\/[^\s"']+\.m3u8[^\s"']*)/g;
    const m3u8Matches = html.match(m3u8Regex);
    const m3u8Url = m3u8Matches ? m3u8Matches[0] : null;

    console.log('Extracted m3u8 URL:', m3u8Url);

    // İframe'leri tespit et
    return parse(html, {
      replace: (domNode) => {
        // iframe etiketlerini EnhancedVideoPlayer ile değiştir
        if (domNode.type === 'tag' && domNode.name === 'iframe') {
          console.log('Found iframe:', domNode);

          // iframe src attribute'undan URL'yi al
          let videoUrl = '';
          if (domNode.attribs && domNode.attribs.src) {
            videoUrl = domNode.attribs.src;
          } else if (m3u8Url) {
            // Eğer iframe'in src'si yoksa ama içerikte m3u8 URL'si varsa onu kullan
            videoUrl = m3u8Url;
          }

          // Video URL'si varsa EnhancedVideoPlayer ile değiştir
          if (videoUrl && (videoUrl.includes('.m3u8') || videoUrl.includes('.mp4'))) {
            return (
              <EnhancedVideoPlayer
                videoUrl={videoUrl}
                onComplete={handleVideoEnded}
                ref={videoRef}
                autoPlay={shouldAutoPlay}
                preventAutoPlayAfterComplete={completedVideoSteps.has(currentStep)}
              />
            );
          }
        }

        return undefined; // Değişiklik yapılmayacak element için undefined döndür
      },
    });
  };

  const renderContent = (content) => {
    // İçeriği orijinal HTML sırasıyla işle
    return (
      <div
        className={`content-wrapper ${hasText(content) ? 'has-text' : ''} ${isVideoContent(content) ? 'has-video' : ''}`}
      >
        {processContent(content)}
      </div>
    );
  };

  const isNextDisabled = () => {
    const currentContent = contentArray[currentStep];
    return (
      !isCompleted && // Eğer tamamlanmamışsa
      isCurrentContentVideo(currentContent) &&
      !isVideoCompleted &&
      !completedVideoSteps.has(currentStep)
    );
  };

  const handleNextButtonMouseEnter = () => {
    if (isNextDisabled()) {
      setShowTooltip(true);
    }
  };

  const handleNextButtonMouseLeave = () => {
    setShowTooltip(false);
  };

  return (
    <Dialog
      open={open}
      onClose={() => onClose({ completed: false })}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      className={`ai-modal ${className}`}
      scroll="paper"
    >
      <DialogTitle>
        <Box display="flex" alignItems="center">
          {typeof title === 'string' && !title.includes('<svg') ? title : parse(title)}
          {showCloseButton && (
            <IconButton
              onClick={() => onClose({ completed: false })}
              className="close-button"
              size="small"
            >
              <CloseIcon />
            </IconButton>
          )}
        </Box>
      </DialogTitle>
      <DialogContent>{renderContent(contentArray[currentStep])}</DialogContent>

      {footer ? (
        <DialogActions className="modal-footer">{footer}</DialogActions>
      ) : contentArray.length > 1 ? (
        <Box className="modal-navigation">
          {currentStep > 0 && (
            <Button variant="text" size="medium" onClick={handlePrev} disabled={currentStep === 0}>
              {t('modal.previous')}
            </Button>
          )}
          <Tooltip
            title={t('modal.watchVideoFirst')}
            placement="top"
            arrow
            open={showTooltip && isNextDisabled()}
          >
            <span
              onMouseEnter={handleNextButtonMouseEnter}
              onMouseLeave={handleNextButtonMouseLeave}
            >
              <Button
                variant={isLastStep || !isNextDisabled() ? 'contained' : 'text'}
                color={isLastStep || !isNextDisabled() ? 'primary' : 'default'}
                size="medium"
                onClick={isLastStep ? handleComplete : handleNext}
                disabled={isNextDisabled()}
              >
                {isLastStep ? t('modal.complete') : t('modal.next')}
              </Button>
            </span>
          </Tooltip>
        </Box>
      ) : null}
    </Dialog>
  );
};

AIModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  content: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.node,
    PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.node])),
  ]).isRequired,
  maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
  fullWidth: PropTypes.bool,
  showCloseButton: PropTypes.bool,
  cardId: PropTypes.string,
  isCompleted: PropTypes.bool,
  className: PropTypes.string,
  footer: PropTypes.node,
};

export default AIModal;
