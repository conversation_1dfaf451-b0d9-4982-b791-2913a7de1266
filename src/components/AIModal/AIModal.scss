@use '../../styles/abstracts/variables' as *;

.ai-modal {
  .MuiDialog-paper {
    border-radius: $border-radius-md;
    overflow: hidden; // Border radius için içeriğin taşmasını engelle
    max-height: 90vh; // Ekranın %90'ını geçmesin
    
    .MuiDialogTitle-root {
      padding: $spacing-3 $spacing-3 !important;
      background-color: $bg-paper;
      border-bottom: 1px solid $divider-color;
      font-weight: $font-weight-semibold;
      font-size: $font-size-lg;
      .close-button {
        position: absolute;
        right: $spacing-3 !important;
        top: $spacing-3 !important;
        color: $text-secondary;
        padding: $spacing-1;
        
        &:hover {
          color: $text-primary;
          background-color: $bg-light;
        }
      }
    }
    
    .MuiDialogContent-root {
      padding: $spacing-3 !important;
      
      // Video stil
      video {
        width: 100%;
        border-radius: $border-radius-sm;
        background-color: $text-primary;
        aspect-ratio: 16 / 9;
      }

      // Heading styles
      h1, h2, h3, h4, h5, h6 {
        color: $text-primary;
        margin-bottom: $spacing-1 !important;
        font-weight: $font-weight-semibold;
        margin-top:0 !important;
        padding: 0 !important;
      }

      h1 { font-size: $font-size-xl; }
      h2 { font-size: $font-size-lg; }
      h3 { font-size: $font-size-md; }
      h4 { font-size: $font-size-sm; }
      h5, h6 { font-size: $font-size-xs; }

      img{
        margin:0 !important;
        max-height: 380px !important;
        object-fit: cover;
      }
      // Paragraph styles
      p {
        color: $text-secondary;
        font-size: $font-size-sm;
        line-height: 1.6;
        margin-bottom: $spacing-3;
        &:last-child{
          margin-bottom: 0 !important; 
        }
      }

      // List styles
      ul, ol {
        color: $text-secondary;
        font-size: $font-size-md;
        padding-left: $spacing-4;
        margin-bottom: $spacing-3;

        li {
          margin-bottom: $spacing-2;
        }
      }

      // Image styles
      img {
        max-width: 100%;
        height: auto;
        border-radius: $border-radius-md;
        margin: $spacing-3 0;
      }
    }

    // Navigation buttons container
    .modal-navigation {
      margin-top: auto; // İçeriğin altına yapıştır
      border-top: 1px solid $divider-color;
      padding: $spacing-2;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: $spacing-2;

      // İlk step'te next butonu sağa yaslı olsun
      &:has(> :only-child) {
        justify-content: flex-end;
      }

      // Button wrapper for tooltip
      > span {
        display: inline-flex;
      }
    }
    
    // Özel footer için stil
    .modal-footer {
      margin-top: auto;
      border-top: 1px solid $divider-color;
      padding: $spacing-3 !important;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      // Butonlar arası mesafe
      .MuiStack-root {
        width: 100%;
      }
    }
  }
} 