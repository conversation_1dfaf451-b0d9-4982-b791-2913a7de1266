import React, { useState, Suspense, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  IconButton,
  Menu,
  MenuItem,
  Button,
  Container,
  useTheme,
  useMediaQuery,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Divider,
  Avatar,
  ListItemIcon,
  CircularProgress,
} from '@mui/material';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import MenuIcon from '@mui/icons-material/Menu';
import LanguageIcon from '@mui/icons-material/Language';
import PersonIcon from '@mui/icons-material/Person';
import aibsDefaultLogo from '../../assets/aibs-logo.png';
import { useSelector, useDispatch } from 'react-redux';
import { logOut, selectCurrentUser } from '../../redux/features/auth/authSlice';
import {
  selectGeneralSettings,
  setGeneralSettings,
} from '../../redux/features/settings/settingsSlice';
import './Header.scss';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import FormModal from '../FormModal/FormModal';
import FormRender from '../FormRender/FormRender';
import 'react-toastify/dist/ReactToastify.css';
import { useHelpFormContext } from '../Layout/Layout';
import PanelButton from '../PanelButton/PanelButton';
import { useGetGeneralSettingsQuery } from '../../redux/services/platform-settings-api';

// Menu configuration - we will get this from API later
const MENU_CONFIG = {
  navigation: [
    { id: 'training', text: 'header.training', path: '/training' },
    { id: 'apply', text: 'header.apply', path: '/apply' },
    { id: 'create', text: 'header.create', path: '/create' },
    { id: 'innovate', text: 'header.innovate', path: '/innovate' },
    { id: 'cockpit', text: 'header.cockpit', path: '/cockpit' },
  ],
  userMenu: [
    { id: 'account', text: 'header.accountSettings', path: '/account-settings' },
    { id: 'help', text: 'header.help', path: '#help' },
  ],
};

const HeaderContent = () => {
  const { t, i18n } = useTranslation();
  const [profileAnchorEl, setProfileAnchorEl] = useState(null);
  const [navAnchorEl, setNavAnchorEl] = useState(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeMenu, setActiveMenu] = useState(null);
  const [languageAnchorEl, setLanguageAnchorEl] = useState(null);
  const open = Boolean(profileAnchorEl);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const user = useSelector(selectCurrentUser);
  const generalSettings = useSelector(selectGeneralSettings);
  const dispatch = useDispatch();
  const location = useLocation();

  // Layout'tan gelen help form context'ini kullanıyoruz
  const {
    isHelpModalOpen,
    supportFormData,
    isLoading,
    error,
    isSubmittingForm,
    helpFormData,
    helpFormErrors,
    hasInteracted,
    resetCounter,
    formRenderRef,
    handleOpenHelpModal,
    handleCloseHelpModal,
    handleSubmitHelpForm,
    handleHelpFormChange,
  } = useHelpFormContext();

  const {
    data: generalSettingsData,
    refetch: refetchGeneralSettings,
    isLoading: isGeneralSettingsLoading,
  } = useGetGeneralSettingsQuery();

  const platformLogo = isGeneralSettingsLoading
    ? null
    : generalSettingsData?.data?.platformLogo || aibsDefaultLogo;

  useEffect(() => {
    // Önce localStorage'dan dil tercihini kontrol et
    const savedLanguage = localStorage.getItem('userLanguage');

    if (savedLanguage) {
      i18n.changeLanguage(savedLanguage);
    } else if (user?.onboarding?.language) {
      // Eğer localStorage'da dil tercihi yoksa, onboarding'deki dili kullan
      i18n.changeLanguage(user.onboarding.language);
      // Dil tercihini localStorage'a kaydet
      localStorage.setItem('userLanguage', user.onboarding.language);
    }
  }, [user, i18n]);

  useEffect(() => {
    if (generalSettings === null) {
      refetchGeneralSettings().then((data) => {
        dispatch(setGeneralSettings(data.data.data));
      });
    }
  }, [generalSettings, refetchGeneralSettings]);

  // Link yönlendirmelerini kontrol eden fonksiyon
  const getRedirectPath = (originalPath) => {
    return user?.onboarding === null ? '/' : originalPath;
  };

  const handleProfileClick = (event) => {
    setProfileAnchorEl(event.currentTarget);
  };

  const handleProfileClose = () => {
    setProfileAnchorEl(null);
  };

  const handleNavMenuOpen = (event, menuId) => {
    setNavAnchorEl(event.currentTarget);
    setActiveMenu(menuId);
  };

  const handleNavMenuClose = () => {
    setNavAnchorEl(null);
    setActiveMenu(null);
  };

  const handleMobileMenuToggle = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleLanguageClick = (event) => {
    setLanguageAnchorEl(event.currentTarget);
  };

  const handleLanguageClose = () => {
    setLanguageAnchorEl(null);
  };

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
    // Dil değişikliğini localStorage'a kaydet
    localStorage.setItem('userLanguage', lng);
    handleLanguageClose();
  };

  const handleLogout = () => {
    localStorage.removeItem('i18nextLng');
    localStorage.removeItem('userLanguage');
    dispatch(logOut());
  };

  // Aktif menü linkini kontrol eden fonksiyon
  const isActiveLink = (path) => {
    return location.pathname === path;
  };

  const renderMobileMenu = () => (
    <Drawer
      anchor="right"
      open={mobileMenuOpen}
      onClose={handleMobileMenuToggle}
      className="mobile-menu"
    >
      <Box className="mobile-menu-container">
        <List>
          {/* Kullanıcı Profili */}
          <ListItem className="mobile-user-info">
            <AccountCircleIcon className="mobile-user-icon" />
            <ListItemText primary={user?.name} secondary={user?.email} />
          </ListItem>
          <Divider />

          {/* Navigation Links */}
          {MENU_CONFIG.navigation.map((link) => (
            <div key={link.id}>
              <ListItem
                component={link.submenu ? 'div' : Link}
                to={!link.submenu ? getRedirectPath(link.path) : undefined}
                onClick={link.submenu ? null : handleMobileMenuToggle}
                className={`mobile-menu-item ${link.submenu ? 'has-submenu' : ''}`}
              >
                <ListItemText primary={t(link.text)} />
              </ListItem>

              {link.submenu && (
                <List component="div" disablePadding className="submenu">
                  {link.submenu.map((subItem) => (
                    <ListItem
                      key={subItem.id}
                      component={Link}
                      to={getRedirectPath(subItem.path)}
                      onClick={handleMobileMenuToggle}
                      className="mobile-menu-item submenu-item"
                    >
                      <ListItemText primary={t(subItem.text)} inset />
                    </ListItem>
                  ))}
                </List>
              )}
            </div>
          ))}
          <Divider />

          {/* Menu Items */}
          {MENU_CONFIG.userMenu.map((item) => (
            <ListItem
              key={item.id}
              component={item.path === '#help' ? 'div' : Link}
              to={item.path !== '#help' ? getRedirectPath(item.path) : undefined}
              onClick={item.path === '#help' ? handleOpenHelpModal : handleMobileMenuToggle}
              className="mobile-menu-item"
            >
              <ListItemText primary={t(item.text)} />
            </ListItem>
          ))}
          <Divider />

          {/* Sign Out */}
          <ListItem onClick={handleLogout} className="mobile-menu-item sign-out">
            <ListItemText primary={t('header.signOut')} />
          </ListItem>
        </List>
      </Box>
    </Drawer>
  );

  return (
    <AppBar className="header" position="static">
      <Container maxWidth="lg">
        <Toolbar className="toolbar">
          {/* Logo Alanı */}
          <Link to="/" className="logo-wrapper">
            {isGeneralSettingsLoading ? (
              <CircularProgress size={24} />
            ) : (
              <img src={platformLogo} alt={t('header.logo')} className="logo-image" />
            )}
          </Link>

          {/* Desktop Menu */}
          {!isMobile && (
            <>
              <Box className="nav-links">
                {MENU_CONFIG.navigation.map((link) =>
                  link.submenu ? (
                    <Box key={link.id}>
                      <Button
                        className="nav-link"
                        endIcon={<KeyboardArrowDownIcon />}
                        onClick={(e) => handleNavMenuOpen(e, link.id)}
                      >
                        {t(link.text)}
                      </Button>
                      <Menu
                        anchorEl={navAnchorEl}
                        open={activeMenu === link.id}
                        onClose={handleNavMenuClose}
                        onClick={handleNavMenuClose}
                        PaperProps={{ elevation: 3, sx: { mt: 1.5 } }}
                        transformOrigin={{ horizontal: 'left', vertical: 'top' }}
                        anchorOrigin={{ horizontal: 'left', vertical: 'bottom' }}
                      >
                        {link.submenu.map((item) => (
                          <MenuItem
                            key={item.id}
                            component={Link}
                            to={getRedirectPath(item.path)}
                            onClick={handleNavMenuClose}
                          >
                            {t(item.text)}
                          </MenuItem>
                        ))}
                      </Menu>
                    </Box>
                  ) : (
                    <Link
                      key={link.id}
                      to={getRedirectPath(link.path)}
                      className={`nav-link ${isActiveLink(link.path) ? 'active' : ''}`}
                    >
                      {t(link.text)}
                    </Link>
                  )
                )}
              </Box>

              <Box className="profile-area">
                {user?.role === 'Administrator' && (
                  <>
                    <IconButton
                      color="inherit"
                      onClick={handleLanguageClick}
                      className="language-button"
                    >
                      <LanguageIcon />
                    </IconButton>
                    <Menu
                      anchorEl={languageAnchorEl}
                      open={Boolean(languageAnchorEl)}
                      onClose={handleLanguageClose}
                      className="language-menu"
                    >
                      <MenuItem onClick={() => changeLanguage('en')}>
                        <ListItemIcon>
                          <img src="/flags/en.webp" alt="English" style={{ width: 24 }} />
                        </ListItemIcon>
                        <ListItemText primary="English" />
                      </MenuItem>
                      <MenuItem onClick={() => changeLanguage('de')}>
                        <ListItemIcon>
                          <img src="/flags/de.webp" alt="Deutsch" style={{ width: 24 }} />
                        </ListItemIcon>
                        <ListItemText primary="Deutsch" />
                      </MenuItem>
                    </Menu>
                  </>
                )}

                <Button
                  onClick={handleProfileClick}
                  startIcon={
                    <Avatar className="profile-avatar">
                      <PersonIcon />
                    </Avatar>
                  }
                  className="profile-button"
                  endIcon={null}
                >
                  {user?.name}
                </Button>
                <Menu
                  anchorEl={profileAnchorEl}
                  open={open}
                  onClose={handleProfileClose}
                  className="profile-menu"
                  MenuListProps={{ 'aria-labelledby': 'basic-button' }}
                  anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                  transformOrigin={{ vertical: 'top', horizontal: 'right' }}
                >
                  <MenuItem className="menu-user-info">
                    <Box className="menu-user-content">
                      <Avatar className="menu-avatar">
                        <PersonIcon />
                      </Avatar>
                      <Box className="user-info">
                        <Typography variant="subtitle1">{user?.name}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {user?.email}
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>

                  <Divider className="menu-divider" />

                  {MENU_CONFIG.userMenu.map((item) => (
                    <MenuItem
                      key={item.id}
                      onClick={item.path === '#help' ? handleOpenHelpModal : handleProfileClose}
                      component={item.path !== '#help' ? Link : 'div'}
                      to={item.path !== '#help' ? getRedirectPath(item.path) : undefined}
                      className="menu-item"
                    >
                      {t(item.text)}
                    </MenuItem>
                  ))}

                  <Divider className="menu-divider" />
                  {user?.role === 'Administrator' && <PanelButton size="small" />}
                  <Divider className="menu-divider" />

                  <MenuItem
                    onClick={() => {
                      handleProfileClose();
                      handleLogout();
                    }}
                    className="menu-item sign-out"
                  >
                    {t('header.signOut')}
                  </MenuItem>
                </Menu>
              </Box>
            </>
          )}

          {/* Mobile Menu Button */}
          {isMobile && (
            <IconButton className="mobile-menu-button" onClick={handleMobileMenuToggle} edge="end">
              <MenuIcon />
            </IconButton>
          )}
        </Toolbar>
      </Container>
      {renderMobileMenu()}

      {/* Help Modal */}
      <FormModal
        open={isHelpModalOpen}
        onClose={handleCloseHelpModal}
        title={supportFormData?.topics?.[0]?.title || t('header.contactUs')}
        onSubmit={handleSubmitHelpForm}
        width="600px"
        loading={isSubmittingForm}
        formErrors={helpFormErrors}
        skipInitialValidation={!hasInteracted}
      >
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error">{t('header.supportFormNotFound')}</Typography>
        ) : isSubmittingForm ? (
          <Box sx={{ position: 'relative' }}>
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                zIndex: 2,
              }}
            >
              <Box sx={{ textAlign: 'center' }}>
                <CircularProgress size={60} />
                <Typography sx={{ mt: 2 }}>{t('header.submittingRequest')}</Typography>
              </Box>
            </Box>
            {/* Form işlemi sırasında */}
            <Box>
              <FormRender
                formData={supportFormData}
                hideTitle
                hideDescription
                onChange={handleHelpFormChange}
                values={helpFormData}
                defaultValues={helpFormData}
                initialValues={helpFormData}
                formInitialValues={helpFormData}
                disabled={isSubmittingForm}
                errors={helpFormErrors}
                ref={formRenderRef}
              />
            </Box>
          </Box>
        ) : (
          <Box>
            {/* Form bileşeni */}
            <Box key={`form-container-${resetCounter}`}>
              <FormRender
                key={`form-render-${resetCounter}`}
                formData={supportFormData}
                hideTitle
                hideDescription
                onChange={handleHelpFormChange}
                values={helpFormData}
                defaultValues={helpFormData}
                initialValues={helpFormData}
                formInitialValues={helpFormData}
                disabled={isSubmittingForm}
                errors={helpFormErrors}
                ref={formRenderRef}
              />
            </Box>
          </Box>
        )}
      </FormModal>

      {/* Panel Button */}
    </AppBar>
  );
};

const Header = () => {
  return (
    <Suspense
      fallback={
        <Box
          sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '64px' }}
        >
          <CircularProgress />
        </Box>
      }
    >
      <HeaderContent />
    </Suspense>
  );
};

export default Header;
