import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import Header from './Header';

// Mock store oluşturuyoruz
const mockStore = configureStore({
  reducer: {
    auth: (state = {
      user: {
        name: '<PERSON>',
        email: '<EMAIL>'
      }
    }) => state
  }
});

export default {
  title: 'Components/Header',
  component: Header,
  decorators: [
    (Story) => (
      <Provider store={mockStore}>
        <BrowserRouter>
          <Story />
        </BrowserRouter>
      </Provider>
    )
  ],
  parameters: {
    layout: 'fullscreen',
  },
};

// Default story
export const Default = {
  args: {}
};

// Mobile view story
export const Mobile = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
};

// Tablet view story
export const Tablet = {
  parameters: {
    viewport: {
      defaultViewport: 'tablet'
    }
  }
};
