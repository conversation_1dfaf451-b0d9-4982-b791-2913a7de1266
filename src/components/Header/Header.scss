.header {
  background-color: #fff !important;
  height: 108px;
  border-bottom: 1px solid #ecede8;
  box-shadow: none !important;

  .toolbar {
    justify-content: space-between;
    height: 100%;
    min-height: 108px !important;
    padding: 0;
  }

  .logo-wrapper {
    color: #122b46;
    height: 100%;
    display: flex;
    align-items: center;
    text-decoration: none;

    .logo-image {
      width: 168px;
      height: auto;
      object-fit: contain;
    }
  }

  .nav-links {
    display: flex;
    align-items: center;
    gap:32px;
    height: 100%;

    .nav-item-wrapper {
      display: inline-block;
      position: relative;

      &:hover {
        .submenu-dropdown {
          display: block;
          animation: fadeIn 0.2s ease-in-out;
        }

        .submenu-indicator {
          transform: rotate(180deg);
        }
      }
    }

    .has-submenu {
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 4px;
      color: white;

      .submenu-indicator {
        font-size: 10px;
        transition: transform 0.2s ease;
        display: inline-block;
        margin-left: 4px;
        color: white;
      }
    }

    .submenu-dropdown {
      display: none;
      background: #2c3e50;  // Koyu arka plan
      border-radius: 4px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
      min-width: 220px;
      margin-top: 8px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      
      &::before {
        content: '';
        position: absolute;
        top: -6px;
        left: 20px;
        width: 12px;
        height: 12px;
        background: #2c3e50;
        transform: rotate(45deg);
        border-left: 1px solid rgba(255, 255, 255, 0.1);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
      }
      
      .submenu-item {
        display: block;
        padding: 12px 16px;
        color: white;
        text-decoration: none;
        transition: all 0.2s ease;
        font-size: 14px;
        
        &:hover {
          background-color: #34495e;
          padding-left: 20px;
        }

        &.active {
          background-color: #3498db;
        }

        &:first-child {
          border-radius: 4px 4px 0 0;
        }

        &:last-child {
          border-radius: 0 0 4px 4px;
        }
      }
    }

    .nav-link {
      color: #122b46;
      text-decoration: none;
      margin: 0 16px;
      font-size: 16px;
      font-weight: 400;
      text-transform: none;
      position: relative;

      &:hover {
        color: #377dff;
        background: transparent;
      }

      &.active {
        color: #377dff;

      }
    }
  }

  .profile-area {
    height: 100%;
    display: flex;
    align-items: center;
    gap: 16px;

    .language-button {
      color: #122b46;
      padding: 8px;

      &:hover {
        background-color: rgba(55, 125, 255, 0.1);
      }

      .MuiSvgIcon-root {
        font-size: 24px;
      }
    }

    .profile-button {
      color: #122b46;
      text-transform: none;
      padding: 6px 8px;
      font-weight: 400;
      font-size: 14px;

      .profile-avatar {
        width: 32px;
        height: 32px;
        background-color: #377dff;
        font-size: 14px;
        font-weight: 500;
      }

      &:hover {
        color: #377dff;
        background-color: rgba(55, 125, 255, 0.1);
      }
    }
  }

  .mobile-menu-button {
    color: #122b46;
  }
}

// Desktop Menu Styles
.menu-user-info {
  flex-direction: column;
  align-items: flex-start;
  pointer-events: none;

  &:hover {
    background-color: transparent;
  }
}

.menu-divider {
  border-bottom: 1px solid #ecede8;
  padding: 0;
  margin: 8px 0;
}

.menu-sign-out {
  color: #d32f2f;
}

// Mobile Menu Styles
.mobile-menu {
  .mobile-menu-container {
    width: 280px;
    padding: 16px 0;
  }

  .mobile-user-info {
    padding: 16px;
    
    .mobile-user-icon {
      font-size: 40px;
      margin-right: 16px;
      color: #122b46;
    }

    .MuiListItemText-secondary {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }

  .mobile-menu-item {
    padding: 12px 24px;
    color: #122b46;
    text-decoration: none;

    &:hover {
      background-color: rgba(55, 125, 255, 0.1);
    }

    &.sign-out {
      color: #d32f2f;
    }
  }
}

// Profile Menu Styles
.profile-menu {
  .MuiPaper-root {
    margin-top: 8px;
    min-width: 280px;
    border-radius: 8px;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.15);
  }

  .menu-user-info {
    padding: 16px;
    pointer-events: none;

    .menu-user-content {
      display: flex;
      align-items: center;
      gap: 12px;
      width: 100%;
    }

    .menu-avatar {
      width: 40px;
      height: 40px;
      background-color: #377dff;
      font-size: 16px;
      font-weight: 500;
    }

    .user-info {
      text-align: left;

      .MuiTypography-subtitle1 {
        font-size: 14px;
        font-weight: 500;
        color: #122b46;
        line-height: 1.2;
      }

      .MuiTypography-body2 {
        font-size: 12px;
        color: #6c757d;
        margin-top: 4px;
      }
    }

    &:hover {
      background-color: transparent;
    }
  }

  .menu-divider {
    margin: 8px 0;
  }

  .menu-item {
    padding: 12px 16px;
    font-size: 14px;
    color: #122b46;

    &:hover {
      background-color: rgba(55, 125, 255, 0.1);
    }

    &.sign-out {
      color: #d32f2f;

      &:hover {
        background-color: rgba(211, 47, 47, 0.1);
      }
    }
  }
}

// Language Menu Styles
.language-menu {
  .MuiPaper-root {
    margin-top: 8px;
    min-width: 180px;
    border-radius: 8px;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.15);
  }

  .MuiMenuItem-root {
    padding: 12px 16px;
    
    &:hover {
      background-color: rgba(55, 125, 255, 0.1);
    }

    .MuiListItemIcon-root {
      min-width: 36px;
      
      img {
        border-radius: 2px;
      }
    }

    .MuiListItemText-primary {
      font-size: 14px;
      color: #122b46;
    }
  }
}

// Responsive adjustments
@media (max-width: 900px) {
  .header {
    height: 72px;

    .toolbar {
      min-height: 72px !important;
    }

    .logo-wrapper {
      .logo-image {
        width: 120px;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
