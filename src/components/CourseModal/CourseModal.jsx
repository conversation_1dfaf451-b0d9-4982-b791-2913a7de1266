import React from 'react';
import { 
  Box, 
  Dialog, 
  DialogContent, 
  DialogTitle, 
  Grid, 
  IconButton, 
  Typography
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import CourseCard, { CardContent } from '@/components/CourseCard/CourseCard';
import './CourseModal.scss';

const CourseModal = ({ open, onClose, title, courses }) => {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || 'en';

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      className="course-modal"
    >
      <DialogTitle>
        {title}
        <IconButton
          aria-label="close"
          onClick={onClose}
          className="close-button"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={3}>
          {courses.map((course, index) => {
            const localizedContent = course.translations[currentLanguage] || course.translations.en;
            
            return (
              <Grid item xs={12} md={6} key={course.id}>
                <CourseCard
                  buttonText={localizedContent.buttonText}
                  buttonType="URL"
                  buttonVariant="text"
                  imageSrc={course.imageSrc}
                  locked={false}
                  variant={course.variant}
                  label={localizedContent.courseType}
                >
                  <CardContent
                    title={localizedContent.title}
                    description={localizedContent.description}
                    buttonURL={localizedContent.buttonURL}
                    newTab={course.newTab}
                    buttonText={localizedContent.buttonText}
                    courseNumber={index + 1}
                    duration={course.duration}
                  />
                </CourseCard>
              </Grid>
            );
          })}
        </Grid>
      </DialogContent>
    </Dialog>
  );
};

CourseModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  courses: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      translations: PropTypes.object.isRequired,
      courseType: PropTypes.string,
      buttonURL: PropTypes.string.isRequired,
      imageSrc: PropTypes.string.isRequired,
      newTab: PropTypes.bool,
      duration: PropTypes.string,
      variant: PropTypes.string
    })
  ).isRequired
};

export default CourseModal; 