@use '../../styles/abstracts/variables' as *;

// Özel DALL-E stilleri eklemek isterseniz buraya ekleyebilirsiniz 

.dalle-playground {
  display: flex;
  flex-direction: column;
  height: auto;
  border: 1px solid $border-color;
  background-color: $bg-paper;
  margin-top: $spacing-4;
  border-radius: $border-radius-md;
  position: relative;

  &__container {
    display: flex;
    flex: 1;
    padding: $spacing-4;
  }

  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 500px;
    position: relative;

    @media (max-width: $tablet) {
      height: 400px;
    }
  }

  &__welcome {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;

    img {
      margin-bottom: $spacing-2;
    }

    .MuiTypography-h6 {
      font-weight: $font-weight-medium;
    }

    @media (max-width: $tablet) {
      img {
        width: 32px;
        height: 32px;
      }

      .MuiTypography-h6 {
        font-size: $font-size-lg;
      }
    }
  }

  &__messages {
    display: flex;
    flex-direction: column;
    gap: $spacing-2;
    padding: $spacing-3;
    height: calc(100% - 150px);
    overflow-y: auto;
    overflow-x: hidden;
    margin-bottom: $spacing-3;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 3px;
    }
  }

  &__message {
    display: flex;
    align-items: flex-start;
    padding: $spacing-3;
    border-radius: $border-radius-md;
    max-width: calc(100% - $spacing-5);
    word-break: break-word;

    &.user {
      background-color: rgba(#79AC9B, 0.1);
      margin-left: auto;
      max-width: 85%;
    }

    &.assistant {
      background-color: $bg-default;
      margin-right: auto;
      border: 1px solid $border-color;
      width: 100%;
    }
  }

  &__image-response {
    display: flex;
    flex-direction: column;
    gap: $spacing-2;
    width: 100%;
    max-width:100%;
  }

  &__image-container {
    width: 300px;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
  }

  &__image-zoom-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.1);
    opacity: 1;
    transition: background-color 0.2s ease-in-out;
    
    &:hover {
      background: rgba(0, 0, 0, 0.2);
    }
  }

  &__generated-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    cursor: pointer;
  }

  &__input {
    margin-top: auto;
    position: relative;
    padding: $spacing-3;
    background-color: $bg-paper;
    border-top: 1px solid $border-color;

    textarea {
      width: 100%;
      padding: $spacing-3;
      border: 1px solid $border-color;
      border-radius: $border-radius-md;
      resize: none;
      font-family: inherit;
      font-size: $font-size-md;
      line-height: 1.5;
      color: $text-primary;
      background-color: $bg-paper;
      box-sizing: border-box;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      min-height: 56px;

      &[rows="1"] {
        height: 56px;
      }

      &[rows="4"] {
        height: 150px;
      }

      &:focus {
        outline: none;
        border-color: #79AC9B;
      }

      @media (max-width: $tablet) {
        font-size: $font-size-sm;
        padding: $spacing-2;

        &[rows="1"] {
          height: 48px;
        }

        &[rows="4"] {
          height: 120px;
        }
      }
    }

    .dalle-playground__send {
      position: absolute;
      right: $spacing-4;
      bottom: 50%;
      transform: translateY(50%);
      color: #79AC9B;
      background-color: rgba(#fff, 1) !important;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      
      &:hover {
        background-color: rgba(#79AC9B, 0.1);
      }
    }
  }

  &__footer {
    padding: $spacing-4;
    border-top: 1px solid $border-color;
    text-align: center;

    @media (max-width: $tablet) {
      padding: $spacing-3;
      font-size: $font-size-xs;
    }
  }

  &__back {
    position: absolute;
    top: $spacing-4;
    left: $spacing-4;
    z-index: 1;

    &-button {
      color: $text-secondary !important;
      text-transform: none !important;
      background-color: #F5F5F5 !important;
      font-weight: $font-weight-medium !important;
      padding: $spacing-1 $spacing-2 !important;
      transition: all 0.3s ease !important;
      overflow: hidden !important;
      white-space: nowrap !important;

      .MuiButton-startIcon {
        margin-right: 0;
        transition: margin-right 0.3s ease;
      }

      .back-text {
        display: inline-block;
        transition: opacity 0.2s ease, transform 0.3s ease;
        margin-left: $spacing-1;
      }

      &.collapsed {
        min-width: 32px;
        .back-text {
          opacity: 0;
          transform: translateX(-20px);
          width: 0;
          margin-left: 0;
        }
      }

      &:hover {
        background-color: #f1f1f1 !important;

        &.collapsed {
          padding-right: $spacing-2 !important;

          .back-text {
            opacity: 1;
            transform: translateX(0);
            width: max-content;
            margin-left: $spacing-1;
          }

          .MuiButton-startIcon {
            margin-right: $spacing-1;
          }
        }
      }
    }

    @media (max-width: $tablet) {
      position: static;
      padding: $spacing-3 $spacing-3 0 $spacing-3;

      &-button {
        font-size: $font-size-xs;
      }
    }
  }
} 