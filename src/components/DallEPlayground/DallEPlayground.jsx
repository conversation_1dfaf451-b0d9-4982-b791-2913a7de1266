import { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  IconButton,
  Button,
  CircularProgress,
  Modal,
  Backdrop,
  Fade,
  Tooltip,
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import OpenAIIcon from '../../assets/images/logos/openai-logo.svg';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import CloseIcon from '@mui/icons-material/Close';
import { useTranslation } from 'react-i18next';
import { useUseDallEConnectorsMutation } from '../../redux/services/connectors-api';
import { useUpdateAppTrackingMutation } from '../../redux/services/app-tracking-api';
import { useSelector } from 'react-redux';
import './DallEPlayground.scss';

const DallEPlayground = ({ initialPrompt = '', onBack, disableBack = false, onGenerate }) => {
  const { t } = useTranslation();
  const user = useSelector((state) => state.auth.user);
  const [message, setMessage] = useState(initialPrompt);
  const [messages, setMessages] = useState([]);
  const [isTextareaFocused, setIsTextareaFocused] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [openLightbox, setOpenLightbox] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const messagesRef = useRef(null);
  const [generateImage] = useUseDallEConnectorsMutation();
  const [updateAppTracking] = useUpdateAppTrackingMutation();

  const scrollToBottom = () => {
    if (messagesRef.current) {
      const scrollHeight = messagesRef.current.scrollHeight;
      messagesRef.current.scrollTo({
        top: scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    setTimeout(scrollToBottom, 300);
  }, [messages]);

  useEffect(() => {
    if (isTextareaFocused && messages.length > 0) {
      scrollToBottom();
    }
  }, [isTextareaFocused, messages.length]);

  const handleTextareaFocus = () => {
    setIsTextareaFocused(true);
    scrollToBottom();
  };

  const handleGenerate = async () => {
    if (!message.trim() || isLoading) return;

    try {
      setIsLoading(true);

      const newMessages = [
        ...messages,
        {
          role: 'user',
          content: message,
          type: 'prompt',
        },
      ];
      setMessages(newMessages);

      const response = await generateImage({
        name: 'Dall-e',
        prompt: message,
        temperature: 0.4,
        frequency: 0,
        presence: 0,
      });

      if (response.error) {
        // Don't show error message for usage limit errors as they're handled in the API layer
        if (response.error.isUsageLimitExceeded) {
          return; // Exit early, usage limit toast is already shown
        }
        throw new Error(response.error.data || t('common.errors.imageGenerationFailed'));
      }

      const imageResponse = {
        role: 'assistant',
        content: {
          url: response.data?.data?.url,
          prompt: message,
        },
        type: 'image',
      };

      setMessages([...newMessages, imageResponse]);
      setMessage('');
      setIsTextareaFocused(false);

      const userId = user?._id;
      if (userId) {
        const currentDate = new Date();
        try {
          await updateAppTracking({
            userId: userId,
            appId: 'playgrounds/dall-e',
            appType: 'playground',
            title: 'Dall-E Playground',
            translations: {
              english: {
                title: 'Dall-E Playground',
              },
              german: {
                title: 'Dall-E Playground',
              },
            },
            year: currentDate.getFullYear().toString(),
            month: (currentDate.getMonth() + 1).toString(),
            day: currentDate.getDate().toString(),
          }).unwrap();
        } catch (trackingError) {
          console.error('Tracking update failed:', trackingError);
        }
      }

      if (onGenerate) {
        onGenerate();
      }
    } catch (error) {
      console.error('Error generating image:', error);
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          role: 'assistant',
          content: {
            url: '',
            prompt: `Error: ${error.message || 'Failed to generate image. Please try again.'}`,
          },
          type: 'error',
        },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenLightbox = (imageUrl) => {
    setSelectedImage(imageUrl);
    setOpenLightbox(true);
  };

  const handleCloseLightbox = () => {
    setOpenLightbox(false);
  };

  return (
    <Box className="dalle-playground">
      {!disableBack && (
        <Box className="dalle-playground__back">
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={onBack}
            size="small"
            className={`dalle-playground__back-button ${messages.length > 0 ? 'collapsed' : ''}`}
          >
            <span className="back-text">{t('playgrounds.common.backToPrompt')}</span>
          </Button>
        </Box>
      )}

      <Box className="dalle-playground__container">
        <Box className="dalle-playground__content">
          {messages.length === 0 ? (
            <Box className="dalle-playground__welcome">
              <Box component="img" src={OpenAIIcon} alt="OpenAI" width={40} height={40} />
              <Typography variant="h6">{t('playgrounds.dallE.title')}</Typography>
            </Box>
          ) : (
            <Box className="dalle-playground__messages" ref={messagesRef}>
              {messages.map((msg, index) => (
                <Box
                  key={index}
                  className={`dalle-playground__message ${msg.role === 'user' ? 'user' : 'assistant'}`}
                >
                  {msg.role === 'assistant' && (
                    <Box
                      component="img"
                      src={OpenAIIcon}
                      alt="OpenAI"
                      width={24}
                      height={24}
                      sx={{ marginRight: 1 }}
                    />
                  )}
                  {msg.type === 'prompt' ? (
                    <Typography>{msg.content}</Typography>
                  ) : (
                    <Box className="dalle-playground__image-response">
                      <Box
                        className="dalle-playground__image-container"
                        sx={{ position: 'relative', cursor: 'pointer' }}
                        onClick={() => msg.content.url && handleOpenLightbox(msg.content.url)}
                      >
                        <Box
                          component="img"
                          src={msg.content.url}
                          alt="Generated"
                          className="dalle-playground__generated-image"
                        />
                        <Box
                          className="dalle-playground__image-zoom-overlay"
                          sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            background: 'rgba(0, 0, 0, 0.1)',
                            opacity: 1,
                            transition: 'background-color 0.2s',
                          }}
                        >
                          <Tooltip
                            title={t('imageUsecase.output.clickToZoom', 'Click to zoom')}
                            arrow
                          >
                            <Box
                              sx={{
                                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                borderRadius: '50%',
                                width: 40,
                                height: 40,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                            >
                              <ZoomInIcon />
                            </Box>
                          </Tooltip>
                        </Box>
                      </Box>
                      <Typography variant="caption" color="textSecondary">
                        {msg.content.prompt}
                      </Typography>
                    </Box>
                  )}
                </Box>
              ))}
            </Box>
          )}

          <Box className="dalle-playground__input">
            <Box
              component="textarea"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder={t('playgrounds.dallE.typePrompt')}
              rows={messages.length === 0 || isTextareaFocused ? 4 : 1}
              onFocus={handleTextareaFocus}
              onBlur={(e) => {
                if (!e.target.value.trim() && messages.length > 0) {
                  setIsTextareaFocused(false);
                }
              }}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleGenerate();
                }
              }}
              disabled={isLoading}
            />
            <IconButton
              className="dalle-playground__send"
              onClick={handleGenerate}
              title={t('playgrounds.dallE.generateImage')}
              disabled={isLoading}
            >
              {isLoading ? <CircularProgress size={24} /> : <SendIcon />}
            </IconButton>
          </Box>
        </Box>
      </Box>

      <Box className="dalle-playground__footer">
        <Typography variant="caption" color="textSecondary">
          {t('playgrounds.dallE.disclaimer')}
        </Typography>
      </Box>

      {/* Lightbox Modal */}
      <Modal
        open={openLightbox}
        onClose={handleCloseLightbox}
        closeAfterTransition
        BackdropComponent={Backdrop}
        BackdropProps={{
          timeout: 500,
        }}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Fade in={openLightbox}>
          <Box
            sx={{
              position: 'relative',
              maxWidth: '90vw',
              maxHeight: '90vh',
              outline: 'none',
            }}
          >
            <IconButton
              onClick={handleCloseLightbox}
              sx={{
                position: 'absolute',
                top: -40,
                right: -40,
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 1)',
                },
              }}
            >
              <CloseIcon />
            </IconButton>
            {selectedImage && (
              <Box
                component="img"
                src={selectedImage}
                alt="Generated"
                sx={{
                  maxWidth: '100%',
                  maxHeight: '90vh',
                  objectFit: 'contain',
                  borderRadius: 1,
                  boxShadow: 24,
                }}
              />
            )}
          </Box>
        </Fade>
      </Modal>
    </Box>
  );
};

DallEPlayground.propTypes = {
  initialPrompt: PropTypes.string,
  onBack: PropTypes.func,
  disableBack: PropTypes.bool,
  onGenerate: PropTypes.func,
};

export default DallEPlayground;
