import PropTypes from 'prop-types';
import { Box, Typography, LinearProgress, Grid } from '@mui/material';
import Button from '../Button/Button';
import './WelcomeHeader.scss';
import { useTranslation } from 'react-i18next';

const WelcomeHeader = ({
  name,
  functionLabel,
  jobRoleLabel,
  showProgress = false,
  level = '',
  progress = 0,
  completedSteps = 0,
  totalSteps = 0,
  isPageView = false,
  title,
  description,
  buttons = [],
}) => {
  const { t, i18n } = useTranslation();

  const getFunctionLabel = () => {
    return (
      functionLabel?.translations?.[i18n.language] ||
      functionLabel?.translations?.en ||
      functionLabel?.slug ||
      ''
    );
  };

  const getJobRoleLabel = () => {
    if (!jobRoleLabel) return '';
    return (
      jobRoleLabel?.translations?.[i18n.language] ||
      jobRoleLabel?.translations?.en ||
      jobRoleLabel?.slug ||
      ''
    );
  };

  // Butonların render edilip edilmeyeceğini kontrol et
  const shouldRenderButtons = buttons && buttons.length > 0;

  return (
    <Box className={`welcome-header ${isPageView ? 'no-border' : ''}`}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={showProgress || shouldRenderButtons ? 8 : 12}>
          <Box className="welcome-text">
            <Typography variant="h4" component="h1" className="welcome-title">
              {isPageView ? title : t('home.welcome', { name })}
            </Typography>
            <Typography
              variant="subtitle1"
              color="text.secondary"
              dangerouslySetInnerHTML={{
                __html: isPageView
                  ? description
                  : `${t('home.jobRolePrefix')}${getFunctionLabel()}, ${getJobRoleLabel()}`,
              }}
            />
          </Box>
        </Grid>

        {showProgress && (
          <Grid item xs={12} md={4}>
            <Box className="progress-section">
              <Typography variant="subtitle2" color="text.secondary" className="level-text">
                {t('training.currentLevel.prefix')}{' '}
                <span className="bold-text capitalize-text">{level}</span>
              </Typography>
              <Box className="progress-bar-container">
                <LinearProgress
                  variant="determinate"
                  value={progress}
                  className="custom-progress"
                />
              </Box>
              <Box className="progress-info">
                <Typography variant="subtitle2" color="text.secondary">
                  {progress}%
                </Typography>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('training.progress.stepsCompleted', {
                    completed: completedSteps,
                    total: totalSteps,
                  })}
                </Typography>
              </Box>
            </Box>
          </Grid>
        )}

        {shouldRenderButtons && !showProgress && (
          <Grid item xs={12} md={4}>
            <Box className="buttons-section">
              {buttons.map((button, index) => {
                // Kullanıcı rolü kontrolü
                const isVisible =
                  typeof button.isVisible === 'function'
                    ? button.isVisible()
                    : button.isVisible !== undefined
                      ? button.isVisible
                      : true;

                if (!isVisible) return null;

                return (
                  <Button
                    key={index}
                    variant={button.variant || 'contained'}
                    color={button.color || 'primary'}
                    onClick={button.onClick}
                    className={`header-button ${button.className || ''}`}
                    disabled={button.disabled}
                  >
                    {button.label}
                  </Button>
                );
              })}
            </Box>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

WelcomeHeader.propTypes = {
  name: PropTypes.string,
  functionLabel: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.shape({
      slug: PropTypes.string,
      translations: PropTypes.object,
    }),
  ]),
  jobRoleLabel: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.shape({
      slug: PropTypes.string,
      translations: PropTypes.object,
    }),
  ]),
  showProgress: PropTypes.bool,
  level: PropTypes.string,
  progress: PropTypes.number,
  completedSteps: PropTypes.number,
  totalSteps: PropTypes.number,
  isPageView: PropTypes.bool,
  title: PropTypes.string,
  description: PropTypes.string,
  buttons: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      onClick: PropTypes.func.isRequired,
      variant: PropTypes.string,
      color: PropTypes.string,
      className: PropTypes.string,
      disabled: PropTypes.bool,
      isVisible: PropTypes.oneOfType([PropTypes.bool, PropTypes.func]),
      sx: PropTypes.object,
      startIcon: PropTypes.node,
      endIcon: PropTypes.node,
    })
  ),
};

export default WelcomeHeader;
