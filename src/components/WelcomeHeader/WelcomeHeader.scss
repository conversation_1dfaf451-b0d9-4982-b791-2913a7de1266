@use '../../styles/abstracts/variables' as *;

.welcome-header {
  margin-top: $spacing-5;
  padding-bottom: $spacing-4;
  border-bottom: 1px solid $divider-color;
  &.no-border {
    border-bottom: none;
    padding-bottom: 0;
    .welcome-title{
      color: $text-primary;
      font-weight: $font-weight-bold;
    }
  }
  
  .welcome-title {
    font-size: $font-size-xl;
    font-weight: $font-weight-regular;
    color: $text-primary;
    margin-bottom: $spacing-2 !important;
    line-height: 1.2 !important;
  }

  .MuiTypography-subtitle1 {
    color: $text-secondary;
    font-size: $font-size-md;
    line-height: 24px;
    font-weight: $font-weight-regular;
    p{
      &:last-child{
        margin:0;
      }
    }
  }
  .MuiTypography-subtitle2 {
    color: $text-secondary; 
    font-size: $font-size-xs;
    line-height: 24px;
    font-weight: $font-weight-regular;
  }

  .progress-section {
    
    .progress-bar-container {
      
      .custom-progress {
        height: 8px;
        border-radius: $border-radius-sm;
        border:1px solid $border-color;
        background-color: $bg-light;
        
        .MuiLinearProgress-bar {
          background-color: $success-color;
        }
      }
    }
    
    .progress-info {
      display: flex;
      justify-content: space-between;
      color: $text-secondary;
    }
  }

  .buttons-section {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
    
    .header-button {
      min-width: 90px;
      font-weight: 500;

      @media (max-width: 899px) {
        margin-top: 16px;
      }
    }

    @media (max-width: 899px) {
      justify-content: flex-start;
      margin-top: 8px;
    }
  }

  .bold-text {
    font-weight: $font-weight-bold;
  }

  .capitalize-text {
    text-transform: capitalize;
  }

  @media (max-width: $tablet) {
    .welcome-title {
      font-size: $font-size-xs;
    }

    .MuiTypography-subtitle1 {
      font-size: $font-size-xs;
    }
  }
} 