import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, IconButton, Menu, MenuItem, ListItemIcon, ListItemText } from '@mui/material';
import LanguageIcon from '@mui/icons-material/Language';

const LanguageSelector = () => {
  const { i18n, t } = useTranslation();
  const [anchorEl, setAnchorEl] = React.useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
    handleClose();
  };

  return (
    <Box>
      <IconButton color="inherit" aria-label={t('header.language')} onClick={handleClick}>
        <LanguageIcon />
      </IconButton>
      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>
        <MenuItem onClick={() => changeLanguage('en')}>
          <ListItemIcon>
            <img src="/flags/en.png" alt="English" style={{ width: 24 }} />
          </ListItemIcon>
          <ListItemText primary="English" />
        </MenuItem>
        <MenuItem onClick={() => changeLanguage('de')}>
          <ListItemIcon>
            <img src="/flags/de.png" alt="Deutsch" style={{ width: 24 }} />
          </ListItemIcon>
          <ListItemText primary="Deutsch" />
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default LanguageSelector;
