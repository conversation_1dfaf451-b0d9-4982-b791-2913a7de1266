import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import AIModal from '../AIModal/AIModal';
import { Stack, Box, Button as MuiButton } from '@mui/material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import {
  useGetJourneyTrackingQuery,
  useUpdateSeenModalsMutation,
} from '../../redux/services/journey-api';
import './RoleUpgradeModal.scss';

// Modal'ın takip edilmesi için kullanılacak key
const MODAL_TRACKING_KEY = 'limited_use_upgrade_modal';

const RoleUpgradeModal = ({ onClose }) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const user = useSelector((state) => state.auth.user);

  // Tracking verilerini çekiyoruz
  const { data: trackingData } = useGetJourneyTrackingQuery(user?._id, {
    skip: !user?._id,
  });

  // Modal görüldü bilgisini güncellemek için mutation
  const [updateSeenModals] = useUpdateSeenModalsMutation();

  useEffect(() => {
    // Kullanıcı giriş yapmışsa, previous_role limited-use ise ve daha önce modalı görmemişse göster
    if (
      user &&
      user.previous_role === 'limited-use' &&
      trackingData &&
      !trackingData.seenModals?.includes(MODAL_TRACKING_KEY)
    ) {
      setOpen(true);
    }
  }, [user, trackingData]);

  const handleClose = async (result) => {
    setOpen(false);

    // Modal görüldü bilgisini kaydet
    if (user?._id) {
      try {
        await updateSeenModals({
          userId: user._id,
          modalKey: MODAL_TRACKING_KEY,
        });
      } catch {
        // Hata sessizce işleniyor
      }
    }

    if (onClose) {
      onClose(result);
    }
  };

  // Modal başlığında gösterilecek SVG ikon
  const modalTitle = `<svg viewBox="0 0 24 24" style="width: 24px; height: 24px;">
    <path fill="#1976d2" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4.59-12.42L10 14.17l-2.59-2.58L6 13l4 4 8-8z"/>
  </svg>${t('roleUpgradeModal.title')}`;

  // Modal içeriği
  const modalContent = `
    <div>
      <p>${t('roleUpgradeModal.accountUpgraded')}</p>
      <p>${t('roleUpgradeModal.extendedAccess')}</p>
    </div>
  `;

  // Özel footer içeriği
  const modalFooter = (
    <Stack direction="row" spacing={2} sx={{ width: '100%' }} justifyContent="flex-end">
      <Box>
        <MuiButton
          color="primary"
          variant="contained"
          onClick={() => handleClose({ action: 'confirm', confirmed: true })}
          startIcon={<CheckCircleOutlineIcon />}
          size="medium"
          className="upgrade-confirm-button"
        >
          {t('roleUpgradeModal.understand')}
        </MuiButton>
      </Box>
    </Stack>
  );

  return (
    <AIModal
      open={open}
      onClose={handleClose}
      title={modalTitle}
      content={modalContent}
      maxWidth="md"
      fullWidth={true}
      showCloseButton={true}
      className="role-upgrade-modal"
      footer={modalFooter}
    />
  );
};

RoleUpgradeModal.propTypes = {
  onClose: PropTypes.func,
};

export default RoleUpgradeModal;
