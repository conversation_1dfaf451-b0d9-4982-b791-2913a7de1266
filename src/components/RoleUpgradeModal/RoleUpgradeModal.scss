@use '../../styles/abstracts/variables' as *;

.role-upgrade-modal {
  .MuiDialog-paper {
    border-radius: 12px;
    overflow: hidden;
  }

  .MuiDialogTitle-root {
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
    padding: 16px 20px !important;
    display: flex;
    align-items: center;

    svg {
      color: #1976d2 !important;
      margin-right: 8px;
    }
  }

  .MuiDialogContent-root {
    padding: 0px !important;

    .content-wrapper{
      padding-top: 0;
    }
    
    & > div {
      padding: 20px;
    }
    
    p {
      margin-bottom: 12px;
      line-height: 1.5;
      font-size: 16px !important;
      
      &:first-child {
        font-weight: 500;
      }
    }
  }

  .MuiDialogActions-root {
    padding: 12px 20px !important;
    background-color: #fafafa;
    border-top: 1px solid #e0e0e0;
  }

  .upgrade-confirm-button {
    background-color: #e3f2fd !important;
    color: #1976d2 !important;
    border: 1px solid #bbdefb !important;
    font-weight: 500 !important;
    transition: all 0.2s ease-in-out;
    
    &:hover {
      background-color: #bbdefb !important;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
    }

    .MuiSvgIcon-root {
      color: #1976d2 !important;
    }
  }
} 