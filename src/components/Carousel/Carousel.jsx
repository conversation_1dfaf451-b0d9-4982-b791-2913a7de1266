import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, A11y } from 'swiper/modules';
import Box from '@mui/material/Box';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import './Carousel.scss';

const defaultBreakpoints = {
  320: { slidesPerView: 1, spaceBetween: 24 },
  768: { slidesPerView: 2, spaceBetween: 24 },
  1024: { slidesPerView: 3, spaceBetween: 24 },
};

const Carousel = forwardRef(
  ({ children, onSlideChange, swiperProps, onSwiper, breakpoints = defaultBreakpoints }, ref) => {
    const handleSwiper = (swiper) => {
      if (onSwiper) {
        onSwiper(swiper);
      }
    };

    return (
      <Box>
        <Swiper
          ref={ref}
          modules={[Navigation, Pagination, A11y]}
          spaceBetween={24}
          slidesPerView={1}
          navigation={false}
          pagination={{ clickable: true }}
          className="mySwiper"
          onSwiper={handleSwiper}
          onSlideChange={onSlideChange}
          breakpoints={breakpoints}
          observer={true}
          observeParents={true}
          {...swiperProps}
        >
          {React.Children.map(children, (child, index) => (
            <SwiperSlide key={index}>{child}</SwiperSlide>
          ))}
        </Swiper>
      </Box>
    );
  }
);

Carousel.displayName = 'Carousel';

Carousel.propTypes = {
  children: PropTypes.node.isRequired,
  onSlideChange: PropTypes.func,
  swiperProps: PropTypes.object,
  onSwiper: PropTypes.func,
  breakpoints: PropTypes.object,
};

export default Carousel;
