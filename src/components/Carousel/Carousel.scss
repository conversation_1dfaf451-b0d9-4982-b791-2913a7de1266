.carousel-section {
  padding: 24px 32px 16px 32px;
  background-color:#fff;
  border-radius: 8px;
  border:1px solid #ecede8;
  .MuiContainer-root{
    @media (min-width: 768px) {
      padding:0;
    }
  } 
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.custom-navigation {
  display: flex;
  gap: 8px;

  .nav-button {
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    border:1px solid #ecede8;
    color: #0072E5;
    transition: all 0.2s ease;

    &:hover {
      background: #f8f9fa;
      border:1px solid #0072E5;
      transform: translateY(-2px);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .MuiSvgIcon-root {
      font-size: 24px;
    }
  }
}


.card-button {
  width: 100%;
  text-transform: none;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:not(.locked):hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 114, 229, 0.2);
  }

  &.locked {
    background-color: #f5f5f5;
    color: #9e9e9e;
    
    &:hover {
      background-color: #eeeeee;
    }
  }
}

@media (max-width: 768px) {
  .carousel-section {
    padding: 16px 0;
  }
} 