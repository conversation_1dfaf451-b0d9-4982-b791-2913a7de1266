import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Box,
  Typography,
  Stack,
  Grid,
  Tooltip,
  Select,
  MenuItem,
  Slider,
  CircularProgress,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import MicrosoftSmall from '../../assets/images/logos/microsoft-small.svg';
import WordIcon from '../../assets/images/logos/word.svg';
import ChatGPTIcon from '../../assets/images/logos/chatgpt.svg';
import OpenAIIcon from '../../assets/images/logos/openai-logo-vector.svg';
import OutlookIcon from '../../assets/images/logos/outlook.svg';
import PowerPointIcon from '../../assets/images/logos/powerpoint.svg';
import ExcelIcon from '../../assets/images/logos/excel.svg';
import DallEIcon from '../../assets/images/logos/dall-e.svg';
import OneNoteIcon from '../../assets/images/logos/onenote.svg';
import parse from 'html-react-parser';
import './PromptModal.scss';
import { functions } from '../../mockData/promptLibraryData'; // functions'ı import edelim
import Button from '@mui/material/Button';
import SendIcon from '@mui/icons-material/Send';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import ChatGPTPlayground from '../ChatGPTPlayground/ChatGPTPlayground';
import DallEPlayground from '../DallEPlayground/DallEPlayground';
import { fileTemplates } from '../../mockData/fileTemplates';
import { toast } from 'react-toastify';
import FavoriteButton from '@components/FavoriteButton';
import DescriptionIcon from '@mui/icons-material/Description';
import ArticleIcon from '@mui/icons-material/Article';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import TableChartIcon from '@mui/icons-material/TableChart';
import SlideshowIcon from '@mui/icons-material/Slideshow';

// config-global'den MSAL yapılandırmasını import edelim
import { PublicClientApplication } from '@azure/msal-browser';
import { MSAL_CONFIG } from '../../config-global';
import { isMsalConfigured } from '../../utils/msalUtils';

// MSAL konfigürasyonu
const msalConfig = {
  auth: {
    clientId: MSAL_CONFIG.clientId,
    authority: `https://login.microsoftonline.com/${MSAL_CONFIG.tenantId}`,
    redirectUri: window.location.origin,
  },
  cache: {
    cacheLocation: 'sessionStorage',
    storeAuthStateInCookie: false,
  },
};

// İstek için gerekli bilgiler
const loginRequest = {
  scopes: MSAL_CONFIG.scopes,
};

// Microsoft Graph API endpoint'leri
const graphConfig = {
  graphMeEndpoint: 'https://graph.microsoft.com/v1.0/me',
  graphDriveItemsEndpoint: 'https://graph.microsoft.com/v1.0/me/drive/root/children',
  graphSharedItemsEndpoint: 'https://graph.microsoft.com/v1.0/me/drive/sharedWithMe',
};

// MSAL örneği oluşturulması
const msalInstance = new PublicClientApplication(msalConfig);

// MSAL başlatma işlemi
let msalInitialized = false;
(async () => {
  try {
    await msalInstance.initialize();
    msalInitialized = true;
  } catch (error) {
    console.error('MSAL initialization failed', error);
  }
})();

const PromptModal = ({ open, onClose, prompt }) => {
  const { t } = useTranslation();
  const [copyTooltip, setCopyTooltip] = useState(t('promptModal.copyPrompt'));
  const [isOpenAIChat, setIsOpenAIChat] = useState(false);
  const [isDallEChat, setIsDallEChat] = useState(false);
  const [message, setMessage] = useState('');

  // MSAL entegrasyonu - doğrudan component içinde yönetiyoruz
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [account, setAccount] = useState(null);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [showFileSelector, setShowFileSelector] = useState(false);
  const [userFiles, setUserFiles] = useState([]);

  // Slider state'leri
  const [temperature, setTemperature] = useState(1);
  const [topP, setTopP] = useState(1);
  const [frequencyPenalty, setFrequencyPenalty] = useState(0);
  const [presencePenalty, setPresencePenalty] = useState(0);

  const [selectedFile, setSelectedFile] = useState('');
  const [showFileWarning, setShowFileWarning] = useState(false);

  // Dosya yükleme durumu için state
  const [loadingFiles, setLoadingFiles] = useState(false);

  // Sayfa yüklendiğinde, mevcut bir hesap olup olmadığını kontrol et
  useEffect(() => {
    const checkAccounts = async () => {
      if (!msalInitialized) {
        console.log(t('promptModal.loading.msalNotInitialized'));
        try {
          await msalInstance.initialize();
          msalInitialized = true;
          console.log(t('promptModal.loading.msalInitialized'));
        } catch (error) {
          console.error(t('promptModal.loading.msalInitFailed'), error);
          return;
        }
      }

      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        setAccount(accounts[0]);
        setIsAuthenticated(true);
      }
    };

    checkAccounts();
  }, [t]);

  // Kullanıcı giriş yaptığında file selector'ı göster
  useEffect(() => {
    if (isAuthenticated && prompt?.showOnWork) {
      setShowFileSelector(true);
      // Kullanıcının dosyalarını getir
      fetchUserFiles();
    } else {
      setShowFileSelector(false);
    }
  }, [isAuthenticated, prompt]);

  // Microsoft Graph API ile kullanıcının dosyalarını getirme
  const fetchUserFiles = async () => {
    try {
      setLoadingFiles(true);
      // Token alınması
      const token = await getAuthToken();

      if (!token) {
        console.error(t('promptModal.errors.tokenAcquisitionFailed'));
        setUserFiles(fileTemplates); // Fallback olarak mock veriler
        return;
      }

      // "My Files" dosyalarını al
      const myFilesResponse = await fetch(graphConfig.graphDriveItemsEndpoint, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      // "Shared" dosyalarını al
      const sharedFilesResponse = await fetch(graphConfig.graphSharedItemsEndpoint, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      // Tüm dosyaları içerecek array
      let allFiles = [];

      // "My Files" dosyalarını işle
      if (myFilesResponse.ok) {
        const data = await myFilesResponse.json();
        // Sadece dosyaları filtrele (klasörleri değil)
        const files = data.value.filter((item) => item.file);
        allFiles = [...allFiles, ...files];
      } else {
        console.error(t('promptModal.errors.personalFilesError'), await myFilesResponse.text());
      }

      // "Shared" dosyalarını işle
      if (sharedFilesResponse.ok) {
        const data = await sharedFilesResponse.json();
        // Shared dosyaları ekle (bazı shared dosyalar farklı bir şemaya sahip olabilir)
        const sharedFiles = data.value.map((item) => {
          // Bazı paylaşılan dosyalar farklı bir formatta gelebilir, uyumlu hale getirelim
          if (!item.file && item.remoteItem && item.remoteItem.file) {
            return {
              ...item,
              file: item.remoteItem.file,
              // webUrl özelliği yanlış olabilir, remoteItem'dan alalım
              webUrl: item.remoteItem.webUrl || item.webUrl,
            };
          }
          return item;
        });

        allFiles = [...allFiles, ...sharedFiles];
      } else {
        console.error(t('promptModal.errors.sharedFilesError'), await sharedFilesResponse.text());
      }

      console.log(t('promptModal.logs.allUserFiles'), allFiles);

      // Desteklenen Office dosya türleri için MIME tipi kontrolü
      const officeFormats = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // Excel
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // Word
        'application/msword',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PowerPoint
        'application/vnd.ms-powerpoint',
        'application/pdf', // PDF
      ];

      // MIME tiplerini kullanıcı dostu formatlara dönüştürme
      const getMimeTypeLabel = (mimeType) => {
        switch (mimeType) {
          case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
          case 'application/vnd.ms-excel':
            return 'Excel';
          case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          case 'application/msword':
            return 'Word';
          case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
          case 'application/vnd.ms-powerpoint':
            return 'PowerPoint';
          case 'application/pdf':
            return 'PDF';
          default:
            return 'Dosya';
        }
      };

      // Dosya türüne göre simge döndürme
      const getFileIcon = (mimeType) => {
        switch (mimeType) {
          case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
          case 'application/vnd.ms-excel':
            return <TableChartIcon className="file-icon--excel" />;
          case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          case 'application/msword':
            return <DescriptionIcon className="file-icon--word" />;
          case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
          case 'application/vnd.ms-powerpoint':
            return <SlideshowIcon className="file-icon--powerpoint" />;
          case 'application/pdf':
            return <PictureAsPdfIcon className="file-icon--pdf" />;
          default:
            return <InsertDriveFileIcon className="file-icon--default" />;
        }
      };

      // Sadece Office formatındaki dosyaları filtrele
      const officeFiles = allFiles.filter((item) => {
        // item.file kontrol edilmeli, bazı paylaşılan öğeler file property'sine sahip olmayabilir
        if (!item.file) {
          // Eğer remoteItem varsa ve dosya içeriyorsa
          if (item.remoteItem && item.remoteItem.file) {
            return officeFormats.includes(item.remoteItem.file.mimeType);
          }
          return false;
        }
        return officeFormats.includes(item.file.mimeType);
      });

      // Dosyaları UI için formatlama
      const formattedFiles = officeFiles.map((file) => {
        // Dosya bilgisini belirle (doğrudan file veya remoteItem.file)
        const fileInfo = file.file || (file.remoteItem && file.remoteItem.file);
        const mimeType = fileInfo ? fileInfo.mimeType : '';
        const webUrl = file.webUrl || (file.remoteItem && file.remoteItem.webUrl) || '';

        return {
          id: file.id,
          name: file.name,
          webUrl: webUrl,
          extension: mimeType,
          typeLabel: getMimeTypeLabel(mimeType),
          icon: getFileIcon(mimeType),
          // Shared öğesi mi takip etmek için
          isShared: !!file.remoteItem,
        };
      });

      // Alfabetik olarak sırala
      formattedFiles.sort((a, b) => a.name.localeCompare(b.name));

      setUserFiles(formattedFiles.length > 0 ? formattedFiles : fileTemplates);
    } catch (error) {
      console.error(t('promptModal.errors.tokenAcquisitionFailed'), error);
      setUserFiles(fileTemplates); // Hata durumunda mock veriler
    } finally {
      setLoadingFiles(false);
    }
  };

  // Microsoft Graph API için token alma
  const getAuthToken = async () => {
    try {
      if (!account) return null;

      const silentRequest = {
        scopes: loginRequest.scopes,
        account: account,
      };

      try {
        const response = await msalInstance.acquireTokenSilent(silentRequest);
        return response.accessToken;
      } catch (error) {
        // Silent token acquisition failed, fallback to interactive method
        console.log(t('promptModal.loading.usingInteractiveMethod'));
        const response = await msalInstance.acquireTokenPopup(silentRequest);
        return response.accessToken;
      }
    } catch (error) {
      console.error(t('promptModal.errors.tokenAcquisitionFailed'), error);
      return null;
    }
  };

  // Login işlemi
  const handleLogin = async () => {
    try {
      setIsLoggingIn(true);

      // MSAL başlatılmış mı kontrol et
      if (!msalInitialized) {
        console.log(t('promptModal.loading.msalNotInitialized'));
        try {
          await msalInstance.initialize();
          msalInitialized = true;
          console.log(t('promptModal.loading.msalInitDuringLogin'));
        } catch (error) {
          console.error(t('promptModal.loading.msalInitFailedDuringLogin'), error);
          setIsLoggingIn(false);
          return null;
        }
      }

      const loginResponse = await msalInstance.loginPopup(loginRequest);
      setAccount(loginResponse.account);
      setIsAuthenticated(true);

      // Başarılı login bildirimi
      toast.success(t('promptModal.loading.loginSuccess', { name: loginResponse.account.name }), {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });

      return loginResponse;
    } catch (error) {
      console.error(t('promptModal.errors.loginError'), error);

      // Hata bildirimi
      toast.error(t('promptModal.loading.loginError'), {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });

      return null;
    } finally {
      setIsLoggingIn(false);
    }
  };

  if (!prompt) return null;

  // Provider logosu için helper fonksiyon
  const getProviderLogo = (provider) => {
    switch (provider) {
      case 'Microsoft':
        return MicrosoftSmall;
      case 'Open AI':
        return OpenAIIcon;
      default:
        return MicrosoftSmall;
    }
  };

  // App logosu için helper fonksiyon
  const getAppLogo = (app) => {
    switch (app) {
      case 'Word':
        return WordIcon;
      case 'Outlook':
        return OutlookIcon;
      case 'PowerPoint':
        return PowerPointIcon;
      case 'Excel':
        return ExcelIcon;
      case 'OneNote':
        return OneNoteIcon;
      case 'ChatGPT':
        return ChatGPTIcon;
      case 'Dall-E':
        return DallEIcon;
      default:
        return WordIcon;
    }
  };

  // App ismini görüntülemek için helper fonksiyon
  const getAppDisplayName = (app) => {
    switch (app) {
      case 'Word':
        return 'Microsoft Word';
      case 'Outlook':
        return 'Microsoft Outlook';
      case 'PowerPoint':
        return 'Microsoft PowerPoint';
      case 'Excel':
        return 'Microsoft Excel';
      case 'OneNote':
        return 'Microsoft OneNote';
      default:
        return app;
    }
  };

  // Function label'ını bulmak için helper fonksiyon
  const getFunctionLabel = (functionValue) => {
    if (Array.isArray(functionValue)) {
      return functionValue
        .map((func) => {
          const foundFunc = functions.find((f) => f.value === func.toLowerCase());
          return foundFunc ? foundFunc.label : func;
        })
        .join(', ');
    }
    const func = functions.find((f) => f.value === functionValue);
    return func ? func.label : functionValue;
  };

  const handleCopyClick = () => {
    if (prompt?.Title) {
      navigator.clipboard.writeText(prompt.Title);
      setCopyTooltip(t('promptModal.copied'));
      setTimeout(() => {
        setCopyTooltip(t('promptModal.copyPrompt'));
      }, 2000);
    }
  };

  // Try this prompt butonunun görünürlüğünü kontrol eden fonksiyon
  const shouldShowTryButton = () => {
    // MSAL yapılandırması kontrol edilir
    // Eğer MSAL yapılandırılmamışsa ve Microsoft ürünleri için bir buton ise gösterme
    if (prompt.showOnWork && !isMsalConfigured()) {
      return false;
    }

    // Eğer kullanıcı giriş yapmışsa ve dosya seçme kısmı görünür haldeyse
    // Try butonunu gösterme
    if (isAuthenticated && showFileSelector) return false;

    // Eğer Products içerisinde OneNote veya Outlook varsa Try butonunu gösterme
    if (
      prompt.Products &&
      (prompt.Products.includes('OneNote') ||
        prompt.Products.includes('Outlook') ||
        prompt.Products.includes('PowerPoint'))
    ) {
      return false;
    }

    if (prompt.ProviderName === 'Open AI') {
      return prompt.Products.some((product) => ['ChatGPT', 'Dall-E'].includes(product));
    }

    const microsoftOfficeProducts = ['Word', 'Excel', 'PowerPoint', 'OneNote'];
    return (
      prompt.showOnWork &&
      prompt.Products.length === 1 &&
      microsoftOfficeProducts.includes(prompt.Products[0])
    );
  };

  const handleTryButtonClick = async () => {
    // Microsoft ürünleri için login olma kontrolü
    if (prompt.showOnWork) {
      try {
        // Login işlemi başladı
        const loginResponse = await handleLogin();

        if (loginResponse) {
          // Login başarılı ise dosya seçimini göster
          setShowFileSelector(true);
        }
      } catch (error) {
        console.error(t('promptModal.errors.loginError'), error);
      }
    } else if (prompt.ProviderName === 'Open AI') {
      if (prompt.Products.includes('ChatGPT')) {
        setIsOpenAIChat(true);
      } else if (prompt.Products.includes('Dall-E')) {
        setIsDallEChat(true);
      }
    }
  };

  const handleBackToPrompt = () => {
    setIsOpenAIChat(false);
    setIsDallEChat(false);
  };

  // Tooltip açıklamaları için helper obje
  const settingDescriptions = {
    temperature: t('promptModal.settings.temperatureDescription'),
    topP: t('promptModal.settings.topPDescription'),
    frequencyPenalty: t('promptModal.settings.frequencyPenaltyDescription'),
    presencePenalty: t('promptModal.settings.presencePenaltyDescription'),
  };

  const renderSettingLabel = (label, description) => (
    <Box display="flex" alignItems="center" gap={0.5}>
      <Typography className="prompt-modal__setting-label">{label}</Typography>
      <Tooltip title={description} arrow placement="top">
        <IconButton size="small" className="info-icon-button">
          <InfoOutlinedIcon className="info-icon" />
        </IconButton>
      </Tooltip>
    </Box>
  );

  const renderOpenAIChat = () => {
    return (
      <Box className="prompt-modal__chat">
        <Box className="prompt-modal__chat-container">
          {/* Sol Taraf - Chat Alanı */}
          <Box className="prompt-modal__chat-content">
            <Box className="prompt-modal__chat-welcome">
              <img src={OpenAIIcon} alt="OpenAI" width={40} height={40} />
              <Typography variant="h6">{t('promptModal.howCanIHelp')}</Typography>
            </Box>

            {/* Textarea Alanı */}
            <Box className="prompt-modal__chat-input">
              <textarea
                value={prompt.Title}
                onChange={(e) => setMessage(e.target.value)}
                placeholder={t('promptModal.typeMessage')}
                rows={4}
              />
              <IconButton className="prompt-modal__chat-send">
                <SendIcon />
              </IconButton>
            </Box>
          </Box>

          {/* Sağ Taraf - Ayarlar */}
          <Box className="prompt-modal__chat-settings">
            <Typography variant="h6">{t('promptModal.settings.title')}</Typography>

            <Box className="prompt-modal__chat-setting">
              <Typography>{t('promptModal.settings.model')}</Typography>
              <Select value="gpt-4-turbo" fullWidth size="small">
                <MenuItem value="gpt-4-turbo">gpt-4-turbo</MenuItem>
                <MenuItem value="gpt-4-0125-preview">gpt-4-0125-preview</MenuItem>
                <MenuItem value="gpt-4">gpt-4</MenuItem>
                <MenuItem value="gpt-3.5-turbo-0125">gpt-3.5-turbo-0125</MenuItem>
                <MenuItem value="gpt-3.5-turbo">gpt-3.5-turbo</MenuItem>
              </Select>
            </Box>

            <Box className="prompt-modal__chat-setting">
              <Box display="flex" justifyContent="space-between" alignItems="center">
                {renderSettingLabel(
                  t('promptModal.settings.temperature'),
                  settingDescriptions.temperature
                )}
                <Typography>{temperature.toFixed(2)}</Typography>
              </Box>
              <Slider
                value={temperature}
                onChange={(_, newValue) => setTemperature(newValue)}
                min={0}
                max={2}
                step={0.01}
              />
            </Box>

            <Box className="prompt-modal__chat-setting">
              <Box display="flex" justifyContent="space-between" alignItems="center">
                {renderSettingLabel(t('promptModal.settings.topP'), settingDescriptions.topP)}
                <Typography>{topP.toFixed(2)}</Typography>
              </Box>
              <Slider
                value={topP}
                onChange={(_, newValue) => setTopP(newValue)}
                min={0}
                max={1}
                step={0.01}
              />
            </Box>

            <Box className="prompt-modal__chat-setting">
              <Box display="flex" justifyContent="space-between" alignItems="center">
                {renderSettingLabel(
                  t('promptModal.settings.frequencyPenalty'),
                  settingDescriptions.frequencyPenalty
                )}
                <Typography>{frequencyPenalty.toFixed(2)}</Typography>
              </Box>
              <Slider
                value={frequencyPenalty}
                onChange={(_, newValue) => setFrequencyPenalty(newValue)}
                min={0}
                max={2}
                step={0.01}
              />
            </Box>

            <Box className="prompt-modal__chat-setting">
              <Box display="flex" justifyContent="space-between" alignItems="center">
                {renderSettingLabel(
                  t('promptModal.settings.presencePenalty'),
                  settingDescriptions.presencePenalty
                )}
                <Typography>{presencePenalty.toFixed(2)}</Typography>
              </Box>
              <Slider
                value={presencePenalty}
                onChange={(_, newValue) => setPresencePenalty(newValue)}
                min={0}
                max={2}
                step={0.01}
              />
            </Box>
          </Box>
        </Box>

        <Box className="prompt-modal__chat-footer">
          <Typography variant="caption" color="textSecondary">
            {t('common.warnings.dataPrivacy')}
          </Typography>
        </Box>
      </Box>
    );
  };

  // Modal kapanırken state'i sıfırla
  const handleClose = () => {
    setIsOpenAIChat(false); // ChatGPT görünümünü sıfırla
    setIsDallEChat(false);
    onClose(); // Modal'ı kapat
  };

  // Dosya seçildiğinde uyarıyı kaldıralım ve seçilen dosyayı açalım
  const handleFileSelect = (e) => {
    setSelectedFile(e.target.value);
    setShowFileWarning(false);

    // Seçilen dosyayı bul
    const selectedFileObj = userFiles.find((file) => file.name === e.target.value);
    if (selectedFileObj && selectedFileObj.webUrl) {
      // URL'ye parametreyi ekle
      const separator = selectedFileObj.webUrl.includes('?') ? '&' : '?';
      const modifiedUrl = `${selectedFileObj.webUrl}${separator}&wdOrigin=handoff-copilotlab`;
      // Dosyayı yeni pencerede aç
      window.open(modifiedUrl, '_blank');
    } else {
      console.log('Selected file:', e.target.value);
      // Fallback: webUrl yoksa
    }
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        // Sadece Close butonuna tıklandığında kapanmasını sağla
        if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
          handleClose();
        }
      }}
      maxWidth="md"
      fullWidth
      className="prompt-modal"
      disableEscapeKeyDown
    >
      <DialogTitle className="prompt-modal__header">
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">{t('promptModal.title')}</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent className="prompt-modal__content">
        {isOpenAIChat ? (
          <ChatGPTPlayground
            initialPrompt={prompt.Title}
            initialTemperature={1}
            initialTopP={1}
            library={true}
            initialFrequencyPenalty={0}
            initialPresencePenalty={0}
            onBack={handleBackToPrompt}
            backButtonText={t('promptModal.backToPrompt')}
          />
        ) : isDallEChat ? (
          <DallEPlayground
            initialPrompt={prompt.Title}
            onBack={handleBackToPrompt}
            backButtonText={t('promptModal.backToPrompt')}
          />
        ) : (
          <Grid container spacing={3}>
            {/* Sol Sütun */}
            <Grid item xs={8}>
              <Box className="prompt-modal__main">
                <Box className="prompt-modal__prompt-box">
                  {prompt.Title && renderTitle(prompt.Title)}
                </Box>
                {/* Butonlar */}
                <Box className="prompt-modal__actions">
                  {/* Dosya seçme alanını sadece giriş yapmış kullanıcılar için göster */}
                  {prompt.showOnWork && showFileSelector && (
                    <>
                      <Select
                        value={selectedFile}
                        onChange={handleFileSelect}
                        className="prompt-modal__file-select"
                        size="small"
                        displayEmpty
                        disabled={loadingFiles}
                      >
                        <MenuItem value="" disabled>
                          {loadingFiles
                            ? t('promptModal.fileSelector.loadingFiles')
                            : t('promptModal.fileSelector.selectFile')}
                        </MenuItem>
                        {userFiles.map((file) => (
                          <MenuItem key={file.id} value={file.name} className="file-menu-item">
                            <Box className="menu-item-content">
                              <Box className="menu-item-content__icon-container">{file.icon}</Box>
                              <Box className="menu-item-content__text-container">
                                {file.name}
                                {file.isShared && (
                                  <Typography
                                    component="span"
                                    className="menu-item-content__shared-label"
                                  >
                                    ({t('promptModal.fileSelector.shared')})
                                  </Typography>
                                )}
                              </Box>
                              {file.typeLabel && (
                                <Box className="menu-item-content__file-type">{file.typeLabel}</Box>
                              )}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>

                      {showFileWarning && (
                        <Typography className="prompt-modal__file-warning">
                          {t('promptModal.fileSelector.pleaseSelectFile')}
                        </Typography>
                      )}
                    </>
                  )}

                  <Tooltip title={copyTooltip} arrow>
                    <IconButton onClick={handleCopyClick} className="prompt-modal__action-button">
                      <ContentCopyIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>

                  <FavoriteButton shortcutID={prompt._id} shortcutType="prompt" iconOnly={false} />
                </Box>
              </Box>

              {prompt.DisplayDescription && prompt.DisplayDescription.trim() !== '' && (
                <Box className="prompt-modal__make-it-your-own">
                  <Typography variant="h2">{t('promptModal.makeItYourOwn')}</Typography>
                  <Box className="prompt-modal__sections">{parse(prompt.DisplayDescription)}</Box>
                </Box>
              )}
            </Grid>

            {/* Sağ Sütun */}
            <Grid item xs={4}>
              {/* Try this prompt butonu */}
              {shouldShowTryButton() && (
                <Box className="prompt-modal__try-button-container">
                  {prompt.showOnWork ? (
                    <Tooltip title={t('promptModal.warnings.loginTooltip')} arrow>
                      <span>
                        <Button
                          variant="text"
                          fullWidth
                          className={`prompt-modal__try-in-app prompt-modal__try-in-app--${prompt.Products[0].toLowerCase()}`}
                          onClick={handleTryButtonClick}
                          disabled={isLoggingIn}
                        >
                          {isLoggingIn ? (
                            <CircularProgress size={24} />
                          ) : (
                            <Box display="flex" alignItems="center" gap={1}>
                              <img
                                src={getAppLogo(prompt.Products[0])}
                                alt={prompt.Products[0]}
                                style={{ width: 16, height: 16 }}
                              />
                              <Typography
                                variant="body2"
                                className={`prompt-modal__try-in-app-text prompt-modal__try-in-app-text--${prompt.Products[0].toLowerCase()}`}
                              >
                                {isAuthenticated
                                  ? t('promptModal.open')
                                  : t('promptModal.tryItIn', {
                                      app: getAppDisplayName(prompt.Products[0]),
                                    })}
                              </Typography>
                            </Box>
                          )}
                        </Button>
                      </span>
                    </Tooltip>
                  ) : (
                    <Button
                      variant="contained"
                      fullWidth
                      className="prompt-modal__try-button prompt-modal__try-button--openai"
                      onClick={handleTryButtonClick}
                    >
                      {t('promptModal.tryThisPrompt')}
                    </Button>
                  )}
                </Box>
              )}
              <Box className="prompt-modal__info">
                <Box className="prompt-modal__info-item">
                  <Typography variant="subtitle2">{t('promptModal.author')}</Typography>
                  <Box display="flex" alignItems="center" gap={1}>
                    <img
                      src={getProviderLogo(prompt.ProviderName)}
                      alt={prompt.ProviderName}
                      width={16}
                      height={16}
                    />
                    <Typography>
                      {prompt.ProviderName === 'Open AI' ? 'OpenAI' : prompt.ProviderName}
                    </Typography>
                  </Box>
                </Box>

                <Box className="prompt-modal__info-item">
                  <Typography variant="subtitle2">{t('promptModal.worksIn')}</Typography>
                  <Box display="flex" alignItems="center" gap={1} flexWrap="wrap">
                    {prompt.Products.map((product, index) => (
                      <Box key={index} display="flex" alignItems="center" gap={1}>
                        <img src={getAppLogo(product)} alt={product} width={16} height={16} />
                        <Typography>{getAppDisplayName(product)}</Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>

                {prompt.Function &&
                  Array.isArray(prompt.Function) &&
                  prompt.Function.length > 0 && (
                    <Box className="prompt-modal__info-item">
                      <Typography variant="subtitle2">{t('promptModal.function')}</Typography>
                      <Typography>{getFunctionLabel(prompt.Function)}</Typography>
                    </Box>
                  )}
              </Box>
            </Grid>
          </Grid>
        )}
      </DialogContent>
    </Dialog>
  );
};

const renderTitle = (text) => {
  if (!text) return null;
  const parts = text.split(/(<placeholder>.*?<\/placeholder>|\[.*?\])/g);
  return (
    <Typography>
      {parts.map((part, index) => {
        if (part.startsWith('<placeholder>') && part.endsWith('</placeholder>')) {
          const content = part.replace('<placeholder>', '').replace('</placeholder>', '').trim();
          return (
            <span key={index} className="prompt-modal__highlight">
              {content ? `{${content}}` : '<placeholder>'}
            </span>
          );
        } else if (part.startsWith('[') && part.endsWith(']')) {
          const content = part.substring(1, part.length - 1).trim();
          return (
            <span key={index} className="prompt-modal__highlight">
              {`[${content}]`}
            </span>
          );
        }
        return part;
      })}
    </Typography>
  );
};

PromptModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  prompt: PropTypes.object,
};

export default PromptModal;
