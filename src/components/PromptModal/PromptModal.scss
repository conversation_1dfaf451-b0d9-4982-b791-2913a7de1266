@use 'sass:color';
@use '../../styles/abstracts/variables' as *;

// <PERSON><PERSON>a ikonları için stiller
.file-icon {
  &--excel {
    color: #217346;
    font-size: 1.2rem;
  }
  
  &--word {
    color: #2b579a;
    font-size: 1.2rem;
  }
  
  &--powerpoint {
    color: #b7472a;
    font-size: 1.2rem;
  }
  
  &--pdf {
    color: #f40f02;
    font-size: 1.2rem;
  }
  
  &--default {
    color: #5f6368;
    font-size: 1.2rem;
  }
}

// Info Icon butonu için stiller
.info-icon-button {
  padding: 2px !important;
  
  .info-icon {
    font-size: 16px !important;
    color: $text-secondary !important;
  }
}

// MenuItem içeriği için flex container
.menu-item-content {
  display: flex;
  align-items: center;
  width: 100%;
  
  &__icon-container {
    margin-right: 8px;
  }
  
  &__text-container {
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
  }
  
  &__shared-label {
    font-size: 0.7rem;
    color: $text-secondary;
    margin-left: 4px;
    vertical-align: top;
  }
  
  &__file-type {
    margin-left: 8px;
    font-size: 0.75rem;
    color: $text-secondary;
    min-width: 50px;
    text-align: right;
  }
}

// MenuItem overflow kontrolü
.file-menu-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 290px;
}

.prompt-modal {
  .MuiDialog-paper {
    border-radius: $border-radius-lg;
  }

  &__header {
    padding: $spacing-4;
    border-bottom: 1px solid $border-color;

    .MuiTypography-h6 {
      font-weight: $font-weight-bold;
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    padding: $spacing-4;
  }

  &__main {
    margin-bottom: $spacing-4;
  }

  &__prompt-box {
    background-color: $bg-paper;
    padding: $spacing-4;
    border-radius: $border-radius-md;
    border: 1px solid $border-color;
    margin-top: $spacing-4;
    position: relative;

    .MuiTypography-root {
      font-weight: $font-weight-bold !important;
    }

    .prompt-modal__highlight {
      background: linear-gradient(90deg, #ff75cd, #8449c0, #09e0b5);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: $font-weight-bold !important;
    }
  }

  &__highlight {
    color: $primary-color;
    font-weight: $font-weight-bold;
  }

  &__info {
    background-color: $bg-default;
    padding: $spacing-4;
    border-radius: $border-radius-md;
    height: fit-content;
    position: sticky;
    top: $spacing-4;

    &-item {
      &:not(:last-child) {
        margin-bottom: $spacing-3;
      }
      .MuiTypography-subtitle2 {
        font-size: $font-size-xs !important;
        color: $text-secondary;
        margin-bottom: $spacing-1;
      }
    }
  }

  &__make-it-your-own {
    h2 {
      font-size: $font-size-lg !important;
      font-weight: $font-weight-bold;
      margin-bottom: $spacing-3;
      color: $text-primary;
    }
  }

  &__sections {
    background-color: $bg-default;
    border-radius: $border-radius-md;
    padding: $spacing-4;
    font-size: calc($font-size-md * 0.9) !important;
    color: $primary-text-color !important;
    pre{
      max-width: 100% !important;
      white-space: pre-wrap !important;
      word-wrap: break-word !important;
      overflow-x: auto !important;
      background-color: $bg-paper !important;
      padding: 0 $spacing-3 $spacing-3 $spacing-3 !important;
      border-radius: $border-radius-sm !important;
      border: 1px solid $border-color !important;
      code{
        max-width: 100% !important;
        width: 100% !important;
        padding:0 !important;
        margin:0 !important;
        display: inline-block !important;
        white-space: pre-wrap !important;
        word-break: break-word !important;
      }
    }
    b {
      color: $primary-text-color !important;
      display: block;
      margin-top: $spacing-3;
      margin-bottom: $spacing-1;

      &:first-child {
        margin-top: 0;
      }
    }
  }

  &__actions {
    display: flex;
    gap: $spacing-2;
    margin-top: $spacing-3;
    transition: opacity 0.2s ease-in-out;
    position: relative;
    flex-wrap: wrap;
    align-items: center;
  }

  &__action-button {
    background-color: $bg-paper !important;
    border: 1px solid $border-color !important;
    padding: 6px !important;

    &:hover {
      background-color: color.scale($bg-paper, $lightness: -5%) !important;
    }

    svg {
      width: 18px;
      height: 18px;
      color: $text-secondary;
    }
  }

  &__prompt-box:hover {
    .prompt-modal__actions {
      opacity: 1;
    }
  }

  &__try-button-container {
    margin-top: $spacing-4;
    margin-bottom: $spacing-2;
  }

  &__try-button {
    text-transform: none !important;
    font-weight: $font-weight-medium !important;
    padding: $spacing-2 $spacing-3 !important;
    border-radius: $border-radius-md !important;
    font-size: $font-size-sm !important;
    box-shadow: none !important;
    color: white !important;

    &:hover {
      box-shadow: $shadow-sm !important;
    }

    &--microsoft {
      background-color: #185ABD !important;

      &:hover {
        background-color: color.scale(#185ABD, $lightness: -10%) !important;
      }
    }
    
    &--word {
      background-color: #185ABD !important;
      color: white !important;

      &:hover {
        background-color: color.scale(#185ABD, $lightness: -10%) !important;
      }
    }
    
    &--excel {
      background-color: #217346 !important;
      color: white !important;

      &:hover {
        background-color: color.scale(#217346, $lightness: -10%) !important;
      }
    }
    
    &--powerpoint {
      background-color: #B7472A !important;
      color: white !important;

      &:hover {
        background-color: color.scale(#B7472A, $lightness: -10%) !important;
      }
    }
    
    &--onenote {
      background-color: #7719AA !important;
      color: white !important;

      &:hover {
        background-color: color.scale(#7719AA, $lightness: -10%) !important;
      }
    }

    &--openai {
      background: #79AC9B !important;
      color: white !important;

      &:hover {
        background: color.scale(#79AC9B, $lightness: -10%) !important;
      }
    }
  }

  &__try-in-app {
    background-color: rgba(24, 90, 189, 0.08) !important;
    justify-content: center !important;
    padding: $spacing-2 $spacing-3 !important;
    border-radius: $border-radius-md !important;
    text-transform: none !important;
    font-weight: $font-weight-medium !important;
    font-size: $font-size-sm !important;
    height: 40px !important;

    &:hover {
      background-color: rgba(24, 90, 189, 0.12) !important;
      box-shadow: $shadow-sm !important;
    }

    &:disabled {
      opacity: 0.6 !important;
      cursor: not-allowed !important;
    }
    
    &--word {
      background-color: rgba(24, 90, 189, 0.08) !important;
      &:hover {
        background-color: rgba(24, 90, 189, 0.12) !important;
      }
    }
    
    &--excel {
      background-color: rgba(33, 115, 70, 0.08) !important;
      &:hover {
        background-color: rgba(33, 115, 70, 0.12) !important;
      }
    }
    
    &--powerpoint {
      background-color: rgba(183, 71, 42, 0.08) !important;
      &:hover {
        background-color: rgba(183, 71, 42, 0.12) !important;
      }
    }
    
    &--onenote {
      background-color: rgba(119, 25, 170, 0.08) !important;
      &:hover {
        background-color: rgba(119, 25, 170, 0.12) !important;
      }
    }

    &-text {
      font-size: 14px !important;
      font-weight: 500 !important;
      
      &--word {
        color: #185ABD !important;
      }
      
      &--excel {
        color: #217346 !important;
      }
      
      &--powerpoint {
        color: #B7472A !important;
      }
      
      &--onenote {
        color: #7719AA !important;
      }
    }
  }

  &__file-select {
    min-width: 180px !important;
    height: 32px !important;
    background-color: $bg-paper !important;
    border: 1px solid $border-color !important;
    margin-right: $spacing-1 !important;
    border-radius: $border-radius-sm !important;
    max-width: 300px !important;

    .MuiSelect-select {
      padding: 5px 32px 5px 12px !important;
      font-size: $font-size-sm !important;
      color: $text-secondary !important;
      display: flex !important;
      align-items: center !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }

    .MuiOutlinedInput-notchedOutline {
      border: none !important;
    }

    .MuiSvgIcon-root {
      color: $text-secondary !important;
      font-size: 20px !important;
      right: 8px !important;
    }

    &:hover {
      background-color: color.scale($bg-paper, $lightness: -3%) !important;
      border-color: color.scale($border-color, $lightness: -10%) !important;
    }

    &.Mui-focused {
      background-color: $bg-paper !important;
      border-color: #79AC9B !important;
      
      .MuiSvgIcon-root {
        color: #79AC9B !important;
      }
    }

    .MuiMenuItem-root {
      font-size: $font-size-sm !important;
      padding: 8px 12px !important;
      
      &:hover {
        background-color: rgba(#79AC9B, 0.08) !important;
      }
      
      &.Mui-selected {
        background-color: rgba(#79AC9B, 0.12) !important;
        
        &:hover {
          background-color: rgba(#79AC9B, 0.16) !important;
        }
      }
    }
  }

  &__file-warning {
    color: #d32f2f !important;
    font-size: $font-size-sm !important;
    margin-left: auto !important;
    font-weight: 500 !important;
    
    &::before {
      content: "⚠️";
      margin-right: $spacing-1;
    }
  }
} 