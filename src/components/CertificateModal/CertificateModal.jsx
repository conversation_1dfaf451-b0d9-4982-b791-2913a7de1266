import React from 'react';
import PropTypes from 'prop-types';
import {
  Modal,
  Box,
  Typography,
  Button,
  IconButton,
  Paper,
  useTheme,
  useMediaQuery
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import DownloadIcon from '@mui/icons-material/Download';
import LockIcon from '@mui/icons-material/Lock';
import './CertificateModal.scss';

const CertificateModal = ({ 
  open, 
  onClose, 
  certificateUrl, 
  courseId 
}) => {
  const handleDownloadCertificate = () => {
    if (!certificateUrl) return;
    
    try {
      // Create a temporary anchor element
      const downloadLink = document.createElement('a');
      downloadLink.href = certificateUrl;
      downloadLink.download = `certificate-${courseId || 'course'}.pdf`; 
      downloadLink.target = '_blank';
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    } catch (error) {
      console.error('Error downloading certificate:', error);
      // Fallback - open in new tab
      window.open(certificateUrl, '_blank');
    }
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="certificate-modal-title"
      aria-describedby="certificate-modal-description"
      className="certificate-modal"
    >
      <Paper 
        className="certificate-modal-paper"
        elevation={5}
      >
        {/* Header */}
        <Box className="certificate-modal-header">
          <Typography variant="h6" component="h2" id="certificate-modal-title" className="certificate-modal-header-title">
            Course Certificate
          </Typography>
          <IconButton 
            onClick={onClose}
            aria-label="close" 
            size="small"
            className="certificate-modal-header-close-button"
          >
            <CloseIcon />
          </IconButton>
        </Box>
        
        {/* Content */}
        <Box 
          className={`certificate-modal-content ${
            certificateUrl 
              ? 'certificate-modal-content-with-certificate' 
              : 'certificate-modal-content-without-certificate'
          }`}
        >
          {certificateUrl ? (
            <>
              <Box className="certificate-modal-content-iframe-container">
                <iframe
                  src={certificateUrl}
                  title="Certificate"
                />
              </Box>
              
              <Button
                variant="contained"
                color="primary"
                startIcon={<DownloadIcon />}
                onClick={handleDownloadCertificate}
                className="certificate-modal-content-download-button"
              >
                Download Certificate
              </Button>
            </>
          ) : (
            <Box className="certificate-modal-content-empty-state">
              <LockIcon className="lock-icon" />
              <Typography 
                variant="h6" 
                className="empty-title"
              >
                Certificate Not Available Yet
              </Typography>
              <Typography 
                variant="body1"
                className="empty-description"
              >
                Your certificate will appear here once you complete this course.
              </Typography>
            </Box>
          )}
        </Box>
      </Paper>
    </Modal>
  );
};

CertificateModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  certificateUrl: PropTypes.string,
  courseId: PropTypes.string
};

export default CertificateModal;