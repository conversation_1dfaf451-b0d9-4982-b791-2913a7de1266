@use '../../styles/abstracts/variables' as *;

.certificate-modal {
  &-paper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    padding: $spacing-3;
    border-radius: $border-radius-md;
    overflow: hidden;
    display: flex;
    flex-direction: column; 
    
    @media (max-width: 600px) {
      width: 95%;
    }
  }
  
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-2;
    padding-bottom: $spacing-2;
    border-bottom: 1px solid $border-color;
    
    &-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
    }
    
    &-close-button {
      color: $text-secondary;
      
      &:hover {
        background-color: rgba($text-secondary, 0.05);
      }
    }
  }
  
  &-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    &-with-certificate {
      justify-content: flex-start;
    }
    
    &-without-certificate {
      justify-content: center;
    }
    
    &-iframe-container {
      width: 100%;
      height: calc(90vh - 140px);
      overflow: hidden;
      border-radius: $border-radius-sm;
      border: 1px solid $border-color;
      margin-bottom: $spacing-2;
      
      iframe {
        width: 100%;
        height: 100%;
        border: none;
      }
    }
    
    &-download-button {
      padding: $spacing-1 $spacing-3;
      border-radius: $border-radius-md;
      font-weight: $font-weight-medium;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: $shadow-md;
      }
    }
    
    &-empty-state {
      text-align: center;
      padding: $spacing-5 $spacing-3;
      max-width: 500px;
      
      .lock-icon {
        font-size: 4rem;
        color: $text-secondary;
        margin-bottom: $spacing-2;
      }
      
      .empty-title {
        margin-bottom: $spacing-1;
        font-weight: $font-weight-semibold;
      }
      
      .empty-description {
        color: $text-secondary;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -45%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
} 