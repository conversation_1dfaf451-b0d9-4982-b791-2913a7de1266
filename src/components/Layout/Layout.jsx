import { useLocation, Outlet } from 'react-router-dom';
import { Box } from '@mui/material';
import Header from '../Header/Header';
import Footer from '../Footer/Footer';
import { createContext, useContext, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import useHelpForm from '../../domains/form/hook/useHelpForm';
import { updateTrainingProgressVisibility } from '../../redux/features/training/trainingSlice';
import JourneyCompletionChecker from '../../middleware/JourneyCompletionChecker';
import RoleUpgradeModal from '../RoleUpgradeModal/RoleUpgradeModal';

// Yardım formu için context oluşturuyoruz
export const HelpFormContext = createContext(null);

// Hook kullanımını kolaylaştıracak helper
export const useHelpFormContext = () => {
  const context = useContext(HelpFormContext);
  if (!context) {
    throw new Error('useHelpFormContext must be used within a HelpFormProvider');
  }
  return context;
};

const Layout = () => {
  const location = useLocation();
  const dispatch = useDispatch();
  const isLoginPage = location.pathname === '/login';
  const isCopilotPage =
    location.pathname === '/microsoft-copilot' || location.pathname === '/microsoft-copilot/';
  const isCoursePage = location.pathname.startsWith('/course/');

  // Sayfa yolu değiştiğinde TrainingProgress görünürlüğünü güncelle
  useEffect(() => {
    dispatch(updateTrainingProgressVisibility({ pathname: location.pathname }));
  }, [location.pathname, dispatch]);

  // Help formu hook'unu burada çağırıyoruz
  const helpFormValues = useHelpForm();

  const mainContent = (
    <Box
      component="main"
      sx={{
        flex: 1,
        width: '90%',
        alignSelf: 'center',
        display: 'flex',
        flexDirection: 'column',
        ...(isLoginPage && {
          justifyContent: 'center',
          py: 4,
        }),
        ...(!isCoursePage && {
          mb: 5,
        }),
      }}
    >
      <Outlet />
    </Box>
  );

  const user = useSelector((state) => state.auth.user);

  return (
    <HelpFormContext.Provider value={helpFormValues}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          minHeight: '100vh',
          backgroundColor: '#FBFBFB',
        }}
      >
        {!isLoginPage && <Header />}
        {isCopilotPage || isCoursePage ? (
          <>
            <Box sx={{ width: '100%' }}>
              <Outlet />
            </Box>
          </>
        ) : (
          mainContent
        )}
        {!isLoginPage && (
          <Box sx={{ mt: 'auto' }}>
            <Footer />
          </Box>
        )}
        {/* Journey tamamlanma kontrolü için bileşen */}
        {user && !isLoginPage && <JourneyCompletionChecker />}

        {/* Previous Role Modal - tüm sayfalarda kontrol edilecek */}
        {user && <RoleUpgradeModal />}
      </Box>
    </HelpFormContext.Provider>
  );
};

export default Layout;
