import React from 'react';
import { Box, Container, CircularProgress } from '@mui/material';
import reactLogo from '../../assets/aibs_logo.svg';

const Loading = () => (
  <Container
    maxWidth="xs"
    sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    }}
  >
    <Box
      sx={{
        position: 'relative',
        display: 'inline-flex',
      }}
    >
      <CircularProgress
        size={100}
        sx={{
          color: 'primary.main',
        }}
      />
      <Box
        component="img"
        src={reactLogo}
        alt="Loading"
        sx={{
          position: 'absolute',
          width: '80%',
          height: 'auto',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          animation: 'fade 1.5s ease-in-out infinite',
          '@keyframes fade': {
            '0%': { opacity: 0.3 },
            '50%': { opacity: 1 },
            '100%': { opacity: 0.3 },
          },
        }}
      />
    </Box>
  </Container>
);

export default Loading;
