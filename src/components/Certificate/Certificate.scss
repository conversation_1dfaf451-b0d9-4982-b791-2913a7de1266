@use '../../styles/abstracts/variables' as *;

.certificate {
  &-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  &-iframe-container {
    width: 100%;
    height: 70vh;
    overflow: hidden;
    border-radius: $border-radius-md;
    border: 1px solid $border-color;
  }

  &-iframe {
    width: 100%;
    height: 100%;
    border: none;
  }

  &-actions {
    margin-top: $spacing-4;
    display: flex;
    justify-content: center;

    .download-button {
      padding: $spacing-2 $spacing-4;
      border-radius: $border-radius-md;
      font-weight: $font-weight-medium;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: $shadow-sm;
      }
    }
  }

  &-unavailable {
    text-align: center;
    padding: $spacing-6 $spacing-4;
    
    .lock-icon {
      font-size: 3rem;
      color: $text-secondary;
      margin-bottom: $spacing-3;
    }

    h6 {
      margin-bottom: $spacing-2;
      font-weight: $font-weight-semibold;
      color: $text-primary;
    }

    p {
      color: $text-secondary;
    }
  }
} 