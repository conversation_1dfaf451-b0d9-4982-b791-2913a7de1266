import React from 'react';
import PropTypes from 'prop-types';
import { Box, Button, Typography } from '@mui/material';
import ArticleIcon from '@mui/icons-material/Article';
import LockIcon from '@mui/icons-material/Lock';
import DownloadIcon from '@mui/icons-material/Download';
import './Certificate.scss';

const Certificate = ({ certificateUrl, courseId }) => {
  const handleDownloadCertificate = () => {
    if (!certificateUrl) return;
    
    // Create a temporary anchor element
    const downloadLink = document.createElement('a');
    downloadLink.href = certificateUrl;
    downloadLink.download = `certificate-${courseId}.pdf`;
    downloadLink.target = '_blank';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
  };

  if (!certificateUrl) {
    return (
      <Box className="certificate-unavailable">
        <LockIcon className="lock-icon" />
        <Typography variant="h6">Certificate Not Available Yet</Typography>
        <Typography variant="body1" color="text.secondary">
          Your certificate will appear here once you complete this course.
        </Typography>
      </Box>
    );
  }

  return (
    <Box className="certificate-container">
      <Box className="certificate-iframe-container">
        <iframe 
          src={certificateUrl} 
          title="Certificate" 
          className="certificate-iframe"
        />
      </Box>
      <Box className="certificate-actions">
        <Button 
          variant="contained" 
          color="primary" 
          onClick={handleDownloadCertificate}
          startIcon={<DownloadIcon />}
          className="download-button"
        >
          Download Certificate
        </Button>
      </Box>
    </Box>
  );
};

Certificate.propTypes = {
  certificateUrl: PropTypes.string,
  courseId: PropTypes.string.isRequired
};

export default Certificate; 