import PropTypes from 'prop-types';
const Button = ({
  children,
  variant = 'contained',
  color = 'default',
  size = 'medium',
  onClick,
  disabled = false,
  className = '',
  type = 'button',
  href,
  target,
  rel,
  status,
}) => {
  const buttonClasses = [
    'button',
    `button--${variant}`,
    `button--${color}`,
    `button--${size}`,
    status && `button--${status}`,
    className,
    disabled ? 'button--disabled' : '',
  ]
    .filter(Boolean)
    .join(' ');

  const TagName = href ? 'a' : 'button';
  const linkProps = href ? { href, target, rel } : {};

  return (
    <TagName
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
      type={!href ? type : undefined}
      {...linkProps}
    >
      {children}
    </TagName>
  );
};

Button.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf(['contained', 'outlined', 'text']),
  color: PropTypes.oneOf(['default', 'primary', 'secondary', 'success']),
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  onClick: PropTypes.func,
  disabled: PropTypes.bool,
  className: PropTypes.string,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  href: PropTypes.string,
  target: PropTypes.string,
  rel: PropTypes.string,
  status: PropTypes.oneOf(['completed', 'in-progress', 'locked']),
};

export default Button;
