import { useEffect, useState, useRef } from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  Button,
  IconButton,
  LinearProgress,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  ZoomOut as ZoomOutIcon,
  ZoomIn as ZoomInIcon,
  Fullscreen as FullscreenIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import * as pdfjsLib from 'pdfjs-dist';
import PropTypes from 'prop-types';

pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`;

const PdfViewer = ({ pdfUrl, title, description }) => {
  const [numPages, setNumPages] = useState(null);
  const [pdfError, setPdfError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [pdfDoc, setPdfDoc] = useState(null);
  const [scale, setScale] = useState(1.0);
  const [currentPage, setCurrentPage] = useState(1);
  const canvasContainerRef = useRef(null);
  const pdfContainerRef = useRef(null);

  const loadPDF = async (url) => {
    try {
      setIsLoading(true);

      const loadingTask = pdfjsLib.getDocument({
        url: url,
        cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
        cMapPacked: true,
      });

      // Yükleme ilerleme durumu
      loadingTask.onProgress = function (progress) {
        return (
          <LinearProgress value={(progress.loaded / progress.total) * 100} variant="determinate" />
        );
      };

      const pdf = await loadingTask.promise;

      setPdfDoc(pdf);
      setNumPages(pdf.numPages);
      setCurrentPage(1);
      setIsLoading(false);
      setPdfError(null);
    } catch (err) {
      setPdfError(err);
      setIsLoading(false);
    }
  };

  const renderAllPages = async () => {
    if (!pdfDoc || !canvasContainerRef.current) return;

    try {
      canvasContainerRef.current.innerHTML = '';

      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        const page = await pdfDoc.getPage(pageNum);

        const pageContainer = document.createElement('div');
        pageContainer.className = 'pdf-page-container';
        pageContainer.setAttribute('data-page-number', pageNum);

        const pageNumberElement = document.createElement('div');
        pageNumberElement.className = 'pdf-page-number';
        pageNumberElement.textContent = `Sayfa ${pageNum} / ${numPages}`;
        pageContainer.appendChild(pageNumberElement);

        const canvas = document.createElement('canvas');
        canvas.className = 'pdf-canvas';
        pageContainer.appendChild(canvas);

        canvasContainerRef.current.appendChild(pageContainer);

        // PDF sayfasını render et
        const context = canvas.getContext('2d', { alpha: false });

        // Yüksek kaliteli render için viewport ayarları
        const viewport = page.getViewport({
          scale: scale,
          rotation: 0,
          offsetX: 0,
          offsetY: 0,
        });

        const pixelRatio = Math.max(window.devicePixelRatio || 1, 3);
        canvas.width = Math.floor(viewport.width * pixelRatio);
        canvas.height = Math.floor(viewport.height * pixelRatio);

        // Canvas stilini scale değerine göre dinamik olarak ayarla - tam genişlik yerine gerçek boyut
        canvas.style.width = `${viewport.width}px`;
        canvas.style.height = `${viewport.height}px`;
        canvas.style.maxWidth = 'none';

        // Yüksek çözünürlük için context'i scale et
        context.scale(pixelRatio, pixelRatio);

        context.imageSmoothingEnabled = true;
        context.imageSmoothingQuality = 'high';

        await page.render({
          canvasContext: context,
          viewport: viewport,
          enableWebGL: true, // WebGL hızlandırma
          renderInteractiveForms: true,
          annotationMode: 2, // ENABLE_FORMS = 2
          intent: 'display',
          canvasFactory: null,
          background: 'rgba(255,255,255,1)',
          renderTextLayer: false,
          renderAnnotations: true,
          optionalContentConfigPromise: null,
          transform: null,
        }).promise;
      }
    } catch (err) {
      console.error('PDF sayfalarını render ederken hata:', err);
      setPdfError(err);
    }
  };

  // Zoom fonksiyonu
  const handleZoom = (delta) => {
    setScale((prevScale) => {
      // Zoom adımı
      const zoomStep = 0.25;

      // Zoom değişimi ve sınırları ayarla
      let newScale;
      if (delta < 0) {
        // Zoom out - 0.25 azalt, minimum 0.5
        newScale = Math.max(0.5, prevScale - zoomStep);
      } else {
        // Zoom in - 0.25 artır, maximum 4.0
        newScale = Math.min(4.0, prevScale + zoomStep);
      }

      // Zoom değişimini tamamla
      return Math.round(newScale * 100) / 100; // İki ondalık basamağa yuvarla
    });
  };

  // Tam ekran fonksiyonu
  const handleFullscreen = () => {
    if (pdfContainerRef.current) {
      if (
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      ) {
        // Farklı tarayıcılar için uyumlu exitFullscreen çağrısı
        const exitMethod =
          document.exitFullscreen ||
          document.webkitExitFullscreen ||
          document.mozCancelFullScreen ||
          document.msExitFullscreen;

        if (exitMethod) {
          exitMethod
            .call(document)
            .catch((err) => console.error('Tam ekrandan çıkarken hata:', err));
        }
      } else {
        // Farklı tarayıcılar için uyumlu requestFullscreen çağrısı
        const element = pdfContainerRef.current;
        const requestMethod =
          element.requestFullscreen ||
          element.webkitRequestFullscreen ||
          element.mozRequestFullScreen ||
          element.msRequestFullscreen;

        if (requestMethod) {
          requestMethod
            .call(element)
            .catch((err) => console.error('Tam ekran yaparken hata:', err));
        } else {
          console.warn('Tarayıcınız tam ekran modunu desteklemiyor');
        }
      }
    }
  };

  // Önceki sayfaya git
  const handlePrevPage = () => {
    if (currentPage > 1) {
      const newPage = currentPage - 1;
      setCurrentPage(newPage);
      scrollToPage(newPage);
    }
  };

  // Sonraki sayfaya git
  const handleNextPage = () => {
    if (currentPage < numPages) {
      const newPage = currentPage + 1;
      setCurrentPage(newPage);
      scrollToPage(newPage);
    }
  };

  // Belirli bir sayfaya scroll yapar
  const scrollToPage = (pageNum) => {
    const pageElem = canvasContainerRef.current?.querySelector(`[data-page-number="${pageNum}"]`);
    if (pageElem) {
      pageElem.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // PDF URL'si değiştiğinde PDF'i yükle
  useEffect(() => {
    if (pdfUrl) {
      loadPDF(pdfUrl);
    }
  }, [pdfUrl]);

  // PDF yüklendiğinde ve scale değiştiğinde sayfaları render et
  useEffect(() => {
    if (pdfDoc) {
      renderAllPages();
    }
  }, [pdfDoc, scale]);

  // Scroll olayını dinle
  useEffect(() => {
    const handleScroll = () => {
      if (!canvasContainerRef.current) return;

      // Ekranda görünen sayfayı tespit et
      const containerRect = canvasContainerRef.current.getBoundingClientRect();
      const pageElements = canvasContainerRef.current.querySelectorAll('.pdf-page-container');

      // En çok görünen sayfayı bul
      let mostVisiblePage = 1;
      let maxVisibleArea = 0;

      pageElements.forEach((pageElem) => {
        const pageRect = pageElem.getBoundingClientRect();
        const pageNum = parseInt(pageElem.getAttribute('data-page-number'));

        // Sayfa ve container arasındaki kesişim alanını hesapla
        const visibleTop = Math.max(pageRect.top, containerRect.top);
        const visibleBottom = Math.min(pageRect.bottom, containerRect.bottom);
        const visibleHeight = Math.max(0, visibleBottom - visibleTop);

        if (visibleHeight > maxVisibleArea) {
          maxVisibleArea = visibleHeight;
          mostVisiblePage = pageNum;
        }
      });

      if (mostVisiblePage !== currentPage) {
        setCurrentPage(mostVisiblePage);
      }
    };

    const container = canvasContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [currentPage]);

  return (
    <Box sx={{ p: 2 }}>
      {title && (
        <Typography variant="h5" gutterBottom>
          {title}
        </Typography>
      )}
      {description && (
        <Typography variant="body1" color="text.secondary" gutterBottom>
          {description}
        </Typography>
      )}

      {pdfUrl ? (
        <Box
          ref={pdfContainerRef}
          sx={{
            mt: 3,
            width: '100%',
            height: '80vh',
            border: '1px solid #ddd',
            borderRadius: 1,
            overflow: 'hidden',
            position: 'relative',
            bgcolor: 'background.paper',
            '.pdf-controls': {
              display: 'flex',
              alignItems: 'center',
              padding: '8px 16px',
              borderBottom: '1px solid #ddd',
              backgroundColor: '#f5f5f5',
            },
            '.pdf-control-button': {
              mx: 0.5,
            },
            '.page-info': {
              mx: 2,
              fontSize: '0.875rem',
            },
            '.zoom-info': {
              mx: 2,
              fontSize: '0.875rem',
            },
            '.controls-divider': {
              height: '24px',
              width: '1px',
              backgroundColor: '#ddd',
              mx: 2,
            },
            '.canvas-container': {
              height: 'calc(100% - 48px)',
              overflowY: 'auto',
              padding: '16px',
            },
            '.pdf-page-container': {
              position: 'relative',
              marginBottom: '24px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            },
            '.pdf-page-number': {
              fontSize: '0.75rem',
              color: '#666',
              marginBottom: '8px',
            },
            '.pdf-canvas': {
              border: '1px solid #ddd',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            },
          }}
        >
          {isLoading ? (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
              }}
            >
              <CircularProgress />
            </Box>
          ) : pdfError ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                p: 3,
                textAlign: 'center',
              }}
            >
              <Typography color="error" gutterBottom>
                Error loading PDF
              </Typography>
              <Button
                variant="outlined"
                color="primary"
                onClick={() => window.open(pdfUrl, '_blank')}
              >
                Open in new tab
              </Button>
            </Box>
          ) : (
            <>
              <div className="pdf-controls">
                <IconButton
                  onClick={handlePrevPage}
                  className="pdf-control-button"
                  disabled={currentPage <= 1}
                >
                  <ArrowBackIcon />
                </IconButton>

                <Typography className="page-info">
                  {currentPage} / {numPages}
                </Typography>

                <IconButton
                  onClick={handleNextPage}
                  className="pdf-control-button"
                  disabled={currentPage >= numPages}
                >
                  <ArrowForwardIcon />
                </IconButton>

                <div className="controls-divider"></div>

                <IconButton onClick={() => handleZoom(-1)} className="pdf-control-button">
                  <ZoomOutIcon />
                </IconButton>

                <Typography className="zoom-info">{Math.round(scale * 100)}%</Typography>

                <IconButton onClick={() => handleZoom(1)} className="pdf-control-button">
                  <ZoomInIcon />
                </IconButton>

                <IconButton onClick={handleFullscreen} className="pdf-control-button">
                  <FullscreenIcon />
                </IconButton>

                <Box sx={{ marginLeft: 'auto' }}>
                  <Button
                    variant="outlined"
                    color="primary"
                    startIcon={<DownloadIcon />}
                    onClick={() => window.open(pdfUrl, '_blank')}
                  >
                    Download
                  </Button>
                </Box>
              </div>

              <div className="canvas-container" ref={canvasContainerRef}></div>
            </>
          )}
        </Box>
      ) : (
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: '1px dashed',
            borderColor: 'divider',
            borderRadius: 1,
            textAlign: 'center',
          }}
        >
          <Typography variant="body1" color="text.secondary">
            PDF not found
          </Typography>
        </Box>
      )}
    </Box>
  );
};

PdfViewer.propTypes = {
  pdfUrl: PropTypes.string,
  title: PropTypes.string,
  description: PropTypes.string,
};

export default PdfViewer;
