@use '../../styles/abstracts/variables' as *;

.card-with-icon {

  padding: $spacing-4;
  background: $bg-paper;
  border-radius: $border-radius-md;
  height: calc(100% - #{$spacing-4 * 2});
  display: flex;
  box-shadow: $shadow-sm;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid $divider-color;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.08);
  }

  &__icon {
    margin-bottom: $spacing-2;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: $border-radius-md;
    margin-bottom: $spacing-3;
    flex-shrink: 0;
    border: 1px solid transparent;
    overflow: hidden;
    
    svg {
      width: 24px;
      height: 24px;
    }

    // İkon varyantları
    &--favorite {
      background-color: $icon-bg-favorite;
      border-color: $icon-bg-favorite;
      color: $icon-favorite;
    }

    &--sales {
      background-color: $icon-bg-sales;
      border-color: $icon-bg-sales;
      color: $icon-sales;
    }

    &--management {
      background-color: $icon-bg-management;
      border-color: $icon-bg-management;
      color: $icon-management;
    }

    &--toolkit {
      background-color: $icon-bg-toolkit;
      border-color: $icon-bg-toolkit;
      color: $icon-toolkit;
    }

    // Resim stili
    img.card-with-icon__image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: $spacing-1;
    flex: 1;
  }

  &__title {
    font-size: $font-size-md !important;
    font-weight: 900 !important;
    color: $text-primary;
    margin: 0 0 $spacing-1 0;
  }

  &__date {
    font-size: $font-size-xs !important;
    color: $text-disabled;
    margin: $spacing-2 0 0 0 !important;
    display: flex;
    align-items: center;
  }

  &__description {
    font-size: ($font-size-md - 2px) !important;
    color: $text-secondary;
    margin: 0;
    line-height: 1.5;
    flex: 1;

    // Line clamp varyantları
    &--clamp-1 {
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &--clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &--clamp-3 {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &--clamp-4 {
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &--clamp-5 {
      display: -webkit-box;
      -webkit-line-clamp: 5;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &__button {
    border:none !important;
    padding:0 !important;
    color: $primary-color !important;
    margin-top: $spacing-2 !important;
    text-transform: none !important;
    align-self: flex-start;
    &:hover{
      background-color: transparent !important;
      color: $primary-color-dark !important;
    }
  }

  // Playground varyantı için stiller
  &.playground {
    background: #ffffff;
    border: 1px solid #ecede8;

    &:hover {
      transform: translateY(-6px);
      box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.08);
    }

    .icon-wrapper {
      background: transparent;
      margin-bottom: 16px;
    }

    .title {
      font-size: $font-size-md !important;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .description {
      font-size: ($font-size-md - 2px) !important;
      color: #666;
      line-height: 1.5;
    }
  }

  // Logo varyantı için stiller
  &.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $spacing-4;
    height: calc(100% - #{$spacing-4 * 2});
    background: #ffffff;
    border: 1px solid $border-color;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.08);
    }

    .card-with-icon__logo {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        object-fit: contain;
        &.openai , &.microsoft , &.github{
          max-height:28px;
        }
        &.aiplanet , &.aibs , &.aws , &.workday , &.sap , &.proalpha , &.uipath{
          max-width:75%;
        }
      }
    }
  }

  &.locked {
    opacity: 0.7;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow: $shadow-sm;
    }

    .card-with-icon__icon {

      .card-with-icon__lock {
        position: absolute;
        right: 0;
        transform: translate(-50%);
        background: rgba(0, 0, 0, 0.2);
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;

        .MuiSvgIcon-root {
          color: white;
          font-size: 20px;
        }
      }
    }
  }
}

.card-with-icon-tooltip {
  background-color: $primary-color;
  font-size: $font-size-xs;
  padding: $spacing-2 $spacing-3;
  border-radius: $border-radius-sm;
} 