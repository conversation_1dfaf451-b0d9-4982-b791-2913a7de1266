import PropTypes from 'prop-types';
import './CardWithIcon.scss';
import { Box, Typography, Tooltip, Button } from '@mui/material';
import LockIcon from '@mui/icons-material/Lock';
import React from 'react';
import { useNavigate } from 'react-router-dom';

const CardWithIcon = ({
  icon,
  image,
  title,
  description,
  variant,
  locked,
  tooltipText,
  buttonURL,
  buttonType,
  buttonFunction,
  buttonText,
  newTab,
  date,
  lineClamp,
  class: customClass,
}) => {
  const navigate = useNavigate();

  const formatDate = (dateString) => {
    if (!dateString) return '';

    try {
      const dateObj = new Date(dateString);

      // UTC tarih ve saat bilgilerini al
      const day = String(dateObj.getUTCDate()).padStart(2, '0');
      const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0'); // Ay 0'dan ba<PERSON>lar
      const year = dateObj.getUTCFullYear();

      // UTC saat ve dakika
      const hours = String(dateObj.getUTCHours()).padStart(2, '0');
      const minutes = String(dateObj.getUTCMinutes()).padStart(2, '0');

      // DD/MM/YYYY - HH:MM formatında döndür
      return `${day}/${month}/${year} - ${hours}:${minutes}`;
    } catch (error) {
      console.error('Date formatting error:', error);
      return dateString;
    }
  };

  const handleCardClick = () => {
    if (buttonType === 'URL' && !buttonText) {
      if (newTab) {
        window.open(buttonURL, '_blank');
      } else {
        navigate(buttonURL);
      }
    } else if (buttonType === 'FUNCTION') {
      buttonFunction();
    }
  };

  const cardContent = (
    <Box
      className={`card-with-icon ${variant} ${locked ? 'locked' : ''}`}
      onClick={!locked && !buttonText ? handleCardClick : undefined}
    >
      {variant === 'logo' ? (
        <Box className="card-with-icon__logo">
          <img src={image} alt={title} className={`card-with-icon__image ${customClass || ''}`} />
        </Box>
      ) : (
        <>
          <Box className={`card-with-icon__icon ${icon ? `card-with-icon__icon--${variant}` : ''}`}>
            {image ? (
              <img
                src={image}
                alt={title}
                className={`card-with-icon__image ${customClass || ''}`}
              />
            ) : (
              icon
            )}
            {locked && (
              <Box className="card-with-icon__lock">
                <LockIcon />
              </Box>
            )}
          </Box>
          <Box className="card-with-icon__content">
            <Typography className="card-with-icon__title">{title}</Typography>
            {date && (
              <Typography className="card-with-icon__date">
                Submitted on {formatDate(date)}
              </Typography>
            )}
            <Typography
              variant="body1"
              className={`card-with-icon__description ${lineClamp ? `card-with-icon__description--clamp-${lineClamp}` : ''}`}
            >
              {description}
            </Typography>
            {buttonText && (
              <Button
                variant="outlined"
                color="primary"
                size="small"
                onClick={() => {
                  if (buttonType === 'URL') {
                    if (newTab) {
                      window.open(buttonURL, '_blank');
                    } else {
                      navigate(buttonURL);
                    }
                  } else if (buttonType === 'FUNCTION') {
                    buttonFunction();
                  }
                }}
                className="card-with-icon__button"
              >
                {buttonText}
              </Button>
            )}
          </Box>
        </>
      )}
    </Box>
  );

  return locked && tooltipText ? (
    <Tooltip
      title={tooltipText}
      arrow
      placement="top"
      classes={{ tooltip: 'card-with-icon-tooltip' }}
    >
      {cardContent}
    </Tooltip>
  ) : (
    cardContent
  );
};

CardWithIcon.propTypes = {
  icon: PropTypes.node,
  image: PropTypes.string,
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  onClick: PropTypes.func,
  variant: PropTypes.oneOf(['favorite', 'sales', 'management', 'toolkit', 'playground', 'logo']),
  locked: PropTypes.bool,
  tooltipText: PropTypes.string,
  buttonURL: PropTypes.string,
  buttonType: PropTypes.string,
  buttonFunction: PropTypes.func,
  buttonText: PropTypes.string,
  newTab: PropTypes.bool,
  date: PropTypes.string,
  lineClamp: PropTypes.number,
  class: PropTypes.string,
};

export default CardWithIcon;
