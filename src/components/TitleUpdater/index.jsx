import { useEffect } from 'react';
import { useGetPublicSettingsQuery } from '../../redux/services/platform-settings-api';

const TitleUpdater = () => {
  const { data: generalSettings, isLoading, error } = useGetPublicSettingsQuery();

  useEffect(() => {
    // Update document title when platform settings are loaded
    if (!isLoading && !error && generalSettings?.data?.platformName) {
      document.title = generalSettings.data.platformName;
    } else if (!isLoading && (error || !generalSettings?.data?.platformName)) {
      // Fallback title if API fails or platformName is not available
      document.title = 'AI Adoption Platform';
    }
  }, [generalSettings, isLoading, error]);

  // This component doesn't render anything
  return null;
};

export default TitleUpdater;
