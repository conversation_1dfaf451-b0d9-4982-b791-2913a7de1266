import { Box, Typography, Divider, CircularProgress } from '@mui/material';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import WelcomeStep from './steps/WelcomeStep';
import PasswordStep from './steps/PasswordStep';
import LanguageStep from './steps/LanguageStep';
import FunctionStep from './steps/FunctionStep';
import JobRoleStep from './steps/JobRoleStep';
import ManagementRoleStep from './steps/ManagementRoleStep';
import TechnicalBackgroundStep from './steps/TechnicalBackgroundStep';
import AIExperienceStep from './steps/AIExperienceStep';
import IndustryStep from './steps/IndustryStep';
import TermsStep from './steps/TermsStep';
import ReviewStep from './steps/ReviewStep';
import MandatoryVideoStep from './steps/MandatoryVideoStep';

// Step type constants
const StepTypes = {
  welcome: 0,
  changePassword: 1,
  language: 2,
  businessFunction: 3,
  jobRole: 4,
  managementRole: 5,
  technicalBackground: 6,
  aiExperience: 7,
  industry: 8,
  terms: 9,
  review: 10,
  mandatoryVideo: 11,
};

// Total number of steps shown in the counter (excluding welcome and video)
// For SSO users, we skip the changePassword step, so total is 8
// For regular users, total is 9
const TOTAL_COUNTED_STEPS_REGULAR = 9;
const TOTAL_COUNTED_STEPS_SSO = 8;

const StepContent = ({
  activeStep,
  formData,
  setFormData,
  data,
  isLoading,
  showPassword,
  setShowPassword,
  showAllIndustries,
  setShowAllIndustries,
  validatePassword,
  userData,
  onVideoComplete,
}) => {
  const { t } = useTranslation();
  const getStepTitle = (step) => {
    if (step === StepTypes.welcome) {
      return t('onboarding.welcome.title', { username: userData?.name || 'User' });
    }

    const titles = {
      [StepTypes.changePassword]: t('onboarding.steps.changePassword'),
      [StepTypes.language]: t('onboarding.steps.language'),
      [StepTypes.businessFunction]: t('onboarding.steps.businessFunction'),
      [StepTypes.jobRole]: t('onboarding.steps.jobRole'),
      [StepTypes.managementRole]: t('onboarding.steps.managementRole'),
      [StepTypes.technicalBackground]: t('onboarding.steps.technicalBackground'),
      [StepTypes.aiExperience]: t('onboarding.steps.aiExperience'),
      [StepTypes.industry]: t('onboarding.steps.industry'),
      [StepTypes.terms]: t('onboarding.steps.termsAndConditions'),
      [StepTypes.review]: t('onboarding.steps.review.title'),
      [StepTypes.mandatoryVideo]: t('onboarding.mandatoryVideo.title'),
    };
    return titles[step] || '';
  };
  const renderStep = () => {
    if (isLoading[Object.keys(isLoading)[activeStep]]) {
      return (
        <Box className="onboarding-modal__loading-container">
          <CircularProgress />
        </Box>
      );
    }

    const steps = {
      [StepTypes.welcome]: <WelcomeStep userData={userData} />,
      [StepTypes.changePassword]: (
        <PasswordStep
          formData={formData}
          setFormData={setFormData}
          showPassword={showPassword}
          setShowPassword={setShowPassword}
          validatePassword={validatePassword}
        />
      ),
      [StepTypes.language]: (
        <LanguageStep formData={formData} setFormData={setFormData} languages={data.languages} />
      ),
      [StepTypes.businessFunction]: (
        <FunctionStep formData={formData} setFormData={setFormData} functions={data.functions} />
      ),
      [StepTypes.jobRole]: (
        <JobRoleStep
          formData={formData}
          setFormData={setFormData}
          jobRoles={data.jobRoles}
          functions={data.functions}
        />
      ),
      [StepTypes.managementRole]: (
        <ManagementRoleStep
          formData={formData}
          setFormData={setFormData}
          managementRoles={data.managementRoles}
        />
      ),
      [StepTypes.technicalBackground]: (
        <TechnicalBackgroundStep
          formData={formData}
          setFormData={setFormData}
          technicalBackgrounds={data.technicalBackgrounds}
        />
      ),
      [StepTypes.aiExperience]: (
        <AIExperienceStep
          formData={formData}
          setFormData={setFormData}
          aiExperience={data.aiExperience}
        />
      ),
      [StepTypes.industry]: (
        <IndustryStep
          formData={formData}
          setFormData={setFormData}
          industries={data.industry}
          showAllIndustries={showAllIndustries}
          setShowAllIndustries={setShowAllIndustries}
        />
      ),
      [StepTypes.terms]: <TermsStep formData={formData} setFormData={setFormData} />,
      [StepTypes.review]: <ReviewStep formData={formData} data={data} />,
      [StepTypes.mandatoryVideo]: <MandatoryVideoStep onVideoComplete={onVideoComplete} />,
    };

    return steps[activeStep] || null;
  };

  return (
    <Box>
      {/* Hide header and divider for video step */}
      {activeStep !== StepTypes.mandatoryVideo && (
        <>
          <Box
            className={`onboarding-modal__step-content-header ${activeStep === StepTypes.welcome ? 'onboarding-modal__step-content-header--centered' : ''}`}
          >
            <Typography
              variant="subtitle1"
              className="onboarding-modal__step-content-title"
              fontWeight={900}
              align={activeStep === StepTypes.welcome ? 'center' : 'left'}
              sx={{ width: activeStep === StepTypes.welcome ? '100%' : 'auto' }}
            >
              {getStepTitle(activeStep)}
            </Typography>
            {activeStep !== StepTypes.welcome && (
              <Typography
                variant="caption"
                className={`onboarding-modal__step-content-counter ${activeStep === StepTypes.review ? 'completed' : ''}`}
              >
                {activeStep === StepTypes.review
                  ? userData?.sso_login
                    ? TOTAL_COUNTED_STEPS_SSO
                    : TOTAL_COUNTED_STEPS_REGULAR
                  : userData?.sso_login
                    ? activeStep > StepTypes.changePassword
                      ? activeStep - 1
                      : activeStep
                    : activeStep}{' '}
                {t('onboarding.steps.of')}{' '}
                {userData?.sso_login ? TOTAL_COUNTED_STEPS_SSO : TOTAL_COUNTED_STEPS_REGULAR}
              </Typography>
            )}
          </Box>
          <Divider className="onboarding-modal__divider" />
        </>
      )}
      <Box
        className={`onboarding-modal__step-content-inner-container ${activeStep === StepTypes.mandatoryVideo ? 'onboarding-modal__video-step-container' : ''}`}
      >
        {renderStep()}
      </Box>
    </Box>
  );
};

StepContent.propTypes = {
  activeStep: PropTypes.number.isRequired,
  formData: PropTypes.object.isRequired,
  setFormData: PropTypes.func.isRequired,
  data: PropTypes.object.isRequired,
  isLoading: PropTypes.object.isRequired,
  showPassword: PropTypes.object,
  setShowPassword: PropTypes.func.isRequired,
  showAllIndustries: PropTypes.bool,
  setShowAllIndustries: PropTypes.func.isRequired,
  validatePassword: PropTypes.func.isRequired,
  userData: PropTypes.shape({
    _id: PropTypes.string,
    name: PropTypes.string,
    email: PropTypes.string,
    sso_login: PropTypes.bool,
  }),
  onVideoComplete: PropTypes.func,
};

export default StepContent;
