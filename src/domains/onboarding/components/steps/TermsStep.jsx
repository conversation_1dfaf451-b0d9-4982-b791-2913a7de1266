import { Box, FormControlLabel, Checkbox, Typography, Link } from '@mui/material';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

const TermsStep = ({ formData, setFormData }) => {
  const { t } = useTranslation();

  const handleTermsAccept = (event) => {
    setFormData({
      ...formData,
      termsAccepted: event.target.checked,
    });
  };

  return (
    <Box className="onboarding-modal__terms-container">
      <FormControlLabel
        control={
          <Checkbox
            checked={formData.termsAccepted || false}
            onChange={handleTermsAccept}
            color="primary"
          />
        }
        label={
          <Typography variant="body2">
            {t('onboarding.terms.accept')}{' '}
            <Link href="/privacy-policy" target="_blank" rel="noopener">
              {t('onboarding.terms.privacyPolicy')}
            </Link>{' '}
            {t('onboarding.terms.and')}{' '}
            <Link href="/terms-and-conditions" target="_blank" rel="noopener">
              {t('onboarding.terms.termsAndConditions')}
            </Link>
          </Typography>
        }
      />
    </Box>
  );
};

TermsStep.propTypes = {
  formData: PropTypes.object.isRequired,
  setFormData: PropTypes.func.isRequired,
};

export default TermsStep;
