import React from 'react';
import { Box, Typography, Paper, Link } from '@mui/material';
import { useTranslation } from 'react-i18next';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

const ReviewStep = ({ formData, data }) => {
  const { t } = useTranslation();

  const renderOptions = (options, selectedValue, type) => {
    if (!options) return null;

    const sortedOptions = [...options].sort((a, b) => {
      const isASelected = type === 'language' 
        ? selectedValue === a.lang
        : selectedValue?.slug === a.slug;
      
      const isBSelected = type === 'language' 
        ? selectedValue === b.lang
        : selectedValue?.slug === b.slug;
      
      if (isASelected) return -1;
      if (isBSelected) return 1;
      return 0;
    });

    return (
      <Box className="onboarding-modal__list-buttons-container">
        {sortedOptions.map((option) => {
          const isSelected = type === 'language' 
            ? selectedValue === option.lang
            : selectedValue?.slug === option.slug;

          return (
            <Paper
              key={option._id}
              variant="outlined"
              className={`onboarding-modal__${type}-button ${
                isSelected 
                  ? `onboarding-modal__${type}-button--contained`
                  : `onboarding-modal__${type}-button--outlined`
              }`}
            >
              <Typography variant="body2" className={`onboarding-modal__${type}-text`}>
                {type === 'language' 
                  ? option.name 
                  : option.translations?.[formData.language] || option.translations?.en}
              </Typography>
            </Paper>
          );
        })}
      </Box>
    );
  };

  const renderSection = (title, options, selectedValue, type, stepNumber) => {
    if (!options) return null;

    const isNoGradient = ['language', 'management-role', 'technical-background'].includes(type);

    return (
      <Box className={`onboarding-modal__review-section ${isNoGradient ? 'no-gradient' : ''}`}>
        <Box className="onboarding-modal__review-section-header">
          <Typography 
            variant="subtitle1" 
            className="onboarding-modal__step-content-title"
          >
            {title}
          </Typography>
          <Typography variant="caption" className="onboarding-modal__step-content-counter">
            {stepNumber} of 9
          </Typography>
        </Box>
        {renderOptions(options, selectedValue, type)}
      </Box>
    );
  };

  return (
    <Box className="onboarding-modal__review-container">
      <Box className="onboarding-modal__review-sections">
        <Typography 
          variant="body1" 
          className="onboarding-modal__review-title"
        >
          {t('onboarding.steps.review.description')}
        </Typography>
        {renderSection(
          t('onboarding.steps.language'),
          data?.languages,
          formData.language,
          'language',
          '2'
        )}
        
        {renderSection(
          t('onboarding.steps.businessFunction'),
          data?.functions,
          formData.function_label,
          'functions',
          '3'
        )}
        
        {renderSection(
          t('onboarding.steps.jobRole'),
          data?.jobRoles,
          formData.job_role_label,
          'job-role',
          '4'
        )}
        
        {formData.management_role_label &&
          renderSection(
            t('onboarding.steps.managementRole'),
            data?.managementRoles,
            formData.management_role_label,
            'management-role',
            '5'
          )}
        
        {renderSection(
          t('onboarding.steps.technicalBackground'),
          data?.technicalBackgrounds,
          formData.technical_background_label,
          'technical-background',
          '6'
        )}
        
        {renderSection(
          t('onboarding.steps.aiExperience'),
          data?.aiExperience,
          formData.ai_knowledge_label,
          'ai-experience',
          '7'
        )}
        
        {renderSection(
          t('onboarding.steps.industry'),
          data?.industry,
          formData.industry_label,
          'industry',
          '8'
        )}

        <Box className="onboarding-modal__review-section no-gradient">
          <Box className="onboarding-modal__review-section-header">
            <Typography 
              variant="subtitle1" 
              className="onboarding-modal__step-content-title"
            >
              {t('onboarding.steps.termsAndConditions')}
            </Typography>
            <Typography variant="caption" className="onboarding-modal__step-content-counter">
              9 of 9
            </Typography>
          </Box>
          <Box className="onboarding-modal__terms-container" sx={{ px: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CheckCircleIcon color="success" />
              <Typography variant="body2">
                I accept the{' '}
                <Link href="/privacy-policy" target="_blank" rel="noopener">
                  Privacy Policy
                </Link>{' '}
                and{' '}
                <Link href="/terms-and-conditions" target="_blank" rel="noopener">
                  Terms and Conditions
                </Link>
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ReviewStep; 