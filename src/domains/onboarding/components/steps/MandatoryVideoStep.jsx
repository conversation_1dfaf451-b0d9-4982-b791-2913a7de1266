import { useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import EnhancedVideoPlayer from '../../../../components/LMS/EnhancedVideoPlayer';

const MandatoryVideoStep = ({ onVideoComplete }) => {
  const { t } = useTranslation();

  // Get the video URL from translations
  const videoUrl = t('onboarding.mandatoryVideo.videoUrl');

  // Force video to play automatically
  useEffect(() => {
    // Force video to play automatically
    setTimeout(() => {
      const videoElement = document.querySelector(
        '.onboarding-modal__mandatory-video-container video'
      );
      if (videoElement) {
        const playPromise = videoElement.play();
        if (playPromise !== undefined) {
          playPromise.catch(() => {
            // Auto-play was prevented, try again with user interaction simulation
            document.addEventListener(
              'click',
              () => {
                videoElement.play();
              },
              { once: true }
            );
          });
        }
      }
    }, 500);
  }, []);

  // Handle video completion
  const handleVideoComplete = () => {
    if (onVideoComplete) {
      onVideoComplete(true);
    }
  };

  return (
    <Box className="onboarding-modal__mandatory-video-container">
      <Box className="onboarding-modal__mandatory-video-video-wrapper">
        <EnhancedVideoPlayer
          videoUrl={videoUrl}
          onComplete={handleVideoComplete}
          preventAutoPlayAfterComplete={true}
          autoPlay={true}
          muted={false}
        />
      </Box>
      <Typography variant="caption" className="onboarding-modal__mandatory-video-footer">
        {t('onboarding.mandatoryVideo.footerNote')}
      </Typography>
    </Box>
  );
};

MandatoryVideoStep.propTypes = {
  onVideoComplete: PropTypes.func,
  userData: PropTypes.object,
};

export default MandatoryVideoStep;
