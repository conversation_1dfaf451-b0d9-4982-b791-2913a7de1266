import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

const WelcomeStep = ({ userData }) => {
  const { t } = useTranslation();

  // SSO kullanıcıları için adım sayısı 8, normal kullanıcılar için 9
  const stepCount = userData?.sso_login ? 8 : 9;

  return (
    <Box className="onboarding-modal__welcome-container">
      <Typography
        variant="body1"
        className="onboarding-modal__welcome-description"
        dangerouslySetInnerHTML={{
          __html: t('onboarding.welcome.description', {
            stepCount,
            interpolation: { escapeValue: false },
          }),
        }}
      />
    </Box>
  );
};

WelcomeStep.propTypes = {
  userData: PropTypes.shape({
    firstName: PropTypes.string,
    name: PropTypes.string,
    sso_login: PropTypes.bool,
  }),
};

export default WelcomeStep;
