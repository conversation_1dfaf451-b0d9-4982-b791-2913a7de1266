import React from 'react';
import { Box, Button, Typography } from '@mui/material';

const ManagementRoleStep = ({ formData, setFormData, managementRoles }) => {
  const handleManagementRoleSelect = (role) => {
    setFormData({
      ...formData,
      management_role_label: {
        slug: role.slug,
        translations: role.translations,
        _id: role._id,
        order: role.order,
        description: role.description,
      },
    });
  };

  return (
    <Box className="onboarding-modal__list-buttons-container">
      {managementRoles?.map((role) => (
        <Button
          key={role._id}
          variant={formData.management_role_label?.slug === role.slug ? 'contained' : 'outlined'}
          onClick={() => handleManagementRoleSelect(role)}
          className={`onboarding-modal__management-role-button ${
            formData.management_role_label?.slug === role.slug
              ? 'onboarding-modal__management-role-button--contained'
              : 'onboarding-modal__management-role-button--outlined'
          }`}
        >
          <Typography variant="body2" className="onboarding-modal__management-role-text">
            {role.translations[formData.language] || role.translations.en}
          </Typography>
        </Button>
      ))}
    </Box>
  );
};

export default ManagementRoleStep;
