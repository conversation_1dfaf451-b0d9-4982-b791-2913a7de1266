import React from 'react';
import { Box, Button, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

const LanguageStep = ({ formData, setFormData, languages }) => {
  const { i18n } = useTranslation();

  const handleLanguageChange = (lang) => {
    setFormData({ ...formData, language: lang });
    i18n.changeLanguage(lang);
    // Dil tercihini localStorage'a kaydet
    localStorage.setItem('userLanguage', lang);
  };

  return (
    <Box className="onboarding-modal__list-buttons-container">
      {languages?.map((lang) => (
        <Button
          key={lang._id}
          variant={formData.language === lang.lang ? 'contained' : 'outlined'}
          onClick={() => handleLanguageChange(lang.lang)}
          className={`onboarding-modal__language-button ${
            formData.language === lang.lang
              ? 'onboarding-modal__language-button--contained'
              : 'onboarding-modal__language-button--outlined'
          }`}
        >
          <Typography variant="body1" className="onboarding-modal__language-button-text">
            {lang.name}
          </Typography>
        </Button>
      ))}
    </Box>
  );
};

export default LanguageStep;
