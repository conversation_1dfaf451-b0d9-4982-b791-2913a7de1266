import React from 'react';
import {
  Box,
  TextField,
  Typography,
  InputAdornment,
  IconButton,
  LinearProgress,
} from '@mui/material';
import { Check, Close, Visibility, VisibilityOff } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

const PasswordStep = ({
  formData,
  setFormData,
  validatePassword,
  showPassword,
  setShowPassword,
}) => {
  const { t } = useTranslation();
  const validations = validatePassword(formData.password || '');

  // Hesapla kaç kriter sağlanmış
  const validCount = Object.values(validations).filter(Boolean).length;
  const totalCount = Object.keys(validations).length;
  const progress = (validCount / totalCount) * 100;

  const getValidationText = (key) => {
    const validationKeys = {
      minLength: 'onboarding.password.validation.minLength',
      hasUpperCase: 'onboarding.password.validation.hasUpperCase',
      hasLowerCase: 'onboarding.password.validation.hasLowerCase',
      hasSpecialChar: 'onboarding.password.validation.hasSpecialChar',
      passwordsMatch: 'onboarding.password.validation.passwordsMatch',
    };
    return t(validationKeys[key]);
  };

  return (
    <>
      <Box className="onboarding-modal__change-password-container">
        <TextField
          className="onboarding-modal__change-password-field"
          label={t('onboarding.password.newPassword')}
          type={showPassword.password ? 'text' : 'password'}
          variant="outlined"
          fullWidth
          value={formData.password || ''}
          onChange={(e) => setFormData({ ...formData, password: e.target.value })}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={() =>
                    setShowPassword({ ...showPassword, password: !showPassword.password })
                  }
                  edge="end"
                >
                  {showPassword.password ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
        <TextField
          className="onboarding-modal__change-password-field"
          label={t('onboarding.password.confirmPassword')}
          type={showPassword.confirmPassword ? 'text' : 'password'}
          variant="outlined"
          fullWidth
          value={formData.confirmPassword || ''}
          onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={() =>
                    setShowPassword({
                      ...showPassword,
                      confirmPassword: !showPassword.confirmPassword,
                    })
                  }
                  edge="end"
                >
                  {showPassword.confirmPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
      </Box>
      <Box className="onboarding-modal__change-password-requirements">
        <Typography className="onboarding-modal__change-password-requirements-title">
          {t('onboarding.password.requirements')}:
        </Typography>
        <LinearProgress
          variant="determinate"
          value={progress}
          className="onboarding-modal__change-password-progress"
        />
        <ul className="onboarding-modal__change-password-requirements-list">
          {Object.entries(validations).map(([key, value]) => (
            <li key={key} className={value ? 'valid' : ''}>
              {value ? <Check /> : <Close />}
              {getValidationText(key)}
            </li>
          ))}
        </ul>
      </Box>
    </>
  );
};

export default PasswordStep;
