import PropTypes from 'prop-types';
import { Box, Button, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

const IndustryStep = ({
  formData,
  setFormData,
  industries,
  showAllIndustries,
  setShowAllIndustries,
}) => {
  const { t } = useTranslation();

  const handleIndustrySelect = (industry) => {
    setFormData({
      ...formData,
      industry_label: {
        slug: industry.slug,
        translations: industry.translations,
        _id: industry._id,
        order: industry.order,
        description: industry.description,
      },
    });
  };

  const displayedIndustries = showAllIndustries ? industries : industries?.slice(0, 10);

  return (
    <Box className="onboarding-modal__list-buttons-container">
      {displayedIndustries?.map((industry) => (
        <Button
          key={industry._id}
          variant={formData.industry_label?.slug === industry.slug ? 'contained' : 'outlined'}
          onClick={() => handleIndustrySelect(industry)}
          className={`onboarding-modal__industry-button ${
            formData.industry_label?.slug === industry.slug
              ? 'onboarding-modal__industry-button--contained'
              : 'onboarding-modal__industry-button--outlined'
          }`}
        >
          <Typography variant="body2" className="onboarding-modal__industry-text">
            {industry.translations[formData.language] || industry.translations.en}
          </Typography>
        </Button>
      ))}

      {industries?.length > 10 && (
        <Button
          fullWidth
          variant="text"
          onClick={() => setShowAllIndustries(!showAllIndustries)}
          className="onboarding-modal__show-more-button"
        >
          {showAllIndustries ? t('onboarding.industry.hide') : t('onboarding.industry.showAll')}
        </Button>
      )}
    </Box>
  );
};

IndustryStep.propTypes = {
  formData: PropTypes.shape({
    language: PropTypes.string,
    industry_label: PropTypes.oneOfType([
      PropTypes.shape({
        slug: PropTypes.string,
      }),
      PropTypes.oneOf([null]),
    ]),
  }),
  setFormData: PropTypes.func.isRequired,
  industries: PropTypes.array,
  showAllIndustries: PropTypes.bool.isRequired,
  setShowAllIndustries: PropTypes.func.isRequired,
};

export default IndustryStep;
