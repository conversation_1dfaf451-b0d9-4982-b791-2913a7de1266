import React, { useState, useMemo, useRef, useEffect } from 'react';
import { Box, Button, Typography, TextField, Alert } from '@mui/material';
import { useTranslation } from 'react-i18next';

// Tekrar eden olası fonksiyon alanlarını sabit değişkene taşıyorum
const POSSIBLE_FUNCTION_FIELDS = [
  'functionLabel', 'function', 'functionId', 'functionSlug', 'function_label', 
  'function_id', 'functionLabelId', 'functionLabelSlug', 'parent_function'
];

const JobRoleStep = ({ formData, setFormData, jobRoles, functions }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isOtherFunction, setIsOtherFunction] = useState(formData.function_label?.slug === 'other');
  const selectedFunctionRef = useRef(formData.function_label);
  const { t } = useTranslation();

  // Yardımcı fonksiyonlar

  // Bir objeden fonksiyon etiket değerini çıkaran yardımcı fonksiyon
  const extractFunctionLabelValue = (obj) => {
    // Direkt functionLabel değeri varsa kullan
    if (obj.functionLabel) return obj.functionLabel;
    
    // Olası diğer alanları kontrol et
    for (const field of POSSIBLE_FUNCTION_FIELDS) {
      if (obj[field]) return obj[field];
    }
    
    // ID'den çıkarma girişimi
    if (obj._id) {
      const idParts = obj._id.split('_');
      if (idParts.length > 1) return idParts[0];
    }
    
    return null;
  };

  // Fonksiyon etiketi eşleşmesini bulan yardımcı fonksiyon
  const findMatchingFunction = (functionSlug) => {
    if (!functions || !functions.length || !functionSlug) return null;
    
    // Tam eşleşme ara
    let matchingFunction = functions.find(
      func => String(func.slug).trim() === String(functionSlug).trim()
    );
    
    // Tam eşleşme yoksa kısmi eşleşme dene
    if (!matchingFunction) {
      matchingFunction = functions.find(
        func => String(func.slug).includes(String(functionSlug).trim()) ||
               String(functionSlug).trim().includes(String(func.slug))
      );
    }
    
    return matchingFunction;
  };

  // formData.function_label değiştiğinde isOtherFunction durumunu güncelle
  useEffect(() => {
    if (!formData.job_role_label) { // Sadece job_role_label yoksa güncelleme yap
      setIsOtherFunction(formData.function_label?.slug === 'other');
    }
  }, [formData.function_label]);

  // job_role_label değiştiğinde gerekli güncellemeyi yap
  useEffect(() => {
    if (!formData.job_role_label) return;

    // İlgili fonksiyon etiketini çıkar
    const functionLabelValue = extractFunctionLabelValue(formData.job_role_label);
    
    if (!functionLabelValue) return;
    
    // Eşleşen fonksiyonu bul
    const matchingFunction = findMatchingFunction(functionLabelValue);
    
    if (matchingFunction) {
      // State güncelleme
      const updatedFormData = {
        ...formData,
        function_label: {
          slug: matchingFunction.slug,
          translations: matchingFunction.translations,
          _id: matchingFunction._id,
          order: matchingFunction.order,
          description: matchingFunction.description
        }
      };
      
      // Referansı güncelle
      selectedFunctionRef.current = updatedFormData.function_label;
      
      // formData'yı güncelle
      setFormData(updatedFormData);
    }
  }, [formData.job_role_label, functions]);

  // Component unmount olduğunda çalışacak
  useEffect(() => {
    return () => {
      if (selectedFunctionRef.current && selectedFunctionRef.current.slug === 'other') {
        setFormData(prev => ({
          ...prev,
          function_label: {
            slug: selectedFunctionRef.current.slug,
            translations: selectedFunctionRef.current.translations,
            _id: selectedFunctionRef.current._id,
            order: selectedFunctionRef.current.order,
            description: selectedFunctionRef.current.description
          }
        }));
      }
    };
  }, [setFormData]);

  const handleJobRoleSelect = (role) => { 
    // Rol için fonksiyon etiket değerini belirle
    const functionLabelValue = extractFunctionLabelValue(role);
    
    // Yeni job_role_label nesnesi oluştur
    const jobRoleLabel = {
      slug: role.slug,
      translations: role.translations,
      _id: role._id,
      order: role.order,
      description: role.description,
      functionLabel: functionLabelValue
    };
    
    // Eşleşen fonksiyonu bul
    const matchingFunction = functionLabelValue ? findMatchingFunction(functionLabelValue) : null;
    
    if (matchingFunction) {
      // formData'yı doğrudan güncelle
      setFormData(prevState => ({
        ...prevState,
        job_role_label: jobRoleLabel,
        function_label: {
          slug: matchingFunction.slug,
          translations: matchingFunction.translations,
          _id: matchingFunction._id,
          order: matchingFunction.order,
          description: matchingFunction.description
        }
      }));
      
      // Referansı güncelle
      selectedFunctionRef.current = matchingFunction;
    } else {
      // Eşleşen fonksiyon bulunamadıysa sadece job_role_label'ı güncelle
      setFormData(prevState => ({
        ...prevState,
        job_role_label: jobRoleLabel
      }));
    }
  };

  const filterJobRolesByFunction = (roles, functionSlug) => {
    if (!roles || !functionSlug || functionSlug === 'other') return roles || [];
    
    return roles.filter(role => {
      const roleFunctionValue = extractFunctionLabelValue(role);
      return roleFunctionValue && String(roleFunctionValue).trim() === String(functionSlug).trim();
    });
  };

  const displayedJobRoles = useMemo(() => {
    // Eğer other function seçiliyse ve arama yapılıyorsa
    if (isOtherFunction) {
      if (!searchQuery || searchQuery.length < 3) {
        return [];
      }
      
      // Arama sorgusuna göre filtreleme yap
      return jobRoles?.filter((role) => {
        const roleText = role.translations[formData.language] || role.translations.en;
        return roleText.toLowerCase().includes(searchQuery.toLowerCase());
      });
    }
    
    // Eğer other function seçili değilse, seçili fonksiyona ait rolleri göster
    return filterJobRolesByFunction(jobRoles, formData.function_label?.slug);
  }, [jobRoles, searchQuery, formData.language, formData.function_label, isOtherFunction]);

  return (
    <Box className="onboarding-modal__list-buttons-container">
      {isOtherFunction && (
        <TextField
          fullWidth
          variant="outlined"
          placeholder={t('onboarding.jobRole.searchPlaceholder')}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      )}

      {isOtherFunction && searchQuery.length >= 3 && displayedJobRoles?.length === 0 && (
        <Alert severity="info" sx={{ width: '100%', mt: 0, mb: 0 }}>
          {t('onboarding.jobRole.noResults')}
        </Alert>
      )}

      {displayedJobRoles?.map((role) => (
        <Button
          key={role._id}
          variant={formData.job_role_label?.slug === role.slug ? 'contained' : 'outlined'}
          onClick={() => handleJobRoleSelect(role)}
          className={`onboarding-modal__job-role-button ${
            formData.job_role_label?.slug === role.slug
              ? 'onboarding-modal__job-role-button--contained'
              : 'onboarding-modal__job-role-button--outlined'
          }`}
        >
          <Typography variant="body2" className="onboarding-modal__job-role-text">
            {role.translations[formData.language] || role.translations.en}
          </Typography>
        </Button>
      ))}
    </Box>
  );
};

export default JobRoleStep;
