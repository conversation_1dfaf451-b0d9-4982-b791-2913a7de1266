import React from 'react';
import { Box, Button, Typography } from '@mui/material';

const FunctionStep = ({ formData, setFormData, functions }) => {
  const handleFunctionSelect = (func) => {
    // Tüm seçenekler için aynı nesne formatını kullan
    setFormData({
      ...formData,
      function_label: {
        slug: func.slug,
        translations: func.translations,
        _id: func._id,
        order: func.order,
        description: func.description,
      },
      // İş fonksiyonu değiştiğinde bağımlı alanları sıfırla
      job_role_label: '',
      management_role_label: undefined,
      technical_background_label: '',
    });
  };

  return (
    <Box className="onboarding-modal__list-buttons-container">
      {functions?.map((func) => (
        <Button
          key={func._id}
          variant={formData.function_label?.slug === func.slug ? 'contained' : 'outlined'}
          onClick={() => handleFunctionSelect(func)}
          className={`onboarding-modal__functions-button ${
            formData.function_label?.slug === func.slug
              ? 'onboarding-modal__functions-button--contained'
              : 'onboarding-modal__functions-button--outlined'
          }`}
        >
          <Typography variant="body2" className="onboarding-modal__functions-text">
            {func.translations[formData.language] || func.translations.en}
          </Typography>
        </Button>
      ))}
    </Box>
  );
};

export default FunctionStep;
