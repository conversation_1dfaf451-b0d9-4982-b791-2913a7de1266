import React from 'react';
import { Box, Button, Typography } from '@mui/material';

const TechnicalBackgroundStep = ({ formData, setFormData, technicalBackgrounds }) => {
  const handleTechnicalBackgroundSelect = (background) => {
    setFormData({
      ...formData,
      technical_background_label: {
        slug: background.slug,
        translations: background.translations,
        _id: background._id,
        order: background.order,
        description: background.description,
      },
    });
  };

  return (
    <Box className="onboarding-modal__list-buttons-container">
      {technicalBackgrounds?.map((background) => (
        <Button
          key={background._id}
          variant={
            formData.technical_background_label?.slug === background.slug ? 'contained' : 'outlined'
          }
          onClick={() => handleTechnicalBackgroundSelect(background)}
          className={`onboarding-modal__technical-background-button ${
            formData.technical_background_label?.slug === background.slug
              ? 'onboarding-modal__technical-background-button--contained'
              : 'onboarding-modal__technical-background-button--outlined'
          }`}
        >
          <Typography variant="body2" className="onboarding-modal__technical-background-text">
            {background.translations[formData.language] || background.translations.en}
          </Typography>
        </Button>
      ))}
    </Box>
  );
};

export default TechnicalBackgroundStep;
