import React from 'react';
import { Box, Button, Typography } from '@mui/material';

const AIExperienceStep = ({ formData, setFormData, aiExperience }) => {
  const handleAIExperienceSelect = (experience) => {
    setFormData({
      ...formData,
      ai_knowledge_label: {
        slug: experience.slug,
        translations: experience.translations,
        _id: experience._id,
        order: experience.order,
        description: experience.description,
      },
    });
  };

  return (
    <Box className="onboarding-modal__list-buttons-container column">
      {aiExperience?.map((experience) => (
        <Button
          key={experience._id}
          variant={formData.ai_knowledge_label?.slug === experience.slug ? 'contained' : 'outlined'}
          onClick={() => handleAIExperienceSelect(experience)}
          className={`onboarding-modal__ai-experience-button ${
            formData.ai_knowledge_label?.slug === experience.slug
              ? 'onboarding-modal__ai-experience-button--contained'
              : 'onboarding-modal__ai-experience-button--outlined'
          }`}
        >
          <Typography variant="body2" className="onboarding-modal__ai-experience-text">
            {experience.translations[formData.language] || experience.translations.en}
          </Typography>
        </Button>
      ))}
    </Box>
  );
};

export default AIExperienceStep;
