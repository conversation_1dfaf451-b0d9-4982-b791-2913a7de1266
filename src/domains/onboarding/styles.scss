@use '../../styles/abstracts/variables' as *;

.onboarding-modal {
  &__backdrop {
    background-color: rgba($bg-paper, 0.5);
    backdrop-filter: blur(8px);
  }

  &__container {
    position: absolute;
    outline:none !important;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 90vw;
    width: 600px;
    max-height: 90vh;
    background-color: $bg-paper;
    border-radius: $border-radius-lg;
    box-shadow: $shadow-lg;
    overflow-y: auto;

    // Special styling for video step
    &.video-step-active {
      width: 80vw;
      max-width: 800px;
      height: auto;
      max-height: 80vh;
      padding: 0;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      @media (max-width: 768px) {
        width: 95vw;
        height: auto;
      }

      @media (max-height: 768px) {
        max-width:500px;
      }
    }
  }

  &__divider {
    margin: $spacing-3 0 !important;
    border-color: $border-color !important;
  }

  &__loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;

    .MuiCircularProgress-root {
      color: $primary-color;
    }
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    margin-top: $spacing-3;
    border-top: 1px solid $border-color;
    padding:$spacing-3;
    button{
      padding-top: $spacing-2 + 2px !important;
      padding-bottom: $spacing-2 + 2px !important;
    }
  }

  &__step-content {
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: $spacing-3 $spacing-3 0 $spacing-3;

      &--centered {
        justify-content: center;
        text-align: center;

        .onboarding-modal__step-content-title {
          text-align: center;
          font-size: $font-size-lg !important;
        }
      }
    }

    &-title {
      font-size: $font-size-md !important;
      font-weight: $font-weight-bold !important;
      flex: 1;
    }

    &-counter {
      color: $primary-color;
      font-weight: $font-weight-bold;
      background-color: rgba($primary-color, 0.1);
      border-radius: $border-radius-sm;
      padding: $spacing-2;

      &.completed{
        background-color: rgba($success-color, 0.1);
        color: $success-color;
      }
    }

    &-inner-container {
      padding: 0 $spacing-3 0 $spacing-3;
    }
  }
  &__list-buttons-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    gap: $spacing-3;
    width: 100%;
    @media (max-height: 768px) {
      gap: $spacing-1;
    }
    &.column{
      flex-direction: column;
    }
    button{
      width: max-content !important;
      border-radius: $spacing-4 !important;
      min-height: 44px !important;
      text-transform: none !important;
      padding: $spacing-2 $spacing-3 !important;
      &.onboarding-modal__show-more-button{
        width: 100% !important;
        background-color: transparent !important;
        color: $primary-color !important;
        font-weight: $font-weight-medium !important;
        font-size: $font-size-sm !important;
        text-transform: none !important;
        padding: $spacing-2 $spacing-3 !important;
        border-radius: $border-radius-md !important;

      }
      p{
        font-size: calc($font-size-md - 2px) !important;
        &.onboarding-modal__functions-text{
          min-height: auto !important;
        }
      }
      &.MuiButton-contained{
        background-color: $primary-color !important;
        color: $bg-paper !important;
        box-shadow: none !important;
        border:1px solid $primary-color !important;
      }
      &.MuiButton-outlined{
        background-color: transparent !important;
        color: $text-secondary !important;
        box-shadow: none !important;
        border:1px solid $border-color !important;
      }
    }
  }
  &__language-button {
    display: flex;
    flex-direction: column;
    width: 100%;
    justify-content: center;
    align-items: center;

    &--outlined {
      background-color: $bg-paper;

      &:hover {
        background-color: rgba($primary-color, 0.04);
      }
    }

    &--contained {
      background-color: $primary-color;
      color: $bg-paper;
    }
  }

  &__language-button-text {
    font-weight: $font-weight-bold;
    text-transform: capitalize;
  }

  &__functions {
    &-button {
      min-height: 44px !important;
      padding: $spacing-2 $spacing-3 !important;
      text-transform: none !important;
      width: 100%;
      border-radius: $border-radius-md !important;

      &--outlined {
        border: 1px solid $border-color !important;
        background-color: transparent !important;

        &:hover {
          border-color: $primary-color !important;
          background-color: rgba($primary-color, 0.04) !important;
        }
      }

      &--contained {
        background-color: $primary-color !important;
        color: $bg-paper !important;
        border: none !important;

        &:hover {
          background-color: $primary-color-dark !important;
        }
      }
    }

    &-text {
      font-size: $font-size-sm;
      line-height: 1.3;
      font-weight: $font-weight-medium;
      text-align: center;
      width: 100%;
      white-space: normal;
      color: inherit;

      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      height: auto;
      min-height: 34px;
    }
  }
  &__terms {
    &-container {
    p {
      color: $text-secondary !important;
      font-size: $font-size-sm !important;
      line-height: 1.5 !important;

      a {
        color: $primary-color !important;
        text-decoration: none;
        font-weight: $font-weight-medium !important;

        &:hover {
          text-decoration: underline;
        }

        &:focus {
          outline: none;
          text-decoration: underline;
        }
      }
    }
    }
  }
  &__change-password {
    &-container {
      display: flex;
      flex-direction: column;
      gap: $spacing-3;
      background-color: $bg-paper;
      border-radius: $border-radius-md;
      width: 100%;
    }

    &-field {
      width: 100%;

      .MuiOutlinedInput-root {
        background-color: $bg-light;
        border-radius: $border-radius-md;
        height: 52px !important;

        &:hover {
          .MuiOutlinedInput-notchedOutline {
            border-color: $primary-color;
          }
        }

        &.Mui-focused {
          .MuiOutlinedInput-notchedOutline {
            border-color: $primary-color;
            border-width: 1px;
          }
        }

        &.Mui-error {
          .MuiOutlinedInput-notchedOutline {
            border-color: $error-color;
          }
        }
      }

      .MuiOutlinedInput-notchedOutline {
        border-color: $border-color;
      }

      .MuiInputLabel-root {
        color: $text-secondary;

        &.Mui-focused {
          color: $primary-color;
        }

        &.Mui-error {
          color: $error-color;
        }
      }

      input {
        padding: $spacing-2 $spacing-3;
        font-size: $font-size-sm !important;

        &::placeholder {
          color: $text-disabled;
        }
      }

      .MuiInputAdornment-root {
        .MuiIconButton-root {
          color: $text-secondary;
          padding: $spacing-1;

          &:hover {
            background-color: rgba($primary-color, 0.04);
          }
        }
      }
    }

    &-requirements {
      margin-top: $spacing-3;
      padding: $spacing-3;
      background-color: $bg-paper;
      border-radius: $border-radius-md;
      border: 1px solid $border-color;

      &-title {
        font-size: $font-size-sm !important;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-2 !important;
      }

      .onboarding-modal__change-password-progress {
        margin: $spacing-2 0;
        height: 6px;
        border-radius: $border-radius-sm;
        background-color: rgba($primary-color, 0.12);

        .MuiLinearProgress-bar {
          background-color: $success-color;
          border-radius: $border-radius-sm;
          transition: transform 0.4s ease;
        }

        &.MuiLinearProgress-root {
          background-color: rgba($primary-color, 0.12);
        }
      }

      &-list {
        list-style: none;
        padding: 0;
        margin: $spacing-2 0 0;

        li {
          display: flex;
          align-items: center;
          gap: $spacing-2;
          font-size: $font-size-xs !important;
          color: $error-color;
          margin-bottom: $spacing-1;

          &.valid {
            color: $success-color;
          }

          svg {
            font-size: $font-size-lg;

            &.MuiSvgIcon-root {
              color: inherit;
            }
          }
        }
      }
    }
  }

  &__job-roles {
    &-button {
      min-height: 44px !important;
      padding: 8px 12px !important;
      text-transform: none !important;
      width: 100%;
      border-radius: 8px !important;

      &--outlined {
        border: 1px solid #E0E0E0 !important;
        background-color: transparent !important;

        &:hover {
          border-color: #5A4DFF !important;
          background-color: rgba(90, 77, 255, 0.04) !important;
        }
      }

      &--contained {
        background-color: #5A4DFF !important;
        color: white !important;
        border: none !important;

        &:hover {
          background-color: #4a3ee6 !important;
        }
      }
    }

    &-text {
      font-size: 14px;
      font-weight: 500;
      line-height: 1.3;
      text-align: center;
      width: 100%;
    }
  }

  &__management-roles,
  &__technical-backgrounds,
  &__ai-experience,
  &__industry {
    &-button {
      min-height: 44px !important;
      padding: 8px 12px !important;
      text-transform: none !important;
      width: 100%;
      border-radius: 8px !important;

      &--outlined {
        border: 1px solid #E0E0E0 !important;
        background-color: transparent !important;

        &:hover {
          border-color: #5A4DFF !important;
          background-color: rgba(90, 77, 255, 0.04) !important;
        }
      }

      &--contained {
        background-color: #5A4DFF !important;
        color: white !important;
        border: none !important;

        &:hover {
          background-color: #4a3ee6 !important;
        }
      }
    }

    &-text {
      font-size: 14px;
      font-weight: 500;
      line-height: 1.3;
      text-align: center;
      width: 100%;
    }
  }

  &__welcome {
    &-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: $spacing-3 $spacing-3;
      background-color: $bg-paper;
      border-radius: $border-radius-md;
      width: calc(100% - $spacing-3 * 2);
    }

    &-description {
      color: $text-secondary !important;
      font-size: $font-size-md !important;
      line-height: 1.6 !important;
      margin: 0 auto;
    }
  }

  &__mandatory-video {
    &-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: 0;
      margin: 0;
      height: 100%;

      > div:first-child {
        flex: 1;
      }

      // Hide video controls
      .video-controls {
        display: none !important;
      }

      // Hide play icon
      .center-play-icon {
        display: none !important;
      }

      // Disable all interaction with video
      .video-wrapper,
      .main-video {
        pointer-events: none !important;
      }
    }

    &-video-wrapper {
      width: 100%;
      height: calc(100% - 60px);
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;

      @media (max-width: 768px) {
        height: calc(100% - 80px);
      }
    }

    &-footer {
      font-size: $font-size-xs !important;
      line-height: 1.4 !important;
      color: $text-secondary !important;
      margin: 16px 0 16px 0 !important;
      padding: 0 16px !important;
      text-align: center !important;
      display: block !important;
      width: calc(100% - 16px * 2) !important;
    }
  }

  &__video-step-container {
    padding: 0 !important;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &__review-container {
    max-width: 600px;
    margin: 0 auto;

    .onboarding-modal__review {
      &-title {
        color: $text-secondary !important;
        font-size: $font-size-sm !important;
        line-height: 1.5 !important;
        margin-bottom: $spacing-2 !important;
      }

      &-sections {
        display: flex;
        flex-direction: column;
        gap: $spacing-2;
      }

      &-section {
        margin-bottom: $spacing-3;
        background-color: $bg-paper !important;
        border: 1px solid $border-color !important;
        border-radius: $border-radius-md !important;
        padding-bottom: $spacing-3;
        max-height: 200px !important;
        overflow: hidden;
        position: relative;
        &.no-gradient{
          &:after{
            display: none;
          }
        }
        &:after{
          position: absolute;
          bottom: 0;
          left: 0;
          content: '';
          display: block;
          height: 100px;
          width: 100%;
          background: linear-gradient(to bottom, rgba(255, 255, 255, 0), $bg-paper) !important;
        }

        &-header {
          display: flex;
          align-items: center;
          margin-bottom: $spacing-3;
          border-bottom: 1px solid $border-color !important;
          padding: $spacing-3 $spacing-3;

          .onboarding-modal__step-content-title {
            font-size: $font-size-md !important;
            color: $text-primary;
            font-weight: $font-weight-bold !important;
          }

          .onboarding-modal__step-content-counter {
            margin-left: $spacing-2;
            color: $primary-color;
            font-weight: $font-weight-bold;
            background-color: rgba($primary-color, 0.1);
            border-radius: $border-radius-sm;
            padding: $spacing-1 $spacing-2;
          }
        }
      }
    }

    .onboarding-modal__list-buttons-container {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: flex-start;
      gap: $spacing-2;
      width: 100%;
      padding: 0 $spacing-3;

      @media (max-height: 768px) {
        gap: $spacing-1;
      }

      .MuiPaper-root {
        @extend button;
        display: flex;
        align-items: center;
        justify-content: center;
        width: max-content !important;
        min-height: 24px !important;
        line-height: 24px !important;
        cursor: default;
        opacity: 0.6;

        &[class*="--contained"] {
          @extend .MuiButton-contained;
          opacity: 1;
          box-shadow: 0 2px 4px rgba($primary-color, 0.2) !important;
        }

        &[class*="--outlined"] {
          @extend .MuiButton-outlined;
        }

        p {
          font-size: calc($font-size-md - 2px) !important;
          font-weight: $font-weight-medium;
          line-height: 1.3;
          text-align: center;
          width: 100%;
          white-space: normal;
          margin: 0;
        }
      }
    }
  }
}

.ml-auto {
  margin-left: auto;
}

