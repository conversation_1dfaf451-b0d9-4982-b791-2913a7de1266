import { useState, useRef, Suspense } from 'react';
import { Modal, Box, CircularProgress, Typography } from '@mui/material';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { setCredentials } from '../../redux/features/auth/authSlice';
import { useOnboardingQueries } from './hooks/useOnboardingQueries';
import { useDefinePasswordMutation } from '../../redux/services/auth-api';
import { useUpdateUserOnboardingMutation } from '../../redux/services/user-api';
import StepContent from './components/StepContent';
import Button from '../../components/Button/Button';
import './styles.scss';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

// Step definitions with types and translation keys
const StepTypes = {
  welcome: 0,
  changePassword: 1,
  language: 2,
  businessFunction: 3,
  jobRole: 4,
  managementRole: 5,
  technicalBackground: 6,
  aiExperience: 7,
  industry: 8,
  terms: 9,
  review: 10,
  mandatoryVideo: 11,
};

// Translation keys for each step
const stepTranslationKeys = {
  [StepTypes.welcome]: 'onboarding.steps.welcome',
  [StepTypes.changePassword]: 'onboarding.steps.changePassword',
  [StepTypes.language]: 'onboarding.steps.language',
  [StepTypes.businessFunction]: 'onboarding.steps.businessFunction',
  [StepTypes.jobRole]: 'onboarding.steps.jobRole',
  [StepTypes.managementRole]: 'onboarding.steps.managementRole',
  [StepTypes.technicalBackground]: 'onboarding.steps.technicalBackground',
  [StepTypes.aiExperience]: 'onboarding.steps.aiExperience',
  [StepTypes.industry]: 'onboarding.steps.industry',
  [StepTypes.terms]: 'onboarding.steps.termsAndConditions',
  [StepTypes.review]: 'onboarding.steps.review.title',
  [StepTypes.mandatoryVideo]: 'onboarding.mandatoryVideo.title',
};

// Array of step keys in order
const steps = Object.values(stepTranslationKeys);

const OnboardingContent = ({ open, userData = null }) => {
  // If user logged in with SSO, start from language step instead of changePassword
  const initialStep = userData?.sso_login ? StepTypes.language : StepTypes.welcome;
  const [activeStep, setActiveStep] = useState(initialStep);
  const [formData, setFormData] = useState({
    language: 'en',
    function_label: '',
    job_role_label: '',
    management_role_label: undefined,
    technical_background_label: '',
    ai_knowledge_label: '',
    industry_label: null,
    termsAccepted: false,
  });
  const [showAllIndustries, setShowAllIndustries] = useState(false);
  const [showPassword, setShowPassword] = useState({
    password: false,
    confirmPassword: false,
  });
  const [isCompleting, setIsCompleting] = useState(false);
  const [isVideoComplete, setIsVideoComplete] = useState(false);
  // Reference to track if we're already handling video completion
  const isHandlingVideoComplete = useRef(false);
  const { t } = useTranslation();

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { data, isLoading } = useOnboardingQueries(activeStep, formData);
  const [definePassword] = useDefinePasswordMutation();
  const [updateUserOnboarding] = useUpdateUserOnboardingMutation();
  // Handle video completion - automatically complete onboarding
  const handleVideoCompletion = (completed) => {
    setIsVideoComplete(completed);

    // If video is completed and user is not an admin, automatically complete onboarding
    if (
      completed &&
      !(userData && userData.role && userData.role.toLowerCase() === 'administrator')
    ) {
      if (!isHandlingVideoComplete.current) {
        isHandlingVideoComplete.current = true;
        // Complete onboarding immediately without delay
        handleComplete();
      }
    }
  };

  const handleNext = () => {
    // Skip password validation for SSO users
    if (activeStep === StepTypes.changePassword && !userData?.sso_login) {
      const validations = validatePassword(formData.password || '');
      if (!Object.values(validations).every((v) => v)) {
        return;
      }
      handleDefinePassword();
      return;
    }

    // If welcome step and user is SSO, skip directly to language step
    if (activeStep === StepTypes.welcome && userData?.sso_login) {
      setActiveStep(StepTypes.language);
      return;
    }

    let canProceed = true;
    switch (activeStep) {
      case StepTypes.welcome:
        canProceed = true;
        break;
      case StepTypes.language:
        canProceed = !!formData.language;
        break;
      case StepTypes.businessFunction:
        canProceed = !!formData.function_label;
        break;
      case StepTypes.jobRole:
        canProceed = !!formData.job_role_label;
        break;
      case StepTypes.managementRole:
        canProceed = formData.management_role_label !== undefined;
        break;
      case StepTypes.technicalBackground:
        canProceed = !!formData.technical_background_label;
        break;
      case StepTypes.aiExperience:
        canProceed = !!formData.ai_knowledge_label;
        break;
      case StepTypes.industry:
        canProceed = !!formData.industry_label;
        break;
      case StepTypes.terms:
        canProceed = formData.termsAccepted;
        break;
      case StepTypes.review:
        canProceed = true;
        break;
      case StepTypes.mandatoryVideo:
        // If user is admin, they can skip the video
        canProceed =
          isVideoComplete ||
          (userData && userData.role && userData.role.toLowerCase() === 'administrator');
        break;
    }

    if (!canProceed) {
      return;
    }

    if (activeStep === steps.length - 1) {
      handleComplete();
    } else {
      setActiveStep((prev) => prev + 1);
    }
  };

  const handleDefinePassword = () => {
    definePassword({
      password: formData.password,
      email: userData.email,
    }).then(() => {
      setActiveStep((prev) => prev + 1);
    });
  };

  const handleBack = () => {
    // If current step is language and user logged in with SSO, go back to welcome step
    if (activeStep === StepTypes.language && userData?.sso_login) {
      setActiveStep(StepTypes.welcome);
    } else {
      setActiveStep((prev) => prev - 1);
    }
  };

  const handleComplete = async () => {
    try {
      setIsCompleting(true);

      const onboardingData = {
        language: formData.language,
        function_label: formData.function_label,
        job_role_label: formData.job_role_label,
        management_role_label: formData.management_role_label,
        technical_background_label: formData.technical_background_label,
        ai_knowledge_label: formData.ai_knowledge_label,
        industry_label: formData.industry_label,
        termsAccepted: formData.termsAccepted,
      };

      dispatch(
        setCredentials({
          user: { ...userData, onboarding: onboardingData },
          token: userData.token,
        })
      );

      const response = await updateUserOnboarding({
        userId: userData._id,
        onboarding: onboardingData,
      });

      // Check if response exists and has the expected structure
      if (response && response.data && response.data.status === 'success') {
        navigate('/');
        window.location.reload();
      } else {
        // Still redirect to homepage even if response structure is unexpected
        navigate('/');
        window.location.reload();
      }
    } catch (error) {
      console.error('Onboarding error:', error);
      // Even in case of error, we want to redirect to homepage
      navigate('/');
      window.location.reload();
    }
  };

  const validatePassword = (password) => {
    const isPasswordEmpty = !formData.password || formData.password.length === 0;
    const isConfirmEmpty = !formData.confirmPassword || formData.confirmPassword.length === 0;

    return {
      minLength: password.length >= 8,
      hasUpperCase: /[A-Z]/.test(password),
      hasLowerCase: /[a-z]/.test(password),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
      passwordsMatch: !isPasswordEmpty && !isConfirmEmpty && password === formData.confirmPassword,
    };
  };

  const isNextButtonDisabled = () => {
    switch (activeStep) {
      case StepTypes.welcome:
        return false;
      case StepTypes.changePassword:
        return !Object.values(validatePassword(formData.password || '')).every((v) => v);
      case StepTypes.language:
        return !formData.language;
      case StepTypes.businessFunction:
        return !formData.function_label;
      case StepTypes.jobRole:
        return !formData.job_role_label;
      case StepTypes.managementRole:
        return formData.management_role_label === undefined;
      case StepTypes.technicalBackground:
        return !formData.technical_background_label;
      case StepTypes.aiExperience:
        return !formData.ai_knowledge_label;
      case StepTypes.industry:
        return !formData.industry_label;
      case StepTypes.terms:
        return !formData.termsAccepted;
      case StepTypes.review:
        return false;
      case StepTypes.mandatoryVideo:
        // If user is admin, they can skip the video
        return (
          !isVideoComplete &&
          !(userData && userData.role && userData.role.toLowerCase() === 'administrator')
        );
      default:
        return false;
    }
  };

  if (!userData) {
    return (
      <Modal
        open={open}
        disableEscapeKeyDown
        onClose={null}
        slotProps={{
          backdrop: { className: 'onboarding-modal__backdrop' },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            bgcolor: 'background.paper',
            borderRadius: 2,
            p: 4,
            minWidth: 300,
          }}
        >
          <CircularProgress />
          <Typography variant="body1" sx={{ mt: 2 }}>
            {t('common.loading')}
          </Typography>
        </Box>
      </Modal>
    );
  }

  return (
    <Modal
      open={open}
      disableEscapeKeyDown
      onClose={null}
      slotProps={{
        backdrop: { className: 'onboarding-modal__backdrop' },
      }}
    >
      <Box
        className={`onboarding-modal__container ${activeStep === StepTypes.mandatoryVideo ? 'video-step-active' : ''}`}
      >
        {isCompleting ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '300px',
              gap: 2,
            }}
          >
            <CircularProgress />
            <Typography variant="body1">{t('onboarding.completion.processing')}</Typography>
          </Box>
        ) : (
          <>
            <StepContent
              activeStep={activeStep}
              formData={formData}
              setFormData={setFormData}
              showPassword={showPassword}
              setShowPassword={setShowPassword}
              showAllIndustries={showAllIndustries}
              setShowAllIndustries={setShowAllIndustries}
              data={data}
              isLoading={isLoading}
              validatePassword={validatePassword}
              userData={userData}
              onVideoComplete={handleVideoCompletion}
            />
            {/* Hide footer buttons for video step unless user is admin */}
            {(activeStep !== StepTypes.mandatoryVideo ||
              (userData && userData.role && userData.role.toLowerCase() === 'administrator')) && (
              <Box className="onboarding-modal__footer">
                {activeStep !== StepTypes.welcome &&
                  !(
                    activeStep === StepTypes.mandatoryVideo &&
                    userData &&
                    userData.role &&
                    userData.role.toLowerCase() === 'administrator'
                  ) && (
                    <Button variant="outlined" color="primary" onClick={handleBack}>
                      {t('modal.previous')}
                    </Button>
                  )}
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleNext}
                  disabled={isNextButtonDisabled()}
                  className={
                    activeStep === StepTypes.welcome ||
                    (activeStep === StepTypes.mandatoryVideo &&
                      userData &&
                      userData.role &&
                      userData.role.toLowerCase() === 'administrator')
                      ? 'ml-auto'
                      : ''
                  }
                >
                  {activeStep === steps.length - 1 ||
                  (activeStep === StepTypes.mandatoryVideo &&
                    userData &&
                    userData.role &&
                    userData.role.toLowerCase() === 'administrator')
                    ? t('modal.complete')
                    : t('modal.next')}
                </Button>
              </Box>
            )}
          </>
        )}
      </Box>
    </Modal>
  );
};

OnboardingContent.propTypes = {
  open: PropTypes.bool.isRequired,
  userData: PropTypes.shape({
    _id: PropTypes.string,
    email: PropTypes.string,
    token: PropTypes.string,
    sso_login: PropTypes.bool,
  }),
};

const Onboarding = ({ open, userData = null }) => {
  return (
    <Suspense
      fallback={
        <Modal
          open={open}
          disableEscapeKeyDown
          onClose={null}
          slotProps={{
            backdrop: { className: 'onboarding-modal__backdrop' },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              bgcolor: 'background.paper',
              borderRadius: 2,
              p: 4,
              minWidth: 300,
            }}
          >
            <CircularProgress />
          </Box>
        </Modal>
      }
    >
      <OnboardingContent open={open} userData={userData} />
    </Suspense>
  );
};

Onboarding.propTypes = {
  open: PropTypes.bool.isRequired,
  userData: PropTypes.shape({
    _id: PropTypes.string,
    email: PropTypes.string,
    token: PropTypes.string,
    sso_login: PropTypes.bool,
  }),
};

export default Onboarding;
