import PropTypes from 'prop-types';
import { Dialog, DialogActions, DialogContent, DialogTitle, Button } from '@mui/material';
import { useTranslation } from 'react-i18next';
import ReactConfetti from 'react-confetti';
import useUserRole from '@/hooks/useUserRole';

const JourneyLevelUpModal = ({
  open,
  onClose,
  modalContent = '',
  showConfetti = false,
  levelData = null,
}) => {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || 'en';

  // useUserRole hook'unu kullan
  const { isLimitedUser } = useUserRole();

  // onClose fonksiyonunu çağıran wrapper fonksiyonu
  const handleClose = () => {
    if (typeof onClose === 'function') {
      onClose();
    }
  };

  // Doğru modal içeriğini seç - önce levelData'dan, varsa
  const getModalContent = () => {
    // Kullanıcı limited-use rolüne sahipse ve beginner seviyesini bitirdiyse özel mesaj göster
    if (isLimitedUser() && levelData?.slug === 'beginner') {
      // Limited-use uyarısını ekleyelim
      const limitedUseWarning = t('journey.limitedUser.beginnerCompleted');
      // Limited-use uyarısını ekleyelim
      const accessWarning = t('journey.limitedUser.accessWarning');

      // Yeni mesajı oluştur (HTML içerik olduğunu unutmayalım)
      return `<strong>${limitedUseWarning}</strong>🎉<br/><br/>⚠️${accessWarning}<br/><br/>`;
    }

    if (levelData) {
      // Dil bazlı modal içeriğini seç
      if (levelData.translations) {
        const langKey = `${currentLanguage}_modal`;
        if (levelData.translations[langKey]) {
          return levelData.translations[langKey];
        }
        // Fallback olarak İngilizce kullan
        if (levelData.translations.en_modal) {
          return levelData.translations.en_modal;
        }
      }
    }
    // Hiçbiri yoksa direkt gelen modalContent'i kullan
    return modalContent;
  };

  const finalModalContent = getModalContent();

  return (
    <>
      {showConfetti && (
        <ReactConfetti
          width={window.innerWidth}
          height={window.innerHeight}
          recycle={false}
          numberOfPieces={500}
        />
      )}

      <Dialog
        open={open}
        onClose={(event, reason) => {
          // Only allow closing via the continue button
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            handleClose();
          }
        }}
        aria-labelledby="level-up-dialog-title"
        maxWidth="sm"
        fullWidth
        disableEscapeKeyDown
      >
        <DialogTitle id="level-up-dialog-title">
          {t('common.congratulations', 'Tebrikler!')}
        </DialogTitle>
        <DialogContent className="journey-level-up-modal-content">
          <div
            dangerouslySetInnerHTML={{
              __html: finalModalContent,
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="primary" className="continue-button">
            {t('common.continue', 'Devam Et')}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

JourneyLevelUpModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  modalContent: PropTypes.string,
  showConfetti: PropTypes.bool,
  levelData: PropTypes.object,
};

export default JourneyLevelUpModal;
