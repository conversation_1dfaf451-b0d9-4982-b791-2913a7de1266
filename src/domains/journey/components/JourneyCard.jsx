import React from 'react';
import PropTypes from 'prop-types';
import LockIcon from '@mui/icons-material/Lock';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import Button from '@/components/Button/Button.jsx';
import './JourneyCard.scss';
import { Box, Typography, Tooltip } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import useUserRole from '@/hooks/useUserRole';

const CardContent = React.forwardRef(
  (
    {
      title = '',
      description,
      isLocked,
      isCompleted,
      buttonText,
      buttonURL,
      newTab,
      hideButton,
      onClick,
      journeyType,
      buttonType,
      imageSrc,
    },
    ref
  ) => {
    const { t } = useTranslation();
    const handleClick = (e) => {
      console.log('CardContent handleClick çağrıldı:', journeyType, buttonType);
      if (onClick) {
        e.preventDefault();
        onClick();
      }
    };

    const buttonIcon = isCompleted ? (
      <CheckCircleIcon />
    ) : buttonType === 'File' ? (
      <FileDownloadIcon />
    ) : (
      <PlayCircleOutlineIcon />
    );

    return (
      <Box ref={ref} className="card-content journey-card-content">
        {imageSrc && (
          <Box className="card-image">
            <img src={imageSrc} alt={title} />
          </Box>
        )}
        <Typography className="card-title">{title}</Typography>
        <Typography className="card-description">{description}</Typography>
      </Box>
    );
  }
);

CardContent.displayName = 'CardContent';

const JourneyCard = ({
  children,
  isLocked = true,
  percent = 0,
  buttonText,
  buttonType,
  onClick,
  showStatus = true,
  imageSrc,
  title,
}) => {
  const { t } = useTranslation();
  const isCompleted = percent === 100;
  const isInProgress = false;

  // useUserRole hook'unu kullan
  const { user, isAdministrator, isLimitedUser } = useUserRole();

  // Administrator rolüne sahip kullanıcılar için kart kilidini kaldır
  const adjustedIsLocked = isAdministrator() ? false : isLocked;

  const getStatusContent = () => {
    if (adjustedIsLocked) {
      return {
        icon: <LockIcon />,
        text: t('home.journeyCard.locked'),
      };
    }

    if (isCompleted) {
      return {
        icon: <CheckCircleIcon />,
        text: t('home.journeyCard.completed'),
      };
    }

    return {
      icon: <PlayCircleOutlineIcon />,
      text: t('home.journeyCard.playNow'),
    };
  };

  const status = getStatusContent();

  // Limited-use kullanıcıları için tooltip mesajı
  const getLockTooltip = () => {
    if (isLimitedUser() && adjustedIsLocked) {
      return t('journey.limitedUser.lockTooltip');
    }

    return;
  };

  const cardContent = (
    <Box
      className={`carousel-card ${isCompleted ? 'completed' : ''} ${isInProgress ? 'in-progress' : ''} ${adjustedIsLocked ? 'locked' : ''}`}
      onClick={onClick}
      style={{ cursor: 'pointer' }}
    >
      {showStatus && (
        <Box className="card-status">
          <Box
            className={`status-badge ${isCompleted ? 'completed' : ''} ${isInProgress ? 'in-progress' : ''} ${adjustedIsLocked ? 'locked' : ''}`}
          >
            {status.icon}
            {status.text && <span>{status.text}</span>}
          </Box>
        </Box>
      )}
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, {
            isLocked: isCompleted ? false : adjustedIsLocked,
            isCompleted,
            isInProgress,
            buttonType,
            buttonText,
            hideButton: isCompleted ? false : adjustedIsLocked,
            imageSrc,
            title,
          });
        }
        return child;
      })}
    </Box>
  );

  // Kilitli kart için tooltip ekle
  return adjustedIsLocked ? (
    <Tooltip title={getLockTooltip()} arrow placement="top">
      {cardContent}
    </Tooltip>
  ) : (
    cardContent
  );
};

JourneyCard.propTypes = {
  children: PropTypes.node.isRequired,
  isLocked: PropTypes.bool.isRequired,
  buttonText: PropTypes.string.isRequired,
  buttonType: PropTypes.oneOf(['URL', 'File', 'Selected Content']).isRequired,
  percent: PropTypes.number,
  onClick: PropTypes.func,
  showStatus: PropTypes.bool,
  imageSrc: PropTypes.string,
  title: PropTypes.string,
};

CardContent.propTypes = {
  title: PropTypes.string,
  description: PropTypes.string.isRequired,
  isLocked: PropTypes.bool,
  isCompleted: PropTypes.bool,
  buttonText: PropTypes.string.isRequired,
  buttonURL: PropTypes.string.isRequired,
  newTab: PropTypes.bool,
  hideButton: PropTypes.bool,
  onClick: PropTypes.func,
  journeyType: PropTypes.string,
  buttonType: PropTypes.oneOf(['URL', 'File', 'Selected Content']),
  imageSrc: PropTypes.string,
};

export { CardContent };
export default JourneyCard;
