import { Dialog, DialogActions, DialogContent, DialogTitle, Button } from '@mui/material';
import PropTypes from 'prop-types';

const SkipJourneyModal = ({
  openSkipJourneyConfirmDialog,
  handleCloseSkipJourneyConfirmDialog,
  confirmSkipJourney,
  i18n
}) => {
  return (
    <Dialog
      open={openSkipJourneyConfirmDialog}
      onClose={handleCloseSkipJourneyConfirmDialog}
    >
      <DialogTitle>
        {i18n.t('home.skipJourney', 'Skip Journey')}
      </DialogTitle>
      <DialogContent>
        {i18n.t('home.skipJourneyModal.description', 'Based on your responses during onboarding, we understand that you already possess a solid foundational knowledge of AI. If you wish, you can skip the Beginner Journey and proceed directly to the Expert Journey.')}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCloseSkipJourneyConfirmDialog} color="primary">
          {i18n.t('modal.cancel', 'Cancel')}
        </Button>
        <Button onClick={confirmSkipJourney} color="primary" autoFocus>
          {i18n.t('modal.confirm', 'Confirm')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

SkipJourneyModal.propTypes = {
  openSkipJourneyConfirmDialog: PropTypes.bool.isRequired,
  handleCloseSkipJourneyConfirmDialog: PropTypes.func.isRequired,
  confirmSkipJourney: PropTypes.func.isRequired,
  i18n: PropTypes.object.isRequired
};

export default SkipJourneyModal; 