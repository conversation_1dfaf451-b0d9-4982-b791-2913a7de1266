/**
 * GenAI kartının durumunu kontrol eder.
 * @param {string} userId - Kullanıcı ID'si
 * @returns {Promise} Kontrol sonucu
 */
const checkGenAICard = async (userId) => {
  try {
    // Eğer userId yoksa işlemi yapma
    if (!userId) {
      return { status: 'error', message: 'User ID is required' };
    }

    // Şimdilik doğrudan fetch kullanılıyor
    const response = await fetch(`/api/genai/check-status?userId=${userId}`);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('GenAI card check error:', error);
    return { status: 'error', message: error.message || 'An error occurred' };
  }
};

/**
 * GenAI kartının tamamlanma durumunu işler ve journey tracking'i günceller
 * @param {object} params - İşlem parametreleri
 * @param {string} params.userId - Kullanıcı ID'si
 * @param {object} params.trackingData - Mevcut tracking verisi
 * @param {string} params.selectedLevel - Se<PERSON>ili seviye
 * @param {object} params.activeJourneyCard - Aktif journey kartı
 * @param {Function} params.refetchTracking - Tracking verilerini yeniden çekme fonksiyonu
 * @returns {Promise} İşlem sonucu
 */
const processGenAICard = async ({
  userId,
  trackingData,
  selectedLevel,
  activeJourneyCard,
  refetchTracking,
}) => {
  try {
    // Eğer kart GenAI Playground değilse çık
    if (!activeJourneyCard || activeJourneyCard.journeyType !== 'GenAI Playground') {
      return { status: 'not_applicable' };
    }

    // GenAI durumunu kontrol et
    const data = await checkGenAICard(userId);

    if (data?.status === 'success' && data.data?.deployed_stacks_count > 0) {
      // Kart zaten tamamlanmış mı kontrol et
      const currentTracking = trackingData?.[selectedLevel?.toLowerCase()] || [];
      if (currentTracking.includes(activeJourneyCard._id)) {
        return { status: 'already_completed' };
      }

      try {
        // API isteğini manuel olarak oluştur (Redux store olmadan)
        const response = await fetch(`/api/journeys/tracking/${userId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            data: {
              [selectedLevel.toLowerCase()]: [
                ...(trackingData?.[selectedLevel.toLowerCase()] || []),
                activeJourneyCard._id,
              ],
            },
          }),
        });

        const updateResult = await response.json();

        if (updateResult?.status === 'success') {
          // Tracking verilerini yenile
          if (refetchTracking) {
            await refetchTracking();
          }
          return { status: 'success', message: 'GenAI card marked as completed' };
        }
      } catch (updateError) {
        console.error('Failed to update journey tracking:', updateError);
        return {
          status: 'error',
          message: updateError.message || 'Failed to update journey tracking',
        };
      }

      return { status: 'error', message: 'Failed to update journey tracking' };
    }

    return { status: 'pending', message: 'Waiting for GenAI Card to be deployed' };
  } catch (error) {
    console.error('Process GenAI card error:', error);
    return { status: 'error', message: error.message || 'An error occurred' };
  }
};

export { checkGenAICard, processGenAICard };
export default checkGenAICard;
