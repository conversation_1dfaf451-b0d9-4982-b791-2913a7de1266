import { useUpdateJourneyTrackingMutation } from '../../../redux/services/journey-api';

/**
 * Custom hook to update journey tracking
 * @returns {Function} updateJourneyTrackingCard - Function to update journey tracking
 */
export const useUpdateJourneyTracking = () => {
  const [updateJourneyTracking] = useUpdateJourneyTrackingMutation();

  /**
   * Updates journey tracking for a specific card
   * @param {Object} params - Parameters for updating journey tracking
   * @param {string} params.userId - User ID
   * @param {Object} params.journeyTrackingData - Current journey tracking data
   * @param {string} params.userLevel - User's current journey level (beginner, expert, master)
   * @param {string} params.cardId - Journey card ID to add to tracking
   * @returns {Promise} - Promise that resolves when tracking is updated
   */
  const updateJourneyTrackingCard = async ({ userId, journeyTrackingData, userLevel, cardId }) => {
    if (!userId || !journeyTrackingData || !userLevel || !cardId) {
      console.error('Missing required parameters for updating journey tracking');
      return;
    }

    try {
      const currentTracking = journeyTrackingData[userLevel] || [];

      if (currentTracking.includes(cardId)) {
        return { status: 'success', message: 'Card already tracked' };
      }

      // Add card to tracking
      const updatedTracking = [...currentTracking, cardId];

      // Update journey tracking
      const result = await updateJourneyTracking({
        userId,
        journeyTracking: {
          ...journeyTrackingData,
          [userLevel]: updatedTracking,
        },
      }).unwrap();

      return result;
    } catch (error) {
      console.error('Error updating journey tracking:', error);
      throw error;
    }
  };

  return { updateJourneyTrackingCard };
};

export default useUpdateJourneyTracking;
