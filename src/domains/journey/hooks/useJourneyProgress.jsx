import { useMemo } from 'react';
import { useGetJourneysQuery } from '../../../redux/services/journey-api';

/**
 * <PERSON>llanıcının journey ilerlemesini hesaplayan custom hook
 * @param {Object} props - Hook parametreleri
 * @param {Object} props.user - Kullanıcı bilgileri
 * @param {Object} props.trackingData - Kullanıcının journey tracking verileri
 * @param {boolean} props.showUserProgress - İlerleme gösterilmesi gerekip gerekmediği
 * @returns {Object} İlerleme bilgileri
 */
const useJourneyProgress = ({ user, trackingData, showUserProgress = false }) => {
  // Kullanıcının gerçek seviyesi
  const userActualLevel = user?.journeyLevel?.name || 'beginner';

  // Kullanıcının gerçek seviyesindeki journey verilerini çekiyoruz
  const { data: userActualJourneyData } = useGetJourneysQuery(
    {
      function: user?.onboarding?.function_label,
      levels: userActualLevel,
      managementRole: user?.onboarding?.management_role_label?.slug,
    },
    {
      skip: !user?._id || !userActualLevel || !showUserProgress,
    }
  );

  // Kullanıcının gerçek seviyesine ait ilerleme bilgilerini hesapla
  const { completedSteps, progress, totalSteps } = useMemo(() => {
    if (!userActualJourneyData || !trackingData) {
      return { completedSteps: 0, progress: 0, totalSteps: 0 };
    }

    // Kullanıcının gerçek seviyesindeki tamamlanan journey'leri al
    const completedJourneys = trackingData[userActualLevel.toLowerCase()] || [];
    let totalPercent = 0;
    let completedCount = 0;

    // Kullanıcının gerçek seviyesindeki kartları ve tamamlanma durumunu kontrol et
    userActualJourneyData.forEach((journey) => {
      if (journey._id && completedJourneys.includes(journey._id)) {
        completedCount++;
        if (typeof journey.percent === 'number') {
          totalPercent += journey.percent;
        }
      }
    });

    return {
      completedSteps: completedCount,
      progress: totalPercent,
      totalSteps: userActualJourneyData.length || 0,
    };
  }, [userActualJourneyData, trackingData, userActualLevel]);

  return {
    userActualLevel,
    userActualCompletedSteps: completedSteps,
    userActualProgress: progress,
    userActualTotalSteps: totalSteps,
  };
};

export default useJourneyProgress;
