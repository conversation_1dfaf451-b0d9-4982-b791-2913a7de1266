import { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import useUpdateJourneyTracking from '../utils/updateJourneyTracking';
import { useGetUseCaseByIdQuery } from '../../../redux/services/use-case-api';
import { useFormByIdQuery } from '../../../redux/services/form-service';
import { useSubmitFormResponseMutation } from '../../../redux/services/form-response';
import { useLevelsQuery } from '../../../redux/services/cds-api';
import { useDispatch } from 'react-redux';
import { setCurrentCardId } from '../../../redux/features/courses/courseSlice';
import { useTranslation } from 'react-i18next';
import { IDEA_FORM_ID } from '../../../constants/form-constants';
import useUserRole from '@/hooks/useUserRole';

/**
 * Journey kartları için ortak işlemleri yöneten hook
 * @param {Object} props - Hook parametreleri
 * @param {Object} props.trackingData - Kullanıcının journey takip verileri
 * @param {string} props.selectedLevel - Seçili journey seviyesi
 * @param {Object} props.user - Kullanıcı bilgileri
 * @returns {Object} Journey kartları için gerekli fonksiyonlar ve state'ler
 */
const useJourneyCardHandlers = ({ trackingData, selectedLevel, user }) => {
  const navigate = useNavigate();
  const { i18n } = useTranslation();
  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();

  // userRole hook'unu kullan
  const { isAdministrator, isLimitedUser, getUserJourneyLevel } = useUserRole();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState({
    title: '',
    content: '',
    cardId: null,
    isCompleted: false,
  });
  const [currentUseCaseId, setCurrentUseCaseId] = useState(null);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const [currentJourneyCardId, setCurrentJourneyCardId] = useState(null);

  // Ideation form için state'ler
  const [isIdeationModalOpen, setIsIdeationModalOpen] = useState(false);
  const [ideationFormId, setIdeationFormId] = useState(null);
  const [ideationCardId, setIdeationCardId] = useState(null);
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({});
  const [formErrors, setFormErrors] = useState({});

  // Levels API'den veri çekme
  const { data: levelsData } = useLevelsQuery();

  const currentLanguage = i18n.language || user?.onboarding?.language?.slug || 'en';

  // dispatch tanımla
  const dispatch = useDispatch();

  // Mevcut seviye için modal içeriğini bulma
  const getCurrentLevelModalContent = useCallback(
    (level, getPreviousLevel = false) => {
      if (!levelsData?.data) return null;

      // Level değerinin geçerli olduğundan emin ol
      if (!level) return null;

      // Seviye sıralaması
      const levelOrder = ['beginner', 'expert', 'master'];

      // Hedef seviye slug'ını belirle
      let targetLevelSlug = level.toLowerCase();

      // Eğer önceki seviyenin içeriğini istiyorsak
      if (getPreviousLevel) {
        const currentIndex = levelOrder.indexOf(targetLevelSlug);

        // Eğer geçerli bir seviye ise ve ilk seviye değilse
        if (currentIndex > 0) {
          // Bir önceki seviyeyi al
          targetLevelSlug = levelOrder[currentIndex - 1];
        }
      }

      // Hedef seviyenin verisini bul
      const targetLevelData = levelsData.data.find((level) => level.slug === targetLevelSlug);

      if (!targetLevelData) {
        return null;
      }

      // İçerik için dil bazlı kontrolü yap
      let content = '';

      // Dil sıralaması - önce tercih edilen dil, yoksa İngilizce
      const languagePreferences = [`${currentLanguage}_modal`, 'en_modal', 'de_modal'];

      // Dil tercihlerine göre içerik bul
      for (const langKey of languagePreferences) {
        if (targetLevelData.translations?.[langKey]) {
          content = targetLevelData.translations[langKey];
          break;
        }
      }

      // Eğer hala içerik bulunamadıysa modalContent'e bak
      if (!content && targetLevelData.modalContent?.length > 0) {
        content = targetLevelData.modalContent[0];
      }

      // Sonuç objesini döndür
      const modalData = {
        content,
        levelSlug: targetLevelData.slug,
        nextLevel: targetLevelData.nextLevel,
        isLastLevel: targetLevelData.isLastLevel,
        levelData: targetLevelData,
      };

      return modalData;
    },
    [levelsData, currentLanguage]
  );

  /**
   * Bir değerin boş olup olmadığını kontrol eder
   */
  const checkIfEmpty = useCallback((value) => {
    if (value === undefined || value === null) {
      return true;
    }

    if (typeof value === 'string') {
      return value.trim() === '';
    }

    if (Array.isArray(value)) {
      return value.length === 0;
    }

    if (typeof value === 'object') {
      return Object.keys(value).length === 0;
    }

    return false;
  }, []);

  // Kullanıcının dil tercihini al - Önce i18n'den al, eğer yoksa kullanıcı tercihini kullan
  const userLanguage = i18n.language || user?.onboarding?.language?.slug || 'en';

  // Ideation formunu çekme - Kullanıcının aktif dil tercihine göre
  const { data: ideationFormData, isLoading: isIdeationFormLoading } = useFormByIdQuery(
    {
      formId: ideationFormId,
      lang: userLanguage,
    },
    {
      skip: !ideationFormId,
      // Dil değiştiğinde formu yeniden çek
      refetchOnMountOrArgChange: true,
    }
  );

  // Form gönderimi için
  const [submitFormResponse, { isLoading: isSubmittingForm }] = useSubmitFormResponseMutation();

  // Use case verilerini çek
  const { data: useCaseData } = useGetUseCaseByIdQuery(currentUseCaseId, {
    skip: !currentUseCaseId,
  });

  // useCaseData değiştiğinde yönlendirme yap
  useEffect(() => {
    if (pendingNavigation && useCaseData) {
      if (useCaseData.slug) {
        // Usecase sayfasına yönlendirken journey tracking parametrelerini de gönder
        navigate(`/usecase/${useCaseData.slug}`, {
          state: {
            journeyCardId: currentJourneyCardId,
            journeyTrackingData: trackingData,
            journeyLevel: selectedLevel,
          },
        });
      } else {
        // Slug yoksa ID ile yönlendir
        navigate(`/usecase/${pendingNavigation}`, {
          state: {
            journeyCardId: currentJourneyCardId,
            journeyTrackingData: trackingData,
            journeyLevel: selectedLevel,
          },
        });
      }
      setPendingNavigation(null);
    }
  }, [useCaseData, pendingNavigation, navigate, currentJourneyCardId, trackingData, selectedLevel]);

  /**
   * Belirli bir journey kartının tamamlanıp tamamlanmadığını kontrol eder
   * @param {string} cardId - Kontrol edilecek kartın ID'si
   * @returns {boolean} Kart tamamlandıysa true, aksi halde false
   */
  const isJourneyCompleted = useCallback(
    (cardId) => {
      if (!trackingData || !selectedLevel) return false;

      // Sadece kartın tamamlanma durumunu dön, kilitli olup olmadığını kontrol etme
      const completedJourneys = trackingData[selectedLevel.toLowerCase()] || [];
      return completedJourneys.includes(cardId);
    },
    [trackingData, selectedLevel]
  );

  /**
   * Önceki kartın tamamlanıp tamamlanmadığını kontrol eder
   * @param {number} index - Mevcut kartın indeksi
   * @param {Array} journeyData - Journey verileri
   * @returns {boolean} Önceki kart tamamlandıysa veya ilk kartsa true, aksi halde false
   */
  const isPreviousCompleted = useCallback(
    (index, journeyData) => {
      // Kullanıcının gerçek seviyesi ile seçilen seviye kontrolü
      const userJourneyLevel = getUserJourneyLevel();
      const currentSelectedLevel = selectedLevel?.toLowerCase();

      // Eğer kullanıcı Administrator rolündeyse her zaman erişim ver
      if (isAdministrator()) {
        return true;
      }

      // Eğer kullanıcı limited-use rolündeyse ve seçilen seviye beginner değilse, tüm kartları kilitle
      if (isLimitedUser() && currentSelectedLevel !== 'beginner') {
        return false;
      }

      // Eğer kullanıcının gerçek seviyesi ile seçilen seviye aynı değilse tüm kartlar kilitli olsun
      if (userJourneyLevel !== currentSelectedLevel) {
        return false;
      }

      // İlk kart her zaman erişilebilir olmalı (sadece kullanıcının gerçek seviyesinde)
      if (index === 0) return true;

      // Diğer kartlar için önceki kartın tamamlanma durumunu kontrol et
      if (!journeyData || !journeyData[index - 1]) return false;
      return isJourneyCompleted(journeyData[index - 1]._id);
    },
    [isJourneyCompleted, selectedLevel, isAdministrator, isLimitedUser, getUserJourneyLevel]
  );

  /**
   * İlk tıklanabilir kartın indeksini bulur
   * @param {Array} journeyData - Journey verileri
   * @returns {number} İlk tıklanabilir kartın indeksi
   */
  const getFirstClickableCardIndex = useCallback(
    (journeyData) => {
      if (!journeyData) return 0;

      // Eğer kullanıcı Administrator rolündeyse her zaman ilk kartı göster
      if (isAdministrator()) {
        return 0;
      }

      // Kullanıcının gerçek seviyesi ile seçilen seviye kontrolü
      const userJourneyLevel = getUserJourneyLevel();
      const currentSelectedLevel = selectedLevel?.toLowerCase();

      // Eğer kullanıcı limited-use rolündeyse ve seçilen seviye beginner değilse ilk kartı göster ama kilitli olsun
      if (isLimitedUser() && currentSelectedLevel !== 'beginner') {
        return 0;
      }

      // Eğer kullanıcının gerçek seviyesi ile seçilen seviye aynı değilse ilk kartı göster ama kilitli olsun
      if (userJourneyLevel !== currentSelectedLevel) {
        return 0;
      }

      // Tamamlanmış son kartı bul
      let lastCompletedIndex = -1;
      for (let i = 0; i < journeyData.length; i++) {
        if (isJourneyCompleted(journeyData[i]._id)) {
          lastCompletedIndex = i;
        } else {
          break;
        }
      }

      // Tamamlanmış son karttan sonraki kartı döndür veya ilk kartı
      return lastCompletedIndex + 1 < journeyData.length ? lastCompletedIndex + 1 : 0;
    },
    [isJourneyCompleted, selectedLevel, isAdministrator, isLimitedUser, getUserJourneyLevel]
  );

  /**
   * Dil çevirisi için kullanılacak anahtarı belirler
   * @param {string} currentLanguage - Mevcut dil
   * @param {Object} userLanguage - Kullanıcının dil tercihi
   * @returns {string} Çeviri anahtarı
   */
  const getTranslationKey = useCallback((currentLanguage, userLanguage) => {
    if (currentLanguage === 'de') return 'german';
    if (userLanguage?.slug === 'german') return 'german';
    return 'english';
  }, []);

  /**
   * Karta tıklandığında çalışacak fonksiyon
   * @param {Object} item - Tıklanan kart verisi
   * @param {string} language - Mevcut dil
   */
  const handleCardClick = useCallback(
    (item, language) => {
      // Eğer kart tamamlanmışsa veya kullanıcı Administrator ise her zaman tıklanabilir olmalı
      const isCardCompleted = isJourneyCompleted(item._id);

      // Kullanıcının gerçek seviyesi ile seçilen seviye kontrolü
      // Eğer kart tamamlanmışsa veya kullanıcı Administrator ise seviye kontrolünü atla
      if (!isCardCompleted && !isAdministrator()) {
        const userJourneyLevel = getUserJourneyLevel();
        const currentSelectedLevel = selectedLevel?.toLowerCase();

        // Eğer kullanıcı limited-use rolündeyse ve seçilen seviye beginner değilse kartlara tıklanamaz
        if (isLimitedUser() && currentSelectedLevel !== 'beginner') {
          return;
        }

        // Eğer kullanıcının gerçek seviyesi ile seçilen seviye aynı değilse kartlara tıklanamaz
        if (userJourneyLevel !== currentSelectedLevel) {
          return;
        }
      }

      const translationKey = language === 'de' ? 'german' : 'english';
      const selectedTranslation =
        item.translations?.[translationKey] || item.translations?.english || {};

      // Eğer journeyType "Usecases" ise ve selectedFunctionSpecUseCases varsa
      if (item.journeyType === 'Usecases' && item.selectedFunctionSpecUseCases) {
        // Use case ID'sini ayarla ve yönlendirme için beklet
        setCurrentUseCaseId(item.selectedFunctionSpecUseCases);
        setPendingNavigation(item.selectedFunctionSpecUseCases);
        setCurrentJourneyCardId(item._id);

        return;
      }

      // Eğer journeyType "Ideation Project" ise
      if (item.journeyType === 'Ideation Project' && item.ideation) {
        // Sabit IDEA_FORM_ID kullanıyoruz, item.ideation değeri yerine
        setIdeationFormId(IDEA_FORM_ID);
        setIdeationCardId(item._id);
        setIsIdeationModalOpen(true);
        return;
      }

      // App Creator türündeki kartlar için yönlendirme yapılmalı
      if (item.journeyType === 'App Creator') {
        // ButtonURL varsa onu kullan, yoksa varsayılan yönlendirme yap
        const targetUrl = item.buttonURL || '/app-creator';
        navigate(targetUrl, {
          state: { cardId: item._id },
        });
        return;
      }

      // Workflow Creator türündeki kartlar için yönlendirme yapılmalı
      if (item.journeyType === 'Workflow Creator') {
        // ButtonURL varsa onu kullan, yoksa varsayılan yönlendirme yap
        const targetUrl = item.buttonURL || '/workflow-creator';
        navigate(targetUrl, {
          state: { cardId: item._id },
        });
        return;
      }

      // GenAI Playground türündeki kartlar için yönlendirme yapılmalı
      if (item.journeyType === 'GenAI Playground') {
        // ButtonURL varsa onu kullan, yoksa varsayılan yönlendirme yap
        const targetUrl = item.buttonURL || '/genai-playground';
        navigate(targetUrl, {
          state: { cardId: item._id },
        });
        return;
      }

      // Content türündeki kartlar için modal açılmalı
      if (item.journeyType === 'Content' || item.journeyType === 'content') {
        setModalContent({
          title: selectedTranslation.title || item.title,
          content: selectedTranslation.content || item.content,
          cardId: item._id,
          isCompleted: isJourneyCompleted(item._id),
        });
        setIsModalOpen(true);
        return;
      }

      // Course türündeki kartlar için yönlendirme yapılmalı
      if (item.journeyType === 'Course' || item.journeyType === 'course') {
        // CardId'yi Redux'a kaydet
        dispatch(setCurrentCardId(item._id));

        // Debug bilgisi

        // Journey bilgilerini sessionStorage'a kaydet (state'in kaybolması durumuna karşı)
        sessionStorage.setItem('journeyCardId', item._id);
        sessionStorage.setItem('journeyLevel', selectedLevel);

        // Sadece kurs sayfasına yönlendir, URL'e cardId ekleme
        navigate(`/course/${item.course}`, {
          state: {
            journeyCardId: item._id,
            journeyTrackingData: trackingData,
            journeyLevel: selectedLevel,
          },
        });
        return;
      }

      // buttonType kontrolü
      if (item.buttonType === 'Selected Content' || item.buttonType?.toLowerCase() === 'selected') {
        setModalContent({
          title: selectedTranslation.title || item.title,
          content: selectedTranslation.content || item.content,
          cardId: item._id,
          isCompleted: isJourneyCompleted(item._id),
        });
        setIsModalOpen(true);
      } else if (item.buttonType?.toLowerCase() === 'url') {
        if (item.newTab) {
          window.open(item.buttonURL, '_blank');
        } else {
          navigate(item.buttonURL);
        }
      } else if (item.buttonType?.toLowerCase() === 'course') {
        // Course buttonType için cardId ekleme
        dispatch(setCurrentCardId(item._id));

        // Debug bilgisi

        // Journey bilgilerini sessionStorage'a kaydet (state'in kaybolması durumuna karşı)
        sessionStorage.setItem('journeyCardId', item._id);
        sessionStorage.setItem('journeyLevel', selectedLevel);

        // Sadece kurs sayfasına yönlendir, URL'e cardId ekleme
        navigate(`/course/${item.courseId}`, {
          state: {
            journeyCardId: item._id,
            journeyTrackingData: trackingData,
            journeyLevel: selectedLevel,
          },
        });
      }
    },
    [
      navigate,
      isJourneyCompleted,
      setCurrentUseCaseId,
      setPendingNavigation,
      setCurrentJourneyCardId,
      selectedLevel,
      dispatch,
      isAdministrator,
      isLimitedUser,
      getUserJourneyLevel,
    ]
  );

  /**
   * Tech şirket kartına tıklandığında çalışacak fonksiyon
   * @param {string} url - Yönlendirilecek URL
   */
  const handleCardClickTechCompany = useCallback((url) => {
    window.open(url, '_blank');
  }, []);

  /**
   * Modal kapatıldığında çalışacak fonksiyon
   * @param {Object} result - Modal sonucu
   * @param {Function} refetchTracking - Tracking verilerini yeniden çekmek için fonksiyon
   * @param {Function} refetchJourneys - Journey verilerini yeniden çekmek için fonksiyon
   * @param {Function} setIsInitialized - Başlatma durumunu güncellemek için fonksiyon
   */
  const handleModalClose = useCallback(
    async (result, refetchTracking, refetchJourneys, setIsInitialized) => {
      setIsModalOpen(false);

      if (result && result.completed && modalContent.cardId && !modalContent.isCompleted) {
        try {
          // Kullanıcının profil level'ı değil, seçili seviyeyi kullan
          await updateJourneyTrackingCard({
            userId: user?._id,
            journeyTrackingData: trackingData,
            userLevel: selectedLevel?.toLowerCase(), // Arayüzde seçilen seviyeyi kullan
            cardId: modalContent.cardId,
          });

          // Verileri yenile
          if (refetchTracking) {
            await refetchTracking();
          }
          if (refetchJourneys) {
            await refetchJourneys();
          }
          if (setIsInitialized) {
            setIsInitialized(false);
          }
        } catch {
          // Hata durumunda sessizce devam et
        }
      }
    },
    [modalContent, user, trackingData, selectedLevel, updateJourneyTrackingCard]
  );

  /**
   * Ideation formunun gönderilmesi işlemini yönetir
   * @param {Function} refetchTracking - Tracking verilerini yenilemek için fonksiyon
   * @param {Function} refetchJourneys - Journey verilerini yenilemek için fonksiyon
   */
  const handleIdeationFormSubmit = useCallback(
    async (refetchTracking, refetchJourneys) => {
      // Fonksiyon çağrıldığında ideationFormData kontrolü
      console.warn(
        'handleIdeationFormSubmit çağrıldı - ideationFormData durumu:',
        !!ideationFormData
      );

      if (!ideationFormData) {
        console.error('ideationFormData henüz yüklenmedi, form gönderilemiyor.');
        return false;
      }
      try {
        // Form API verisinin debug'ı
        console.warn('Try bloğu içinde ideationFormData durumu:', !!ideationFormData);

        // Form için alanları hazırla
        let formFields = [];
        // Yeni API yanıt yapısına göre güncellenmiş alan kontrolü
        if (ideationFormData?.data?.fields && ideationFormData.data.fields.length > 0) {
          formFields = ideationFormData.data.fields;
        } else if (ideationFormData?.fields && ideationFormData.fields.length > 0) {
          formFields = ideationFormData.fields;
        } else if (ideationFormData?.data?.forms && ideationFormData.data.forms[0]?.fields) {
          formFields = ideationFormData.data.forms[0].fields;
        } else if (ideationFormData?.data?.topics && ideationFormData.data.topics.length > 0) {
          // Tüm konulardan alanları birleştir
          formFields = [];
          ideationFormData.data.topics.forEach((topic) => {
            if (topic.fields && topic.fields.length > 0) {
              formFields = [...formFields, ...topic.fields];
            }
          });
          console.warn(
            'Topics içindeki tüm alanlar birleştirildi, bulunan alan sayısı:',
            formFields.length
          );
        } else {
          formFields = [];
        }

        // Form fields boş mu kontrol et
        if (formFields.length === 0) {
          developmentError('No form fields found in API response');
          return false;
        }

        // Form yanıtlarını hazırla
        const responses = formFields
          .filter((field) => !checkIfEmpty(formData[field.name]))
          .map((field) => ({
            fieldId: field._id,
            name: field.label.toLowerCase(),
            type: field.type,
            value: formData[field.name],
          }));

        // Form verilerini gönder
        const response = await submitFormResponse({
          formId: ideationFormId,
          formType: 'ideation',
          title: ideationFormData?.data?.title || ideationFormData?.title || 'Ideation Form',
          description: ideationFormData?.data?.description || ideationFormData?.description || '',
          responses,
          lang: userLanguage,
        }).unwrap();

        // Başarılı yanıt alındığında journey tracking'i güncelle
        if (response && (response.status === 'success' || response._id)) {
          // Journey tracking güncellemesi - Kullanıcının profil level'ı değil, seçili seviyeyi kullan
          await updateJourneyTrackingCard({
            userId: user?._id,
            journeyTrackingData: trackingData,
            userLevel: selectedLevel?.toLowerCase(), // Arayüzde seçilen seviyeyi kullan
            cardId: ideationCardId,
          });

          // Verileri yenile
          if (refetchTracking) {
            await refetchTracking();
          }
          if (refetchJourneys) {
            await refetchJourneys();
          }

          // Modal'ı kapat ve form verilerini temizle
          setIsIdeationModalOpen(false);
          setFormData({});
          setFormErrors({});
          setActiveStep(0);
          setIdeationFormId(null);
          setIdeationCardId(null);

          return true;
        }

        return false;
      } catch (error) {
        developmentError('Form submission error:', error);
        return false;
      }
    },
    [
      ideationFormData,
      formData,
      user,
      trackingData,
      selectedLevel,
      ideationCardId,
      ideationFormId,
      setIsIdeationModalOpen,
      setFormData,
      setFormErrors,
      setActiveStep,
      updateJourneyTrackingCard,
      submitFormResponse,
      checkIfEmpty,
      userLanguage,
    ]
  );

  /**
   * Ideation formunun kapatılması
   */
  const handleIdeationModalClose = useCallback(() => {
    setIsIdeationModalOpen(false);
    setFormData({});
    setFormErrors({});
    setActiveStep(0);
    setIdeationFormId(null);
    setIdeationCardId(null);
  }, [
    setIsIdeationModalOpen,
    setFormData,
    setFormErrors,
    setActiveStep,
    setIdeationFormId,
    setIdeationCardId,
  ]);

  /**
   * Form verileri değiştiğinde çalışan fonksiyon
   */
  const handleFormChange = useCallback(
    (newData) => {
      if (!newData || Object.keys(newData).length === 0) {
        return;
      }

      if (newData.__errors) {
        const updatedErrors = { ...formErrors };

        Object.entries(newData.__errors).forEach(([key, value]) => {
          if (value === null) {
            // Hata temizleniyor
            delete updatedErrors[key];
          } else {
            // Yeni hata ekleniyor
            updatedErrors[key] = value;
          }
        });

        // Validasyon hatalarını güncelle
        setFormErrors(updatedErrors);
        delete newData.__errors;
      }

      // Form sayfasındaki veriler
      if (newData.__formState) {
        // Bu durumda, doğrudan tüm form verilerini güncelleyebiliriz
        setFormData(newData.__formState);
        delete newData.__formState;
      } else {
        // __userInteracted ve __formState özel alanlarını temizle
        delete newData.__userInteracted;

        // Form verilerini güncelle
        const updatedFields = Object.keys(newData).filter((k) => k !== '__errors');
        if (updatedFields.length > 0) {
          const updatedFormData = { ...formData };

          updatedFields.forEach((fieldName) => {
            updatedFormData[fieldName] = newData[fieldName];

            // Eğer alan doldurulmuşsa ve bir hata varsa, hatayı temizle
            if (!checkIfEmpty(newData[fieldName]) && formErrors[fieldName]) {
              const updatedErrors = { ...formErrors };
              delete updatedErrors[fieldName];
              setFormErrors(updatedErrors);
            }
          });

          setFormData(updatedFormData);
        }
      }
    },
    [formData, formErrors, checkIfEmpty]
  );

  return {
    isModalOpen,
    setIsModalOpen,
    modalContent,
    setModalContent,
    isJourneyCompleted,
    isPreviousCompleted,
    getFirstClickableCardIndex,
    getTranslationKey,
    handleCardClick,
    handleCardClickTechCompany,
    handleModalClose,
    useCaseData,
    currentUseCaseId,
    // Ideation form için
    isIdeationModalOpen,
    handleIdeationModalClose,
    handleIdeationFormSubmit,
    ideationFormData,
    isIdeationFormLoading,
    isSubmittingForm,
    formData,
    formErrors,
    activeStep,
    setActiveStep,
    handleFormChange,
    // Levels API için
    getCurrentLevelModalContent,
  };
};

export default useJourneyCardHandlers;
