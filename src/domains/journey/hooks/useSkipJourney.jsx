import { useUpdateUserJourneyLevelMutation } from '@/redux/services/user-api';

/**
 * Kullanıcının journey seviyesini atlamak için basit bir hook
 */
const useSkipJourney = () => {
  const [updateUserJourneyLevel, { isLoading }] = useUpdateUserJourneyLevelMutation();

  /**
   * Kullanıcının journey seviyesini günceller
   * @param {string} userId - Kullanıcı ID'si
   * @param {string} targetLevel - Atlanacak seviye (intermediate, advanced, etc.)
   * @returns {Promise} API yanıtı
   */
  const skipToLevel = async (userId, targetLevel) => {
    if (!userId) {
      console.error('User ID is required');
      return { success: false, error: 'User ID is required' };
    }
    let skippedJourney = [];
    if (targetLevel == 'expert') {
      // push beginner to skippedJourney
      skippedJourney.push('beginner');
    }
    if (targetLevel == 'master') {
      skippedJourney.push('expert');
    }
    console.log(skippedJourney);

    try {
      const response = await  updateUserJourneyLevel({
        userId: userId,
        journeyLevel: {
          name: targetLevel,
          translations: {
            en: targetLevel,
            de: targetLevel,
          },
          skippedJourney: skippedJourney,
        },
      }).unwrap();
      console.log('response', response);
      return response;
    } catch (error) {
      console.error('Journey atlama hatası:', error);
      return { success: false, error };
    }
  };

  
  return {
    skipToLevel,
    isLoading
  };
};

export default useSkipJourney;
