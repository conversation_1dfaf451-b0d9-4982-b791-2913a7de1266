import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  useGetUserJourneyLevelQuery,
  useUpdateUserJourneyLevelMutation,
} from '../../../redux/services/user-api';
import { updateUser } from '../../../redux/features/auth/authSlice';
import { setSelectedLevel } from '../../../redux/features/training/trainingSlice';

/**
 * Journey level yönetimini sağlayan custom hook
 * @param {Object} props - Hook parametreleri
 * @param {Object} props.user - Kullanıcı bilgileri
 * @param {string} props.selectedLevel - Seçili seviye
 * @param {Function} props.onJourneyLevelUpdate - Seviye değiştiğinde çağrılacak callback
 * @returns {Object} Journey level yönetim fonksiyonları ve verileri
 */
const useJourneyLevelManagement = ({ user, selectedLevel, onJourneyLevelUpdate }) => {
  const dispatch = useDispatch();
  const [updateUserJourneyLevel, { isLoading: isUpdatingLevel }] =
    useUpdateUserJourneyLevelMutation();

  // Journey level verisini çek
  const { data: journeyLevel, refetch: refetchJourneyLevel } = useGetUserJourneyLevelQuery(
    user?._id,
    {
      skip: !user?._id,
    }
  );

  // İlk kez kaydolan kullanıcılara journey level atama
  useEffect(() => {
    if (user?._id && user?.journeyLevel?.name === undefined) {
      updateUserJourneyLevel({
        userId: user._id,
        journeyLevel: {
          name: 'beginner',
          translations: {
            en: 'Beginner',
            de: 'Anfänger',
          },
        },
      })
        .unwrap()
        .then((response) => {
          if (response?.status === 'success') {
            dispatch(
              updateUser({
                ...user,
                journeyLevel: {
                  name: 'beginner',
                },
              })
            );
            // Kullanıcının seviyesi ayarlandığında, seçili seviyeyi de aynı yap
            if (!selectedLevel) {
              dispatch(setSelectedLevel('beginner'));
            }
          }
        })
        .catch(() => {
          // Hata durumunda sessizce devam et
        });
    }
  }, [user?._id, dispatch, user, updateUserJourneyLevel, selectedLevel]);

  // Seçili seviyeyi kullanıcının seviyesine eşitle
  useEffect(() => {
    if (selectedLevel === null && user?.journeyLevel?.name) {
      dispatch(setSelectedLevel(user.journeyLevel.name));
    }
  }, [user?.journeyLevel?.name, selectedLevel, dispatch]);

  // Journey level değiştiğinde callback çağır
  useEffect(() => {
    if (journeyLevel && onJourneyLevelUpdate) {
      onJourneyLevelUpdate(journeyLevel);
    }
  }, [journeyLevel, onJourneyLevelUpdate]);

  /**
   * Kullanıcının journey seviyesini günceller
   * @param {string} newLevel - Yeni seviye
   * @param {Object} translations - Seviye çevirileri
   * @param {Array} skippedJourney - Atlanan seviyeler
   * @returns {Promise} Güncelleme sonucu
   */
  const updateJourneyLevel = async (newLevel, translations = {}, skippedJourney = []) => {
    if (!user?._id) return { status: 'error', message: 'User ID is required' };

    try {
      const result = await updateUserJourneyLevel({
        userId: user._id,
        journeyLevel: {
          name: newLevel,
          translations,
          ...(skippedJourney.length > 0 ? { skippedJourney } : {}),
        },
      }).unwrap();

      if (result?.status === 'success') {
        dispatch(
          updateUser({
            ...user,
            journeyLevel: {
              name: newLevel,
              translations,
              ...(skippedJourney.length > 0 ? { skippedJourney } : {}),
            },
          })
        );

        dispatch(setSelectedLevel(newLevel));
        refetchJourneyLevel();
        return { status: 'success' };
      }

      return { status: 'error', message: result?.message || 'Update failed' };
    } catch (error) {
      console.error('Journey level update failed:', error);
      return { status: 'error', message: error.message || 'Update failed' };
    }
  };

  // Sayfa yüklendiğinde pendingCardClick kontrolü
  useEffect(() => {
    const pendingCardClick = sessionStorage.getItem('pendingCardClick');
    if (pendingCardClick) {
      try {
        // eslint-disable-next-line no-unused-vars
        const { cardId } = JSON.parse(pendingCardClick);
        // Veriyi kullandıktan sonra temizle
        sessionStorage.removeItem('pendingCardClick');
      } catch (error) {
        console.error('Error processing pendingCardClick:', error);
        sessionStorage.removeItem('pendingCardClick');
      }
    }
  }, []);

  return {
    journeyLevel,
    isUpdatingLevel,
    updateJourneyLevel,
    refetchJourneyLevel,
  };
};

export default useJourneyLevelManagement;
