import { useState, useEffect, useCallback } from 'react';
import { useUpdateUserJourneyLevelMutation } from '@/redux/services/user-api';
import {
  useUpdateSeenModalsMutation,
  useGetJourneyTrackingQuery,
  useGetJourneysQuery,
} from '@/redux/services/journey-api';
import { useDispatch, useSelector } from 'react-redux';
import { updateUser } from '@/redux/features/auth/authSlice';
import { setSelectedLevel } from '@/redux/features/training/trainingSlice';
import { developmentLogs, developmentError, developmentWarn } from '@/utils/developmentLogs';

/**
 * Journey level upgrade işlemlerini yöneten hook
 * @returns {Object} Hook fonksiyonları ve state'leri
 */
const useJourneyLevelUpgrade = () => {
  const [updateUserJourneyLevel] = useUpdateUserJourneyLevelMutation();
  const [updateSeenModals] = useUpdateSeenModalsMutation();
  const user = useSelector((state) => state.auth.user);
  const dispatch = useDispatch();

  // State'ler
  const [showConfetti, setShowConfetti] = useState(false);
  const [showCongratulations, setShowCongratulations] = useState(false);
  const [pendingLevelUpgrade, setPendingLevelUpgrade] = useState(null);
  const [processingAction, setProcessingAction] = useState(false);
  const [completedLevel, setCompletedLevel] = useState(null);

  // Kullanıcının journey tracking verisini çekiyoruz
  const { data: trackingData, refetch: refetchTracking } = useGetJourneyTrackingQuery(user?._id, {
    skip: !user?._id,
  });

  // Component mount olduğunda seenModals verilerini kontrol et
  useEffect(() => {
    if (!user?._id || processingAction || !trackingData?.seenModals) return;

    try {
      // Kullanıcının seviyesini al
      const userLevel = user?.journeyLevel?.name?.toLowerCase();
      if (!userLevel) return;

      // Seviye tamamlanma kontrolü için modal anahtarları
      const cardsCompletedKey = `${userLevel}_cards_completed`;
      const levelCompletionKey = `${userLevel}_completion`;
      const levelUpgradedKey = `${userLevel}_level_upgraded`;

      // Durum 1: Kartlar tamamlanmış ama seviye tamamlanma modalı gösterilmemişse
      if (
        trackingData.seenModals.includes(cardsCompletedKey) &&
        !trackingData.seenModals.includes(levelCompletionKey)
      ) {
        // Tamamlanan seviyeyi belirle
        setCompletedLevel(userLevel);

        // Confetti göster
        setShowConfetti(true);

        // Tebrik modalını göster
        setShowCongratulations(true);

        // Kullanıcının seviyesi yükseltilmemişse, modal kapandığında seviye yükseltecek
        if (!trackingData.seenModals.includes(levelUpgradedKey)) {
          setPendingLevelUpgrade(userLevel);
        }

        developmentLogs(
          'Journey completion modal shown - cards completed but completion modal not seen'
        );
      }
      // Durum 2: Seviye tamamlanma modalı gösterilmiş ama seviye yükseltilmemişse
      // Bu durumu artık kullanmıyoruz, çünkü levelCompletionKey modal kapatıldıktan sonra kaydediliyor
      // ve modal tekrar gösterilmemeli
    } catch (error) {
      developmentError('Error checking seenModals for journey completion:', error);
    }
  }, [user?._id, user?.journeyLevel?.name, processingAction, trackingData?.seenModals]);

  /**
   * Modal anahtarları için tutarlı bir format sağlar
   */
  const getModalKey = useCallback((level, action) => {
    return `${level.toLowerCase()}_${action}`;
  }, []);

  /**
   * Kullanıcının gördüğü modalları kaydetmek için API'ye istek gönderir
   */
  const updateModalSeen = useCallback(
    async (userId, level, action) => {
      if (!userId) return;

      try {
        const modalKey = getModalKey(level, action);

        // Eğer modal daha önce görülmemişse güncelle
        if (!trackingData?.seenModals?.includes(modalKey)) {
          await updateSeenModals({
            userId,
            modalKey,
          });

          // Tracking verilerini güncelle
          refetchTracking();
        }
      } catch (error) {
        developmentError('Modal bilgisini güncellerken hata oluştu:', error);
      }
    },
    [trackingData, updateSeenModals, refetchTracking, getModalKey]
  );

  /**
   * Kullanıcının journey seviyesini yükseltir
   */
  const performLevelUpgrade = useCallback(
    async (fromLevel) => {
      if (!user?._id || processingAction) return false;

      setProcessingAction(true);

      try {
        // Son seviyedeyse yükseltme yapmaz
        if (fromLevel.toLowerCase() === 'master') {
          setProcessingAction(false);
          return false;
        }

        // Bir sonraki seviyeyi belirle
        const nextLevel = fromLevel.toLowerCase() === 'beginner' ? 'expert' : 'master';

        // Seviye yükseltme API isteği
        const response = await updateUserJourneyLevel({
          userId: user._id,
          journeyLevel: {
            name: nextLevel,
            translations: {
              en: nextLevel.charAt(0).toUpperCase() + nextLevel.slice(1),
              de: nextLevel === 'expert' ? 'Experte' : 'Meister',
              tr: nextLevel === 'expert' ? 'Uzman' : 'Usta',
            },
          },
        }).unwrap();

        if (response?.status === 'success') {
          // Redux store'daki user bilgisini güncelle
          dispatch(
            updateUser({
              ...user,
              journeyLevel: {
                name: nextLevel,
              },
            })
          );

          // Seçili seviyeyi güncelle
          dispatch(setSelectedLevel(nextLevel));

          // Yükseltme kaydını tut
          updateModalSeen(user._id, fromLevel, 'level_upgraded');

          setProcessingAction(false);
          return true;
        }

        setProcessingAction(false);
        return false;
      } catch (error) {
        developmentError('Seviye yükseltmede hata:', error);
        setProcessingAction(false);
        return false;
      }
    },
    [user, processingAction, updateUserJourneyLevel, dispatch, updateModalSeen]
  );

  /**
   * Tebrik modalını kapatır
   */
  const handleCloseCongratulations = useCallback(() => {
    setShowConfetti(false);
    setShowCongratulations(false);

    // Eğer tamamlanan seviye varsa, seviyenin tamamlandığını kaydet
    if (completedLevel && !processingAction) {
      // Seviyenin tamamlandığını kaydet
      updateModalSeen(user._id, completedLevel, 'completion');

      // Eğer bekleyen seviye yükseltme varsa, şimdi gerçekleştir
      if (pendingLevelUpgrade) {
        setPendingLevelUpgrade(null);
        performLevelUpgrade(pendingLevelUpgrade);
      }
    }

    // Tamamlanan seviyeyi temizle
    setCompletedLevel(null);

    developmentLogs('Journey completion modal closed');
  }, [
    pendingLevelUpgrade,
    processingAction,
    performLevelUpgrade,
    completedLevel,
    updateModalSeen,
    user,
  ]);

  /**
   * Seviye tamamlandığında yapılan işlemler
   */
  const handleLevelCompletion = useCallback(
    async (level, refetchJourneys) => {
      if (processingAction || !user?._id) return;

      setProcessingAction(true);

      try {
        // Tamamlanan seviyeyi belirle
        setCompletedLevel(level);

        // Confetti göster
        setShowConfetti(true);

        // Tebrik modalını göster
        setShowCongratulations(true);

        // Kullanıcının gerçek seviyesi bu seviye ise, modal kapandığında seviye yükseltecek
        if (user.journeyLevel?.name?.toLowerCase() === level.toLowerCase()) {
          setPendingLevelUpgrade(level);
        }

        // Verileri yenile
        if (refetchJourneys) {
          setTimeout(() => {
            refetchJourneys();
          }, 500);
        }

        setProcessingAction(false);
      } catch (error) {
        developmentError('Seviye tamamlama işleminde hata:', error);
        setProcessingAction(false);
      }
    },
    [user, processingAction]
  );

  /**
   * Bir journey seviyesindeki tüm kartların tamamlanıp tamamlanmadığını kontrol eder
   */
  const checkAndHandleLevelCompletion = useCallback(
    ({ journeyData, trackingData, selectedLevel, user, refetchJourneys }) => {
      if (
        !journeyData?.length ||
        !trackingData ||
        !selectedLevel ||
        !user?._id ||
        processingAction
      ) {
        return;
      }

      // Temel değişkenleri belirle
      const levelKey = selectedLevel.toLowerCase();
      const userLevel = user?.journeyLevel?.name?.toLowerCase();
      const journeyCardCount = journeyData.length;
      const completedCount = (trackingData[levelKey] || []).length;

      // Tüm kartlar tamamlandı mı?
      const allCardsCompleted = completedCount >= journeyCardCount && journeyCardCount > 0;

      // Seviye tamamlanma kontrolü için modal anahtarları
      const levelCompletionKey = getModalKey(levelKey, 'completion');
      const cardsCompletedKey = getModalKey(levelKey, 'cards_completed');

      // Daha önce işaretlenmiş mi?
      const hasSeenCompletionModal = trackingData?.seenModals?.includes(levelCompletionKey);
      const hasMarkedCardsCompleted = trackingData?.seenModals?.includes(cardsCompletedKey);

      // Debug
      developmentLogs({
        levelKey,
        journeyCardCount,
        completedCount,
        allCardsCompleted,
        hasSeenCompletionModal,
        hasMarkedCardsCompleted,
        userLevel,
      });

      // Tüm kartlar tamamlandı ve daha önce işaretlenmemişse
      if (allCardsCompleted && !hasMarkedCardsCompleted) {
        // Kartların tamamlandığını kaydet
        updateModalSeen(user._id, levelKey, 'cards_completed');

        // Eğer tamamlama modalı daha önce gösterilmediyse
        if (!hasSeenCompletionModal) {
          // Kullanıcı şu anda görüntülenen seviyeyle aynı seviyedeyse veya admin ise
          const isUserOnSameLevel = userLevel === levelKey;
          const isAdmin = user?.role?.toLowerCase() === 'administrator';

          if (isUserOnSameLevel || isAdmin) {
            // Seviye tamamlama işlemlerini başlat
            handleLevelCompletion(levelKey, refetchJourneys);
          }
        }
      }
    },
    [processingAction, getModalKey, updateModalSeen, handleLevelCompletion]
  );

  // Hook çıktısı
  return {
    showConfetti,
    showCongratulations,
    completedLevel,
    handleCloseCongratulations,
    checkAndUpgradeLevel: checkAndHandleLevelCompletion,
  };
};

export default useJourneyLevelUpgrade;
