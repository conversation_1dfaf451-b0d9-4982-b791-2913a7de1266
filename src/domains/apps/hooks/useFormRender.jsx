import { useState, useEffect } from 'react';
import { FormControl, InputLabel, Select, MenuItem, TextField } from '@mui/material';
import { transformHtmlContent } from '@/middleware/htmlTransformer';

const useFormRender = (data, i18n, defaultDescription) => {
  const [formData, setFormData] = useState({});

  const getLocalizedData = () => {
    let languageKey = 'english';

    if (i18n.language === 'de' || i18n.language === 'german' || i18n.language === 'deutsch') {
      languageKey = 'german';
    }

    let localData;
    if (data.translations && data.translations[languageKey]) {
      localData = { ...data.translations[languageKey] };
    } else if (data.translations && data.translations.english) {
      localData = { ...data.translations.english };
    } else if (data['dall-e_settings']) {
      // Course/learn sayfaları için direkt data yapısını kullan
      localData = {
        title: data.title,
        description: data.description || data.content || defaultDescription,
        'dall-e_settings': data['dall-e_settings'],
        _id: data._id,
        api_type: data.api_type || 'dalle',
      };
    } else {
      localData = {
        title: data.title,
        description: data.content || defaultDescription,
        'dall-e_settings': data['dall-e_settings'] || data.dalle_settings,
        _id: data._id,
      };
    }

    // Description'ı dönüştür
    if (localData.description) {
      localData.description = transformHtmlContent(localData.description);
    }

    // Form fields'ları dil seçimine göre getir
    if (data.api_type === 'dalle' || localData.api_type === 'dalle') {
      localData['dall-e_settings'] = {
        ...localData['dall-e_settings'],
        form_fields:
          localData['dall-e_settings']?.form_fields || data['dall-e_settings']?.form_fields || [],
      };
    } else if (data.api_type === 'completions') {
      localData.completions_settings = {
        ...localData.completions_settings,
        form_fields:
          localData.completions_settings?.form_fields ||
          data.completions_settings?.form_fields ||
          [],
      };
    } else if (data.api_type === 'stream') {
      localData.stream_settings = {
        ...localData.stream_settings,
        form_fields:
          localData.stream_settings?.form_fields ||
          data.stream_settings?.form_fields ||
          data.stream_settings?.stream_form?.form_fields ||
          [],
      };
    } else if (data.api_type === 'assistants') {
      localData.assistants_settings = {
        ...localData.assistants_settings,
        form_fields: localData.assistants_settings?.form_fields || [],
      };
    }

    return localData;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const renderFormField = (field) => {
    switch (field.type) {
      case 'select': {
        const options = field.choices
          .split(/\r\n|\n/)
          .map((option) => option.trim())
          .filter((option) => option.length > 0);

        return (
          <div key={field.name}>
            <FormControl
              fullWidth
              key={field.name}
              className="form-control-no-margin select-field"
              sx={{ mb: 3 }}
            >
              <InputLabel>{field.label}</InputLabel>
              <Select
                label={field.label}
                name={field.name}
                value={formData[field.name] || ''}
                onChange={handleInputChange}
              >
                {options.map((option, optionIndex) => (
                  <MenuItem key={`${field.name}-${optionIndex}`} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>
        );
      }

      case 'textarea': {
        return (
          <div key={field.name}>
            <FormControl
              fullWidth
              key={field.name}
              className="form-control-no-margin textarea-field"
              sx={{ mb: 3 }}
            >
              <TextField
                label={field.label}
                name={field.name}
                value={formData[field.name] || ''}
                onChange={handleInputChange}
                variant="outlined"
                multiline
                rows={4}
                InputProps={{
                  sx: {
                    '& .MuiInputBase-input': {
                      resize: 'vertical',
                    },
                  },
                }}
              />
            </FormControl>
          </div>
        );
      }

      case 'text':
      default: {
        return (
          <div key={field.name}>
            <FormControl
              fullWidth
              key={field.name}
              className="form-control-no-margin text-field"
              sx={{ mb: 3 }}
            >
              <TextField
                label={field.label}
                name={field.name}
                value={formData[field.name] || ''}
                onChange={handleInputChange}
                variant="outlined"
              />
            </FormControl>
          </div>
        );
      }
    }
  };

  const formFields = (() => {
    const localizedData = getLocalizedData();

    if (data.api_type === 'dalle') {
      return localizedData['dall-e_settings']?.form_fields || [];
    } else if (data.api_type === 'completions') {
      return (
        localizedData.completions_settings?.form_fields ||
        data.completions_settings?.form_fields ||
        []
      );
    } else if (data.api_type === 'stream') {
      return localizedData.stream_settings?.form_fields || [];
    } else if (data.api_type === 'assistants') {
      return localizedData.assistants_settings?.form_fields || [];
    }

    return [];
  })();

  // Form initialization
  useEffect(() => {
    const localizedData = getLocalizedData();
    const fields =
      data.api_type === 'dalle'
        ? localizedData['dall-e_settings']?.form_fields
        : data.api_type === 'completions'
          ? localizedData.completions_settings?.form_fields
          : data.api_type === 'stream'
            ? localizedData.stream_settings?.form_fields
            : localizedData.assistants_settings?.form_fields;

    if (!fields) return;

    const initialFormData = {};
    fields.forEach((field) => {
      if (field.default_value) {
        initialFormData[field.name] = field.default_value.trim();
      } else if (field.type === 'select') {
        const options = field.choices
          .split(/\r\n|\n/)
          .map((option) => option.trim())
          .filter((option) => option.length > 0);

        if (options.length > 0) {
          initialFormData[field.name] = options[0];
        }
      }
    });
    setFormData(initialFormData);
  }, [data, i18n.language]);

  return {
    formData,
    setFormData,
    renderFormField,
    formFields,
    handleInputChange,
    getLocalizedData,
  };
};

export default useFormRender;
