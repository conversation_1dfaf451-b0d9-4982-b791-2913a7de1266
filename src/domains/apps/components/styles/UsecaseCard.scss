.usecase-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  background-color: white;
  display: flex;
  font-family: Inter, sans-serif;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #677788;

  @media screen and (max-width: 768px) {
    flex-direction: column;
  }

  .ai-btn {
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 4px;
    background-color: #fff;
    color: #333;
    border: 1px solid #333;
    transition: all ease .3s;

    &-primary {
      background-color: #377dff;
      color: #fff;
      border-color: #2b6be0;
    }

    &-danger {
      background-color: rgb(235, 71, 71);
      color: #fff;
      border-color: rgb(207, 54, 54);
      &:disabled {
        transition: all ease .3s;
        background-color: rgba(235, 71, 71, .5);
        cursor: disabled;
      }
    }

    &.loading {
      cursor: not-allowed;
      &:disabled {
        cursor: disabled;
      }
    }

    span {
      pointer-events: none;
    }
  }

  // Utility classes
  .d-block { display: block; }
  .d-none { display: none; }
  .d-flex { display: flex; }
  .o-0 { opacity: 0; }
  .o-1 { opacity: 1; }

  // Content Section
  .usecase-content {
    flex: 0 0 50%;
    padding: 0;
    width: 50%;
    border: 1px solid #ECEDE8;
    border-right: none;
    border-radius: 12px 0 0 12px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    @media screen and (max-width: 768px) {
      width: 100%;
      border-right: 1px solid #ECEDE8;
      border-radius: 12px 12px 0 0;
    }

    &-header {
      padding: 24px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #ECEDE8;

      h1, h2, h3 {
        display: block;
        padding: 0;
        margin: 0;
        color: #000;
        font-size: 30px;
        font-weight: 500;
      }

      img, svg {
        width: 64px !important;
        height: auto !important;
        object-fit: contain;
        background-color: #E5ECFF;
        border-radius: 12px;
        padding: 8px 10px;
        margin-right: 10px;
      }
    }

    &-body {
      padding: 15px 24px;
      flex: 1;

      &-desc {
        p { margin: 0; }
        ul {
          padding-left: 15px;
          margin: 0;
        }
      }

      &-form {
        display: flex;
        flex-direction: column;
        margin-top: 15px;

        .form-item {
          margin-bottom: 15px;
          width: 100%;
          display: flex;
          flex-direction: column;

          label {
            width: 100%;
            margin: 0 0 10px;
            display: block;
            color: #000;
            font-size: 20px;
            font-weight: 500;
          }

          input, textarea {
            display: block;
            border-radius: 12px;
            border: 1px solid #ECEDE8;
            color: var(--text-body, #677788);
            font-size: 0.875rem;
            outline: none;
            font-weight: 400;
            padding: 14px 24px;
            line-height: 21px;
          }

          textarea {
            font-family: inherit;
            line-height: inherit;
            box-sizing: border-box;
            width: 100%;
            display: flex;
            height: 100%;
            resize: vertical;
            min-height: 100px;

            &::-webkit-scrollbar {
              background-color: rgba(200, 228, 222, .25);
              width: 6px;
              border-radius: 8px;
            }
            &::-webkit-scrollbar-thumb {
              background: rgba(150, 238, 220, 0.72);
            }
          }

          select {
            display: block;
            width: 100%;
            border-radius: 12px;
            padding: 16px 24px;
            resize: none;
            border: 1px solid #ECEDE8;
            color: var(--text-body, #677788);
            background-image: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='%23677788' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.72,15.78a.75.75,0,0,1-.53.22h-.38a.77.77,0,0,1-.53-.22L6.15,10.64a.5.5,0,0,1,0-.71l.71-.71a.49.49,0,0,1,.7,0L12,13.67l4.44-4.45a.5.5,0,0,1,.71,0l.7.71a.5.5,0,0,1,0,.71Z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 16px center;
            background-size: 1rem 1rem;
            font-size: 12px;
            outline: none;
            font-weight: 400;
            line-height: 21px;
            -webkit-appearance: none;
            -moz-appearance: none;
            text-indent: 1px;
            text-overflow: '';
          }
        }
      }

      .pdf-viewer-container {
        height: 100%;
        min-height: 400px;
        
        .pdf-viewer {
          height: 100%;
          display: flex;
          flex-direction: column;

          .pdf-controls {
            display: flex;
            align-items: center;
            padding: 8px;
            gap: 8px;
            border-bottom: 1px solid #ECEDE8;
          }

          .canvas-container {
            flex: 1;
            overflow: auto;
            padding: 16px;
            display: flex;
            justify-content: center;
            background-color: rgba(150, 238, 220, 0.12);

            canvas {
              background-color: white;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            &::-webkit-scrollbar {
              background-color: rgba(200, 228, 222, .25);
              width: 6px;
              border-radius: 8px;
            }
            &::-webkit-scrollbar-thumb {
              background: rgba(150, 238, 220, 0.72);
            }
          }
        }
      }
    }

    &-footer {
      padding: 15px 24px;

      .usecase-field-form-submission {
        padding: 16px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left small {
          font-size: 11px;
        }

        .right {
          display: flex;
          align-items: center;
          justify-content: space-between;
          flex: 0 0 auto;

          small {
            font-size: 10px;
            animation: blinker 1s linear infinite;
          }

          .usecase-field-form-submit,
          .usecase-field-form-cancel {
            margin-left: 10px;
          }
        }
      }
    }
  }

  // Chat Section
  .usecase-chat {
    border: 1px solid #ECEDE8;
    padding: 24px 16px 24px 24px;
    flex: 0 0 50%;
    width: 50%;
    background-color: rgba(150, 238, 220, 0.12);
    border-radius: 0 12px 12px 0;
    box-sizing: border-box;
    height: auto;

    @media screen and (max-width: 768px) {
      width: 100%;
      border-radius: 0 0 12px 12px;
    }

    .inner {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    &-header {
      padding-bottom: 10px;
      h1, h2, h3, h4 {
        margin: 0;
        display: block;
        font-weight: 600 !important;
        color: #000;
        font-size: 15px;
        line-height: 20px;
      }
    }

    &-body {
      overflow-y: scroll;
      flex: 1;
      box-sizing: border-box;
      padding-right: 6px;

      &::-webkit-scrollbar {
        background-color: rgba(200, 228, 222, .25);
        width: 6px;
        border-radius: 8px;
      }
      &::-webkit-scrollbar-thumb {
        background: rgba(150, 238, 220, 0.72);
      }

      &-item-content {
        animation: blink-caret 0.75s step-end infinite;
        line-height: 1.5;
        font-size: 15px;
        p {
          font-size: 15px;
          margin: 10px 0;
        }
      }
    }
  }
}

@keyframes blinker {
  50% { opacity: 0; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: black; }
} 