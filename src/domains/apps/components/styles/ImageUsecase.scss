@use "sass:color";
@use '../../../../styles/abstracts/variables' as *;

.image-usecase {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
  background-color: $bg-default;
  height: auto; // Header ve margin için alan b<PERSON>
  padding: 0px !important;
  margin: 24px 0px;

  .form-card, .output-card {
    height: calc(100vh - 180px); // Container ile aynı yükseklik
    box-shadow: $shadow-md;
    border-radius: $border-radius-md;
    background-color: $bg-paper;
    display: flex;
    flex-direction: column;
    overflow: hidden; // Taşmaları engelle
  }

  // Aspect Ratio Toggle butonları için stiller
  .MuiToggleButtonGroup-root {
      .MuiSvgIcon-root {
        font-size: $font-size-md;
        color: $text-secondary;
        margin-right: $spacing-1;
      }

      &:hover {
        background-color: rgba(80, 80, 80, 0.05);
      }
  
  }

  .card-header {
    padding: $spacing-3 $spacing-4;
    flex-shrink: 0;

    .title {
      font-weight: $font-weight-semibold;
      color: $text-primary;
      font-size: $font-size-lg;
      margin-right: 5px;
    }

    .subtitle {
      color: $text-secondary;
      margin-top: $spacing-1;
      font-size: $font-size-sm;
    }

    .helper-icon {
      color: $text-secondary;
      cursor: help;
      transition: color 0.2s ease;
    }

    border-bottom: 1px solid $divider-color;
  }

  .card-content {
    background-color: #fcfbfb;
    flex: 1;
    min-height: 0; // Flex child için önemli
    display: flex;
    flex-direction: column;

    &.output-content {
      overflow-y: auto;
      scroll-behavior: smooth; // Smooth scroll için
      // Blueprint tarzı gelişmiş arka plan - gri tonlarla
      background-color: #fafafa;
      background-image: 
        // Ana grid çizgileri
        linear-gradient(rgba(80, 80, 80, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(80, 80, 80, 0.05) 1px, transparent 1px),
        // Alt grid çizgileri
        linear-gradient(rgba(80, 80, 80, 0.025) 0.5px, transparent 0.5px),
        linear-gradient(90deg, rgba(80, 80, 80, 0.025) 0.5px, transparent 0.5px);
      background-size: 20px 20px, 20px 20px, 5px 5px, 5px 5px;
      background-position: center center;
      border-radius: $border-radius-md;
      position: relative;


      // Output içindeki tüm içeriği üste çıkar
      & > * {
        position: relative;
        z-index: 1;
      }

      .empty-output-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        gap: $spacing-2;
        opacity: 0.9;
        text-align: center;
        padding: $spacing-4;
        position: relative;
        z-index: 2;

        // Aspect ratio önizleme alanı
        .aspect-ratio-preview {
          width: 100%;
          max-width: 450px;
          padding: $spacing-3;

          &__container {
            position: relative;
            overflow: visible;

          }
          
        }

        .preview-image-svg {
          width: 120px;
          height: 120px;
          background: transparent;
          box-shadow: none;
          border-radius: 8px;
          padding: 0px;
          border: none;
          margin-bottom: 0.5rem;
          transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .empty-message-text {
          color: $text-secondary;
          text-align: center;
        }

        .info-icon {
          font-size: 48px;
          color: $text-secondary;
          margin-bottom: $spacing-2;
        }
      }

      // Görsel gösterildiğinde arka plan efekti
      img {
        max-width: 100%;
        border-radius: 8px;
        box-shadow: 0 8px 30px rgba(80, 80, 80, 0.12);
        transition: all 0.3s ease;
      
      }

      // Görsel içeren kutu stillendirilmesi - Portrait görseller için ek düzenlemeler
      img + div, .image-container {
        z-index: 2;
        padding: 0;
        text-align: center;
        background: transparent;
      }
    }

    form {
      flex: 1;
      overflow-y: unset;
      padding-right: 0;
      padding: 4px 8px;
      scroll-behavior: smooth; // Smooth scroll için

      &::-webkit-scrollbar {
        width: 8px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f5f5f5;
        border-radius: 4px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #d0d0d0;
        border-radius: 4px;
        
        &:hover {
          background: #bdbdbd;
        }
      }
      
      .MuiInputLabel-root.MuiInputLabel-shrink {
        transform: translate(14px, -9px) scale(0.75)!important; 
    }

    .MuiTextField-root .MuiInputBase-root input {
      font-size: 14px;
    }

    .MuiTextField-root .MuiInputBase-root input::placeholder {
      font-size: 14px;
    }

    }

    .form-control {
      margin-bottom: 20px;
    }


  }

  .card-actions {
    justify-content: space-between;
    padding: $spacing-3 $spacing-4;
    border-top: 1px solid $divider-color;
    flex-shrink: 0;

    &.output-actions {
      justify-content: flex-end;
    }
  }

  .model-info {
    color: $text-secondary;
    font-size: $font-size-xs;

    .model-name {
      font-weight: $font-weight-medium;
      color: $text-primary;
    }
  }

  .loading-text {
    font-size: $font-size-xs;
    color: $text-secondary;
  }

  // Generate ve Cancel butonları için stiller
  .generate-button {
    background-color: rgba(80, 80, 80, 0.9) !important;
    color: white !important;
    transition: all 0.3s ease !important;
    text-transform: none !important;
    font-weight: 500 !important;
    box-shadow: 0 2px 8px rgba(80, 80, 80, 0.2) !important;
    
    .MuiSvgIcon-root {
      animation: sparkle 2s infinite ease-in-out;
      transition: all 0.3s ease;
    }
    
    @keyframes sparkle {
      0% { transform: rotate(0deg) scale(1); }
      50% { transform: rotate(20deg) scale(1.1); }
      100% { transform: rotate(0deg) scale(1); }
    }
    
    &:hover {
      background-color: rgba(60, 60, 60, 0.95) !important;
      box-shadow: 0 4px 12px rgba(60, 60, 60, 0.3) !important;
      
      .MuiSvgIcon-root {
        animation-duration: 1s;
      }
    }
    
    &:disabled {
      background-color: rgba(80, 80, 80, 0.4) !important;
      color: rgba(255, 255, 255, 0.8) !important;
      
      .MuiSvgIcon-root {
        animation: none;
      }
    }
  }
  
  // Cancel butonu için stil
  .MuiButton-outlined.MuiButton-outlinedError {
    border-color: rgba(80, 80, 80, 0.3) !important;
    color: rgba(80, 80, 80, 0.8) !important;
    text-transform: initial;
    
    &:hover {
      background-color: rgba(80, 80, 80, 0.05) !important;
      border-color: rgba(80, 80, 80, 0.5) !important;
    }
  }

  .action-button {
    background-color: $bg-light;
    
    &:hover {
      background-color: $bg-light-dark;
    }

    .MuiSvgIcon-root {
      font-size: $font-size-md;
      color: $text-secondary;

      &.success {
        color: $success-color;
      }
    }
  }


  img {
    width: 100%;
    max-width: 100%;
    border-radius: 8px;
    box-shadow: $shadow-sm;
    transition: transform 0.2s ease-in-out;
    
    &:hover {
      transform: scale(1.01);
    }
  }

  .aspect-ratio-preview {
    transition: all 0.3s ease;
    
    &__container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 180px;
      overflow: hidden;
    }

    &__info {
      margin-top: 8px;
      text-align: center;
    }
  }
  
  // Output area styles
  .output-content {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .empty-output-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 24px;
    width: 100%;
    
    .info-icon {
      font-size: 48px;
      color: rgba(0, 0, 0, 0.2);
      margin-bottom: 16px;
    }
    
    // Çıktı bölgesinde gösterilen aspect ratio önizlemesi için stiller
    .aspect-ratio-preview {
      width: 100%;
      max-width: 600px;
      margin: 0 auto;
      
      &__container {
        position: relative;
        overflow: hidden;
      }
      

      
      &__info {
        
        .MuiTypography-root {
          opacity: 0.9;
          transition: opacity 0.2s ease;
        }
        
        &:hover .MuiTypography-root {
          opacity: 1;
        }
      }
    }
  }
  
  // Form styles improvements
  .form-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    

  }
  
  // Toggle button styles
  .MuiToggleButtonGroup-root {
    .MuiToggleButton-root {
      text-transform: none;
      font-weight: 500;
      
      &.Mui-selected {
        color: #3c3c3c;
        background-color: rgba(80, 80, 80, 0.12);
        
        &:hover {
          background-color: rgba(80, 80, 80, 0.18);
        }
        
        .MuiSvgIcon-root {
          color: #3c3c3c;
        }
      }
    }
  }

  // Output card styles
  .output-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .card-content {
      padding: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
      
      &.output-content {
        // ... existing code ...
      }
    }
  }

  .resizable-textarea {
    width: 100%;
    
    .MuiInputBase-root {
      resize: vertical !important;
      min-height: 120px !important;
      transition: all 0.2s ease;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 2px;
        right: 2px;
        width: 10px;
        height: 10px;
        cursor: se-resize;
        background: 
          linear-gradient(135deg, transparent 50%, rgba(0,0,0,0.1) 50%),
          linear-gradient(135deg, transparent 55%, rgba(0,0,0,0.15) 55%);
      }
    }
    
    .MuiInputBase-inputMultiline {
      min-height: 80px;
      padding-bottom: 8px;
    }
  }
}

// Form ve Output Container Stilleri
.form-grid-container {
  display: flex;
  flex-direction: column;
}

.form-card-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.scrollable-form-content {
  max-height: 500px;
  overflow: auto;
  height: auto;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
    
    &:hover {
      background: #aaa;
    }
  }
}

.output-grid-container {
  display: flex;
  flex-direction: column;
}

.output-card-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.fixed-output-content {
  height: calc(100vh - 280px);
  min-height: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  .image-box {
    flex: 1;
    width: 100%;
    min-height: 320px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    img {
      width: auto;
      
      &.portrait-image {
        height: auto;
        width: auto;
        max-width: min(90%, 400px);
        object-position: center;
      }
    }
  }
}

// Görsel için özel container stili
.output-content {
  .image-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    max-height: 320px;
    padding: 20px !important;
    position: relative;
    
    @media (max-height: 800px) {
      padding: 12px !important;
    }
    
    img {
      object-fit: contain;
      max-height: inherit;
      width: auto;
      max-width: 100%;
      transition: all 0.3s ease;
      margin: 0 auto;
      padding: 10px;
      background: white;
      border-radius: 8px;
      border: #eceaea solid  2px;
      box-shadow: $shadow-md;
      cursor: pointer;
      
      // Portrait görseller için özel stil
      &.portrait-image {
        max-height: 350px;
        height: auto;
        width: auto;
        max-width: min(90%, 400px);
        object-position: center;
        
        @media (max-height: 800px) {
          max-height: calc(100vh - 380px);
        }
      }
    }
  }
}


// Thumbnails container styling
.thumbnails-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  padding: 15px 10px;
  border-top: 1px dashed rgba(80, 80, 80, 0.15);
  margin-top: 15px;
  max-width: 100%;
  overflow-x: auto;
  
  &::-webkit-scrollbar {
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(240, 240, 240, 0.5);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(180, 180, 180, 0.5);
    border-radius: 3px;
    
    &:hover {
      background: rgba(150, 150, 150, 0.6);
    }
  }
  
  .thumbnail-item {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid rgba(200, 200, 200, 0.3);
    transition: all 0.2s ease;
    cursor: pointer;
    background-color: rgba(245, 245, 245, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(80, 80, 80, 0.08);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(80, 80, 80, 0.15);
      border-color: rgba(180, 180, 180, 0.5);
    }
    
    &.selected {
      border-color: rgba(80, 80, 80, 0.7);
      box-shadow: 0 4px 10px rgba(80, 80, 80, 0.2);
      
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 2px solid rgba(80, 80, 80, 0.5);
        box-sizing: border-box;
        border-radius: 4px;
        pointer-events: none;
      }
      
      .thumbnail-overlay {
        opacity: 1;
        background-color: rgba(80, 80, 80, 0.15);
      }
    }
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.2s ease;
      
      &.portrait-thumb {
        object-fit: contain;
        background-color: rgba(240, 240, 240, 0.5);
        padding: 2px;
      }
    }
    
    .thumbnail-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      padding: 2px 0;
      background-color: rgba(0, 0, 0, 0.2);
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.2s ease;
      opacity: 0.7;
      
      .MuiTypography-caption {
        color: white;
        font-weight: 600;
        font-size: 0.7rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }
  }
  
  .thumbnail-add {
    width: 80px;
    height: 80px;
    border-radius: 6px;
    border: 2px dashed rgba(180, 180, 180, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    background-color: rgba(240, 240, 240, 0.3);   

    &:hover {
      background-color: rgba(240, 240, 240, 0.8);
      border-color: rgba(120, 120, 120, 0.5);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(80, 80, 80, 0.1);
    }
  }
}

// Loading container styling
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  
  .loading-thumbnail-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .loading-thumbnail {
      width: 120px;
      height: 120px;
      border-radius: 8px;
      border: 2px dashed rgba(180, 180, 180, 0.4);
      background-color: rgba(240, 240, 240, 0.5);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      animation: pulse 1.5s infinite ease-in-out;
      
      @keyframes pulse {
        0% { background-color: rgba(240, 240, 240, 0.5); }
        50% { background-color: rgba(240, 240, 240, 0.8); }
        100% { background-color: rgba(240, 240, 240, 0.5); }
      }
    }
  }
}

// Lightbox modalı için stil tanımlamaları
.lightbox-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
  
  .lightbox-container {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    outline: none;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .lightbox-close-button {
      position: absolute;
      top: -45px;
      right: -10px;
      background-color: rgba(0, 0, 0, 0.5);
      color: white;
      z-index: 10;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.7);
      }
    }
    
    .lightbox-image {
      max-width: 90vw;
      max-height: 85vh;
      object-fit: contain;
      border-radius: 4px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
  }
}

// Zoom overlay stillerini scss dosyasına ekleyelim
.image-zoom-overlay {
  cursor: pointer;
  z-index: 10;
  opacity: 1 !important; // Her zaman görünür olsun
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .zoom-icon-container {
    transform: scale(1);
    transition: transform 0.2s ease;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    padding: 10px;
    display: flex;
    align-items: center;
    
    &:hover {
      transform: scale(1.1);
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);    
    }
  }
}

// Mobil animasyonları da kaldıralım
// @media (hover: none) {
//   .image-zoom-overlay {
//     opacity: 1 !important;
//     animation: fadeInOut 3s ease-in-out forwards;
//   }
//   
//   @keyframes fadeInOut {
//     0% { opacity: 0; }
//     20% { opacity: 1; }
//     80% { opacity: 1; }
//     100% { opacity: 0; }
//   }
// } 