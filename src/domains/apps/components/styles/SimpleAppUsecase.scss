.usecase-container {
  display: flex;
  gap: 0;
  padding: 16px; 
  .usecase-content {
    box-shadow: unset;
    &-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 24px;

      .title-inner h2 {
        margin: 0;
        font-size: 1.5rem;
      }
    }

    &-body {
      margin-bottom: 24px;

      &-desc {
        margin-bottom: 24px;
      }

      &-form {
        gap: 6px;

        .form-item {
          margin-bottom: 16px;

          label {
            display: block;
            margin-bottom: 8px;
          }

          .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
          }
        }
      }
    }

    &-footer {
      small {
        display: block;
        margin-bottom: 16px;
        color: #666;
      }

      .loading-text {
        font-size: 10px;
        animation: blinker 1s linear infinite;
      }
      @keyframes blinker {
        50% { opacity: 0; }
      }

      .usecase-field-form-submission {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .model-info {
          color: #666;
          .model-name {
            font-weight: 500;
          }
        }

        .right {
          display: flex;
          align-items: center;
          gap: 16px;
        }
      }
    }
  }

  .usecase-chat {
    .inner {
      padding: 24px;
      height: 100%;
      display: flex;
      flex-direction: column;

      .usecase-chat-header {
        margin-bottom: 16px;
        h3 {
          margin: 0;
          font-size: 1.25rem;
        }
      }

      .usecase-chat-body {
       margin-bottom: 36px;
        border-radius: 4px;
        flex: 1;
        overflow-y: auto; 
      }
    }
  }
} 