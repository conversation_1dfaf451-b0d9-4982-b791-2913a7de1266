@use "sass:color";
@use '../../../../styles/abstracts/variables' as *;

.text-usecase {

  display: flex;
  flex-direction: column;
  gap: $spacing-4;
  background-color: $bg-default;
  padding: 0px !important;
  margin: $spacing-4 0px $spacing-6 0px !important; // Alt margin ekliyoruz

  // Devre dışı butonlar için soluk görünüm
  .faded-button {
    opacity: 0.5;
    transition: opacity 0.2s ease;
    
    &:hover {
      opacity: 0.7;
    }
  }

  .text-form-card-container {
    max-height: 500px !important;
    overflow-y: auto;
    transition: max-height 0.3s ease-in-out;
    position: relative;
    
    &.expanded {
      max-height: none !important;
      
      .show-more-container {
        background: none;
        
        &::before {
          display: none;
        }
      }
    }
    
    &::-webkit-scrollbar {
      width: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: $bg-light;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: $bg-light-dark;
      border-radius: 4px;
      
      &:hover {
        background: color.adjust($bg-light-dark, $lightness: -5%);
      }
    }

    .show-more-container {
      position: sticky;
      bottom: 0;
      left: 0;
      right: 0;
      padding: $spacing-2;
      text-align: center;
      
      &::before {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 0;
        right: 0;
        height: 40px;
        background: linear-gradient(
          to top,
          rgba($bg-paper, 0.3),
          rgba($bg-paper, 0)
        );
      }

      .show-more-button {
        color: rgba(0, 0, 0, 0.6);
        background-color: rgba(0, 0, 0, 0.04);
        text-transform: none;
        font-weight: 400;
        font-size: 0.75rem;
        padding: 4px 12px;
        min-width: 80px;
        min-height: 28px;
        
        &:hover {
          background-color: rgba(0, 0, 0, 0.08);
        }

        .MuiButton-endIcon {
          margin-left: 4px;
          
          .MuiSvgIcon-root {
            font-size: 16px;
          }
        }
      }
    }
  }

  .text-form-card, .text-output-card {
    height: 100% !important;
    box-shadow: $shadow-md !important;
    border-radius: $border-radius-md;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .text-output-card {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0.4;
      z-index: 0;
      pointer-events: none;
      box-shadow: $shadow-md;
    }

    .card-content {
      position: relative;
      z-index: 1;
      background: rgba($bg-paper, 0.85);
      backdrop-filter: blur(8px);
    
    }

    .card-header {
      position: relative;
      z-index: 1;
      background: rgba($bg-paper, 0.95);
      backdrop-filter: blur(8px);
    }
  }

  .card-header {
    padding: $spacing-3 $spacing-4;
    flex-shrink: 0;

    .MuiCardHeader-content {
      flex: 1;
    }

    .MuiCardHeader-action {
      margin: 0;
      align-self: center;

      .action-button {
        background-color: $bg-light;
        padding: 6px;
        
        &:hover {
          background-color: $bg-light-dark;
        }

        .MuiSvgIcon-root {
          font-size: $font-size-md;
          color: $text-secondary;

          &.success {
            color: $success-color;
          }
        }
      }
    }

    .title {
      font-weight: $font-weight-semibold;
      color: $text-primary;
      font-size: $font-size-lg;
      margin-right: 5px;
    }

    .subtitle {
      color: $text-secondary;
      margin-top: $spacing-1;
      font-size: $font-size-sm;
    }

    .helper-icon {
      color: $text-secondary;
      cursor: help;
      transition: color 0.2s ease;

      &:hover {
        color: $primary-color;
      }
    }

    .favorite-button {
      display: flex;
      align-items: center;
      color: $text-secondary;
      text-transform: none;
      font-size: $font-size-sm;
      padding: $spacing-1 $spacing-2;
      min-width: auto;
      letter-spacing: 0;
      font-weight: $font-weight-regular;
      border-radius: $border-radius-sm;
      gap: $spacing-1;
      .MuiButton-startIcon {
        margin-right: 0;
        margin-left: 0;
      }
    }

    border-bottom: 1px solid $divider-color;
  }

  .card-content {
    background: radial-gradient(circle, rgba(255, 255, 255, 0) 50%, rgba(80, 80, 80, 0.05) 100%);
    flex: 1;
    min-height: 0; // Flex child için önemli
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 0px;

    &.output-content {
      overflow-y: auto;
      scroll-behavior: smooth;
      height: auto;
      margin-bottom: 0px !important;
      position: relative;
      padding: 0px !important;
      
      .text-output-empty-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        gap: $spacing-2;
        opacity: 0.8;
        text-align: center;
        padding: $spacing-4;
        position: relative;
        overflow: hidden;
        background-color: #fafafa;
        background-image: 
          linear-gradient(rgba(80, 80, 80, 0.05) 1px, transparent 1px),
          linear-gradient(90deg, rgba(80, 80, 80, 0.05) 1px, transparent 1px),
          linear-gradient(rgba(80, 80, 80, 0.025) 0.5px, transparent 0.5px),
          linear-gradient(90deg, rgba(80, 80, 80, 0.025) 0.5px, transparent 0.5px);
        background-size: 20px 20px, 20px 20px, 10px 10px, 10px 10px;
        background-position: -1px -1px, -1px -1px, -1px -1px, -1px -1px;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f0f0f0' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
          opacity: 0.1;
          z-index: 0;
        }

        .placeholder-content {
          position: relative;
          z-index: 1;
          background: rgba($bg-paper, 0.9);
          margin: 36px;
          padding: $spacing-4;
          border-radius: $border-radius-lg;
          box-shadow: $shadow-md;
          backdrop-filter: blur(4px);
          border: 1px solid rgba($primary-color, 0.1);
          max-width: 400px;
          width: 100%;
          
          .info-icon {
            font-size: 48px;
            color: rgba(0, 0, 0, 0.4);
            margin-bottom: $spacing-2;
            animation: float 3s ease-in-out infinite;
          }

          .placeholder-title {
            color: $text-primary;
            font-weight: $font-weight-semibold;
            margin-bottom: $spacing-2;
            font-size: $font-size-lg;
          }

          .placeholder-subtitle {
            color: $text-secondary;
            font-size: $font-size-sm;
            line-height: 1.6;
            margin-bottom: $spacing-3;
          }

          .placeholder-features {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: $spacing-2;
            margin-top: $spacing-3;
            padding: $spacing-2;
            background: rgba($bg-light, 0.5);
            border-radius: $border-radius-md;

            .feature-item {
              display: flex;
              align-items: center;
              gap: $spacing-2;
              padding: $spacing-2;
              background: rgba($bg-paper, 0.8);
              border-radius: $border-radius-sm;
              border: 1px solid rgba($primary-color, 0.1);

              .MuiSvgIcon-root {
                font-size: 20px;
                color: rgba(0, 0, 0, 0.4);
              }

              .feature-text {
                font-size: $font-size-xs;
                color: $text-secondary;
                font-weight: $font-weight-medium;
              }
            }
          }
        }
      }

      @keyframes float {
        0% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
        100% {
          transform: translateY(0px);
        }
      }
    }

    form {
      flex: 1;
      overflow-y: auto;
      padding: 24px 16px 16px 16px;
      scroll-behavior: smooth; // Smooth scroll için

      &::-webkit-scrollbar {
        width: 8px;
      }
      
      &::-webkit-scrollbar-track {
        background: $bg-light;
        border-radius: 4px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: $bg-light-dark;
        border-radius: 4px;
        
        &:hover {
          background: color.adjust($bg-light-dark, $lightness: -5%);
        }
      }
      .MuiInputLabel-root.MuiInputLabel-shrink {
        transform: translate(14px, -9px) scale(0.75)!important; 
    }
    .MuiTextField-root .MuiInputBase-root input {
      font-size: 14px;
    }

    .MuiTextField-root .MuiInputBase-root input::placeholder {
      font-size: 14px;
    }
    }

    .form-control {
      margin-bottom: $spacing-3;
    }

  }

  .card-actions {
    justify-content: space-between;
    padding: $spacing-3 $spacing-4;
    border-top: 1px solid $divider-color;
    flex-shrink: 0;

    &.output-actions {
      justify-content: flex-end;
    }
  }

  .model-info {
    color: $text-secondary;
    font-size: $font-size-xs;

    .model-name {
      font-weight: $font-weight-medium;
      color: $text-primary;
    }
  }

  .loading-text {
    font-size: $font-size-xs;
    color: $text-secondary;
  }

  .action-button {
    background-color: $bg-light;
    
    &:hover {
      background-color: $bg-light-dark;
    }

    .MuiSvgIcon-root {
      font-size: $font-size-lg;
      color: $text-secondary;

      &.success {
        color: $success-color;
      }
    }
  }

  .code-block {
    position: relative;
    margin: $spacing-3 0;
    border-radius: $border-radius-sm;
    background-color: $bg-light;

    .copy-button {
      position: absolute;
      right: $spacing-2;
      top: $spacing-2;
      background-color: $bg-paper;
      z-index: 1;

      &:hover {
        background-color: $bg-light-dark;
      }
    }

    pre {
      padding-top: $spacing-5;
      margin: 0;
      border-radius: $border-radius-sm;
      font-size: $font-size-sm;
    }
  }

  
  .form-field-wrapper {
    position: relative;
    padding-left: 35px;
    margin-bottom: $spacing-2;

    .form-badge {
      position: absolute;
      left: 0;
      top: 12px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: $primary-color;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      z-index: 1;
    }
  }



  // Butonlar için özel stiller
  .MuiButton-contained {
    background-color: $primary-color;
    color: $bg-paper;
    font-weight: $font-weight-medium;
    
    &:hover {
      background-color: $primary-color-dark;
    }

    &:disabled {
      background-color: $bg-light-dark;
      color: $text-disabled;
    }
  }

  .text-form-card {
    .generate-button {
      text-transform: none;
      background-color: rgba(80, 80, 80, 0.9) !important;
      box-shadow: none !important;
      font-weight: 500;
      padding: 6px 16px;
      color: white;
      
      &:hover {
        background-color: rgba(60, 60, 60, 0.95) !important;
        box-shadow: 0 4px 12px rgba(60, 60, 60, 0.3) !important;
        
        .MuiSvgIcon-root {
          animation-duration: 1s;
        }
      }
      
      &:disabled {
        background-color: rgba(80, 80, 80, 0.4) !important;
        color: rgba(255, 255, 255, 0.8) !important;
        
        .MuiSvgIcon-root {
          animation: none;
        }
      }
      
      &:hover:not(:disabled) {
        .MuiSvgIcon-root {
          transform: rotate(180deg);
        }
      }

      .MuiSvgIcon-root {
        font-size: $font-size-md;
        transition: transform 0.3s ease;
      }
    }
  }

  .markdown-body {
    font-size: $font-size-sm;
    padding: $spacing-3;
    display: none; // Initially hidden
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    overflow-y: auto;
    height: 400px;
    
    &.visible {
      display: block;
      opacity: 1;
    }
  }

  .MuiButton-outlined {
    border-color: $error-color;
    color: $error-color;
    
    &:hover {
      background-color: rgba($error-color, 0.04);
    }
  }

  .cancel-button {
    text-transform: inherit;
    width: auto;
    min-width: fit-content;
    &::first-letter {
      text-transform: inherit;
    }
  }

  // AI uyarı kutusu için yeni sınıf
  .ai-disclaimer-box {
    padding: $spacing-3;
    border-radius: $border-radius-md;
    border: 1px solid #e9ecef;
    background: white;
    font-size: $font-size-sm;
    color: rgba(0, 0, 0, 0.6);
    text-align: center;
    box-shadow: $shadow-md;
  }
} 