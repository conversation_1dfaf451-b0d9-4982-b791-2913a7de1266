.text-usecase {
  // Text usecase specific styles can be added here if needed
  .usecase-content-body {
    &-desc {
      margin-bottom: 20px;
    }
  }

  .usecase-field-form-submission {
    .left {
      .model-info {
        color: #677788;
        font-size: 12px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 2px;

        .model-name {
          color: #377dff;
          font-weight: 500;
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 10px;

      .running-text {
        font-size: 12px;
        color: #666;
        text-align: right;
        line-height: 1.2;
      }

      .ai-btn {
        &.usecase-field-form-submit {
          min-width: 100px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          .timer {
            font-family: 'Courier New', monospace;
            font-size: 15px;
            font-weight: 600;
            letter-spacing: 1px;
            color: #fff;
          }

          &:disabled {
            opacity: 0.85;
            cursor: not-allowed;
          }
        }

        &.usecase-field-form-cancel {
          opacity: 0;
          width: 0;
          padding: 0;
          overflow: hidden;
          transition: all 0.3s ease;

          &.show {
            opacity: 1;
            width: auto;
            padding: 10px 20px;
          }
        }
      }
    }
  }
} 