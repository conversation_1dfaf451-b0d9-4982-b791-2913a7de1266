// Import variables
@use '../../../../styles/abstracts/_variables' as *;

@keyframes flicker {
  0% {
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(33, 150, 243, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
  }
}

.flicker-button {
  position: relative;
  animation: flicker 2s infinite;

  &:disabled {
    opacity: 0.7;
  }
}

.assistant-container {
  margin-top: $spacing-4;
  margin-bottom: $spacing-4;
  padding-left: 0px !important;
  padding-right: 0px !important;
  height: 100%;
  max-width: 100% !important;
  width: 100% !important;
  
  .assistant-grid {
    height: 100%;
    scroll-margin-top: 120px;
  }

  // PDF Alanı Stilleri
  .pdf-grid-item {
    height: 100%;
    
    .pdf-box {
      background-color: #f8fafd;
      border: rgb(191, 191, 191) solid 1px; 
      border-radius: $border-radius-lg;
      box-shadow: $shadow-sm;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .pdf-viewer {
        height: 100%;
        display: flex;
        flex-direction: column;
        
        .pdf-controls {
          padding: 12px 16px;
          border-bottom: 1px solid #e5e7eb;
          display: flex;
          align-items: center;
          gap: 8px;
          background: #fff;
          z-index: 2;

          .pdf-control-button {
            color: #555;
            margin: 0 2px;
            padding: 6px;
            
            &:hover:not(:disabled) {
              background-color: rgba(0, 0, 0, 0.05);
              color: #333;
            }
            
            &:disabled {
              color: #ccc;
            }
          }

          .controls-divider {
            width: 1px;
            height: 24px;
            background-color: rgba(0, 0, 0, 0.15);
            margin: 0 8px;
          }

          .page-info, .zoom-info {
            font-size: 0.875rem;
            color: #555;
            margin: 0 8px;
            min-width: 60px;
            text-align: center;
            font-weight: 500;
          }

          // FavoriteButton için özel stiller
          .MuiBox-root {
            margin-left: auto;
            display: flex;
            align-items: center;

            button {
              margin-left: 8px;
              padding: 6px 12px;
              border-radius: 6px;
              font-size: 0.875rem;
              text-transform: none;
              
              &:hover {
                background-color: rgba(0, 0, 0, 0.05);
              }
            }
          }
        }
        
        .canvas-container {
          display: inline;
          text-align: center;
          flex: 1;
          overflow-y: auto;
          padding: 20px;
          background: #f3f4f6;
          
          .pdf-page-container {
            margin: 0 auto 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            background: white;
            position: relative;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .pdf-page-number {
              background: rgba(0, 0, 0, 0.6);
              color: white;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              width: max-content;
              display: flex;
              margin: 0 auto;
            }
          }
        }
      }
      
      .upload-section {
        height: 100%;
        min-height: 400px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 32px;
        text-align: center;
        
        .upload-icon {
          width: 90px;
          margin-bottom: 1.5rem;
          opacity: 0.7;
        }
        
        .upload-title {
          font-size: $font-size-sm;
          margin-bottom: 1.5rem;
          color: #333;
          font-weight: 500;
        }
        
        .upload-button {
          margin-top: 1rem;
          background-color: #f0f0f0;
          padding: 8px 16px;
          border-radius: 6px;
          color: #555;
          font-weight: 500;
          text-transform: inherit;
          
          &:hover:not(:disabled) {
            background-color: #e8e8e8;
          }
          
          &:disabled {
            opacity: 0.7;
            cursor: not-allowed;
          }
        }
        
        .or-text {
          margin: 1rem 0;
          color: #777;
        }
        
        .sample-button {
          background-color: #2563eb;
          color: white;
          font-weight: 500;
          text-transform: none;
          padding: 8px 16px;
          border-radius: 6px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          transition: all 0.2s ease;
          
          &:hover {
            background-color: #1d4ed8;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
          }
        }
      }
    }
  }

  // Chat Alanı Stilleri
  .chat-grid-item {
    
    .chat-box {
      border: rgb(218, 218, 218) solid 1px; 
      border-radius: $border-radius-lg;
      box-shadow: $shadow-sm;
      overflow: hidden;
      height: 100%;
      max-height: 700px;
      display: flex;
      flex-direction: column;
      
      .chat-body {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        background-color: #f8fafc;
        height: calc(100% - 60px);

        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 4px;
          
          &:hover {
            background: rgba(0, 0, 0, 0.3);
          }
        }
        
        &::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.05);
        }

        .chat-message {
          max-width: 90%;
          display: flex;
          flex-direction: column;
          
          .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            
            .assistant-avatar, .user-avatar {
              width: 28px;
              height: 28px;
              margin-right: 0.5rem;
            }
            
            .assistant-avatar {
              background-color: $primary-color;
              color: white;
            }
            
            .user-avatar {
              background-color: $secondary-color-dark;
              color: white;
            }
            
            .message-sender {
              font-size: 0.8rem;
              font-weight: 600;
              color: #475569;
            }
          }
          
          &.user {
            align-self: flex-end;
            
            .message-header {
              flex-direction: row-reverse;
              
              .user-avatar {
                margin-right: 0;
                margin-left: 0.5rem;
              }
              
              .message-sender {
                color: #475569;
              }
            }
            
            .message-content {
              background-color: #f3f3f3;
              color: black;
              border-radius: 12px 12px 2px 12px;
              
              .message-text {
                color: white;
              }
            }
          }
          
          &.assistant {
            align-self: flex-start;
            
            .message-content {
              background-color: white;
              border: 1px solid rgba(0, 0, 0, 0.1);
              border-radius: 12px 12px 12px 2px;
              
              .loading-indicator {
                margin-right: 0.75rem;
                color: #2563eb;
              }
            }
          }
          
          .message-content {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            
            .message-text {
              font-size: 0.95rem;
              color: #333;
              line-height: 1.5;
            }
          }
        }
      }
      
      .chat-input-container {
        padding: 1rem;
        border-top: 1px solid rgba(0, 0, 0, 0.08);
        background-color: white;
        position: relative;
        
        .predefined-questions-container {
          width: 100%;
          overflow-y: auto;
          transform-origin: bottom center;
          background-color: white;
          box-shadow: 20px 0 rgba(0, 0, 0, 0.1);
          margin: 0 auto;
          padding: 16px;
          position: absolute;
          bottom: -300px;
          left: 0;
          z-index: 10;
          border-top: 1px solid rgba(0, 0, 0, 0.08);
          transition: bottom 0.3s ease-in-out, opacity 0.3s ease-in-out;
          opacity: 0;
          
          &.show {
            bottom: 100%;
            opacity: 1;
          }
          
          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }
          
          &::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            
            &:hover {
              background: rgba(0, 0, 0, 0.25);
            }
          }
          
          .sample-questions-title {
            margin-bottom: 8px;
            color: #000000;
            font-weight: 500;
          }
          
          .question-box {
            display: flex;
            align-items: flex-start;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
            
            &:hover {
              background-color: rgba(37, 99, 235, 0.05);
            }
            
            .question-prefix {
              margin-right: 8px;
              color: rgba(37, 99, 235, 0.8);
              font-weight: bold;
            }
            
            .question-text {
              color: rgba(17, 24, 39, 0.8);
              font-weight: normal;
            }
          }
        }
        
        .input-group-container {
          width: 100%;
          position: relative;
          
          // Help butonu için stil
          .help-button-integrated {
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            width: 32px;
            height: 32px;
            
            &:hover {
              background-color: rgba(0, 0, 0, 0.05);
            }
          }
          
          .chat-input-field {
            .MuiOutlinedInput-root {
              border-radius: 25px;
              background-color: white;
              height: 48px;
              
              // Help butonu varsa sol padding ekle
              &.with-help-button {
                padding-left: 40px; // Help butonu için yer açıyoruz
              }
              
              // Help butonu yoksa normal padding
              &.without-help-button {
                padding-left: 14px;
              }
              
              &:hover .MuiOutlinedInput-notchedOutline {
                border-color: #d0d0d0;
              }
              
              &.Mui-focused .MuiOutlinedInput-notchedOutline {
                border-color: #d0d0d0;
              }
            }
          }
          
          .send-button-integrated {
            margin-right: 0;
            color: #9ca3af;
            width: 40px;
            height: 40px;
            z-index: 5;
            
            &:hover:not(:disabled) {
              background-color: transparent;
              color: #6b7280;
            }
            
            &:disabled {
              color: #d1d5db;
            }
          }
        }
      }
    }
  }
  // AI uyarı kutusu için yeni sınıf
  .ai-disclaimer-box {
    margin-top: $spacing-3;
    padding: $spacing-3;
    border-radius: $border-radius-md;
    border: 1px solid #e9ecef;
    background: white;
    font-size: $font-size-sm;
    color: rgba(0, 0, 0, 0.6);
    text-align: center;
    box-shadow: $shadow-md;
  }
}

// Tooltip stili
.tooltip-text {
  font-size: 0.875rem;
  line-height: 1.5;
}

// Typing cursor style for the typewriter effect
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 1em;
  margin-left: 2px;
  background-color: currentColor;
  vertical-align: text-bottom;
  animation: blink 0.7s infinite;
} 