import React from 'react';
import PropTypes from 'prop-types';
import TextUsecase from './usecases/TextUsecase';
import SimpleAppUsecase from './usecases/SimpleAppUsecase';
import ImageUsecase from './usecases/ImageUsecase';
import AssistantUsecase from './usecases/AssistantUsecase';
import PlaygroundUsecase from './usecases/PlaygroundUsecase';
import VideoUsecase from './usecases/VideoUsecase';

const UsecaseApp = ({ id, data }) => {
  if (data.api_type === 'completions' || data.api_type === 'stream') {
    return <TextUsecase id={id} data={data} />;
  }

  console.log('data:', data);

  const usecaseComponents = {
    simple_app: SimpleAppUsecase,
    image: ImageUsecase,
    assistant: AssistantUsecase,
    playground: PlaygroundUsecase,
    video: VideoUsecase,
  };

  const UsecaseComponent = usecaseComponents[data.api_type];

  return UsecaseComponent ? <UsecaseComponent id={id} data={data} /> : null;
};

UsecaseApp.propTypes = {
  id: PropTypes.string.isRequired,
  data: PropTypes.shape({
    api_type: PropTypes.oneOf([
      'completions',
      'stream',
      'simple_app',
      'image',
      'assistant',
      'playground',
      'video',
    ]).isRequired,
  }).isRequired,
};

export default UsecaseApp;
