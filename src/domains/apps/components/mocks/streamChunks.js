export const mockStreamChunks = [
  {
    id: "chatcmpl-123",
    object: "chat.completion.chunk",
    created: 1694268190,
    model: "gpt-3.5-turbo-0613",
    choices: [{
      index: 0,
      delta: {
        content: "Here"
      },
      finish_reason: null
    }]
  },
  {
    id: "chatcmpl-123",
    object: "chat.completion.chunk",
    created: 1694268190,
    model: "gpt-3.5-turbo-0613",
    choices: [{
      index: 0,
      delta: {
        content: "'s "
      },
      finish_reason: null
    }]
  },
  {
    id: "chatcmpl-123",
    object: "chat.completion.chunk",
    created: 1694268190,
    model: "gpt-3.5-turbo-0613",
    choices: [{
      index: 0,
      delta: {
        content: "the "
      },
      finish_reason: null
    }]
  },
  {
    id: "chatcmpl-123",
    object: "chat.completion.chunk",
    created: 1694268190,
    model: "gpt-3.5-turbo-0613",
    choices: [{
      index: 0,
      delta: {
        content: "JavaScript "
      },
      finish_reason: null
    }]
  },
  {
    id: "chatcmpl-123",
    object: "chat.completion.chunk",
    created: 1694268190,
    model: "gpt-3.5-turbo-0613",
    choices: [{
      index: 0,
      delta: {
        content: "code:\n\n```javascript\n"
      },
      finish_reason: null
    }]
  },
  {
    id: "chatcmpl-123",
    object: "chat.completion.chunk",
    created: 1694268190,
    model: "gpt-3.5-turbo-0613",
    choices: [{
      index: 0,
      delta: {
        content: "let counter = 0;\nconst interval = setInterval(() => {\n  counter++;\n  console.log(counter);\n}, 1000);\n```"
      },
      finish_reason: null
    }]
  },
  {
    id: "chatcmpl-123",
    object: "chat.completion.chunk",
    created: 1694268190,
    model: "gpt-3.5-turbo-0613",
    choices: [{
      index: 0,
      delta: {
        content: "\n\nThis code creates a simple counter that:"
      },
      finish_reason: null
    }]
  },
  {
    id: "chatcmpl-123",
    object: "chat.completion.chunk",
    created: 1694268190,
    model: "gpt-3.5-turbo-0613",
    choices: [{
      index: 0,
      delta: {
        content: "\n1. Initializes a counter variable"
      },
      finish_reason: null
    }]
  },
  {
    id: "chatcmpl-123",
    object: "chat.completion.chunk",
    created: 1694268190,
    model: "gpt-3.5-turbo-0613",
    choices: [{
      index: 0,
      delta: {
        content: "\n2. Sets up an interval that runs every second"
      },
      finish_reason: null
    }]
  },
  {
    id: "chatcmpl-123",
    object: "chat.completion.chunk",
    created: 1694268190,
    model: "gpt-3.5-turbo-0613",
    choices: [{
      index: 0,
      delta: {
        content: "\n3. Increments and logs the counter value"
      },
      finish_reason: null
    }]
  },
  {
    id: "chatcmpl-123",
    object: "chat.completion.chunk",
    created: 1694268190,
    model: "gpt-3.5-turbo-0613",
    choices: [{
      index: 0,
      delta: {
        content: ""
      },
      finish_reason: "stop"
    }]
  }
];

// Helper function to simulate streaming with delays
export const simulateStream = async function* (chunks, delayMs = 100) {
  for (const chunk of chunks) {
    yield chunk;
    await new Promise(resolve => setTimeout(resolve, delayMs));
  }
};

// Usage example:
/*
async function example() {
  const stream = simulateStream(mockStreamChunks);
  for await (const chunk of stream) {
    const content = chunk.choices[0].delta.content;
    if (content) {
      process.stdout.write(content);
    }
  }
}
*/ 