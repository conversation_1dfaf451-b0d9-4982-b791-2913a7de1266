import TextUsecase from './TextUsecase';
import { mockStreamChunks, simulateStream } from '../mocks/streamChunks';

const mockDataCompletions = {
  _id: "674eb52f4782524f9eaf1fca",
  title: "Corporate development, strategy & ESG innovation idea generator",
  slug: "corporate-development-strategy-esg-innovation-idea-generator",
  function: [
      "corporate-development-strategy-esg"
  ],
  use_case_type: [
      "text-generation"
  ],
  uniqueid: "uid-274",
  usecase_icon_url: "https://cdn.aibusinessschool.com/usecases/2024-09-21_13-18-48-svgviewer-output-2024-09-21T161829.918.svg",
  api_type: "completions",
  completions_settings: {
      form_fields: [
          {
              _id: "6798c9a2ec3fdac96b9192e8",
              type: "textarea",
              choices: "",
              label: "Business function",
              name: "business_function",
              default_value: "Corporate development, strategy & ESG (Environmental, Social, and Governance)"
          },
          {
              _id: "6798c9a2ec3fdac96b9192e9",
              type: "select",
              choices: "Incremental innovation \r\nRadical innovation \r\nDisruptive innovation",
              label: "Type of innovation",
              name: "type_of_innovation",
              default_value: "Incremental innovation"
          }
      ],
      completions_prompt: "Generate {type_of_innovation} ideas for {business_function} that will  improve efficiency and create new opportunities within the {business_function} function. Focus on practical, impactful solutions that align with current trends and customer needs.",
      add_manual_translation: true,
      show_language_selection: false,
      disable_language_selection_prompt: false,
      model_selection: "gpt-3.5-turbo",
      max_token: "1024",
      temperature: "1",
      top_p: "1",
      frequency_penalty: "0",
      presence_penalty: "0",
      custom_system_prompt: "",
      _id: "6798c9a2ec3fdac96b9192ea"
  },
  usecase_order: 999,
  is_that_interesting: false,
  translations: {
      de: {
          completions_prompt: "Generiere {type_of_innovation}-Ideen für {business_function}, die die Effizienz verbessern und neue Chancen innerhalb der {business_function} Funktion schaffen. Konzentriere dich auf praktische, wirkungsvolle Lösungen, die mit aktuellen Trends und den Bedürfnissen der Kunden übereinstimmen.",
          _id: "6798c9a2ec3fdac96b9192ec"
      }
  },
  function_order: {
      sales: 100,
      marketing: 100,
      customer_care_after_sales: 100,
      finance: 100,
      hr: 100,
      it_ai_data: 100,
      project_management_consulting: 100,
      management_management_support: 100,
      corporate_development_strategy_esg: 990,
      supply_chain_logistics: 100,
      operations_processing: 100,
      production: 100,
      maintenance: 100,
      quality_management: 100,
      legal_compliance: 100,
      risk_management: 100,
      rd: 100,
      other: 100,
      _id: "6798c9a2ec3fdac96b9192eb"
  }
};

const mockDataStream = {
  _id: "673350ea04f9a7ddea2a30ad",
  title: "Customer care email generator",
  slug: "customer-care-email-generator",
  function: [
    "customer-care-after-sales"
  ],
  use_case_type: [
    "text-generation"
  ],
  uniqueid: "uid-83",
  usecase_icon_url: "https://cdn.aibusinessschool.com/usecases/2024-06-03_15-01-37-svgviewer-output-24-1.svg",
  api_type: "stream",
  stream_settings: {
    is_chat_mode_active: false,
    stream_form: {
      form_fields: [
        {
          type: "text",
          choices: "",
          label: "Client name",
          name: "client_name",
          default_value: "David Shadow "
        },
        {
          type: "text",
          choices: "",
          label: "Your company name",
          name: "company_name",
          default_value: "UrbanFlow Shoes"
        },
        {
          type: "text",
          choices: "",
          label: "Brief description of the investigation",
          name: "investigation",
          default_value: "A lost package"
        },
        {
          type: "textarea",
          choices: "",
          label: "Client's question",
          name: "clients_question",
          default_value: "What is the refund policy, and will the refund be automatically associated with my case regarding the lost package?"
        },
        {
          type: "select",
          choices: "Low\r\nMedium\r\nHigh",
          label: "Urgency",
          name: "urgency",
          default_value: "Medium"
        }
      ]
    },
    stream_prompt: "Act as a customer care specialist working at the company named {company_name} and generate a personalized customer service email for {client_name}. The inquiry pertains to {investigation}, and the question is {clients_question}. Please consider the urgency level {urgency}. You should automatically craft an email response, ensuring swift and tailored communication to address the client's concern promptly.",
    add_manual_translation: true,
    stream_prompt_de: "Versetzen Sie sich in die Rolle eines Kundendienstspezialisten des Unternehmens {company_name} und erstellen Sie eine personalisierte Kundendienst-E-Mail für {client_name}. Die Anfrage bezieht sich auf {investigation}, und die zu beantwortenden Fragen lauten: {clients_question}. Bitte beachten Sie die Dringlichkeitsstufe {Urgency} in Hinblick auf Formulierungen und die damit verbundene Sicherstellung der Kundenzufriedenheit. Sie sollen automatisch eine E-Mail-Antwort verfassen, die eine schnelle und individualisierte Kommunikation gewährleistet, um das Anliegen des Kunden umgehend zu bearbeiten und lösen.",
    show_language_selection: true,
    default_chat_input_text: "",
    prompt_examples: false,
    show_api_settings: false,
    model_selection: "gpt-3.5-turbo",
    max_token: "1024",
    temperature: "0.1",
    top_p: "0.1",
    frequency_penalty: "0",
    presence_penalty: "0",
    custom_system_prompt: ""
  },
  usecase_order: "100",
  function_order: {
    sales: "100",
    marketing: "100",
    customer_care_after_sales: "100",
    finance: "100",
    hr: "100",
    it_ai_data: "100",
    project_management_consulting: "100",
    management_management_support: "100",
    corporate_development_strategy_esg: "100",
    supply_chain_logistics: "100",
    operations_processing: "100",
    production: "100",
    maintenance: "100",
    quality_management: "100",
    legal_compliance: "100",
    risk_management: "100",
    rd: "100",
    other: "100"
  },
  translations: []
};

// Mock the fetch function for the streaming story
const mockStreamingFetch = async () => {
  return {
    ok: true,
    body: {
      getReader: () => {
        let chunks = [];
        // Convert stream chunks to encoded format
        mockStreamChunks.forEach(chunk => {
          const encoder = new TextEncoder();
          const jsonString = `data: ${JSON.stringify(chunk)}\n\n`;
          chunks.push(encoder.encode(jsonString));
        });
        
        let currentChunk = 0;
        
        return {
          async read() {
            if (currentChunk >= chunks.length) {
              return { done: true };
            }
            
            await new Promise(resolve => setTimeout(resolve, 100)); // Simulate network delay
            return {
              done: false,
              value: chunks[currentChunk++]
            };
          }
        };
      }
    }
  };
};

// Mock data for different modification responses
const mockModificationChunks = {
  shorter: [
    {
      id: "chatcmpl-124",
      object: "chat.completion.chunk",
      created: 1694268190,
      model: "gpt-3.5-turbo-0613",
      choices: [{
        index: 0,
        delta: {
          content: "Here's a concise version:\n\n```javascript\nconst handleClick = () => {\n  const users = [];\n  let count = 0;\n  while(count < 3) {\n    users.push({ id: count, name: `User${count}`, email: `user${count}@example.com` });\n    count++;\n  }\n  console.log(users);\n};\n```\n\nThis code creates a simple array of 3 user objects in a while loop."
        },
        finish_reason: null
      }]
    },
    {
      id: "chatcmpl-124",
      object: "chat.completion.chunk",
      created: 1694268190,
      model: "gpt-3.5-turbo-0613",
      choices: [{
        index: 0,
        delta: {
          content: ""
        },
        finish_reason: "stop"
      }]
    }
  ],
  longer: [
    {
      id: "chatcmpl-125",
      object: "chat.completion.chunk",
      created: 1694268190,
      model: "gpt-3.5-turbo-0613",
      choices: [{
        index: 0,
        delta: {
          content: "Here's an expanded version with more details:\n\n```javascript\nconst handleUserGeneration = () => {\n  // Initialize array to store user data\n  const users = [];\n  \n  // Counter for while loop\n  let count = 0;\n  \n  // Generate 3 user records\n  while(count < 3) {\n    // Create user object with unique data\n    const user = {\n      id: count,\n      name: `User${count}`,\n      email: `user${count}@example.com`,\n      createdAt: new Date().toISOString()\n    };\n    \n    // Add user to array\n    users.push(user);\n    count++;\n  }\n  \n  // Log results\n  console.log('Generated Users:', users);\n  return users;\n};\n```\n\nThis enhanced code:\n1. Uses descriptive variable names\n2. Includes comments for clarity\n3. Adds timestamp to each user\n4. Returns the users array\n5. Provides detailed console output"
        },
        finish_reason: null
      }]
    },
    {
      id: "chatcmpl-125",
      object: "chat.completion.chunk",
      created: 1694268190,
      model: "gpt-3.5-turbo-0613",
      choices: [{
        index: 0,
        delta: {
          content: ""
        },
        finish_reason: "stop"
      }]
    }
  ],
  simpler: [
    {
      id: "chatcmpl-126",
      object: "chat.completion.chunk",
      created: 1694268190,
      model: "gpt-3.5-turbo-0613",
      choices: [{
        index: 0,
        delta: {
          content: "Here's a simpler version that's easier to understand:\n\n```javascript\n// Function that runs when button is clicked\nconst createUsers = () => {\n  // Create empty list for users\n  const userList = [];\n  \n  // Count from 0 to 2 (total 3 times)\n  let number = 0;\n  while(number < 3) {\n    // Add a new user to the list\n    userList.push({\n      id: number,\n      name: 'User' + number,\n      email: 'user' + number + '@example.com'\n    });\n    \n    // Move to next number\n    number = number + 1;\n  }\n  \n  // Show the list of users\n  console.log(userList);\n};\n```\n\nThis code creates a list of 3 users with basic information. Each user gets a number (0, 1, or 2) which is used in their name and email."
        },
        finish_reason: null
      }]
    },
    {
      id: "chatcmpl-126",
      object: "chat.completion.chunk",
      created: 1694268190,
      model: "gpt-3.5-turbo-0613",
      choices: [{
        index: 0,
        delta: {
          content: ""
        },
        finish_reason: "stop"
      }]
    }
  ]
};

// Mock fetch function for modifications
const createModificationFetch = (type) => async () => {
  return {
    ok: true,
    body: {
      getReader: () => {
        let chunks = [];
        mockModificationChunks[type].forEach(chunk => {
          const encoder = new TextEncoder();
          const jsonString = `data: ${JSON.stringify(chunk)}\n\n`;
          chunks.push(encoder.encode(jsonString));
        });
        
        let currentChunk = 0;
        
        return {
          async read() {
            if (currentChunk >= chunks.length) {
              return { done: true };
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
            return {
              done: false,
              value: chunks[currentChunk++]
            };
          }
        };
      }
    }
  };
};

export default {
  title: 'Domains/Apps/Usecases/TextUsecase',
  component: TextUsecase,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A simple app usecase component that handles form submission and displays generated results.'
      }
    }
  },
  argTypes: {
    id: { control: 'text' },
    data: { control: 'object' }
  }
};

// Default story
export const DefaultWithCompletions = {
  args: {
    id: "default-id",
    data: mockDataCompletions
  },
  parameters: {
    docs: {
      description: {
        story: 'Default view with standard form fields.'
      }
    }
  }
};

// Streaming response story
export const StreamingResponse = {
  args: {
    id: "streaming-id",
    data: mockDataStream
  },
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates the streaming response with typewriter effect'
      }
    }
  },
  decorators: [
    (Story) => {
      // Mock the global fetch before rendering
      const originalFetch = window.fetch;
      window.fetch = mockStreamingFetch;

      return (
        <div>
          <Story />
        </div>
      );
    }
  ]
};

// Loading state story
export const LoadingState = {
  args: {
    id: "loading-id",
    data: mockDataCompletions
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the component in loading state'
      }
    }
  },
  decorators: [
    (Story) => {
      // Mock the fetch to never resolve
      const originalFetch = window.fetch;
      window.fetch = () => new Promise(() => {});

      return (
        <div>
          <Story />
        </div>
      );
    }
  ]
};

// Error state story
export const ErrorState = {
  args: {
    id: "error-id",
    data: mockDataCompletions
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the component in error state'
      }
    }
  },
  decorators: [
    (Story) => {
      // Mock the fetch to reject
      const originalFetch = window.fetch;
      window.fetch = () => Promise.reject(new Error('API Error'));

      return (
        <div>
          <Story />
        </div>
      );
    }
  ]
};

// Modification stories
export const ShorterResponse = {
  args: {
    id: "shorter-id",
    data: mockDataCompletions
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the response being modified to a shorter version'
      }
    }
  },
  decorators: [
    (Story) => {
      const originalFetch = window.fetch;
      window.fetch = createModificationFetch('shorter');
      return <Story />;
    }
  ]
};

export const LongerResponse = {
  args: {
    id: "longer-id",
    data: mockDataCompletions
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the response being modified to a longer version'
      }
    }
  },
  decorators: [
    (Story) => {
      const originalFetch = window.fetch;
      window.fetch = createModificationFetch('longer');
      return <Story />;
    }
  ]
};

export const SimplerResponse = {
  args: {
    id: "simpler-id",
    data: mockDataCompletions
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the response being modified to a simpler version'
      }
    }
  },
  decorators: [
    (Story) => {
      const originalFetch = window.fetch;
      window.fetch = createModificationFetch('simpler');
      return <Story />;
    }
  ]
};
 