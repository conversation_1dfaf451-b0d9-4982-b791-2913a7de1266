import SimpleAppUsecase from './SimpleAppUsecase';
import { mockStreamChunks, simulateStream } from '../mocks/streamChunks';

const mockData = {
  _id: "6787be40eca28c7f30dea045",
  title: "Product Analysis Tool",
  slug: "product-analysis",
  content: "Get detailed analysis for your product or service",
  simple_app_form: [
    {
      type: "input",
      label: "Product or service name",
      name: "product_or_service",
      default_value: "while loop after button click with user fields id, name, email",
      _id: "6787be40eca28c7f30dea046"
    },
    
    {
      type: "textarea",
      label: "Textarea Example",
      name: "textarea_example",
      default_value: "Write a javascript code for {product_or_service} and after writing finished explain it",
      _id: "6787be40eca28c7f30dea033"
    },
    {
      _id: "6791ffc7ec3fdac96b913bf5",
      type: "select",
      choices: "Incremental innovation \r\nRadical innovation \r\nDisruptive innovation\r\nProcess innovation\r\nSustainable innovation\r\nProduct innovation",
      label: "Type of innovation",
      name: "type_of_innovation",
      default_value: "Incremental innovation"
    }
  ],
  prompt: "Write a javascript code for {product_or_service} and after writing finished explain it",
  model_selection: "gpt-3.5-turbo",
  temperature: "1",
  top_p: "1",
  presence_penalty: "0",
  frequency_penalty: "0",
  userId: "676033dbc8e1fb825bc04369",
  createdAt: "2025-01-15T13:55:12.776Z",
  updatedAt: "2025-01-15T13:55:12.776Z",
  __v: 0
};

// Mock the fetch function for the streaming story
const mockStreamingFetch = async () => {
  return {
    ok: true,
    body: {
      getReader: () => {
        let chunks = [];
        // Convert stream chunks to encoded format
        mockStreamChunks.forEach(chunk => {
          const encoder = new TextEncoder();
          const jsonString = `data: ${JSON.stringify(chunk)}\n\n`;
          chunks.push(encoder.encode(jsonString));
        });
        
        let currentChunk = 0;
        
        return {
          async read() {
            if (currentChunk >= chunks.length) {
              return { done: true };
            }
            
            await new Promise(resolve => setTimeout(resolve, 100)); // Simulate network delay
            return {
              done: false,
              value: chunks[currentChunk++]
            };
          }
        };
      }
    }
  };
};

// Mock data for different modification responses
const mockModificationChunks = {
  shorter: [
    {
      id: "chatcmpl-124",
      object: "chat.completion.chunk",
      created: 1694268190,
      model: "gpt-3.5-turbo-0613",
      choices: [{
        index: 0,
        delta: {
          content: "Here's a concise version:\n\n```javascript\nconst handleClick = () => {\n  const users = [];\n  let count = 0;\n  while(count < 3) {\n    users.push({ id: count, name: `User${count}`, email: `user${count}@example.com` });\n    count++;\n  }\n  console.log(users);\n};\n```\n\nThis code creates a simple array of 3 user objects in a while loop."
        },
        finish_reason: null
      }]
    },
    {
      id: "chatcmpl-124",
      object: "chat.completion.chunk",
      created: 1694268190,
      model: "gpt-3.5-turbo-0613",
      choices: [{
        index: 0,
        delta: {
          content: ""
        },
        finish_reason: "stop"
      }]
    }
  ],
  longer: [
    {
      id: "chatcmpl-125",
      object: "chat.completion.chunk",
      created: 1694268190,
      model: "gpt-3.5-turbo-0613",
      choices: [{
        index: 0,
        delta: {
          content: "Here's an expanded version with more details:\n\n```javascript\nconst handleUserGeneration = () => {\n  // Initialize array to store user data\n  const users = [];\n  \n  // Counter for while loop\n  let count = 0;\n  \n  // Generate 3 user records\n  while(count < 3) {\n    // Create user object with unique data\n    const user = {\n      id: count,\n      name: `User${count}`,\n      email: `user${count}@example.com`,\n      createdAt: new Date().toISOString()\n    };\n    \n    // Add user to array\n    users.push(user);\n    count++;\n  }\n  \n  // Log results\n  console.log('Generated Users:', users);\n  return users;\n};\n```\n\nThis enhanced code:\n1. Uses descriptive variable names\n2. Includes comments for clarity\n3. Adds timestamp to each user\n4. Returns the users array\n5. Provides detailed console output"
        },
        finish_reason: null
      }]
    },
    {
      id: "chatcmpl-125",
      object: "chat.completion.chunk",
      created: 1694268190,
      model: "gpt-3.5-turbo-0613",
      choices: [{
        index: 0,
        delta: {
          content: ""
        },
        finish_reason: "stop"
      }]
    }
  ],
  simpler: [
    {
      id: "chatcmpl-126",
      object: "chat.completion.chunk",
      created: 1694268190,
      model: "gpt-3.5-turbo-0613",
      choices: [{
        index: 0,
        delta: {
          content: "Here's a simpler version that's easier to understand:\n\n```javascript\n// Function that runs when button is clicked\nconst createUsers = () => {\n  // Create empty list for users\n  const userList = [];\n  \n  // Count from 0 to 2 (total 3 times)\n  let number = 0;\n  while(number < 3) {\n    // Add a new user to the list\n    userList.push({\n      id: number,\n      name: 'User' + number,\n      email: 'user' + number + '@example.com'\n    });\n    \n    // Move to next number\n    number = number + 1;\n  }\n  \n  // Show the list of users\n  console.log(userList);\n};\n```\n\nThis code creates a list of 3 users with basic information. Each user gets a number (0, 1, or 2) which is used in their name and email."
        },
        finish_reason: null
      }]
    },
    {
      id: "chatcmpl-126",
      object: "chat.completion.chunk",
      created: 1694268190,
      model: "gpt-3.5-turbo-0613",
      choices: [{
        index: 0,
        delta: {
          content: ""
        },
        finish_reason: "stop"
      }]
    }
  ]
};

// Mock fetch function for modifications
const createModificationFetch = (type) => async () => {
  return {
    ok: true,
    body: {
      getReader: () => {
        let chunks = [];
        mockModificationChunks[type].forEach(chunk => {
          const encoder = new TextEncoder();
          const jsonString = `data: ${JSON.stringify(chunk)}\n\n`;
          chunks.push(encoder.encode(jsonString));
        });
        
        let currentChunk = 0;
        
        return {
          async read() {
            if (currentChunk >= chunks.length) {
              return { done: true };
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
            return {
              done: false,
              value: chunks[currentChunk++]
            };
          }
        };
      }
    }
  };
};

export default {
  title: 'Domains/Apps/Usecases/SimpleAppUsecase',
  component: SimpleAppUsecase,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A simple app usecase component that handles form submission and displays generated results.'
      }
    }
  },
  argTypes: {
    id: { control: 'text' },
    data: { control: 'object' }
  }
};

// Default story
export const Default = {
  args: {
    id: "default-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Default view with standard form fields.'
      }
    }
  }
};

// Streaming response story
export const StreamingResponse = {
  args: {
    id: "streaming-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates the streaming response with typewriter effect'
      }
    }
  },
  decorators: [
    (Story) => {
      // Mock the global fetch before rendering
      const originalFetch = window.fetch;
      window.fetch = mockStreamingFetch;

      return (
        <div>
          <Story />
        </div>
      );
    }
  ]
};

// Loading state story
export const LoadingState = {
  args: {
    id: "loading-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the component in loading state'
      }
    }
  },
  decorators: [
    (Story) => {
      // Mock the fetch to never resolve
      const originalFetch = window.fetch;
      window.fetch = () => new Promise(() => {});

      return (
        <div>
          <Story />
        </div>
      );
    }
  ]
};

// Error state story
export const ErrorState = {
  args: {
    id: "error-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the component in error state'
      }
    }
  },
  decorators: [
    (Story) => {
      // Mock the fetch to reject
      const originalFetch = window.fetch;
      window.fetch = () => Promise.reject(new Error('API Error'));

      return (
        <div>
          <Story />
        </div>
      );
    }
  ]
};

// Modification stories
export const ShorterResponse = {
  args: {
    id: "shorter-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the response being modified to a shorter version'
      }
    }
  },
  decorators: [
    (Story) => {
      const originalFetch = window.fetch;
      window.fetch = createModificationFetch('shorter');
      return <Story />;
    }
  ]
};

export const LongerResponse = {
  args: {
    id: "longer-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the response being modified to a longer version'
      }
    }
  },
  decorators: [
    (Story) => {
      const originalFetch = window.fetch;
      window.fetch = createModificationFetch('longer');
      return <Story />;
    }
  ]
};

export const SimplerResponse = {
  args: {
    id: "simpler-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the response being modified to a simpler version'
      }
    }
  },
  decorators: [
    (Story) => {
      const originalFetch = window.fetch;
      window.fetch = createModificationFetch('simpler');
      return <Story />;
    }
  ]
};
 