import ImageUsecase from './ImageUsecase';

const mockData = {
  _id: "6752f29e1ff89677740015f2",
  title: "Campaign visuals designer",
  slug: "campaign-visuals-designer",
  function: ["marketing"],
  use_case_type: ["image-generation"],
  uniqueid: "uid-34",
  usecase_icon_url: "https://cdn.aibusinessschool.com/usecases/2024-05-30_11-31-55-svgviewer-output-10.svg",
  api_type: "dalle",
  'dall-e_settings': {
    dalle_prompt: "Create an illustration featuring a joyful {icon} enthusiastically showcasing their newly acquired {product}. The image style should be {image_style}, with a {background} background that complements the {product}. Ensure the central focus of the illustration is on highlighting the unique features and benefits of the {product}. For a hyper-realistic effect, the {icon} should be depicted with detailed, lifelike features, expressing genuine excitement. The {product} must be rendered with exceptional clarity, emphasizing its texture, color, and design intricacies. The {background} should enhance the realism of the scene, featuring natural lighting and shadows, and should harmonize with the color scheme and theme of the {product}. The composition of the illustration should be balanced, with attention to realistic proportions and perspective.",
    form_fields: [
      {
        type: "select",
        choices: "Hyper-realistic\r\nRealistic\r\nPop-art\r\nCartoon\r\nVintage\r\nFuturistic\r\nPhoto-realistic",
        label: "Image style",
        name: "image_style",
        default_value: "Hyper-realistic"
      },
      {
        type: "select",
        choices: "Urban skyline\r\nBeach paradise \r\nIndustrial district\r\nGraffiti wall\r\nVintage backdrop \r\nSunset\r\nChic urban scene",
        label: "Background",
        name: "background",
        default_value: "Urban skyline"
      },
      {
        type: "text",
        choices: "",
        label: "Icon",
        name: "icon",
        default_value: "A man in a suit "
      },
      {
        type: "text",
        choices: "",
        label: "Product",
        name: "product",
        default_value: "Perfume"
      }
    ],
    'dall-e_model': "dall-e-3",
    number_of_images: "1",
    image_quality: "standard",
    dalle_2_size: false,
    dalle_3_size: "1024x1024",
    style: "natural"
  }
};

// Mock image URL for stories
const mockImageUrl = "https://picsum.photos/1024/1024";

// Mock the fetch function for image generation
const mockImageFetch = async () => {
  return {
    ok: true,
    json: () => Promise.resolve({
      data: [{ url: mockImageUrl }]
    })
  };
};

// Mock fetch function for loading state
const mockLoadingFetch = () => new Promise(() => {});

// Mock fetch function for error state
const mockErrorFetch = () => Promise.reject(new Error('Failed to generate image'));

export default {
  title: 'Domains/Apps/Usecases/ImageUsecase',
  component: ImageUsecase,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'An image generation usecase component that creates images using DALL-E API.'
      }
    }
  },
  argTypes: {
    id: { control: 'text' },
    data: { control: 'object' }
  }
};

// Default story
export const Default = {
  args: {
    id: "default-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Default view with image generation form fields.'
      }
    }
  }
};

// Generated Image story
export const GeneratedImage = {
  args: {
    id: "generated-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the component with a generated image'
      }
    }
  },
  decorators: [
    (Story) => {
      // Mock the global fetch before rendering
      const originalFetch = window.fetch;
      window.fetch = mockImageFetch;

      return (
        <div>
          <Story />
        </div>
      );
    }
  ]
};

// Loading state story
export const LoadingState = {
  args: {
    id: "loading-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the component in loading state while generating image'
      }
    }
  },
  decorators: [
    (Story) => {
      // Mock the fetch to never resolve
      const originalFetch = window.fetch;
      window.fetch = mockLoadingFetch;

      return (
        <div>
          <Story />
        </div>
      );
    }
  ]
};

// Error state story
export const ErrorState = {
  args: {
    id: "error-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the component in error state'
      }
    }
  },
  decorators: [
    (Story) => {
      // Mock the fetch to reject
      const originalFetch = window.fetch;
      window.fetch = mockErrorFetch;

      return (
        <div>
          <Story />
        </div>
      );
    }
  ]
};

// Different Image Styles story
export const DifferentImageStyles = {
  args: {
    id: "styles-id",
    data: {
      ...mockData,
      'dall-e_settings': {
        ...mockData['dall-e_settings'],
        form_fields: mockData['dall-e_settings'].form_fields.map(field => 
          field.name === 'image_style' 
            ? { ...field, default_value: 'Pop-art' }
            : field
        )
      }
    }
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the component with different image style selections'
      }
    }
  },
  decorators: [
    (Story) => {
      window.fetch = mockImageFetch;
      return <Story />;
    }
  ]
}; 