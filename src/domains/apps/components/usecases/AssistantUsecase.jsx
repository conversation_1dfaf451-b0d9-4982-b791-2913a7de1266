import { useState, useRef, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useLocation } from 'react-router-dom';
import '../styles/UsecaseCard.scss';
import '../styles/AssistantUsecase.scss';
import WelcomeHeader from '@components/WelcomeHeader/WelcomeHeader.jsx';
import FavoriteButton from '@components/FavoriteButton';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';
import { useAssistantConnectorsMutation } from '../../../../redux/services/connectors-api';
import { useUpdateAppTrackingMutation } from '../../../../redux/services/app-tracking-api';
import useUpdateJourneyTracking from '../../../../domains/journey/utils/updateJourneyTracking';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { transformHtmlContent } from '../../../../middleware/htmlTransformer';

import {
  Box,
  Typography,
  Button,
  Stack,
  IconButton,
  Tooltip,
  TextField,
  Container,
  Grid,
  Avatar,
  CircularProgress,
  LinearProgress,
} from '@mui/material';

// PDF.js imports
import * as pdfjsLib from 'pdfjs-dist';

// Icons
import DescriptionIcon from '@mui/icons-material/Description';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import SendIcon from '@mui/icons-material/Send';
import SmartToyOutlinedIcon from '@mui/icons-material/SmartToyOutlined';
import PersonOutlineOutlinedIcon from '@mui/icons-material/PersonOutlineOutlined';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import CloseIcon from '@mui/icons-material/Close';

import fileIcon from '../../../../assets/oc-browse.svg';
import { useSelector } from 'react-redux';
// Worker setup
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`;

// Yeni stil tanımlamaları ekleyelim
const loadingMessageStyles = {
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
  padding: '8px 0',
  '.loading-indicator': {
    color: 'primary.main',
  },
  '.message-text': {
    color: 'text.primary',
  },
};

const AssistantUsecase = ({ data, onGenerate }) => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const currentLanguage = i18n.language.toLowerCase().startsWith('de') ? 'german' : 'english';
  const [generateAssistantResponse] = useAssistantConnectorsMutation();
  const [updateAppTracking] = useUpdateAppTrackingMutation();
  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();
  const user = useSelector((state) => state.auth.user);

  // URL state'inden journey parametrelerini al
  const journeyCardId = location.state?.journeyCardId;
  const journeyTrackingData = location.state?.journeyTrackingData;
  const journeyLevel = location.state?.journeyLevel;

  const [pdfLoaded, setPdfLoaded] = useState(false);
  const [pdfDoc, setPdfDoc] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [scale, setScale] = useState(1.0);
  const [currentPage, setCurrentPage] = useState(1);

  const canvasContainerRef = useRef(null);
  const containerRef = useRef(null);

  // Chat states
  const [message, setMessage] = useState('');
  const [chatHistory, setChatHistory] = useState([]);
  const [isSending, setIsSending] = useState(false);
  const [isSummarizing, setIsSummarizing] = useState(false);
  const gridRef = useRef(null);

  // Typing effect için gerekli state ve ref'ler
  const [isTyping, setIsTyping] = useState(false);
  const [typingSpeed] = useState(5);
  const [displayedContent, setDisplayedContent] = useState({});
  const typewriterRef = useRef({ messageId: null, text: '', index: 0 });

  const [showQuestions, setShowQuestions] = useState(false);

  // Seçilen dile göre soruları al
  const getQuestionsForCurrentLanguage = () => {
    if (
      data.translations &&
      data.translations[currentLanguage]?.assistant_settings?.assistant_questions
    ) {
      return data.translations[currentLanguage].assistant_settings.assistant_questions;
    }
    return [];
  };

  const loadPDF = async () => {
    try {
      setIsSummarizing(true);

      // Seçilen dile göre PDF URL'sini al
      const pdfUrl =
        data.translations?.[currentLanguage]?.assistant_settings?.open_ai_assistant_file_url ||
        data.assistant_settings.open_ai_assistant_file_url;

      // PDF URL kontrolü
      if (!pdfUrl) {
        setPdfLoaded(true);
        setChatHistory([
          {
            role: 'assistant',
            content: t('assistantUsecase.chat.pdfMissing'),
          },
        ]);
        return;
      }

      // Load PDF with proper error handling
      const loadingTask = pdfjsLib.getDocument({
        url: pdfUrl,
        cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
        cMapPacked: true,
      });

      // Loading progress
      loadingTask.onProgress = function (progress) {
        return (
          <LinearProgress value={(progress.loaded / progress.total) * 100} variant="determinate" />
        );
      };

      const pdf = await loadingTask.promise;

      // PDF yüklendi, state'leri güncelle
      setPdfDoc(pdf);
      setNumPages(pdf.numPages);
      setPdfLoaded(true);
      setCurrentPage(1); // İlk sayfaya dön

      // Kullanım kaydını tut
      const currentDate = new Date();
      await updateAppTracking({
        userId: user?._id,
        appId: data._id,
        appType: 'usecase',
        year: currentDate.getFullYear(),
        month: currentDate.getMonth() + 1,
        day: currentDate.getDate(),
      });

      // Journey tracking'i güncelle (eğer journey parametreleri varsa)
      if (journeyCardId && journeyTrackingData && journeyLevel && user?._id) {
        try {
          // Journey tracking veritabanını güncelle
          await updateJourneyTrackingCard({
            userId: user._id,
            journeyTrackingData: journeyTrackingData,
            userLevel: journeyLevel.toLowerCase(),
            cardId: journeyCardId,
          });

          // TrainingProgress için localStorage üzerinden bildirim gönder
          localStorage.setItem('journey_tracking_updated', Date.now().toString());

          // TrainingProgress widget'ını güncellemek için bir custom event tetikleyelim
          window.dispatchEvent(
            new CustomEvent('update_training_progress', {
              detail: {
                source: 'assistant_usecase',
                cardId: journeyCardId,
                trackingData: true,
                timestamp: Date.now(),
              },
            })
          );

          // Kalıcı bir işaretçi ekleyelim - Footer.jsx bunu algılayacak
          localStorage.setItem('tracking_completed_card', journeyCardId);
        } catch (error) {
          if (import.meta.env.DEV) {
            console.error('Failed to update journey tracking for usecase card:', error);
          }
        }
      }

      // Get summary after PDF is loaded
      await handleSummarize();
    } catch (err) {
      console.error('Error loading PDF:', err.message);
      setPdfLoaded(true);
      setChatHistory([
        {
          role: 'assistant',
          content: t('assistantUsecase.chat.pdfError'),
        },
      ]);
    } finally {
      setIsSummarizing(false);
    }
  };

  const renderAllPages = async () => {
    if (!pdfDoc || !canvasContainerRef.current) return;

    try {
      // İçeriği temizle
      canvasContainerRef.current.innerHTML = '';

      // Her sayfayı render et
      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        const page = await pdfDoc.getPage(pageNum);

        // Sayfa container'ı oluştur
        const pageContainer = document.createElement('div');
        pageContainer.className = 'pdf-page-container';
        pageContainer.setAttribute('data-page-number', pageNum);

        // Sayfa numarasını oluştur
        const pageNumberElement = document.createElement('div');
        pageNumberElement.className = 'pdf-page-number';
        pageNumberElement.textContent = `Page ${pageNum} / ${numPages}`;
        pageContainer.appendChild(pageNumberElement);

        // Canvas oluştur
        const canvas = document.createElement('canvas');
        canvas.className = 'pdf-canvas';
        pageContainer.appendChild(canvas);

        // Container'ı ekle
        canvasContainerRef.current.appendChild(pageContainer);

        // PDF sayfasını render et
        const context = canvas.getContext('2d', { alpha: false });
        const viewport = page.getViewport({
          scale: scale,
          rotation: 0,
          offsetX: 0,
          offsetY: 0,
        });

        // Canvas boyutunu viewport'a göre ayarla
        canvas.width = viewport.width;
        canvas.height = viewport.height;

        // Canvas stilini scale değerine göre dinamik olarak ayarla - tam genişlik yerine gerçek boyut
        canvas.style.width = `${viewport.width}px`;
        canvas.style.height = `${viewport.height}px`;
        canvas.style.maxWidth = 'none';

        // Sayfayı render et - tüm PDF.js özelliklerini aktif edelim
        await page.render({
          canvasContext: context,
          viewport: viewport,
          enableWebGL: true, // WebGL hızlandırma
          annotationMode: 2, // ENABLE_FORMS = 2
          intent: 'display', // Ekran için optimizasyon
        }).promise;
      }
    } catch (err) {
      if (import.meta.env.DEV) {
        console.error('Error rendering PDF pages:', err);
      }
      setChatHistory((prev) => [
        ...prev,
        {
          role: 'assistant',
          content: 'An error occurred while displaying the PDF pages. Please try again.',
        },
      ]);
    }
  };

  // Render all pages whenever scale changes or PDF loads
  useEffect(() => {
    if (pdfLoaded && pdfDoc) {
      renderAllPages();
    }
  }, [pdfLoaded, pdfDoc, scale]);

  /**
   * Handles zoom functionality for the PDF viewer
   * @param {number} delta - Amount to zoom in/out
   * @returns {void}
   */
  const handleZoom = (delta) => {
    setScale((prevScale) => {
      // Daha hassas zoom değişimleri için sabit değerler kullanalım
      const zoomStep = 0.25; // Zoom adımını arttırdım

      // Zoom değişimi ve sınırları ayarla
      let newScale;
      if (delta < 0) {
        // Zoom out - 0.25 azalt, minimum 0.5
        newScale = Math.max(0.5, prevScale - zoomStep);
      } else {
        // Zoom in - 0.25 artır, maximum 4.0
        newScale = Math.min(4.0, prevScale + zoomStep);
      }

      // Zoom değişimini tamamla
      return Math.round(newScale * 100) / 100; // İki ondalık basamağa yuvarla
    });
  };

  const handleFullscreen = () => {
    if (containerRef.current) {
      if (
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      ) {
        // Farklı tarayıcılar için uyumlu exitFullscreen çağrısı
        const exitMethod =
          document.exitFullscreen ||
          document.webkitExitFullscreen ||
          document.mozCancelFullScreen ||
          document.msExitFullscreen;

        if (exitMethod) {
          exitMethod
            .call(document)
            .catch((err) => console.error('Tam ekrandan çıkarken hata:', err));
        }
      } else {
        // Farklı tarayıcılar için uyumlu requestFullscreen çağrısı
        const element = containerRef.current;
        const requestMethod =
          element.requestFullscreen ||
          element.webkitRequestFullscreen ||
          element.mozRequestFullScreen ||
          element.msRequestFullscreen;

        if (requestMethod) {
          requestMethod
            .call(element)
            .catch((err) => console.error('Tam ekran yaparken hata:', err));
        } else {
          if (import.meta.env.DEV) {
            console.warn('Tarayıcınız tam ekran modunu desteklemiyor');
          }
        }
      }
    }
  };

  /**
   * Initiates PDF summarization process
   * @async
   * @returns {Promise<void>}
   */
  const handleSummarize = async () => {
    if (import.meta.env.DEV) {
      console.log('handleSummarize function called');
    }
    try {
      // Analiz başladığında yükleniyor mesajını ekle
      const loadingMessageId = `loading-${Date.now()}`;
      setChatHistory([
        {
          id: loadingMessageId,
          role: 'assistant',
          content: t('assistantUsecase.chat.analyzingPdf'),
          isLoading: true,
        },
      ]);

      const result = await generateAssistantResponse({
        assistantId: data.assistant_settings.open_ai_assistant_id,
        prompt:
          'Please analyze this PDF document in detail and provide a comprehensive summary. Focus on key points, main findings, and important statistics.',
        temperature: 1.0,
        frequency: 1.0,
        presence: 0.5,
        instructions: `${data.assistant_settings.assistant_instructions}\nPlease analyze this PDF document thoroughly. Summarize the content, main headings, and key findings. Pay special attention to sales trends, performance metrics, and significant statistics. If the document contains data analysis, please highlight the most important insights.`,
        assistant_settings: data.assistant_settings,
      });

      if (result.error) {
        throw new Error(result.error.data || t('common.errors.textGenerationFailed'));
      }

      const summaryMessageId = `summary-${Date.now()}`;

      // Yükleniyor mesajını kaldır ve yeni mesajı ekle
      setChatHistory((prevHistory) => [
        ...prevHistory.filter((msg) => !msg.isLoading),
        {
          id: summaryMessageId,
          role: 'assistant',
          content: result.data,
        },
      ]);

      // Typing efekti için displayedContent'i güncelle
      setDisplayedContent((prev) => ({
        ...prev,
        [summaryMessageId]: '',
      }));

      // Kısa bir gecikme ile efekti başlat
      setTimeout(() => {
        typewriterRef.current = {
          messageId: summaryMessageId,
          text: result.data,
          index: 0,
        };
        setIsTyping(true);
      }, 100);
    } catch (err) {
      if (import.meta.env.DEV) {
        console.error('PDF summary creation error:', err);
      }
      const errorMessageId = `error-${Date.now()}`;

      setChatHistory((prevHistory) => [
        ...prevHistory.filter((msg) => !msg.isLoading),
        {
          id: errorMessageId,
          role: 'assistant',
          content: t('assistantUsecase.chat.summarizeError'),
        },
      ]);

      setDisplayedContent((prev) => ({
        ...prev,
        [errorMessageId]: '',
      }));

      setTimeout(() => {
        typewriterRef.current = {
          messageId: errorMessageId,
          text: t('assistantUsecase.chat.summarizeError'),
          index: 0,
        };
        setIsTyping(true);
      }, 100);
    }
  };

  const handleSendMessage = async (customMessage = null) => {
    // Eğer özel bir mesaj parametre olarak geldiyse onu kullan, yoksa state'teki message değerini kullan
    const messageToSend = customMessage || message;

    if (!messageToSend.trim() || isSending) {
      return;
    }

    // PDF yüklenmemiş ise toast bildirimi göster
    if (!pdfLoaded) {
      // Toast bildirimi göster
      toast.warning(t('assistantUsecase.chat.pdfLoadWarning'), {
        position: 'top-center',
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });

      return; // İşlemi burada sonlandır
    }

    try {
      // Soru alanı açıksa kapat
      if (showQuestions) {
        setShowQuestions(false);
      }

      setIsSending(true);

      // Kullanıcı mesajını ekle
      const userMessageId = `user-${Date.now()}`;
      const userMessage = {
        id: userMessageId,
        role: 'user',
        content: messageToSend,
      };

      setChatHistory((prev) => [...prev, userMessage]);

      // Eğer özel bir mesaj değilse input alanını temizle
      if (!customMessage) {
        setMessage('');
      }

      // Mesaj ekledikten sonra aşağı kaydır
      setTimeout(() => {
        const chatBody = document.querySelector('.chat-body');
        if (chatBody) {
          chatBody.scrollTop = chatBody.scrollHeight;
        }
      }, 100);

      // AbortController oluştur
      const abortController = new AbortController();

      // Assistant API'yi çağır
      const response = await generateAssistantResponse({
        assistantId: data.assistant_settings.open_ai_assistant_id,
        prompt: messageToSend,
        temperature: 1.0,
        frequency: 1.0,
        presence: 0.5,
        instructions: data.assistant_settings.assistant_instructions,
        assistant_settings: data.assistant_settings,
        abortController,
        onUpdate: (streamContent) => {
          try {
            // Stream içeriğini parse et
            let parsed;
            try {
              if (typeof streamContent === 'string') {
                parsed = JSON.parse(streamContent);
              } else {
                parsed = streamContent;
              }
            } catch (parseError) {
              if (import.meta.env.DEV) {
                console.error('Parse error:', parseError);
              }
              return;
            }

            // Yanıtı kontrol et ve içeriği al
            const content = parsed.data?.content || parsed.content;

            if (content) {
              const assistantMessageId = `assistant-${Date.now()}`;

              setChatHistory((prev) => {
                const lastMessage = prev[prev.length - 1];
                if (lastMessage && lastMessage.role === 'assistant') {
                  // Mevcut asistan mesajını güncelle
                  return [
                    ...prev.slice(0, -1),
                    {
                      ...lastMessage,
                      content: String(lastMessage.content + content),
                      id: assistantMessageId,
                    },
                  ];
                }
                // Yeni asistan mesajı ekle
                return [
                  ...prev,
                  {
                    role: 'assistant',
                    content: String(content),
                    id: assistantMessageId,
                  },
                ];
              });

              setDisplayedContent((prev) => ({
                ...prev,
                [assistantMessageId]: content,
              }));
            }
          } catch (e) {
            if (import.meta.env.DEV) {
              console.error('Processing error:', e);
            }
          }
        },
      });

      if (response.error) {
        throw new Error(response.error.data || t('common.errors.textGenerationFailed'));
      }

      // Mesaj ekledikten sonra aşağı kaydır
      setTimeout(() => {
        const chatBody = document.querySelector('.chat-body');
        if (chatBody) {
          chatBody.scrollTop = chatBody.scrollHeight;
        }
      }, 100);
    } catch (err) {
      if (import.meta.env.DEV) {
        console.error('Error sending message:', err);
      }

      // Hata mesajı için de id oluşturalım ve typing efekti uygulayalım
      const errorMessageId = `error-${Date.now()}`;
      const errorContent = t('assistantUsecase.chat.messageError');

      // Hata mesajını ekle (önce boş içerikle)
      setDisplayedContent((prev) => ({
        ...prev,
        [errorMessageId]: '',
      }));

      setChatHistory((prev) => [
        ...prev,
        {
          id: errorMessageId,
          role: 'assistant',
          content: errorContent,
        },
      ]);

      // Hata mesajı için de typing efekti başlat
      setTimeout(() => {
        typewriterRef.current = {
          messageId: errorMessageId,
          text: errorContent,
          index: 0,
        };
        setIsTyping(true);
      }, 100);
    } finally {
      setIsSending(false);
    }
  };

  const defaultDescription =
    '<p>Upload any PDF document to analyze it, extract key information, and get answers to specific questions about the content.</p>';

  // Dil bazlı veri getiren fonksiyon
  const getLocalizedData = useCallback(() => {
    let languageKey = 'english';

    if (i18n.language === 'de' || i18n.language === 'german' || i18n.language === 'deutsch') {
      languageKey = 'german';
    }

    let localData;
    if (data.translations && data.translations[languageKey]) {
      localData = { ...data.translations[languageKey] };
    } else if (data.translations && data.translations.english) {
      localData = { ...data.translations.english };
    } else {
      localData = {
        title: data.title,
        description: data.content || defaultDescription,
      };
    }

    // Description'ı dönüştür
    if (localData.description) {
      localData.description = transformHtmlContent(localData.description);
    }

    return localData;
  }, [data, i18n.language, defaultDescription]);

  // Call onGenerate if PDF is loaded
  useEffect(() => {
    if (pdfLoaded && onGenerate) {
      onGenerate();
    }
  }, [pdfLoaded, onGenerate]);

  // Typewriter effect için useEffect - güncellenmiş ve daha güvenli versiyonu
  useEffect(() => {
    // Guard clauses
    if (!isTyping) return;
    if (!typewriterRef.current) return;
    if (!typewriterRef.current.messageId) return;
    if (!typewriterRef.current.text) {
      setIsTyping(false);
      return;
    }

    const { messageId, text } = typewriterRef.current;

    // Mesaj zaten tamamen gösterilmişse
    if (displayedContent[messageId] === text) {
      setIsTyping(false);
      return;
    }

    // Scroll yardımcı fonksiyonu
    const scrollToBottom = () => {
      const chatBody = document.querySelector('.chat-body');
      if (chatBody) {
        chatBody.scrollTop = chatBody.scrollHeight;
      }
    };

    // İlk başta scroll yap
    scrollToBottom();

    const timer = setInterval(() => {
      // Her seferinde en güncel değeri al
      const currentIndex = typewriterRef.current.index;

      // Text boş olabilir, kontrol et
      if (!text || typeof text !== 'string') {
        clearInterval(timer);
        setIsTyping(false);
        return;
      }

      // Son karaktere ulaşıldıysa
      if (currentIndex >= text.length) {
        clearInterval(timer);
        setIsTyping(false);

        // Yazma tamamlandığında chat'in en altına scroll yap
        setTimeout(scrollToBottom, 100);
        return;
      }

      // İndex'i güncelle
      const nextIndex = currentIndex + 1;
      typewriterRef.current = {
        messageId,
        text,
        index: nextIndex,
      };

      // Metni güncelle
      setDisplayedContent((prev) => ({
        ...prev,
        [messageId]: text.slice(0, nextIndex),
      }));

      // Her karakterde de chat'in en altına scroll yap
      scrollToBottom();
    }, typingSpeed);

    // Cleanup
    return () => {
      clearInterval(timer);
    };
  }, [isTyping, typingSpeed, displayedContent]);

  // Add initial welcome message when component mounts
  useEffect(() => {
    // Only add welcome message if chat history is empty
    if (chatHistory.length === 0) {
      const welcomeMessageId = `welcome-${Date.now()}`;
      const welcomeContent = t('assistantUsecase.chat.welcomeMessage');

      setChatHistory([
        {
          id: welcomeMessageId,
          role: 'assistant',
          content: welcomeContent,
        },
      ]);

      // Başlangıç mesajını boş olarak başlat ve typing efekti uygula
      setDisplayedContent({
        [welcomeMessageId]: '',
      });

      // Typing efektini başlat
      setTimeout(() => {
        typewriterRef.current = {
          messageId: welcomeMessageId,
          text: welcomeContent,
          index: 0,
        };
        setIsTyping(true);
      }, 500); // Biraz gecikme ekledim ki sayfanın yüklenmesi için zaman olsun
    }
  }, []); // Empty dependency array means this runs once on mount

  // Sayfa yüklendiğinde assistant-grid'e scroll etmek için
  useEffect(() => {
    const timer = setTimeout(() => {
      if (gridRef.current) {
        gridRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 500); // Sayfanın yüklenmesi için kısa bir süre bekle

    return () => clearTimeout(timer);
  }, []); // Sadece component monte edildiğinde çalışır

  /**
   * Önceki sayfaya git
   */
  const handlePrevPage = () => {
    if (currentPage > 1) {
      const newPage = currentPage - 1;
      setCurrentPage(newPage);
      scrollToPage(newPage);
    }
  };

  /**
   * Sonraki sayfaya git
   */
  const handleNextPage = () => {
    if (currentPage < numPages) {
      const newPage = currentPage + 1;
      setCurrentPage(newPage);
      scrollToPage(newPage);
    }
  };

  /**
   * Belirli bir sayfaya scroll yapar
   */
  const scrollToPage = (pageNum) => {
    const pageElem = canvasContainerRef.current?.querySelector(`[data-page-number="${pageNum}"]`);
    if (pageElem) {
      pageElem.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // Canvas container scroll olayını dinle
  useEffect(() => {
    const handleScroll = () => {
      if (!canvasContainerRef.current) return;

      // Ekranda görünen sayfayı tespit et
      const containerRect = canvasContainerRef.current.getBoundingClientRect();
      const pageElements = canvasContainerRef.current.querySelectorAll('.pdf-page-container');

      // En çok görünen sayfayı bul
      let mostVisiblePage = 1;
      let maxVisibleArea = 0;

      pageElements.forEach((pageElem) => {
        const pageRect = pageElem.getBoundingClientRect();
        const pageNum = parseInt(pageElem.getAttribute('data-page-number'));

        // Sayfa ve container arasındaki kesişim alanını hesapla
        const visibleTop = Math.max(pageRect.top, containerRect.top);
        const visibleBottom = Math.min(pageRect.bottom, containerRect.bottom);
        const visibleHeight = Math.max(0, visibleBottom - visibleTop);

        if (visibleHeight > maxVisibleArea) {
          maxVisibleArea = visibleHeight;
          mostVisiblePage = pageNum;
        }
      });

      if (mostVisiblePage !== currentPage) {
        setCurrentPage(mostVisiblePage);
      }
    };

    const container = canvasContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [currentPage, pdfLoaded]);

  // Markdown için özel kod bileşeni
  const Code = ({ inline, className, children, ...props }) => {
    const match = /language-(\w+)/.exec(className || '');
    const codeString = String(children).replace(/\n$/, '');

    return !inline && match ? (
      <div className="code-block">
        <SyntaxHighlighter style={vscDarkPlus} language={match[1]} PreTag="div" {...props}>
          {codeString}
        </SyntaxHighlighter>
      </div>
    ) : (
      <code className={className} {...props}>
        {children}
      </code>
    );
  };

  Code.propTypes = {
    inline: PropTypes.bool,
    className: PropTypes.string,
    children: PropTypes.node.isRequired,
  };

  const CodeBlock = {
    code: Code,
  };

  return (
    <>
      {/* WelcomeHeader component */}

      <WelcomeHeader
        isPageView={true}
        title={getLocalizedData().title || data.title}
        description={getLocalizedData().description || data.content || defaultDescription}
        showProgress={false}
      />

      <Container className="assistant-container" maxWidth="lg">
        <Grid container spacing={3} className="assistant-grid" ref={gridRef}>
          {/* Left side - PDF Viewer */}
          <Grid item xs={12} md={7} className="pdf-grid-item">
            <Box className="pdf-box" sx={{ height: pdfLoaded ? '700px' : '100%' }}>
              {!pdfLoaded ? (
                <div className="upload-section">
                  <img src={fileIcon} alt="Upload" className="upload-icon" />
                  <Typography variant="h6" className="upload-title">
                    {t('assistantUsecase.pdfViewer.uploadTitle')}
                  </Typography>
                  <Tooltip
                    placement="top"
                    title={
                      <Typography variant="body2" className="tooltip-text">
                        {t('assistantUsecase.pdfViewer.uploadFeatureTooltip')}
                      </Typography>
                    }
                  >
                    <span style={{ display: 'inline-block', pointerEvents: 'auto' }}>
                      <Button
                        className="upload-button"
                        onClick={loadPDF}
                        startIcon={<DescriptionIcon />}
                        disabled
                        sx={{ opacity: 0.7 }}
                      >
                        {t('assistantUsecase.pdfViewer.selectLocalFile')}
                      </Button>
                    </span>
                  </Tooltip>
                  <Typography variant="body2" className="or-text">
                    {t('assistantUsecase.pdfViewer.or')}
                  </Typography>
                  <Button
                    variant="text"
                    color="primary"
                    className="sample-button"
                    onClick={loadPDF}
                  >
                    {t('assistantUsecase.pdfViewer.loadSample')}
                  </Button>
                </div>
              ) : (
                <div className="pdf-viewer" ref={containerRef}>
                  <div className="pdf-controls">
                    <IconButton
                      onClick={handlePrevPage}
                      className="pdf-control-button"
                      disabled={currentPage <= 1}
                      aria-label={t('assistantUsecase.pdfViewer.prevPage')}
                    >
                      <ArrowBackIcon />
                    </IconButton>

                    <Typography className="page-info">
                      {currentPage} / {numPages}
                    </Typography>

                    <IconButton
                      onClick={handleNextPage}
                      className="pdf-control-button"
                      disabled={currentPage >= numPages}
                      aria-label={t('assistantUsecase.pdfViewer.nextPage')}
                    >
                      <ArrowForwardIcon />
                    </IconButton>

                    <div className="controls-divider"></div>

                    <IconButton
                      onClick={() => handleZoom(-1)}
                      className="pdf-control-button"
                      aria-label={t('assistantUsecase.pdfViewer.zoomOut')}
                    >
                      <ZoomOutIcon />
                    </IconButton>

                    <Typography className="zoom-info">{Math.round(scale * 100)}%</Typography>

                    <IconButton
                      onClick={() => handleZoom(1)}
                      className="pdf-control-button"
                      aria-label={t('assistantUsecase.pdfViewer.zoomIn')}
                    >
                      <ZoomInIcon />
                    </IconButton>

                    <IconButton
                      onClick={handleFullscreen}
                      className="pdf-control-button"
                      aria-label={t('assistantUsecase.pdfViewer.fullscreen')}
                    >
                      <FullscreenIcon />
                    </IconButton>

                    <Box sx={{ marginLeft: 'auto' }}>
                      <FavoriteButton
                        iconOnly={false}
                        shortcutID={data._id}
                        shortcutType="usecase"
                      />
                    </Box>
                  </div>

                  <div className="canvas-container" ref={canvasContainerRef}>
                    {/* Canvas elements for PDF pages will be added here dynamically */}
                  </div>
                </div>
              )}
            </Box>
          </Grid>

          {/* Right side - Chat */}
          <Grid item xs={12} md={5} className="chat-grid-item">
            <Box className="chat-box">
              <div className="chat-body">
                {isSummarizing && !chatHistory.length ? (
                  <div className="chat-message assistant">
                    <div className="message-header">
                      <Avatar className="assistant-avatar">
                        <SmartToyOutlinedIcon fontSize="small" />
                      </Avatar>
                      <Typography className="message-sender">
                        {t('assistantUsecase.chat.assistant')}
                      </Typography>
                    </div>
                    <div className="message-content">
                      <CircularProgress size={20} thickness={4} className="loading-indicator" />
                      <Typography className="message-text">
                        {t('assistantUsecase.chat.analyzingPdf')}
                      </Typography>
                    </div>
                  </div>
                ) : (
                  chatHistory.map((chat, index) => (
                    <div key={chat.id || index} className={`chat-message ${chat.role}`}>
                      <div className="message-header">
                        <Avatar className={`${chat.role}-avatar`}>
                          {chat.role === 'assistant' ? (
                            <SmartToyOutlinedIcon fontSize="small" />
                          ) : (
                            <PersonOutlineOutlinedIcon fontSize="small" />
                          )}
                        </Avatar>
                        <Typography className="message-sender">
                          {chat.role === 'assistant'
                            ? t('assistantUsecase.chat.assistant')
                            : t('assistantUsecase.chat.you')}
                        </Typography>
                      </div>
                      <div className="message-content">
                        {chat.isLoading ? (
                          <div className="loading-message" style={loadingMessageStyles}>
                            <CircularProgress
                              size={20}
                              thickness={4}
                              className="loading-indicator"
                            />
                            <Typography className="message-text">{chat.content}</Typography>
                          </div>
                        ) : (
                          <>
                            <ReactMarkdown
                              components={{
                                ...CodeBlock,
                                strong: ({ children }) => <strong>{children}</strong>,
                                em: ({ children }) => <em>{children}</em>,
                                h1: ({ children }) => <h1>{children}</h1>,
                                h2: ({ children }) => <h2>{children}</h2>,
                                h3: ({ children }) => <h3>{children}</h3>,
                              }}
                              className="markdown-body"
                            >
                              {String(
                                chat.role === 'assistant'
                                  ? chat.id && displayedContent[chat.id] !== undefined
                                    ? displayedContent[chat.id] || ''
                                    : chat.content || ''
                                  : chat.content || ''
                              )}
                            </ReactMarkdown>
                            {chat.role === 'assistant' &&
                              isTyping &&
                              typewriterRef.current.messageId === chat.id && (
                                <span className="typing-cursor">|</span>
                              )}
                          </>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* Chat input container - translations kontrolüne bağlı olarak gösteriliyor */}
              {data.translations &&
                data.translations[currentLanguage]?.assistant_settings?.assistant_questions &&
                data.translations[currentLanguage].assistant_settings.assistant_questions.length >
                  0 && (
                  <Box className="chat-input-container" sx={{ position: 'relative' }}>
                    {/* Soru alanı - absolute pozisyonda alttan slide ile açılıp kapanacak */}
                    <Box
                      className={`predefined-questions-container ${showQuestions ? 'show' : ''}`}
                    >
                      <Typography variant="body2" className="sample-questions-title">
                        {t('assistantUsecase.chat.sampleQuestions')}
                      </Typography>
                      <Stack direction="column" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
                        {getQuestionsForCurrentLanguage().map((item, index) => (
                          <Box
                            key={index}
                            className="question-box"
                            onClick={() => {
                              handleSendMessage(item.question);
                              setShowQuestions(false);
                            }}
                          >
                            <Typography variant="body2" className="question-prefix">
                              &gt;
                            </Typography>
                            <Typography variant="body2" className="question-text">
                              {item.question}
                            </Typography>
                          </Box>
                        ))}
                      </Stack>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Box
                        className="input-group-container"
                        sx={{ flex: 1, display: 'flex', alignItems: 'center' }}
                      >
                        {/* Help ikonu */}
                        <Tooltip
                          title={
                            showQuestions
                              ? t('assistantUsecase.chat.closeQuestions')
                              : t('assistantUsecase.chat.sampleQuestions')
                          }
                        >
                          <IconButton
                            size="small"
                            onClick={() => setShowQuestions(!showQuestions)}
                            className="help-button-integrated"
                            sx={{
                              ml: 1,
                              color: showQuestions ? '#e53935' : '#9ca3af',
                              '&:hover': { color: showQuestions ? '#d32f2f' : '#6b7280' },
                            }}
                          >
                            {showQuestions ? <CloseIcon /> : <HelpOutlineIcon />}
                          </IconButton>
                        </Tooltip>

                        <TextField
                          fullWidth
                          variant="outlined"
                          placeholder={t('assistantUsecase.chat.askPlaceholder')}
                          value={message}
                          onChange={(e) => setMessage(e.target.value)}
                          disabled={isSummarizing || isTyping} // PDF yüklenmese bile aktif olabilir
                          className="chat-input-field"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              handleSendMessage();
                            }
                          }}
                          InputProps={{
                            // CSS sınıflarını kullanarak stil ekleyelim
                            classes: {
                              root: `input-with-button with-help-button`,
                            },
                            endAdornment: (
                              <IconButton
                                color="primary"
                                onClick={() => handleSendMessage()}
                                disabled={isSummarizing || isSending || !message.trim() || isTyping} // PDF yüklenmese bile aktif olabilir
                                className="send-button-integrated"
                                edge="end"
                              >
                                {isSending ? (
                                  <CircularProgress size={24} color="inherit" />
                                ) : (
                                  <SendIcon />
                                )}
                              </IconButton>
                            ),
                          }}
                        />
                      </Box>
                    </Box>
                  </Box>
                )}
            </Box>
          </Grid>
        </Grid>
        <Box className="ai-disclaimer-box">{t('common.warnings.dataPrivacy')}</Box>
      </Container>
    </>
  );
};

AssistantUsecase.propTypes = {
  id: PropTypes.string.isRequired,
  data: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    content: PropTypes.string,
    description: PropTypes.string,
    selectedFunctionSpecUseCases: PropTypes.string,
    translations: PropTypes.shape({
      english: PropTypes.shape({
        title: PropTypes.string,
        description: PropTypes.string,
      }),
      german: PropTypes.shape({
        title: PropTypes.string,
        description: PropTypes.string,
      }),
    }),
    assistant_settings: PropTypes.shape({
      open_ai_assistant_id: PropTypes.string,
      open_ai_assistant_file_url: PropTypes.string,
      assistant_instructions: PropTypes.string,
      assistant_questions: PropTypes.array,
    }),
  }).isRequired,
  onGenerate: PropTypes.func,
};

export default AssistantUsecase;
