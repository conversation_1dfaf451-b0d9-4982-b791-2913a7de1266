import React from 'react';
import AssistantUsecase from './AssistantUsecase';

const mockData = {
  _id: "6752f29e1ff89677740015b6",
  title: "Financial statement analyzer (PDF)",
  slug: "financial-statement-analyzer",
  function: [
    "finance",
    "hr",
    "it-ai-data"
  ],
  use_case_type: [
    "code-generation",
    "text-generation"
  ],
  uniqueid: "uid-94",
  usecase_icon_url: "https://cdn.aibusinessschool.com/usecases/2024-08-26_14-18-56-fade-stagger-circles.svgnessschool.com/usecases/2024-08-26_14-18-56-fade-stagger-circles.svg",
  api_type: "assistants",
  assistant_settings: {
    assitant_name: "Finance Analyzer",
    assistant_instructions: "You are a Financial Analyzer, Given the uploaded financial file, analyze and provide insights into key financial metrics such as revenue, expenses, profit margins, and any notable trends. Additionally, identify potential areas for improvement or risk based on the financial data. Use the available information to generate a comprehensive financial analysis report.",
    assistant_model: "gpt-4-1106-preview",
    assistant_type: "retrieval",
    open_ai_assistant_file_name: "TSLA-Q3-2023-Update",
    open_ai_assistant_file_url: "https://cdn.aibusinessschool.com/usecases/2024-06-11_12-31-14-TSLA-Q3-2023-Update-3_compressed-compressed_removed.pdf",
    open_ai_assistant_id: "asst_ws1wvWZnpCKoYdCsQ8KVmcg9",
    open_ai_file_id: "file-CtGwLoyjsLau7jTpV2phqE4L",
    assistants_questions: [
      {
        question: "If Tesla's total revenues continue to grow at the same rate as they did in Q3 2023, what would they be in Q3 2024?"
      },
      {
        question: "What is the gross profit margin for Tesla's automotive sales, energy generation and storage, and services and other segments?"
      },
      {
        question: "What is the relationship between Tesla's research and development spending and its revenue growth?"
      },
      {
        question: "What are the biggest risks and challenges facing Tesla?"
      },
      {
        question: "How has Tesla's cash balance changed over the past year?"
      },
      {
        question: "What is the relationship between Tesla's inventory levels and its production levels?"
      },
      {
        question: "How has Tesla's debt level changed over the past year?"
      },
      {
        question: "What is the percentage change in operating cash flow from Q3 2022 to Q3 2023?"
      },
      {
        question: "What is the maturity schedule for Tesla's long-term debt? Briefly describe the company's debt management strategy."
      },
      {
        question: "Break down the components of net cash flow from operating activities for Q3 2023. How do changes in accounts receivable, accounts payable, and inventory compare to previous quarters?"
      }
    ],
    custom_system_prompt: ""
  }
};

// Mock responses for different states
const mockSummaryResponse = {
  content: "Tesla's Q3 2023 financial report shows significant growth in revenue and operational efficiency. Key highlights include:\n\n- Total revenue increased by X% year-over-year\n- Automotive gross margin remained strong at Y%\n- Operating expenses were well controlled\n- Cash position remains robust at $Z billion\n\nThe company continues to focus on cost optimization while maintaining its market leadership in electric vehicles."
};

const mockChatResponse = {
  content: "Based on the Q3 2023 financial statements, Tesla's research and development spending has increased proportionally with revenue growth. Specifically:\n\n1. R&D expenses grew by X% year-over-year\n2. This represents Y% of total revenue\n3. Key focus areas include:\n   - Battery technology\n   - Manufacturing efficiency\n   - Software development"
};

// Mock the fetch function for different scenarios
const createMockFetch = (type) => {
  return async () => {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay

    switch (type) {
      case 'thread':
        return {
          ok: true,
          json: () => Promise.resolve({ id: 'mock-thread-id' })
        };
      case 'message':
        return {
          ok: true,
          json: () => Promise.resolve({ id: 'mock-message-id' })
        };
      case 'run':
        return {
          ok: true,
          json: () => Promise.resolve({ id: 'mock-run-id', status: 'completed' })
        };
      case 'summary':
        return {
          ok: true,
          json: () => Promise.resolve({ data: [{ content: [{ text: mockSummaryResponse }] }] })
        };
      case 'chat':
        return {
          ok: true,
          json: () => Promise.resolve({ data: [{ content: [{ text: mockChatResponse }] }] })
        };
      default:
        return {
          ok: true,
          json: () => Promise.resolve({})
        };
    }
  };
};

export default {
  title: 'Domains/Apps/Usecases/AssistantUsecase',
  component: AssistantUsecase,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A PDF analysis usecase component that uses OpenAI Assistant to analyze financial documents.'
      }
    }
  },
  argTypes: {
    id: { control: 'text' },
    data: { control: 'object' }
  }
};

// Sadece /api/assistant/summarize endpoint'i için mock yapıyoruz
// PDF fetch'lerini etkilemiyoruz
const originalFetch = window.fetch;
window.fetch = async (url, options) => {
  // Eğer URL'de 'api/assistant' varsa bizim API'miz
  if (url.includes('api/assistant')) {
    if (url === '/api/assistant/summarize') {
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ 
          summary: "I've analyzed the Tesla Q3 2023 report. The document highlights key financial metrics, production numbers, and strategic initiatives. Would you like to know more about any specific aspect?" 
        })
      });
    }
  }
  // PDF ve diğer istekler için orijinal fetch'i kullan
  return originalFetch(url, options);
};

// Default story
export const Default = {
  args: {
    id: "1",
    data: {
      title: "Tesla Q3 2023 Update",
      usecase_icon_url: "https://cdn.aibusinessschool.com/usecases/2024-08-26_14-18-56-fade-stagger-circles.svg",
      assistant_settings: {
        open_ai_assistant_file_url: "https://cdn.aibusinessschool.com/usecases/2024-06-11_12-31-14-TSLA-Q3-2023-Update-3_compressed-compressed_removed.pdf"
      }
    }
  },
  parameters: {
    docs: {
      description: {
        story: 'Default state before loading the PDF'
      }
    }
  }
};

// Loading state story
export const Loading = {
  args: {
    id: "loading-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the component while loading the PDF and generating summary'
      }
    }
  },
  decorators: [
    (Story) => {
      window.fetch = () => new Promise(() => {}); // Never resolves
      return <Story />;
    }
  ]
};

// PDF Loaded state
export const PDFLoaded = {
  args: {
    id: "loaded-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the component with PDF loaded and summary generated'
      }
    }
  },
  decorators: [
    (Story) => {
      window.fetch = createMockFetch('summary');
      return <Story />;
    }
  ]
};

// Chat Active state
export const ChatActive = {
  args: {
    id: "chat-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the component with active chat conversation'
      }
    }
  },
  decorators: [
    (Story) => {
      window.fetch = createMockFetch('chat');
      return <Story />;
    }
  ]
};

// Error state
export const Error = {
  args: {
    id: "error-id",
    data: mockData
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the component in error state'
      }
    }
  },
  decorators: [
    (Story) => {
      window.fetch = () => Promise.reject(new Error('Failed to load PDF'));
      return <Story />;
    }
  ]
};

// Single story with working configuration
export const Story = {
  args: {
    id: "1",
    data: {
      title: "Tesla Q3 2023 Update",
      usecase_icon_url: "https://cdn.aibusinessschool.com/usecases/2024-08-26_14-18-56-fade-stagger-circles.svg",
      assistant_settings: {
        open_ai_assistant_file_url: "https://cdn.aibusinessschool.com/usecases/2024-06-11_12-31-14-TSLA-Q3-2023-Update-3_compressed-compressed_removed.pdf"
      }
    }
  }
}; 