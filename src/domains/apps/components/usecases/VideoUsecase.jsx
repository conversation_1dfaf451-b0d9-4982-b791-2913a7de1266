import PropTypes from 'prop-types';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Box } from '@mui/material';
import { useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import useUpdateJourneyTracking from '../../../journey/utils/updateJourneyTracking';
import '../styles/UsecaseCard.scss';
import '../styles/VideoUsecase.scss';
import HeygenVideoCreator from '../../../../components/HeygenVideoCreator';
import WelcomeHeader from '../../../../components/WelcomeHeader/WelcomeHeader';
import { transformHtmlContent } from '../../../../middleware/htmlTransformer';

const VideoUsecase = ({ id, data }) => {
  const { i18n } = useTranslation();
  const location = useLocation();
  const user = useSelector((state) => state.auth.user);
  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();

  // URL state'inden journey parametrelerini al
  const journeyCardId = location.state?.journeyCardId;
  const journeyTrackingData = location.state?.journeyTrackingData;
  const journeyLevel = location.state?.journeyLevel;

  const getLocalizedData = useCallback(() => {
    let languageKey = 'english';

    if (i18n.language === 'de' || i18n.language === 'german' || i18n.language === 'deutsch') {
      languageKey = 'german';
    }

    let localData;
    if (data.translations && data.translations[languageKey]) {
      localData = { ...data.translations[languageKey] };
    } else if (data.translations && data.translations.english) {
      localData = { ...data.translations.english };
    } else {
      localData = {
        title: data.title,
        description: data.description,
      };
    }

    // Description'ı dönüştür
    if (localData.description) {
      localData.description = transformHtmlContent(localData.description);
    }

    return localData;
  }, [data, i18n.language]);

  const localizedData = getLocalizedData();

  return (
    <div className="usecase-card video-usecase" id={id}>
      <WelcomeHeader
        title={localizedData.title}
        description={localizedData.description}
        showProgress={false}
        isPageView={true}
      />
      <Box mt={3}>
        <HeygenVideoCreator id={id} />
      </Box>
    </div>
  );
};

VideoUsecase.propTypes = {
  id: PropTypes.string.isRequired,
  data: PropTypes.shape({
    title: PropTypes.string.isRequired,
    description: PropTypes.string,
    videoUrl: PropTypes.string,
    thumbnailUrl: PropTypes.string,
    translations: PropTypes.objectOf(
      PropTypes.shape({
        title: PropTypes.string.isRequired,
        description: PropTypes.string,
      })
    ),
  }).isRequired,
};

export default VideoUsecase;
