import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import '../styles/UsecaseCard.scss';
import '../styles/SimpleAppUsecase.scss';

// simple-app.svg from adoption-v2-frontend\src\assets/simple-app.svg
import simpleAppIcon from '../../../../assets/simple-app.svg';
import {
  Box,
  Paper,
  Typography,
  Button,
  CircularProgress,
  Stack,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Menu,
  ListItemIcon,
} from '@mui/material';

import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';
import EditIcon from '@mui/icons-material/Edit';
import ClearIcon from '@mui/icons-material/Clear';
import CompressIcon from '@mui/icons-material/Compress';
import ExpandIcon from '@mui/icons-material/Expand';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';

const SimpleAppUsecase = ({ id, data }) => {
  const [formData, setFormData] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState('');
  const [hasGenerated, setHasGenerated] = useState(false);
  const [seconds, setSeconds] = useState(0);
  const [contentHeight, setContentHeight] = useState(0);
  const contentRef = useRef(null);
  const [chatMaxHeight, setChatMaxHeight] = useState('auto');
  const [streamingResponse, setStreamingResponse] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingSpeed] = useState(5); // milliseconds per character
  const typewriterRef = useRef({ text: '', index: 0 });
  const abortControllerRef = useRef(null); // Add this to store abort controller
  const [copied, setCopied] = useState(false);
  const [modifyAnchorEl, setModifyAnchorEl] = useState(null);
  const [isModifying, setIsModifying] = useState(false);

  // Update initialization of formData
  useEffect(() => {
    if (!data.simple_app_form) return; // Guard clause

    const initialFormData = {};
    data.simple_app_form.forEach((field) => {
      if (field.defaultText) {
        initialFormData[field.name] = field.defaultText;
      }
    });
    setFormData(initialFormData);
  }, [data.simple_app_form]); // Only depend on simple_app_form

  /**
   * Counter effect - updated to continue during typing
   */
  useEffect(() => {
    let interval;
    if (isLoading || isTyping) {
      // Modified condition to include isTyping
      interval = setInterval(() => {
        setSeconds((prev) => prev + 1);
      }, 1000);
    } else {
      setSeconds(0);
    }
    return () => clearInterval(interval);
  }, [isLoading, isTyping]); // Added isTyping to dependencies

  /**
   * Measure and set height of the content
   */
  useEffect(() => {
    if (contentRef.current) {
      const height = contentRef.current.offsetHeight;
      setContentHeight(height);
    }
  }, [data]); // Re-measure when data changes

  /**
   * Measure and set height of the chat
   */
  useEffect(() => {
    setTimeout(() => {
      const contentElement = document.querySelector('.usecase-content');
      if (contentElement) {
        const rect = contentElement.getBoundingClientRect();
        setChatMaxHeight(`${rect.height}px`);
      }
    }, 100);
  }, [data]); // Run when data changes

  /**
   * Typewriter effect function
   */
  useEffect(() => {
    if (!isTyping || !streamingResponse) return;

    const text = streamingResponse;
    let index = typewriterRef.current.index;

    const timer = setInterval(() => {
      if (index >= text.length) {
        setIsTyping(false);
        clearInterval(timer);
        return;
      }

      typewriterRef.current = { text, index: index + 1 };
      setResponse(text.slice(0, index + 1));
      index++;
    }, typingSpeed);

    return () => clearInterval(timer);
  }, [streamingResponse, isTyping, typingSpeed]);

  /**
   * Format time function
   */
  const formatTime = (totalSeconds) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  /**
   * Handle input change function
   */
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  /**
   * Replace placeholders function
   */
  const replacePlaceholders = (prompt, variables) => {
    let result = prompt;
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      result = result.replaceAll(placeholder, value || '');
    });
    return result;
  };

  /**
   * Handle Event for Cancel button click
   */
  const handleCancel = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort(); // Abort the fetch request
    }
    setIsLoading(false);
    setIsTyping(false);
    setStreamingResponse(''); // Clear the streaming buffer
    typewriterRef.current = { text: '', index: 0 }; // Reset typewriter
    setHasGenerated(true); // Set to true so button shows "Regenerate"
  };

  /**
   * Handle Event for Generate button click
   */
  const handleButtonClick = async () => {
    try {
      // formData içinde değer var, validation'a gerek yok
      setIsLoading(true);
      setResponse('');
      setStreamingResponse('');
      setSeconds(0);
      typewriterRef.current = { text: '', index: 0 };

      // Create new AbortController for this request
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;

      const processedPrompt = replacePlaceholders(data.prompt || '', formData);

      // Updated API request configuration for OpenAI
      const payload = {
        model: data.model_selection ?? 'gpt-4o-mini', // or whichever model you want to use
        messages: [
          {
            role: 'user',
            content: processedPrompt,
          },
        ],
        max_tokens: 6400,
        temperature: parseFloat(data.temperature),
        top_p: parseFloat(data.top_p),
        frequency_penalty: parseFloat(data.frequency_penalty),
        presence_penalty: parseFloat(data.presence_penalty),
        stream: true,
      };

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`,
        },
        body: JSON.stringify(payload),
        signal, // Add the abort signal to the fetch request
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'API request failed');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      setIsTyping(true);

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value);
        const lines = buffer.split('\n');

        for (let i = 0; i < lines.length - 1; i++) {
          const line = lines[i].trim();
          if (line.startsWith('data: ')) {
            const jsonData = line.slice(6);
            if (jsonData === '[DONE]') continue;

            try {
              const parsed = JSON.parse(jsonData);
              const content = parsed.choices[0]?.delta?.content || '';
              setStreamingResponse((prev) => prev + content);
            } catch (e) {
              console.error('Error parsing JSON:', e);
            }
          }
        }
        buffer = lines[lines.length - 1];
      }

      setHasGenerated(true);
    } catch (error) {
      if (error.name === 'AbortError') {
      } else {
        console.error('Error generating response:', error);
        setResponse('Error generating response. Please try again.');
      }
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null; // Clear the abort controller
    }
  };

  /**
   * Render form field function
   * Get the field type and render the appropriate form field
   */
  const renderFormField = (field) => {
    const fieldName = field.name || field.label?.toLowerCase().replace(/\s+/g, '_') || field._id;

    switch (field.type) {
      case 'input':
        return (
          <FormControl fullWidth key={field._id} sx={{ mb: 2 }}>
            <TextField
              label={field.label}
              name={fieldName} // Fallback mekanizması ile oluşturulan name
              value={formData[fieldName] || field.default_value || ''}
              onChange={handleInputChange}
              variant="outlined"
            />
          </FormControl>
        );
      case 'textarea':
        return (
          <FormControl fullWidth key={field._id} sx={{ mb: 2 }}>
            <TextField
              label={field.label}
              name={field.name}
              value={formData[field.name] || ''} // default_value yerine formData kullan
              onChange={handleInputChange}
              variant="outlined"
              multiline
              rows={4}
            />
          </FormControl>
        );
      case 'select':
        const options = field.choices.split('\r\n').map((option) => option.trim());
        return (
          <FormControl fullWidth key={field._id} sx={{ mb: 2 }}>
            <InputLabel>{field.label}</InputLabel>
            <Select
              label={field.label}
              name={field.name}
              value={formData[field.name] || ''} // default_value yerine formData kullan
              onChange={handleInputChange}
            >
              {options.map((option, index) => (
                <MenuItem key={index} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );
      default:
        return null;
    }
  };

  const formFields = data.simple_app_form || [];

  // Update getModelName to use a default value since model_selection is no longer available
  const getModelName = () => {
    return 'GPT-4'; // Or any default model you want to display
  };

  /**
   * Custom renderer for code blocks with copy button
   */
  const CodeBlock = {
    code({ node, inline, className, children, ...props }) {
      const match = /language-(\w+)/.exec(className || '');
      const [codeString, setCodeString] = useState(String(children).replace(/\n$/, ''));
      const [isCopied, setIsCopied] = useState(false);

      const handleCopyCode = async () => {
        try {
          await navigator.clipboard.writeText(codeString);
          setIsCopied(true);
          setTimeout(() => setIsCopied(false), 2000);
        } catch (err) {
          console.error('Failed to copy code:', err);
        }
      };

      return !inline && match ? (
        <div style={{ position: 'relative' }}>
          <Tooltip title={isCopied ? 'Copied!' : 'Copy code'}>
            <IconButton
              onClick={handleCopyCode}
              size="small"
              sx={{
                position: 'absolute',
                right: '0.5rem',
                top: '0.5rem',
                bgcolor: 'background.paper',
                '&:hover': { bgcolor: 'action.hover' },
                zIndex: 1,
              }}
            >
              {isCopied ? (
                <CheckIcon fontSize="small" color="success" />
              ) : (
                <ContentCopyIcon fontSize="small" />
              )}
            </IconButton>
          </Tooltip>
          <SyntaxHighlighter
            style={vscDarkPlus}
            language={match[1]}
            PreTag="div"
            {...props}
            customStyle={{
              paddingTop: '2.5rem', // Make room for copy button
            }}
          >
            {codeString}
          </SyntaxHighlighter>
        </div>
      ) : (
        <code className={className} {...props}>
          {children}
        </code>
      );
    },
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(response);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  const handleClear = () => {
    setResponse('');
    setStreamingResponse('');
    setHasGenerated(false);
  };

  const handleModifyClick = (event) => {
    setModifyAnchorEl(event.currentTarget);
  };

  const handleModifyClose = () => {
    setModifyAnchorEl(null);
  };

  const handleModifyOption = async (type) => {
    handleModifyClose();
    setIsModifying(true);

    const modificationPrompts = {
      shorter: 'Please make the following response shorter while keeping the main points:',
      longer: 'Please expand the following response with more details and examples:',
      simpler: 'Please simplify the following response to make it easier to understand:',
    };

    try {
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;

      const payload = {
        model: data.model_selection ?? 'gpt-4o-mini',
        messages: [
          {
            role: 'assistant',
            content: response, // Previous response
          },
          {
            role: 'user',
            content: modificationPrompts[type],
          },
        ],
        temperature: parseFloat(data.temperature),
        top_p: parseFloat(data.top_p),
        frequency_penalty: parseFloat(data.frequency_penalty),
        presence_penalty: parseFloat(data.presence_penalty),
        stream: true,
      };

      setIsLoading(true);
      setResponse('');
      setStreamingResponse('');
      setSeconds(0);
      typewriterRef.current = { text: '', index: 0 };

      const apiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`,
        },
        body: JSON.stringify(payload),
        signal,
      });

      if (!apiResponse.ok) {
        throw new Error('Failed to modify response');
      }

      const reader = apiResponse.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      setIsTyping(true);

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value);
        const lines = buffer.split('\n');

        for (let i = 0; i < lines.length - 1; i++) {
          const line = lines[i].trim();
          if (line.startsWith('data: ')) {
            const jsonData = line.slice(6);
            if (jsonData === '[DONE]') continue;

            try {
              const parsed = JSON.parse(jsonData);
              const content = parsed.choices[0]?.delta?.content || '';
              setStreamingResponse((prev) => prev + content);
            } catch (e) {
              console.error('Error parsing JSON:', e);
            }
          }
        }
        buffer = lines[lines.length - 1];
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Error modifying response:', error);
        setResponse('Error modifying response. Please try again.');
      }
    } finally {
      setIsLoading(false);
      setIsModifying(false);
      abortControllerRef.current = null;
    }
  };

  return (
    <Box className="usecase-container">
      <Paper className="usecase-content" elevation={3} ref={contentRef}>
        <Stack spacing={3} sx={{ p: 3 }}>
          <Box className="usecase-content-header">
            <Box component="svg" width={64} height={64} viewBox="0 0 64 64">
              <image
                width="100%"
                height="100%"
                xlinkHref={data.usecase_icon_url ?? simpleAppIcon}
              />
            </Box>
            <Box className="title-inner">
              <Typography variant="h5" component="h2">
                {data.title}
              </Typography>
            </Box>
          </Box>

          <Box className="usecase-content-body">
            <Typography variant="body1" className="usecase-content-body-desc">
              {data.content}
            </Typography>
            <form id={`usecase-field-form-${data._id}`} className="usecase-content-body-form">
              {formFields.map((field) => renderFormField(field))}
            </form>
          </Box>

          <Box className="usecase-content-footer" sx={{ marginTop: '0 !important' }}>
            <Typography variant="caption" component="small">
              You have the flexibility to modify or change these inputs to create a more
              personalized experience.
            </Typography>

            <Box className="usecase-field-form-submission">
              <Typography variant="caption" className="model-info">
                Generated by{' '}
                <Box component="span" className="model-name">
                  {getModelName()}
                </Box>
              </Typography>

              <Stack direction="row" spacing={2} alignItems="center" className="right">
                {(isLoading || isTyping) && (
                  <>
                    <Typography variant="caption" className="loading-text">
                      {isLoading ? 'App is running' : 'Typing response'}, <br />
                      please wait
                    </Typography>
                    <Button variant="outlined" color="error" onClick={handleCancel} size="small">
                      Cancel
                    </Button>
                  </>
                )}
                <Button
                  variant="contained"
                  onClick={handleButtonClick}
                  disabled={isLoading || isTyping}
                  startIcon={(isLoading || isTyping) && <CircularProgress size={20} />}
                >
                  {isLoading || isTyping
                    ? formatTime(seconds)
                    : hasGenerated
                      ? 'Regenerate'
                      : 'Generate'}
                </Button>
              </Stack>
            </Box>
          </Box>
        </Stack>
      </Paper>

      <Paper
        className="usecase-chat"
        elevation={3}
        sx={{ maxHeight: chatMaxHeight, boxShadow: 'none' }}
      >
        <Box className="inner">
          <Typography variant="h6" className="usecase-chat-header" component="div">
            Output
          </Typography>
          <Box
            className="usecase-chat-body"
            sx={{
              height: 'calc(100% - 64px)',
              overflowY: 'auto',
              '& pre': {
                margin: '1rem 0',
                borderRadius: '4px',
              },
              '& code': {
                fontFamily: 'monospace',
                padding: '0.2em 0.4em',
                borderRadius: '3px',
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
              },
            }}
          >
            <ReactMarkdown components={CodeBlock} className="markdown-body">
              {response}
            </ReactMarkdown>
          </Box>
          <Box className="usecase-chat-footer">
            {/* Tools toolbar - only show when there's a response */}
            {response && !isLoading && !isTyping && (
              <Stack
                direction="row"
                spacing={1}
                sx={{
                  justifyContent: 'end',
                  mt: 2,
                  pb: 2,
                  borderTop: '1px solid rgba(0, 0, 0, 0.12)',
                  pt: 2,
                }}
              >
                <Tooltip title="Modify Response">
                  <IconButton
                    onClick={handleModifyClick}
                    size="small"
                    sx={{
                      bgcolor: 'rgba(0, 0, 0, 0.04)',
                      '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.08)' },
                    }}
                    disabled={isLoading || isTyping}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>

                <Menu
                  anchorEl={modifyAnchorEl}
                  open={Boolean(modifyAnchorEl)}
                  onClose={handleModifyClose}
                  anchorOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                  }}
                  transformOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                  }}
                >
                  <MenuItem onClick={() => handleModifyOption('shorter')} disabled={isModifying}>
                    <ListItemIcon>
                      <CompressIcon fontSize="small" />
                    </ListItemIcon>
                    Shorter
                  </MenuItem>
                  <MenuItem onClick={() => handleModifyOption('longer')} disabled={isModifying}>
                    <ListItemIcon>
                      <ExpandIcon fontSize="small" />
                    </ListItemIcon>
                    Longer
                  </MenuItem>
                  <MenuItem onClick={() => handleModifyOption('simpler')} disabled={isModifying}>
                    <ListItemIcon>
                      <AutoAwesomeIcon fontSize="small" />
                    </ListItemIcon>
                    Simpler
                  </MenuItem>
                </Menu>

                <Tooltip title={copied ? 'Copied!' : 'Copy'}>
                  <IconButton
                    onClick={handleCopy}
                    size="small"
                    sx={{
                      bgcolor: 'rgba(0, 0, 0, 0.04)',
                      '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.08)' },
                    }}
                  >
                    {copied ? <CheckIcon fontSize="small" /> : <ContentCopyIcon fontSize="small" />}
                  </IconButton>
                </Tooltip>

                <Tooltip title="Clear">
                  <IconButton
                    onClick={handleClear}
                    size="small"
                    sx={{
                      bgcolor: 'rgba(0, 0, 0, 0.04)',
                      '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.08)' },
                    }}
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Stack>
            )}
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

SimpleAppUsecase.propTypes = {
  id: PropTypes.string,
  data: PropTypes.shape({
    _id: PropTypes.string,
    title: PropTypes.string.isRequired,
    slug: PropTypes.string.isRequired,
    prompt: PropTypes.string,
    simple_app_form: PropTypes.arrayOf(
      PropTypes.shape({
        _id: PropTypes.string.isRequired,
        type: PropTypes.string.isRequired,
        label: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
      })
    ),
    temperature: PropTypes.string.isRequired,
    top_p: PropTypes.string.isRequired,
    frequency_penalty: PropTypes.string.isRequired,
    presence_penalty: PropTypes.string.isRequired,
    userId: PropTypes.string.isRequired,
    createdAt: PropTypes.string.isRequired,
    updatedAt: PropTypes.string.isRequired,
    __v: PropTypes.number.isRequired,
  }).isRequired,
};

export default SimpleAppUsecase;
