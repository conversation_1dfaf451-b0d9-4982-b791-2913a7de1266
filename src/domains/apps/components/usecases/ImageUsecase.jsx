import { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import '../styles/UsecaseCard.scss';
import '../styles/ImageUsecase.scss';
import WelcomeHeader from '@components/WelcomeHeader/WelcomeHeader.jsx';
import FavoriteButton from '@components/FavoriteButton';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { useUpdateAppTrackingMutation } from '../../../../redux/services/app-tracking-api';
import useUpdateJourneyTracking from '../../../journey/utils/updateJourneyTracking';
import useFormRender from '../../hooks/useFormRender.jsx';
import { developmentLogs } from '../../../../utils/developmentLogs.js';
import {
  <PERSON>,
  Typography,
  But<PERSON>,
  CircularProgress,
  <PERSON>ack,
  IconButton,
  Tooltip,
  Container,
  Grid,
  Card,
  CardHeader,
  CardContent,
  CardActions,
  ToggleButtonGroup,
  ToggleButton,
  Modal,
  Backdrop,
  Fade,
} from '@mui/material';

import DownloadIcon from '@mui/icons-material/Download';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import ClearIcon from '@mui/icons-material/Clear';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import CropSquareIcon from '@mui/icons-material/CropSquare';
import Crop169Icon from '@mui/icons-material/Crop169';
import CropPortraitIcon from '@mui/icons-material/CropPortrait';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import CloseIcon from '@mui/icons-material/Close';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import { useTranslation } from 'react-i18next';
import { useUseDallEConnectorsMutation } from '../../../../redux/services/connectors-api';
import AddPhotoAlternateIcon from '@mui/icons-material/AddPhotoAlternate';

const ImageUsecase = ({ data, id, initialPrompt, onGenerate, onlyShowUsecase = false }) => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const user = useSelector((state) => state.auth.user);
  const [updateAppTracking] = useUpdateAppTrackingMutation();
  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();
  const [generateImage] = useUseDallEConnectorsMutation();
  const [isLoading, setIsLoading] = useState(false);
  const [generatedImage, setGeneratedImage] = useState(null);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [seconds, setSeconds] = useState(0);
  const [aspectRatio, setAspectRatio] = useState('1:1');
  const [generatedImages, setGeneratedImages] = useState([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [openLightbox, setOpenLightbox] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const MAX_IMAGES = 5;
  const abortControllerRef = useRef(null);

  // URL state'inden journey parametrelerini al
  const journeyCardId = location.state?.journeyCardId;
  const journeyTrackingData = location.state?.journeyTrackingData;
  const journeyLevel = location.state?.journeyLevel;

  const defaultDescription =
    '<p>Generate stunning and creative images using AI. Describe what you want to see and let the AI bring your ideas to life with visual creativity and artistry.</p>';

  // Kullanıcının diline göre çeviri bilgilerini al
  const currentLang =
    i18n.language === 'en' ? 'english' : i18n.language === 'de' ? 'german' : 'english';
  const translatedTitle =
    (data.translations && data.translations[currentLang]?.title) || data.title;
  const translatedDescription =
    (data.translations && data.translations[currentLang]?.description) ||
    data.content ||
    defaultDescription;

  const { formData, setFormData, renderFormField, formFields } = useFormRender(
    data,
    i18n,
    defaultDescription
  );

  // Counter effect
  useEffect(() => {
    let interval;
    if (isLoading) {
      interval = setInterval(() => {
        setSeconds((prev) => prev + 1);
      }, 1000);
    } else {
      setSeconds(0);
    }
    return () => clearInterval(interval);
  }, [isLoading]);

  // Sayfa yüklendiğinde image-usecase alanına scroll yapmak için useEffect
  useEffect(() => {
    const timer = setTimeout(() => {
      const imageUsecaseElement = document.querySelector('.image-usecase');
      if (imageUsecaseElement) {
        // Elementin pozisyonunu al ve 10px üste scroll et
        const rect = imageUsecaseElement.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const targetPosition = scrollTop + rect.top - 20; // 20px boşluk bırak

        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth',
        });
      }
    }, 500);

    // Komponent mount olduğunda isUpdating ve diğer state'leri sıfırla
    setIsUpdating(false);
    localStorage.removeItem('lastImageGenerateCall');

    return () => clearTimeout(timer);
  }, []);

  const handleAspectRatioChange = (event, newAspectRatio) => {
    // ToggleButtonGroup'tan null dönebilir, bu durumda mevcut değeri koruyalım
    if (newAspectRatio !== null) {
      setAspectRatio(newAspectRatio);
    }
  };

  const formatTime = (totalSeconds) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleButtonClick = async () => {
    try {
      // Maksimum resim sayısını kontrol et
      if (generatedImages.length >= MAX_IMAGES) {
        toast.warning(
          t('imageUsecase.warning.maxImages', 'You can create a maximum of 5 images.'),
          {
            position: 'top-center',
            autoClose: 3000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
          }
        );
        return;
      }

      // İşlem zaten devam ediyorsa, çıkış yap
      if (isUpdating) {
        toast.info(
          t('imageUsecase.wait.process', 'Please wait for the current process to complete...'),
          {
            position: 'bottom-right',
            autoClose: 2000,
          }
        );
        return;
      }

      // İşlem başlıyor - hemen işaretleyelim
      setIsUpdating(true);

      // Son çağrı zamanını kontrol et
      const now = Date.now();
      const lastCallTimeString = localStorage.getItem('lastImageGenerateCall');
      const lastCallTime = lastCallTimeString ? parseInt(lastCallTimeString, 10) : 0;
      const timeSinceLastCall = now - lastCallTime;

      // API istekleri arasında en az 3 saniye bekle
      if (timeSinceLastCall < 3000) {
        toast.info(t('imageUsecase.wait.seconds', 'Please wait a few seconds...'), {
          position: 'bottom-right',
          autoClose: 2000,
        });
        setIsUpdating(false); // İşlemi iptal edip döndüğümüz için state'i sıfırlıyoruz
        return;
      }

      // Son çağrı zamanını güncelle
      localStorage.setItem('lastImageGenerateCall', now.toString());

      // App tracking ve journey tracking işlemlerini API çağrısından önce yap
      // App tracking güncellemesi yap
      const currentDate = new Date();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const year = String(currentDate.getFullYear());
      const day = String(currentDate.getDate()).padStart(2, '0');
      try {
        await updateAppTracking({
          userId: user?._id,
          appId: id,
          appType: 'usecase',
          year: year,
          month: month,
          day: day,
        });
        developmentLogs('App tracking updated at button click');
      } catch (error) {
        developmentLogs('Failed to update app tracking:', error);
      }

      // Journey tracking güncellemesi yap (eğer journey parametreleri varsa)
      if (journeyCardId && journeyTrackingData && journeyLevel && user?._id) {
        try {
          await updateJourneyTrackingCard({
            userId: user._id,
            journeyTrackingData: journeyTrackingData,
            userLevel: journeyLevel.toLowerCase(),
            cardId: journeyCardId,
          });
          developmentLogs('Journey tracking updated at button click');

          // TrainingProgress için localStorage üzerinden bildirim gönder
          localStorage.setItem('journey_tracking_updated', Date.now().toString());

          // TrainingProgress'in next button içeriğini de güncelleyelim
          // Özel bir event yayınlayarak Footer'ın dinlemesini sağlayalım
          window.dispatchEvent(
            new CustomEvent('update_training_progress', {
              detail: {
                source: 'usecase',
                cardId: journeyCardId,
                trackingData: true,
                timestamp: Date.now(),
              },
            })
          );

          // Kalıcı bir işaretci ekleyelim
          localStorage.setItem('tracking_completed_card', journeyCardId);
        } catch (error) {
          developmentLogs('Failed to update journey tracking:', error);
        }
      }

      setIsLoading(true);
      setGeneratedImage(null);
      setSeconds(0);

      abortControllerRef.current = new AbortController();

      // Use the DALL-E prompt template and replace placeholders
      let prompt = data['dall-e_settings'].dalle_prompt;
      Object.entries(formData).forEach(([key, value]) => {
        prompt = prompt.replaceAll(`{${key}}`, value);
      });

      const response = await generateImage({
        prompt: prompt,
        temperature: 0.4,
        frequency: 0,
        presence: 0,
        abortController: abortControllerRef.current,
        aspectRatio: aspectRatio,
      });

      if (response.error) {
        // Don't show toast for usage limit errors as they're handled in the API layer
        if (response.error.isUsageLimitExceeded) {
          return; // Exit early, usage limit toast is already shown
        }
        throw new Error(response.error.data || t('common.errors.imageGenerationFailed'));
      }

      // API'den dönen URL'i doğru şekilde al
      const imageUrl = response.data?.data?.url;

      if (!imageUrl) {
        throw new Error('Image URL could not be retrieved');
      }

      const timestamp = Date.now();
      const newImageItem = {
        url: imageUrl,
        timestamp: timestamp,
        aspectRatio: aspectRatio,
      };

      // Görüntü oluşturuldu, state'leri güncelle
      setGeneratedImages((prev) => [...prev, newImageItem]);
      setGeneratedImage(imageUrl);
      setSelectedImageIndex(generatedImages.length);
      setHasGenerated(true);

      // onGenerate callback'i çağırıp işlemin ilerleme olarak kaydedilmesini sağla
      if (typeof onGenerate === 'function') {
        try {
          // Topic progress güncellemesi için callback'i çağır
          await onGenerate();
        } catch (error) {
          let errorMessage = t(
            'imageUsecase.error.savingProgress',
            'An error occurred while saving progress.'
          );
          if (typeof error === 'string') {
            errorMessage = error;
          } else if (error.message) {
            errorMessage = `${t('imageUsecase.error.prefix', 'Error')}: ${error.message}`;
          }

          toast.error(errorMessage, {
            position: 'bottom-right',
            autoClose: 3000,
          });

          // Hatayı yukarıya aktar
          throw error;
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        toast.error(
          error.message ||
            t('imageUsecase.error.general', 'An error occurred while generating the image')
        );
      }
    } finally {
      // İşlem her durumda tamamlandı olarak işaretle
      setIsLoading(false);
      setIsUpdating(false);
      abortControllerRef.current = null;
    }
  };

  const handleCancel = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setIsLoading(false);
  };

  const handleDownload = async () => {
    try {
      // Mevcut seçili görseli al
      const currentImage = generatedImages[selectedImageIndex]?.url;
      if (!currentImage) return;

      // Canvas elementi oluştur
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Görsel elementi oluştur
      const img = new Image();
      // CORS etkinleştir
      img.crossOrigin = 'anonymous';

      // Görsel yüklemeyi işlemek için promise oluştur
      await new Promise((resolve, reject) => {
        img.onload = () => {
          // Canvas boyutunu görsele göre ayarla
          canvas.width = img.width;
          canvas.height = img.height;

          // Görseli canvas'a çiz
          ctx.drawImage(img, 0, 0);

          // Base64'e dönüştür ve indir
          try {
            const dataUrl = canvas.toDataURL('image/png');

            // Görsel adını oluştur
            const aspectRatio = generatedImages[selectedImageIndex].aspectRatio.replace(':', '-');
            const fileName = `generated-image-${aspectRatio}-${Date.now()}.png`;

            // İndirme linkini oluştur
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            resolve();
          } catch (err) {
            reject(err);
          }
        };

        img.onerror = (error) => {
          reject(error);
        };

        // Görsel kaynağını ayarla, yüklemeyi başlat
        img.src = currentImage;
      });
    } catch {
      // Alternatif indirme yöntemi olarak yeni sekmede aç
      window.open(generatedImages[selectedImageIndex]?.url, '_blank');
    }
  };

  const handleOpenInNewTab = () => {
    const currentImage = generatedImages[selectedImageIndex]?.url;
    if (currentImage) {
      window.open(currentImage, '_blank');
    }
  };

  const handleClear = () => {
    setGeneratedImage(null);
    setHasGenerated(false);
    setGeneratedImages([]);
    setSelectedImageIndex(0);
  };

  const handleSelectImage = (index) => {
    setSelectedImageIndex(index);
    setGeneratedImage(generatedImages[index].url);
  };

  const handleOpenLightbox = () => {
    setOpenLightbox(true);
  };

  const handleCloseLightbox = () => {
    setOpenLightbox(false);
  };

  // Form initialization with initialPrompt
  useEffect(() => {
    if (initialPrompt && !formData.prompt) {
      setFormData((prev) => ({
        ...prev,
        prompt: initialPrompt,
      }));
    }
  }, [initialPrompt, formData.prompt]);

  return (
    <>
      <WelcomeHeader
        isPageView={!onlyShowUsecase}
        title={translatedTitle}
        description={translatedDescription}
        showProgress={false}
      />

      <Container className="image-usecase">
        <Grid container spacing={3}>
          {/* Form Card */}
          <Grid item xs={12} md={5} className="fields-grid-item-no-margin form-grid-container">
            <Card className="form-card form-card-container">
              <CardHeader
                title={
                  <Box
                    className="card-title-wrapper"
                    sx={{ display: 'flex', alignItems: 'center' }}
                  >
                    <Typography variant="h6" className="title">
                      {t('imageUsecase.form.title', 'Image generator')}
                    </Typography>
                    <Tooltip
                      title={t(
                        'imageUsecase.form.tooltip',
                        'Fill in the fields to generate an image.'
                      )}
                      arrow
                      placement="top"
                    >
                      <HelpOutlineIcon className="helper-icon" fontSize="small" />
                    </Tooltip>
                  </Box>
                }
                className="card-header"
              />
              <CardContent className="card-content form-content scrollable-form-content">
                <form id={`usecase-field-form-${data._id}`}>
                  <Box sx={{ mb: 3, mt: 1 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      {t('imageUsecase.form.aspectRatio')}
                    </Typography>
                    <ToggleButtonGroup
                      value={aspectRatio}
                      exclusive
                      onChange={handleAspectRatioChange}
                      aria-label="aspect ratio selection"
                      size="small"
                      fullWidth
                      color="primary"
                    >
                      <ToggleButton value="1:1" aria-label="square">
                        <CropSquareIcon sx={{ mr: 1 }} />
                        {t('imageUsecase.form.aspectRatioTypes.square', 'Square')}
                      </ToggleButton>
                      <ToggleButton value="16:9" aria-label="wide">
                        <Crop169Icon sx={{ mr: 1 }} />
                        {t('imageUsecase.form.aspectRatioTypes.wide', 'Wide')}
                      </ToggleButton>
                      <ToggleButton value="9:16" aria-label="portrait">
                        <CropPortraitIcon sx={{ mr: 1 }} />
                        {t('imageUsecase.form.aspectRatioTypes.portrait', 'Portrait')}
                      </ToggleButton>
                    </ToggleButtonGroup>
                  </Box>
                  {formFields.map((field) => renderFormField(field))}
                </form>
              </CardContent>
              <CardActions className="card-actions">
                <Typography variant="caption" className="model-info">
                  {t('imageUsecase.form.poweredBy', 'Powered by')}{' '}
                  <Box component="span" className="model-name">
                    {/* {data['dall-e_settings']['dall-e_model'] || 'DALL-E 3'} */}
                    {t('imageUsecase.form.model', 'DALL-E 3')}
                  </Box>
                </Typography>

                <Stack direction="row" spacing={2} alignItems="center">
                  {isLoading && (
                    <>
                      <Button variant="outlined" color="error" onClick={handleCancel} size="small">
                        {t('imageUsecase.form.cancel', 'Cancel')}
                      </Button>
                    </>
                  )}
                  <Button
                    variant="contained"
                    onClick={handleButtonClick}
                    disabled={isLoading}
                    className="generate-button"
                    startIcon={isLoading ? <CircularProgress size={20} /> : <AutoFixHighIcon />}
                  >
                    {isLoading
                      ? formatTime(seconds)
                      : hasGenerated
                        ? t('imageUsecase.form.generateNew')
                        : t('imageUsecase.form.generate', 'Generate')}
                  </Button>
                </Stack>
              </CardActions>
            </Card>
          </Grid>

          {/* Output Card */}
          <Grid item xs={12} md={7} className="output-grid-item-no-margin output-grid-container">
            <Card className="output-card output-card-container">
              <CardHeader
                title={
                  <Typography variant="h6" className="title">
                    {t('imageUsecase.output.title', 'Generated Image')}
                  </Typography>
                }
                action={
                  <FavoriteButton iconOnly={false} shortcutID={data._id} shortcutType="usecase" />
                }
                className="card-header"
              />
              <CardContent className="card-content output-content fixed-output-content">
                {!generatedImage && !isLoading && (
                  <Box className="empty-output-message">
                    <Box className="preview-image-svg">
                      <svg
                        width="100%"
                        height="100%"
                        viewBox="0 0 120 120"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <rect width="120" height="120" rx="8" fill="transparent" />
                        <path
                          d="M60 40C53.3726 40 48 45.3726 48 52C48 58.6274 53.3726 64 60 64C66.6274 64 72 58.6274 72 52C72 45.3726 66.6274 40 60 40Z"
                          fill="#B0BEC5"
                        />
                        <path
                          d="M100 100H20C17.8 100 16 98.2 16 96V24C16 21.8 17.8 20 20 20H100C102.2 20 104 21.8 104 24V96C104 98.2 102.2 100 100 100ZM20 24V96H100V24H20Z"
                          fill="#78909C"
                        />
                        <path d="M50 84L30 64L20 96H100L70 66L50 84Z" fill="#78909C" />
                      </svg>
                    </Box>

                    <Typography variant="body2" className="empty-message-text">
                      {t(
                        'imageUsecase.output.emptyMessage',
                        'The image you create will be shown here.'
                      )}
                    </Typography>
                  </Box>
                )}

                {isLoading && (
                  <Box className="loading-container">
                    <Box className="loading-thumbnail-container">
                      <Box className="loading-thumbnail">
                        <CircularProgress size={40} sx={{ color: 'rgba(80, 80, 80, 0.7)' }} />
                        <Typography
                          variant="caption"
                          sx={{ mt: 1, color: 'rgba(80, 80, 80, 0.7)' }}
                        >
                          {t('imageUsecase.output.generating', 'Oluşturuluyor...')}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                )}

                {generatedImage && (
                  <>
                    <Box className="image-box">
                      <img
                        src={generatedImage}
                        alt="Generated"
                        className={
                          generatedImages[selectedImageIndex]?.aspectRatio === '9:16'
                            ? 'portrait-image'
                            : ''
                        }
                        onClick={handleOpenLightbox}
                      />
                      {/* Resim üzerindeki büyüteç overlay */}
                      <Box className="image-zoom-overlay" onClick={handleOpenLightbox}>
                        <Tooltip
                          title={t('imageUsecase.output.clickToZoom', 'Click to zoom')}
                          arrow
                        >
                          <Box className="zoom-icon-container">
                            <ZoomInIcon fontSize="large" sx={{ color: 'rgba(0, 0, 0, 0.7)' }} />
                          </Box>
                        </Tooltip>
                      </Box>
                    </Box>

                    {/* Thumbnails area */}
                    <Box className="thumbnails-container">
                      {generatedImages.map((img, index) => (
                        <Box
                          key={img.timestamp}
                          className={`thumbnail-item ${selectedImageIndex === index ? 'selected' : ''}`}
                          onClick={() => handleSelectImage(index)}
                        >
                          <img
                            src={img.url}
                            alt={`Thumbnail ${index + 1}`}
                            className={img.aspectRatio === '9:16' ? 'portrait-thumb' : ''}
                          />
                          <Box className="thumbnail-overlay">
                            <Typography variant="caption">{index + 1}</Typography>
                          </Box>
                        </Box>
                      ))}
                      {generatedImages.length < MAX_IMAGES && (
                        <Box
                          className="thumbnail-add"
                          onClick={handleButtonClick}
                          sx={{
                            cursor: isLoading ? 'default' : 'pointer',
                            opacity: isLoading ? 0.5 : 1,
                            display: 'none !important',
                            visibility: 'hidden',
                            width: 0,
                            height: 0,
                            position: 'absolute',
                          }}
                        >
                          {isLoading ? (
                            <CircularProgress size={20} sx={{ color: 'rgba(80, 80, 80, 0.7)' }} />
                          ) : (
                            <>
                              <AddPhotoAlternateIcon
                                sx={{ fontSize: 20, color: 'rgba(80, 80, 80, 0.7)' }}
                              />
                              <Typography
                                variant="caption"
                                sx={{ mt: 0.5, color: 'rgba(80, 80, 80, 0.7)' }}
                              >
                                {t('imageUsecase.output.newImage')}
                              </Typography>
                            </>
                          )}
                        </Box>
                      )}
                    </Box>
                  </>
                )}
              </CardContent>

              {generatedImage && !isLoading && (
                <CardActions className="card-actions output-actions">
                  <Typography
                    variant="caption"
                    sx={{ color: 'rgba(0, 0, 0, 0.6)', display: 'none' }}
                  >
                    {generatedImages.length}/5{' '}
                    {t('imageUsecase.output.imagesGenerated', 'resim oluşturuldu')}
                  </Typography>

                  <Stack direction="row" spacing={1}>
                    <Tooltip title={t('imageUsecase.output.download', 'Download Image')}>
                      <IconButton onClick={handleDownload} size="small" className="action-button">
                        <DownloadIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>

                    <Tooltip title={t('imageUsecase.output.openInNewTab', 'Open in New Tab')}>
                      <IconButton
                        onClick={handleOpenInNewTab}
                        size="small"
                        className="action-button"
                      >
                        <OpenInNewIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>

                    <Tooltip title={t('imageUsecase.output.clear', 'Clear')}>
                      <IconButton onClick={handleClear} size="small" className="action-button">
                        <ClearIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Stack>
                </CardActions>
              )}
            </Card>
          </Grid>
        </Grid>
      </Container>

      {/* Lightbox Modal */}
      <Modal
        open={openLightbox}
        onClose={handleCloseLightbox}
        closeAfterTransition
        BackdropComponent={Backdrop}
        BackdropProps={{
          timeout: 500,
        }}
        className="lightbox-modal"
      >
        <Fade in={openLightbox}>
          <Box className="lightbox-container">
            <IconButton
              onClick={handleCloseLightbox}
              className="lightbox-close-button"
              size="large"
            >
              <CloseIcon />
            </IconButton>
            <img src={generatedImage} alt="Generated" className="lightbox-image" />
          </Box>
        </Fade>
      </Modal>
    </>
  );
};

ImageUsecase.propTypes = {
  id: PropTypes.string.isRequired,
  data: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    content: PropTypes.string,
    description: PropTypes.string,
    translations: PropTypes.shape({
      english: PropTypes.shape({
        title: PropTypes.string,
        description: PropTypes.string,
      }),
      german: PropTypes.shape({
        title: PropTypes.string,
        description: PropTypes.string,
      }),
    }),
    'dall-e_settings': PropTypes.object,
    dalle_settings: PropTypes.object,
  }).isRequired,
  initialPrompt: PropTypes.string,
  onGenerate: PropTypes.func,
  onlyShowUsecase: PropTypes.bool,
};

export default ImageUsecase;
