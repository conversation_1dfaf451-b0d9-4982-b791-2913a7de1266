import { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import '../styles/UsecaseCard.scss';
import '../styles/TextUsecase.scss';
import WelcomeHeader from '@components/WelcomeHeader/WelcomeHeader.jsx';
import useFormRender from '../../hooks/useFormRender.jsx';
import {
  Container,
  Grid,
  Card,
  CardHeader,
  CardContent,
  CardActions,
  Typography,
  Button,
  CircularProgress,
  Stack,
  IconButton,
  Tooltip,
  Menu,
  ListItemIcon,
  Box,
  MenuItem,
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';
import EditIcon from '@mui/icons-material/Edit';
import CleaningServicesIcon from '@mui/icons-material/CleaningServices';
import CompressIcon from '@mui/icons-material/Compress';
import ExpandIcon from '@mui/icons-material/Expand';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useTranslation } from 'react-i18next';
import FavoriteButton from '@components/FavoriteButton';
import { useUseAzureOpenAIConnectorsMutation } from '../../../../redux/services/connectors-api';
import { useUpdateAppTrackingMutation } from '../../../../redux/services/app-tracking-api';
import { useSelector } from 'react-redux';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { useLocation } from 'react-router-dom';
import useUpdateJourneyTracking from '../../../journey/utils/updateJourneyTracking';
import HtmlRenderer from '@components/HtmlRenderer';

const TextUsecase = ({
  data,
  onGenerate,
  id,
  onlyShowUsecase = false,
  disableFavorites = false,
  disableTracking = false,
  type = 'usecase',
  hideTitle = false,
}) => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const user = useSelector((state) => state.auth.user);
  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState('');
  const [displayedResponse, setDisplayedResponse] = useState('');
  const [contentQueue, setContentQueue] = useState([]);
  const [typingSpeed] = useState(10);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [seconds, setSeconds] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const abortControllerRef = useRef(null);
  const typingTimerRef = useRef(null);
  const [copied, setCopied] = useState(false);
  const [modifyAnchorEl, setModifyAnchorEl] = useState(null);
  const [generateText] = useUseAzureOpenAIConnectorsMutation();
  const [updateAppTracking] = useUpdateAppTrackingMutation();
  const [hasCalledGenerate, setHasCalledGenerate] = useState(false);
  const [lastGenerateTime, setLastGenerateTime] = useState(0);
  const [isExpanded, setIsExpanded] = useState(false);
  const formRef = useRef(null);
  const outputContentRef = useRef(null);
  const [showExpandButton, setShowExpandButton] = useState(false);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  const lastScrollPositionRef = useRef(0);

  // Önceki usecase ID'sini saklayan bir ref oluştur
  const prevUsecaseIdRef = useRef(id);

  // URL state'inden journey parametrelerini al
  const journeyCardId = location.state?.journeyCardId;
  const journeyTrackingData = location.state?.journeyTrackingData;
  const journeyLevel = location.state?.journeyLevel;

  // TrainingProgress tarafından iletilen resetState bayrağını al
  const resetState = location.state?.resetState === true;

  const defaultDescription =
    '<p>Create a clear and concise meeting agenda based on the meeting title, key topics, participants, and time duration.</p><p>Provide an organized structure for meetings, helping participants stay focused on the relevant topics within the allotted time.</p>';

  const { formData, renderFormField, formFields, getLocalizedData } = useFormRender(
    data,
    i18n,
    defaultDescription
  );

  // State'i komple sıfırlamak için fonksiyon
  const resetAllState = useCallback(() => {
    // Yazma efektini durdur
    if (typingTimerRef.current) {
      clearTimeout(typingTimerRef.current);
      typingTimerRef.current = null;
    }

    // Varsa API isteğini iptal et
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    // Tüm state'leri temizle
    setIsLoading(false);
    setResponse('');
    setDisplayedResponse('');
    setContentQueue([]);
    setHasGenerated(false);
    setSeconds(0);
    setIsTyping(false);
    setHasCalledGenerate(false);
    setLastGenerateTime(0);

    // SessionStorage'dan useCaseReset bayrağını temizle
    sessionStorage.removeItem('useCaseReset');
  }, []);

  // TrainingProgress'den resetState flag'i ile ya da useCaseReset flag'i ile state'i sıfırla
  useEffect(() => {
    // resetState flag'i geldi mi kontrol et
    if (resetState) {
      resetAllState();
    }

    // sessionStorage'dan useCaseReset flag'ini kontrol et
    const useCaseReset = sessionStorage.getItem('useCaseReset') === 'true';
    if (useCaseReset) {
      resetAllState();
    }
  }, [resetState, resetAllState]);

  // usecase ID'si değiştiğinde state'i sıfırla
  useEffect(() => {
    if (prevUsecaseIdRef.current !== id) {
      resetAllState();
      prevUsecaseIdRef.current = id;
    }
  }, [id, resetAllState]);

  useEffect(() => {
    let interval;
    if (isLoading || isTyping) {
      interval = setInterval(() => {
        setSeconds((prev) => prev + 1);
      }, 1000);
    } else {
      setSeconds(0);
    }
    return () => clearInterval(interval);
  }, [isLoading, isTyping]);

  const formatTime = (totalSeconds) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const replacePlaceholders = (prompt, variables) => {
    let result = prompt;
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      result = result.replaceAll(placeholder, value || '');
    });
    return result;
  };

  const handleCancel = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    if (typingTimerRef.current) {
      clearTimeout(typingTimerRef.current);
      typingTimerRef.current = null;
    }
    setIsLoading(false);
    setIsTyping(false);
    setResponse('');
    setDisplayedResponse('');
    setContentQueue([]);
    setHasGenerated(false);
    setSeconds(0);
  };

  useEffect(() => {
    if (!response) {
      setHasCalledGenerate(false);
    }
  }, [response]);

  const handleOnGenerate = useCallback(async () => {
    const now = Date.now();
    if (now - lastGenerateTime < 2000 || hasCalledGenerate) {
      return;
    }

    setLastGenerateTime(now);
    setHasCalledGenerate(true);

    if (onGenerate) {
      try {
        await onGenerate(true);
      } catch (_error) {
        // Hatayı sessizce geç
      }
    }
  }, [onGenerate, hasCalledGenerate, lastGenerateTime]);

  useEffect(() => {
    if (hasGenerated && onGenerate) {
      handleOnGenerate();
    }
  }, [hasGenerated, onGenerate, handleOnGenerate]);

  const handleButtonClick = async () => {
    try {
      const localizedData = getLocalizedData();

      // Basitleştirilmiş form alanları kontrolü
      const formFieldsToCheck = formFields;
      const missingFields = formFieldsToCheck.filter((field) => !formData[field.name]);

      if (missingFields.length > 0) {
        setResponse('Please fill in all required fields.');
        return;
      }

      // Generate başlar başlamaz butonların görünür olması için
      setHasGenerated(true);

      setHasCalledGenerate(false);
      setLastGenerateTime(0);

      // -------- TRACKİNG İŞLEMLERİNİ BURAYA TAŞIYORUZ --------
      // Journey tracking güncellemesi yap
      if (journeyCardId && journeyTrackingData && journeyLevel && user?._id && !disableTracking) {
        try {
          await updateJourneyTrackingCard({
            userId: user._id,
            journeyTrackingData: journeyTrackingData,
            userLevel: journeyLevel.toLowerCase(),
            cardId: journeyCardId,
          });

          // TrainingProgress için localStorage üzerinden bildirim gönder
          localStorage.setItem('journey_tracking_updated', Date.now().toString());

          // TrainingProgress'in next button içeriğini de güncelleyelim
          // Özel bir event yayınlayarak Footer'ın dinlemesini sağlayalım
          window.dispatchEvent(
            new CustomEvent('update_training_progress', {
              detail: {
                source: 'usecase',
                cardId: journeyCardId,
                trackingData: true,
                timestamp: Date.now(),
              },
            })
          );

          // Kalıcı bir işaretci ekleyelim
          localStorage.setItem('tracking_completed_card', journeyCardId);
        } catch (_error) {
          // Tracking hatası işlenmeyecek
        }
      }

      // App tracking güncellemesi yap
      try {
        if (!disableTracking) {
          const currentDate = new Date();
          await updateAppTracking({
            userId: user?._id,
            appId: id,
            appType: 'usecase',
            year: currentDate.getFullYear(),
            month: currentDate.getMonth() + 1,
            day: currentDate.getDate(),
          });
        }
      } catch (_error) {
        // Tracking hatası işlenmeyecek
      }
      // -------- TRACKİNG İŞLEMLERİ SONU --------

      abortControllerRef.current = new AbortController();

      // Yazma efekti için timer'ı temizle
      if (typingTimerRef.current) {
        clearTimeout(typingTimerRef.current);
        typingTimerRef.current = null;
      }

      // Output alanını temizle
      setDisplayedResponse('');
      setContentQueue([]);

      setIsLoading(true);
      setResponse('');
      setSeconds(0);

      let processedPrompt;
      if (data.api_type === 'completions') {
        processedPrompt = replacePlaceholders(
          localizedData.completions_settings?.completions_prompt ||
            data.completions_settings.completions_prompt ||
            '',
          formData
        );
      } else if (data.api_type === 'stream') {
        processedPrompt = replacePlaceholders(
          localizedData.stream_settings?.stream_prompt || data.stream_settings.stream_prompt || '',
          formData
        );
      } else {
        throw new Error('Invalid API type');
      }

      let isFirstResponse = true;

      const result = await generateText({
        prompt: processedPrompt,
        temperature: parseFloat(
          data.api_type === 'completions'
            ? data.completions_settings.temperature
            : data.stream_settings.temperature
        ),
        frequency: parseFloat(
          data.api_type === 'completions'
            ? data.completions_settings.frequency_penalty
            : data.stream_settings.frequency_penalty
        ),
        presence: parseFloat(
          data.api_type === 'completions'
            ? data.completions_settings.presence_penalty
            : data.stream_settings.presence_penalty
        ),
        stream: true,
        html: true,
        abortController: abortControllerRef.current,
        onUpdate: async (streamContent) => {
          try {
            if (!streamContent || streamContent.trim() === '') return;

            if (typeof streamContent === 'string' && streamContent.startsWith('data: ')) {
              const jsonData = streamContent.slice(6).trim();

              if (jsonData === '[DONE]') {
                setIsTyping(false);
                return;
              }

              try {
                const parsed = JSON.parse(jsonData);
                if (parsed.content) {
                  const content = parsed.content;

                  if (content.includes('{"content":')) {
                    return;
                  }

                  setContentQueue((prev) => [...prev, content]);
                  setIsTyping(true);

                  if (isFirstResponse) {
                    isFirstResponse = false;
                  }
                }
              } catch (_parseError) {
                if (jsonData && jsonData.trim()) {
                  if (!jsonData.includes('{"content":')) {
                    setContentQueue((prev) => [...prev, jsonData.trim()]);
                    setIsTyping(true);
                  }
                }
              }
            }
          } catch (_error) {
            setIsTyping(false);
            setContentQueue((prev) => [
              ...prev,
              '\n\nError: ' + (_error?.message || 'Error processing response'),
            ]);
          }
        },
      });

      if (result.error) {
        if (
          result.error.data &&
          typeof result.error.data === 'string' &&
          result.error.data.includes('data: {')
        ) {
          setHasGenerated(true);
          return;
        }

        if (result.error.name === 'AbortError' || result.error.message === 'Response cancelled') {
          return;
        }

        throw new Error(
          typeof result.error.data === 'string'
            ? result.error.data
            : t('common.errors.textGenerationFailed')
        );
      }

      setHasGenerated(true);
    } catch (error) {
      setResponse(error.message || t('common.errors.textGenerationFailed'));
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const getModelName = () => {
    let modelName = 'GPT-4o';

    if (data.api_type === 'completions') {
      modelName = data.completions_settings?.model_selection || 'GPT-4o';
    } else if (data.api_type === 'stream') {
      modelName = data.stream_settings?.model_selection || 'GPT-4o';
    } else if (data.api_type === 'assistants') {
      modelName = data.assistants_settings?.model_selection || 'GPT-4o';
    }

    // GPT-4 içeren model isimlerini GPT-4o olarak değiştir
    if (modelName.includes('GPT-4') || modelName.includes('gpt-4')) {
      modelName = 'GPT-4o';
    }

    return modelName
      .split(' ')
      .map((word) => {
        if (word.length <= 3) {
          return word.toUpperCase();
        } else {
          return word.substring(0, 3).toUpperCase() + word.slice(3);
        }
      })
      .join(' ');
  };

  const stripHtml = (html) => {
    const doc = new DOMParser().parseFromString(html, 'text/html');
    return doc.body.textContent || '';
  };

  const handleCopy = async () => {
    try {
      // Hem HTML hem de düz metin formatında kopyalamayı dene
      const htmlContent = displayedResponse;
      const plainText = stripHtml(displayedResponse);

      // Modern tarayıcılar için ClipboardItem kullanarak kopyala
      if (navigator.clipboard?.write) {
        const clipboardItem = new ClipboardItem({
          'text/html': new Blob([htmlContent], { type: 'text/html' }),
          'text/plain': new Blob([plainText], { type: 'text/plain' }),
        });

        await navigator.clipboard.write([clipboardItem]);
      } else {
        // ClipboardItem desteklenmiyor ise düz metin olarak kopyala
        await navigator.clipboard.writeText(plainText);
      }

      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (_err) {
      // Kopyalama hatası sessizce geçildi
    }
  };

  const handleClear = () => {
    // Mevcut generate işlemini sonlandır
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    // Yazma efektini durdurmak için timer'ı temizle
    if (typingTimerRef.current) {
      clearTimeout(typingTimerRef.current);
      typingTimerRef.current = null;
    }

    setIsLoading(false);
    setIsTyping(false);
    setResponse('');
    setDisplayedResponse('');
    setContentQueue([]);
    setHasGenerated(false);
  };

  const handleModifyClick = (event) => {
    setModifyAnchorEl(event.currentTarget);
  };

  const handleModifyClose = () => {
    setModifyAnchorEl(null);
  };

  const handleModifyOption = async (type) => {
    handleModifyClose();

    // Mevcut yazma işlemini ve generate sürecini iptal et
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    if (typingTimerRef.current) {
      clearTimeout(typingTimerRef.current);
      typingTimerRef.current = null;
    }

    setIsTyping(false);
    setContentQueue([]);

    const modificationPrompts = {
      shorter: 'Please make the following response shorter while keeping the main points:',
      longer: 'Please expand the following response with more details and examples:',
      simpler: 'Please simplify the following response to make it easier to understand:',
    };

    try {
      const localizedData = getLocalizedData();

      setIsLoading(true);
      setResponse('');
      setDisplayedResponse('');
      setContentQueue([]);
      setIsTyping(true);

      // Yeni bir AbortController oluştur
      abortControllerRef.current = new AbortController();

      // HTML içeriği temizleyerek düz metin elde et
      const plainTextContent = stripHtml(displayedResponse);

      // Form verilerinden prompt oluştur
      let basePrompt;
      if (data.api_type === 'completions') {
        basePrompt = replacePlaceholders(
          localizedData.completions_settings?.completions_prompt ||
            data.completions_settings.completions_prompt ||
            '',
          formData
        );
      } else if (data.api_type === 'stream') {
        basePrompt = replacePlaceholders(
          localizedData.stream_settings?.stream_prompt || data.stream_settings.stream_prompt || '',
          formData
        );
      } else {
        throw new Error('Invalid API type');
      }

      // Modifikasyon promptunu base prompt ile birleştir
      const combinedPrompt = `${basePrompt}\n\n${modificationPrompts[type]}\n\n${plainTextContent}`;

      const result = await generateText({
        _id: '6798d8e88cea31048c9f37ba',
        prompt: combinedPrompt,
        temperature: parseFloat(
          data.api_type === 'completions'
            ? data.completions_settings.temperature
            : data.stream_settings.temperature
        ),
        frequency: parseFloat(
          data.api_type === 'completions'
            ? data.completions_settings.frequency_penalty
            : data.stream_settings.frequency_penalty
        ),
        presence: parseFloat(
          data.api_type === 'completions'
            ? data.completions_settings.presence_penalty
            : data.stream_settings.presence_penalty
        ),
        stream: true,
        html: true,
        model:
          data.api_type === 'completions'
            ? data.completions_settings.model_selection
            : data.stream_settings.model_selection,
        abortController: abortControllerRef.current,
        onUpdate: (streamContent) => {
          try {
            if (!streamContent || streamContent.trim() === '') return;

            if (typeof streamContent === 'string' && streamContent.startsWith('data: ')) {
              const jsonData = streamContent.slice(6).trim();

              if (jsonData === '[DONE]') {
                setIsTyping(false);
                return;
              }

              try {
                const parsed = JSON.parse(jsonData);
                if (parsed.content) {
                  const content = parsed.content;

                  if (content.includes('{"content":')) {
                    return;
                  }

                  setContentQueue((prev) => [...prev, content]);
                  setIsTyping(true);
                }
              } catch (_parseError) {
                if (jsonData && jsonData.trim()) {
                  if (!jsonData.includes('{"content":')) {
                    setContentQueue((prev) => [...prev, jsonData.trim()]);
                    setIsTyping(true);
                  }
                }
              }
            }
          } catch (_error) {
            // Hata sessizce işlendi
          }
        },
      });

      if (result.error) {
        if (result.error.name === 'AbortError' || result.error.message === 'Response cancelled') {
          return;
        }
        throw new Error(result.error.data || 'Failed to modify response');
      }

      setHasGenerated(true);
    } catch (error) {
      setResponse(error.message || 'Error modifying response. Please try again.');
    } finally {
      setIsLoading(false);
      setIsTyping(false);
      setHasCalledGenerate(false);
      setLastGenerateTime(0);
      if (onGenerate) {
        try {
          await onGenerate(true);
        } catch (_error) {
          // Hatayı sessizce geç
        }
      }
    }
  };

  useEffect(() => {
    if (formRef.current) {
      const formHeight = formRef.current.scrollHeight;
      setShowExpandButton(formHeight > 500);
    }
  }, [formFields]);

  const handleExpandClick = () => {
    setIsExpanded(!isExpanded);
  };

  useEffect(() => {
    const markdownElement = outputContentRef.current?.querySelector('.markdown-body');
    if (!markdownElement) return;

    let scrollTimer;

    const handleScroll = (e) => {
      // Olay kullanıcıdan mı yoksa programatik mi geldiğini kontrol et
      if (e.isTrusted) {
        // Kullanıcı scroll yaptığında
        setUserHasScrolled(true);

        // Son scroll pozisyonunu kaydet
        lastScrollPositionRef.current = markdownElement.scrollTop;

        // Otomatik scroll'u kapat
        setShouldAutoScroll(false);
      }

      // Son scroll pozisyonunu kontrol et
      const { scrollTop, scrollHeight, clientHeight } = markdownElement;
      const isAtBottom = scrollHeight - (scrollTop + clientHeight) < 20;

      // Eğer kullanıcı en alta geldiyse, otomatik scroll'u tekrar etkinleştir
      if (isAtBottom) {
        setShouldAutoScroll(true);
        setUserHasScrolled(false);
      }

      // Scroll timer'ı temizle ve yeniden başlat
      clearTimeout(scrollTimer);
      scrollTimer = setTimeout(() => {
        // Bu timer sadece scroll olayının bittiğini takip etmek için
      }, 200);
    };

    markdownElement.addEventListener('scroll', handleScroll);

    // Temizleme fonksiyonu
    return () => {
      markdownElement.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimer);
    };
  }, [contentQueue.length]);

  useEffect(() => {
    if (contentQueue.length > 0 && (contentQueue.length > 5 || isTyping === false)) {
      const timer = setTimeout(() => {
        if (contentQueue.length > 0) {
          const nextContent = contentQueue[0];

          // İçeriği güncelle
          setDisplayedResponse((prev) => prev + nextContent);
          setContentQueue((prev) => prev.slice(1));

          // Scroll işlemini yönet
          const markdownElement = outputContentRef.current?.querySelector('.markdown-body');

          if (markdownElement) {
            if (userHasScrolled) {
              // Kullanıcı scroll yaptıysa, onun pozisyonunu koru
              requestAnimationFrame(() => {
                markdownElement.scrollTop = lastScrollPositionRef.current;
              });
            } else if (shouldAutoScroll && nextContent.length > 10) {
              // Kullanıcı scroll yapmadıysa ve otomatik scroll açıksa, en aşağı kaydır
              requestAnimationFrame(() => {
                markdownElement.scrollTop = markdownElement.scrollHeight;
              });
            }
          }
        }
      }, typingSpeed);

      // Timer referansını saklayalım
      typingTimerRef.current = timer;

      return () => {
        clearTimeout(timer);
        typingTimerRef.current = null;
      };
    }
  }, [contentQueue, typingSpeed, shouldAutoScroll, isTyping, userHasScrolled]);

  return (
    <Box sx={{ padding: 0 }}>
      {hideTitle === false && (
        <WelcomeHeader
          isPageView={!onlyShowUsecase}
          title={getLocalizedData().title || data.title}
          description={getLocalizedData().description || data.content || defaultDescription}
          showProgress={false}
        />
      )}

      <Container className="text-usecase">
        <Grid container spacing={3}>
          <Grid item xs={12} md={5} className="fields-grid-item-no-margin">
            <Card
              className={`text-form-card text-form-card-container ${isExpanded ? 'expanded' : ''}`}
            >
              <CardHeader
                title={
                  <Box
                    className="card-title-wrapper"
                    sx={{ display: 'flex', alignItems: 'center' }}
                  >
                    <Typography variant="h6" className="title">
                      {t('textUsecase.form.title')}
                    </Typography>
                    <Tooltip title={t('textUsecase.form.tooltip')} arrow placement="top">
                      <HelpOutlineIcon className="helper-icon" fontSize="small" />
                    </Tooltip>
                  </Box>
                }
                action={
                  <>
                    {!disableFavorites && (
                      <FavoriteButton
                        shortcutID={data._id}
                        shortcutType={type === 'ai_app' ? 'ai_app' : 'usecase'}
                        iconOnly={false}
                      />
                    )}
                  </>
                }
                className="card-header"
              />
              <CardContent className="card-content form-content scrollable-form-content">
                <form id={`usecase-field-form-${data._id}`} ref={formRef}>
                  {formFields.map((field) => renderFormField(field))}
                </form>
                {showExpandButton && (
                  <Box className="show-more-container">
                    <Button
                      onClick={handleExpandClick}
                      className="show-more-button"
                      endIcon={isExpanded ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                    >
                      {isExpanded
                        ? t('common.showLess', 'Show Less')
                        : t('common.showMore', 'Show More')}
                    </Button>
                  </Box>
                )}
              </CardContent>
              <CardActions className="card-actions">
                <Typography variant="caption" className="model-info">
                  {t('textUsecase.form.poweredBy')}{' '}
                  <Box component="span" className="model-name">
                    {getModelName()}
                  </Box>
                </Typography>

                <Stack direction="row" spacing={2} alignItems="center">
                  {(isLoading || isTyping) && (
                    <Button
                      variant="outlined"
                      color="error"
                      onClick={handleCancel}
                      size="small"
                      className="cancel-button"
                      fullWidth={false}
                    >
                      {t('textUsecase.form.cancel')}
                    </Button>
                  )}
                  <Button
                    variant="contained"
                    fullWidth
                    disabled={isLoading || isTyping}
                    onClick={handleButtonClick}
                    className="generate-button"
                    startIcon={
                      isLoading || isTyping ? <CircularProgress size={20} /> : <AutoAwesomeIcon />
                    }
                  >
                    {isLoading || isTyping
                      ? formatTime(seconds)
                      : hasGenerated
                        ? t('textUsecase.form.regenerate')
                        : t('textUsecase.form.generate')}
                  </Button>
                </Stack>
              </CardActions>
            </Card>
          </Grid>

          <Grid item xs={12} md={7} className="output-grid-item-no-margin">
            <Card className="text-output-card">
              <CardHeader
                title={
                  <Typography variant="h6" className="title">
                    {t('textUsecase.output.title')}
                  </Typography>
                }
                action={
                  <Stack direction="row" spacing={1}>
                    <Tooltip title={t('textUsecase.output.modifyResponse')}>
                      <span>
                        <IconButton
                          onClick={handleModifyClick}
                          size="small"
                          className="action-button"
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </span>
                    </Tooltip>

                    <Menu
                      anchorEl={modifyAnchorEl}
                      open={Boolean(modifyAnchorEl)}
                      onClose={handleModifyClose}
                      anchorOrigin={{
                        vertical: 'top',
                        horizontal: 'center',
                      }}
                      transformOrigin={{
                        vertical: 'bottom',
                        horizontal: 'center',
                      }}
                    >
                      <MenuItem onClick={() => handleModifyOption('shorter')}>
                        <ListItemIcon>
                          <CompressIcon fontSize="small" />
                        </ListItemIcon>
                        {t('textUsecase.output.shorter')}
                      </MenuItem>
                      <MenuItem onClick={() => handleModifyOption('longer')}>
                        <ListItemIcon>
                          <ExpandIcon fontSize="small" />
                        </ListItemIcon>
                        {t('textUsecase.output.longer')}
                      </MenuItem>
                      <MenuItem onClick={() => handleModifyOption('simpler')}>
                        <ListItemIcon>
                          <AutoAwesomeIcon fontSize="small" />
                        </ListItemIcon>
                        {t('textUsecase.output.simpler')}
                      </MenuItem>
                    </Menu>

                    <Tooltip
                      title={copied ? t('textUsecase.output.copied') : t('textUsecase.output.copy')}
                    >
                      <span>
                        <IconButton
                          onClick={handleCopy}
                          size="small"
                          className={`action-button ${!displayedResponse ? 'faded-button' : ''}`}
                          disabled={!displayedResponse}
                        >
                          {copied ? (
                            <CheckIcon fontSize="small" />
                          ) : (
                            <ContentCopyIcon fontSize="small" />
                          )}
                        </IconButton>
                      </span>
                    </Tooltip>

                    <Tooltip title={t('textUsecase.output.clear')}>
                      <span>
                        <IconButton
                          onClick={handleClear}
                          size="small"
                          className={`action-button ${!hasGenerated && !displayedResponse ? 'faded-button' : ''}`}
                          disabled={!hasGenerated && !displayedResponse}
                        >
                          <CleaningServicesIcon fontSize="small" />
                        </IconButton>
                      </span>
                    </Tooltip>
                  </Stack>
                }
                className="card-header"
              />
              <CardContent className="card-content output-content" ref={outputContentRef}>
                {!displayedResponse && (
                  <Box className="text-output-empty-message">
                    <Box className="placeholder-content">
                      <InfoOutlinedIcon className="info-icon" />
                      <Typography variant="h6" className="placeholder-title">
                        AI use case
                      </Typography>
                      <Typography variant="body2" className="placeholder-subtitle">
                        {t('textUsecase.output.emptyDescription', {
                          count: formFields?.length || 0,
                          interpolation: { escapeValue: false },
                        })}
                      </Typography>

                      <Box className="placeholder-features">
                        <Box className="feature-item">
                          <AutoAwesomeIcon />
                          <Typography className="feature-text">
                            {t('textUsecase.output.feature1')}
                          </Typography>
                        </Box>
                        <Box className="feature-item">
                          <EditIcon />
                          <Typography className="feature-text">
                            {t('textUsecase.output.feature2')}
                          </Typography>
                        </Box>
                        <Box className="feature-item">
                          <ContentCopyIcon />
                          <Typography className="feature-text">
                            {t('textUsecase.output.feature3')}
                          </Typography>
                        </Box>
                        <Box className="feature-item">
                          <CleaningServicesIcon />
                          <Typography className="feature-text">
                            {t('textUsecase.output.feature4')}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                )}
                {displayedResponse && (
                  <>
                    <HtmlRenderer
                      content={displayedResponse}
                      className="markdown-body visible"
                      sanitize={true}
                      formatCodeBlocks={true}
                    />
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
        <Box className="ai-disclaimer-box">{t('common.warnings.dataPrivacy')}</Box>
      </Container>
    </Box>
  );
};

TextUsecase.propTypes = {
  data: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    shortcutID: PropTypes.string,
    title: PropTypes.string.isRequired,
    content: PropTypes.string,
    usecase_icon_url: PropTypes.string,
    slug: PropTypes.string.isRequired,
    api_type: PropTypes.oneOf(['completions', 'stream', 'assistants']).isRequired,
    translations: PropTypes.objectOf(
      PropTypes.shape({
        title: PropTypes.string.isRequired,
        description: PropTypes.string,
        completions_settings: PropTypes.shape({
          form_fields: PropTypes.arrayOf(
            PropTypes.shape({
              _id: PropTypes.string,
              type: PropTypes.string.isRequired,
              label: PropTypes.string.isRequired,
              name: PropTypes.string.isRequired,
              default_value: PropTypes.string,
              choices: PropTypes.string,
            })
          ),
          completions_prompt: PropTypes.string,
          temperature: PropTypes.string,
          top_p: PropTypes.string,
          frequency_penalty: PropTypes.string,
          presence_penalty: PropTypes.string,
          model_selection: PropTypes.string,
          max_token: PropTypes.string,
        }),
        _id: PropTypes.string,
      })
    ),
    completions_settings: PropTypes.shape({
      form_fields: PropTypes.arrayOf(
        PropTypes.shape({
          _id: PropTypes.string.isRequired,
          type: PropTypes.string.isRequired,
          label: PropTypes.string.isRequired,
          name: PropTypes.string.isRequired,
          default_value: PropTypes.string,
          choices: PropTypes.string,
        })
      ),
      completions_prompt: PropTypes.string,
      temperature: PropTypes.string.isRequired,
      top_p: PropTypes.string.isRequired,
      frequency_penalty: PropTypes.string.isRequired,
      presence_penalty: PropTypes.string.isRequired,
      model_selection: PropTypes.string.isRequired,
      max_token: PropTypes.string.isRequired,
    }),
    stream_settings: PropTypes.shape({
      stream_form: PropTypes.shape({
        form_fields: PropTypes.arrayOf(
          PropTypes.shape({
            _id: PropTypes.string.isRequired,
            type: PropTypes.string.isRequired,
            label: PropTypes.string.isRequired,
            name: PropTypes.string.isRequired,
            default_value: PropTypes.string,
            choices: PropTypes.string,
          })
        ),
      }),
      stream_prompt: PropTypes.string,
      temperature: PropTypes.string.isRequired,
      top_p: PropTypes.string.isRequired,
      frequency_penalty: PropTypes.string.isRequired,
      presence_penalty: PropTypes.string.isRequired,
      model_selection: PropTypes.string.isRequired,
      max_token: PropTypes.string.isRequired,
    }),
    assistants_settings: PropTypes.shape({
      form_fields: PropTypes.arrayOf(
        PropTypes.shape({
          _id: PropTypes.string.isRequired,
          type: PropTypes.string.isRequired,
          label: PropTypes.string.isRequired,
          name: PropTypes.string.isRequired,
          default_value: PropTypes.string,
          choices: PropTypes.string,
        })
      ),
      assistants_prompt: PropTypes.string,
      temperature: PropTypes.string,
      model_selection: PropTypes.string,
    }),
  }).isRequired,
  onGenerate: PropTypes.func,
  id: PropTypes.string.isRequired,
  onlyShowUsecase: PropTypes.bool,
  disableFavorites: PropTypes.bool,
  disableTracking: PropTypes.bool,
  type: PropTypes.string,
  hideTitle: PropTypes.bool,
};

export default TextUsecase;
