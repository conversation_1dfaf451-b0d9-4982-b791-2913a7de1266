import React from 'react';
import { Grid, Stepper, Step, StepLabel } from '@mui/material';
import './StepperHeader.scss';

function StepperHeader({ currentStep, steps }) {
  return (
    <Grid item xs={12} className="stepper-header">
      <Stepper activeStep={currentStep} alternativeLabel>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
    </Grid>
  );
}

export default StepperHeader;
