import React from 'react';
import { Paper, Box, Typography, TextField } from '@mui/material';
import { Editor } from '@tinymce/tinymce-react';
import { useTranslation } from 'react-i18next';
import './AppDescription.scss';

function AppDescription({ appName, description, maxLength, handleNameChange, handleEditorChange }) {
  const { t } = useTranslation();
  
  return (
    <Paper className="app-description">
      <Box className="name-section">
        <Box className="name-header">
          <Typography variant="subtitle2">{t('simpleAIApps.create.nameSection.title')}</Typography>
          <Typography variant="caption">
            {appName.length} / {maxLength}
          </Typography>
        </Box>
        <TextField
          fullWidth
          placeholder={t('simpleAIApps.create.nameSection.placeholder')}
          variant="outlined"
          value={appName}
          onChange={handleNameChange}
          inputProps={{ maxLength }}
        />
      </Box>

      <Box className="description-section">
        <Typography variant="subtitle2">{t('simpleAIApps.create.descriptionSection.title')}</Typography>
        <Editor
          tinymceScriptSrc="/tinymce/tinymce.min.js"
          value={description}
          onEditorChange={handleEditorChange}
          init={{
            height: 300,
            menubar: false,
            plugins: ['lists', 'advlist', 'wordcount'],
            toolbar: 'undo redo | bold italic | bullist numlist',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
            branding: false,
            statusbar: false,
            placeholder: t('simpleAIApps.create.descriptionSection.placeholder')
          }}
        />
      </Box>
    </Paper>
  );
}

export default AppDescription;
