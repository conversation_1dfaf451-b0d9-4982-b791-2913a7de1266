@use '../../../../../styles/abstracts/variables' as *;

.app-description {
  padding: $spacing-4;
  margin-bottom: $spacing-4;
  border-radius: $border-radius-md !important;
  border:1px solid $border-color !important;
  box-shadow: $shadow-sm !important;
  background: url('@/assets/images/effect.png') no-repeat top -80px right -75px #FFF !important;

  .name-section {
    margin-bottom: $spacing-3;

    .name-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-2;

      .MuiTypography-subtitle2 {
        color: $primary-text-color-dark !important;
        font-weight: $font-weight-bold;
      }

      .MuiTypography-caption {
        color: $text-secondary;
      }
    }
  }

  .description-section {
    .MuiTypography-subtitle2 {
      color: $primary-text-color-dark !important;
      font-weight: $font-weight-bold;
      margin-bottom: $spacing-2;
    }
  }
} 