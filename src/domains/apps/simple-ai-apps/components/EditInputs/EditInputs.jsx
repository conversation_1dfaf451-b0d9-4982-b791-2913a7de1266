import React from 'react';
import { Box, Typography, Paper, List, ListItem, ListItemText, IconButton } from '@mui/material';
import { useTranslation } from 'react-i18next';
import Iconify from '../../../../../components/Iconify/iconify';
import './EditInputs.scss';

function EditInputs({ elements, onEditElement }) {
  const { t } = useTranslation();
  
  return (
    <Paper className="edit-inputs">
      <Box className="inputs-section">
        <Typography variant="subtitle2" className="section-title">
          {t('simpleAIApps.create.editInputs.title')}
        </Typography>
        <List>
          {elements.map((element) => (
            <ListItem
              key={element.id}
              disableGutters
              secondaryAction={
                <IconButton edge="end" size="small" onClick={() => onEditElement(element.id)}>
                  <Iconify icon="material-symbols:edit-outline" width={20} />
                </IconButton>
              }
              className="input-item"
            >
              <ListItemText
                primary={element.inputName}
                secondary={element.type}
                className="input-text"
              />
            </ListItem>
          ))}
        </List>
      </Box>
    </Paper>
  );
}

export default EditInputs; 