@use '../../../../../styles/abstracts/variables' as *;

.edit-inputs {
  padding: $spacing-4;
  margin-bottom: $spacing-4;
  border-radius: $border-radius-md !important;
  border: 1px solid $border-color !important;
  box-shadow: $shadow-sm !important;
  background: url('@/assets/images/effect.png') no-repeat top -80px right -75px #FFF !important;

  .inputs-section {
    .section-title {
      color: $primary-text-color-dark !important;
      font-weight: $font-weight-bold;
      margin-bottom: $spacing-3;
    }

    .input-item {
      border: 1px solid $border-color;
      border-radius: $border-radius-sm;
      margin-bottom: $spacing-2;
      padding: $spacing-2 $spacing-3;
      position: relative;

      &:last-child {
        margin-bottom: 0;
      }

      .MuiButtonBase-root {
        position: absolute;
        right: $spacing-3;
        top: 50%;
        transform: translateY(-50%);
      }

      .input-text {
        padding-right: $spacing-6;
        
        .MuiListItemText-primary {
          color: $primary-text-color !important;
          font-size: $font-size-sm !important;
          font-weight: $font-weight-bold !important;
        }

        .MuiListItemText-secondary {
          color: $text-secondary !important;
          font-size:calc($font-size-sm - 2px) !important;
        }
      }
    }
  }
} 