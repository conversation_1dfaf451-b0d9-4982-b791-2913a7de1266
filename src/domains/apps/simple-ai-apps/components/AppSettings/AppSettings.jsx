import React from 'react';
import { Paper, Typography, Box, TextField, Slider, MenuItem, Tooltip } from '@mui/material';
import { useTranslation } from 'react-i18next';
import './AppSettings.scss';

function AppSettings({
  selectedModel,
  temperature,
  topP,
  frequencyPenalty,
  presencePenalty,
  setSelectedModel,
  setTemperature,
  setTopP,
  setFrequencyPenalty,
  setPresencePenalty,
}) {
  const { t } = useTranslation();
  
  return (
    <Paper className="app-settings">
      <Typography variant="subtitle2" className="title">
        {t('simpleAIApps.create.appSettings.title')}
      </Typography>

      <Box className="settings-container">
        <TextField
          select
          fullWidth
          label={t('simpleAIApps.create.appSettings.model')}
          value={selectedModel}
          onChange={(e) => setSelectedModel(e.target.value)}
          className="model-select"
        >
          <MenuItem value="gpt-4o">{t('simpleAIApps.create.appSettings.models.gpt4o')}</MenuItem>
        </TextField>

        <Box className="slider-group">
          
            <Box className="slider-header">
              <Tooltip placement="top" title={t('simpleAIApps.create.appSettings.temperature.tooltip')}>
              <Typography variant="subtitle2">{t('simpleAIApps.create.appSettings.temperature.title')}</Typography>
              </Tooltip>
              <Box className="value-box">
                {temperature}
              </Box>
            </Box>
          <Slider
            value={temperature}
            onChange={(e, value) => setTemperature(value)}
            min={0}
            max={2}
            step={0.1}
          />
        </Box>

        <Box className="slider-group">
            <Box className="slider-header">
              <Tooltip placement="top" title={t('simpleAIApps.create.appSettings.topP.tooltip')}>
              <Typography variant="subtitle2">{t('simpleAIApps.create.appSettings.topP.title')}</Typography>
              </Tooltip>
              <Box className="value-box">
                {topP}
              </Box>
            </Box>
          <Slider value={topP} onChange={(e, value) => setTopP(value)} min={0} max={1} step={0.1} />
        </Box>

        <Box className="slider-group">
            <Box className="slider-header">
            <Tooltip placement="top" title={t('simpleAIApps.create.appSettings.frequencyPenalty.tooltip')}>
              <Typography variant="subtitle2">{t('simpleAIApps.create.appSettings.frequencyPenalty.title')}</Typography>
              </Tooltip>
              <Box className="value-box">
                {frequencyPenalty}
              </Box>
            </Box>
          <Slider
            value={frequencyPenalty}
            onChange={(e, value) => setFrequencyPenalty(value)}
            min={-2}
            max={2}
            step={0.1}
          />
        </Box>

        <Box className="slider-group">
          <Box className="slider-header">
              <Tooltip placement="top" title={t('simpleAIApps.create.appSettings.presencePenalty.tooltip')}>
              <Typography variant="subtitle2">{t('simpleAIApps.create.appSettings.presencePenalty.title')}</Typography>
              </Tooltip>
              <Box className="value-box">
                {presencePenalty}
              </Box>
            </Box>
          <Slider
            value={presencePenalty}
            onChange={(e, value) => setPresencePenalty(value)}
            min={-2}
            max={2}
            step={0.1}
          />
        </Box>
      </Box>
    </Paper>
  );
}

export default AppSettings;
