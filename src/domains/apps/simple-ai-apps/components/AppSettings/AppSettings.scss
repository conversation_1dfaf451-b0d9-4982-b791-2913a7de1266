@use '../../../../../styles/abstracts/variables' as *;

.app-settings {
  padding: $spacing-4;
  margin-top: $spacing-4;
  border-radius: $border-radius-md !important;
  border: 1px solid $border-color !important;
  box-shadow: $shadow-sm !important;
  background: url('@/assets/images/effect.png') no-repeat top -80px right -75px #FFF !important;

  .title {
    color: $primary-text-color-dark !important;
    font-weight: $font-weight-bold;
    margin-bottom: $spacing-4;
    border-bottom: 1px solid $border-color;
    padding-bottom: $spacing-3;
  }

  .settings-container {
    .model-select {
      margin-bottom: $spacing-3;
    }

    .slider-group {

      .slider-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-1;

        .MuiTypography-subtitle2 {
          color: $primary-text-color;
          cursor:default !important;
          font-size: $font-size-xs !important;
          font-weight: $font-weight-bold !important;
        }

        .value-box {
          background-color: $bg-light;
          border: 1px solid $border-color;
          padding: $spacing-1 0;
          border-radius: $border-radius-sm;
          font-size: $font-size-xs !important;
          color: $primary-text-color;
          min-width: 32px;
          text-align: center;
        }
      }

      .MuiSlider-root {
        color: $primary-color;

        .MuiSlider-rail {
          background-color: #e0e0e0;
        }

        .MuiSlider-track {
          background-color: $primary-color;
        }

        .MuiSlider-thumb {
          background-color: $primary-color;
          width: 16px !important;
          height: 16px !important;

          &:hover, &.Mui-focusVisible {
            box-shadow: 0 0 0 8px rgba(0, 0, 0, 0.16);
          }
        }
      }
    }
  }
} 