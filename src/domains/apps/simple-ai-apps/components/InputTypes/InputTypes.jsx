import React, { memo, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Button, Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import './InputTypes.scss';

const InputTypes = ({ elements, elementLimit, inputTypes, handleAddElement, isBouncing }) => {
  const { t, i18n } = useTranslation();
  
  // Her girdi tipinin adını çevirme fonksiyonu
  const getTranslatedTypeName = (typeName) => {
    switch(typeName) {
      case 'Short Text':
        return t('simpleAIApps.create.inputTypes.shortText');
      case 'Paragraph':
        return t('simpleAIApps.create.inputTypes.paragraph');
      case 'Dropdown':
        return t('simpleAIApps.create.inputTypes.dropdown');
      case 'Radio':
        return t('simpleAIApps.create.inputTypes.radio');
      case 'Yes / No':
        return t('simpleAIApps.create.inputTypes.yesNo');
      case 'Checkbox':
        return t('simpleAIApps.create.inputTypes.checkbox');
      case 'File upload':
        return t('simpleAIApps.create.inputTypes.fileUpload');
      default:
        return typeName;
    }
  };
  
  return (
    <Box className={`input-types ${isBouncing ? 'bounce' : ''}`}>
      <Typography variant="subtitle2" className="title">
        {t('simpleAIApps.create.inputTypes.title')} ({elements.length}/{elementLimit})
      </Typography>
      <Grid container spacing={2} className="types-grid">
        {inputTypes.map((type) => (
          <Grid item xs={6} key={type.name}>
            <Button
              variant="outlined"
              fullWidth
              className="type-button"
              onClick={() => handleAddElement(type)}
              disabled={elements.length >= elementLimit || type.disabled}
            >
              {type.icon}
              {getTranslatedTypeName(type.name)}
            </Button>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

InputTypes.propTypes = {
  elements: PropTypes.array.isRequired,
  elementLimit: PropTypes.number.isRequired,
  inputTypes: PropTypes.array.isRequired,
  handleAddElement: PropTypes.func.isRequired,
  isBouncing: PropTypes.bool,
};

export default memo(InputTypes);
