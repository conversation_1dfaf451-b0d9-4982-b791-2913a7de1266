@use '../../../../../styles/abstracts/variables' as *;

.input-types {
  padding: $spacing-4;
  margin-bottom: $spacing-4;
  border-radius: $border-radius-md !important;
  border: 1px solid $border-color !important;
  box-shadow: $shadow-sm !important;
  background: url('@/assets/images/effect.png') no-repeat top -80px right -75px #FFF;

  &.bounce {
    animation: bounce 0.5s cubic-bezier(0.36, 0, 0.66, -0.56) 2;
  }

  .title {
    color: $primary-text-color-dark !important;
    font-weight: $font-weight-bold;
    margin-bottom: $spacing-3;
  }

  .types-grid {
    .type-button {
      display: flex;
      flex-direction: column;
      text-transform: none;
      align-items: center;
      justify-content: center;
      background-color: $bg-paper !important;
      gap: $spacing-2;
      border: 1px solid $border-color !important;
      font-size: $font-size-xs !important;
      padding: $spacing-5 $spacing-3 !important;
      height: auto;
      color: $primary-text-color;
      
      &:hover {
        background-color: $bg-paper !important;
        border-color: $primary-color !important;
        color: $primary-color !important;
      }

      .MuiButton-icon {
        margin: 0 !important;
      }
      
      &.Mui-disabled {
        cursor: not-allowed !important;
        opacity: 0.5 !important;
        pointer-events: all !important;

        &:hover {
          background-color: $bg-paper !important;
          border-color: $border-color !important;
          color: $text-secondary !important;
        }

        * {
          cursor: not-allowed !important;
        }
      }
    }
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-8px);
  }
  75% {
    transform: translateX(8px);
  }
} 