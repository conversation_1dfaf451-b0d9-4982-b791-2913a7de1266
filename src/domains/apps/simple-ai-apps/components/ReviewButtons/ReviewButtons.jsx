import React from 'react';
import PropTypes from 'prop-types';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import Button from '../../../../../components/Button/Button';
import './ReviewButtons.scss';

const ReviewButtons = ({ currentStep, isFormValid, handleBack, handleContinue, isSubmitting = false, updateApp = false }) => {
  const { t } = useTranslation();
  
  // Sadece currentStep 2 ise görünecek
  if (currentStep !== 2) {
    return null;
  }
  
  return (
    <Box className="review-buttons">
      <Typography variant="caption" className="warning-text">
        {t('simpleAIApps.create.review.title')}
      </Typography>
      <Box className="button-group">
        {currentStep > 0 && (
          <Button
            variant="outlined"
            onClick={handleBack}
            className="back-button"
            color="default"
            disabled={isSubmitting}
          >
            {t('simpleAIApps.create.review.previous')}
          </Button>
        )}
        <Button
          variant="contained"
          onClick={handleContinue}
          disabled={!isFormValid() || isSubmitting}
          color="primary"
        >
          {isSubmitting 
            ? (updateApp ? t('simpleAIApps.create.review.updating') : t('simpleAIApps.create.review.creating')) 
            : (updateApp ? t('simpleAIApps.create.review.updateApp') : t('simpleAIApps.create.review.createApp'))}
        </Button>
      </Box>
    </Box>
  );
};

ReviewButtons.propTypes = {
  currentStep: PropTypes.number.isRequired,
  isFormValid: PropTypes.func.isRequired,
  handleBack: PropTypes.func.isRequired,
  handleContinue: PropTypes.func.isRequired,
  isSubmitting: PropTypes.bool,
  updateApp: PropTypes.bool
};

export default ReviewButtons; 