@use '../../../../../styles/abstracts/variables' as *;

.review-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 0;
  width: 100% !important;

  .warning-text {
    color: $primary-text-color-dark;
    letter-spacing: 0px !important;
    font-size: $font-size-md !important;
    font-weight: $font-weight-bold !important;
    display: block;
  }

  .button-group {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    margin-left: auto;
    gap: $spacing-2;

    .back-button {
      border:none !important;
      color: $primary-text-color;
      background-color:#71869d;
      color:#fff;
      &:hover{
        background-color: #607285 !important;
        border-color: #607285 !important;
      }

      &:hover {
        border-color: $primary-color;
        background-color: transparent;
      }
    }

    .MuiButton-contained {
      background-color: $primary-color;
      height: auto !important;
      
      &:hover {
        background-color: $primary-color-dark;
      }

      &.Mui-disabled {
        background-color: rgba($primary-color, 0.12);
        color: rgba($primary-color, 0.26);
      }
    }
  }
} 