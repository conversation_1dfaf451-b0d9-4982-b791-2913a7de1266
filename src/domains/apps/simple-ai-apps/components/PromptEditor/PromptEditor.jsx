import React, { useState, useEffect, memo } from 'react';
import { Paper, Typography, Box } from '@mui/material';
import { Editor } from '@tinymce/tinymce-react';
import { useTranslation } from 'react-i18next';
import './PromptEditor.scss';

function PromptEditor({
  elements,
  promptContent,
  maxPromptLength,
  handlePromptChange,
  handleDragStart,
  handleEditorDrop,
  editorRef,
}) {
  const { t } = useTranslation();
  const [charCount, setCharCount] = useState(0);

  useEffect(() => {
    // HTML taglerini temizleyerek gerçek karakter sayısını hesapla
    const textContent = promptContent.replace(/<[^>]*>/g, '');
    setCharCount(textContent.length);
  }, [promptContent]);

  const handleEditorChange = (content, editor) => {
    const textContent = content.replace(/<[^>]*>/g, '');
    if (textContent.length <= maxPromptLength) {
      handlePromptChange(content, editor);
    }
  };

  return (
    <Paper className="prompt-editor">
      <Typography variant="subtitle2" className="title">
        {t('simpleAIApps.create.promptEditor.title')}
      </Typography>

      <Box className="editor-container">
        <Typography variant="subtitle2" className="prompt-elements-title">
          {t('simpleAIApps.create.promptEditor.elementsTitle')}
        </Typography>
        <Typography variant="body2" className="prompt-elements-description">
          {t('simpleAIApps.create.promptEditor.elementsDescription')}
        </Typography>
        <Box className="variables-list">
          {elements.map((element) => (
            <Box
              key={element.id}
              draggable
              onDragStart={(e) => handleDragStart(e, element)}
              className="variable-item"
            >
              {element.inputName}
            </Box>
          ))}
        </Box>

        <Box className="prompt-header">
          <Typography variant="subtitle2" className="prompt-elements-title">
            {t('simpleAIApps.create.promptEditor.yourPrompt')}
          </Typography>
          <Typography variant="caption" className="char-count" color={charCount > maxPromptLength ? "error" : "textSecondary"}>
            {charCount} / {maxPromptLength}
          </Typography>
        </Box>
        <Editor
          onInit={(evt, editor) => (editorRef.current = editor)}
          value={promptContent}
          onEditorChange={handleEditorChange}
          onDrop={handleEditorDrop}
          init={{
            height: 400,
            menubar: false,
            plugins: ['lists', 'advlist', 'wordcount'],
            toolbar: 'undo redo | bold italic | bullist numlist',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
            branding: false,
            statusbar: false,
          }}
        />
      </Box>
    </Paper>
  );
}

export default memo(PromptEditor);
