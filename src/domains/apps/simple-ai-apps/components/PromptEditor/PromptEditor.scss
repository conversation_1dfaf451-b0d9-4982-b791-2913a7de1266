@use '../../../../../styles/abstracts/variables' as *;

.prompt-editor {
  padding: $spacing-4;
  margin-bottom: $spacing-4;
  border-radius: $border-radius-md !important;
  border: 1px solid $border-color !important;
  box-shadow: $shadow-sm !important;
  background: url('@/assets/images/effect.png') no-repeat top -80px right -75px #FFF !important;

  .title {
    color: $primary-text-color-dark !important;
    font-weight: $font-weight-bold;
    margin-bottom: $spacing-3;
    border-bottom: 1px solid $border-color;
    padding-bottom: $spacing-3;
  }

  .editor-container {
    .prompt-elements-title{
      color: $primary-text-color;
      font-weight: $font-weight-bold;
      font-size: calc($font-size-sm - 1px);
      margin-bottom:$spacing-1;
    }
    .prompt-elements-description{
      color: $primary-text-color;
      font-weight: $font-weight-medium;
      font-size: calc($font-size-sm - 1px);
      margin-bottom: $spacing-2;
    }
    .prompt-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-2;

      .char-count {
        font-size: $font-size-xs;
        color: $text-secondary;
      }
    }
    .variables-list {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-2;
      margin-bottom: $spacing-3;

      .variable-item {
        background-color: $bg-paper;
        padding: $spacing-3 $spacing-3;
        border-radius: $border-radius-sm;
        cursor: move;
        user-select: none;
        font-size: $font-size-xs !important;
        border: 1px solid $border-color;

        &:hover {
          border-color: $primary-color;
          color: $primary-color;
        }
      }
    }
  }
} 