import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { Paper, Box, Typography, TextField, IconButton, Button } from '@mui/material';
import { DragIndicator } from '@mui/icons-material';
import { ReactSortable } from 'react-sortablejs';
import { useTranslation } from 'react-i18next';
import Iconify from '../../../../../components/Iconify/iconify';
import './FormElements.scss';

const FormElements = ({
  elements,
  setElements,
  handleRemoveElement,
  handleElementChange,
  handleOptionChange,
  handleAddOption,
  handleRemoveOption,
  focusedElementId,
  onAddFieldClick,
}) => {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language;
  
  return (
    <Paper className="form-elements">
      <Typography variant="subtitle2" className="form-title">
        {t('simpleAIApps.create.formElements.title')} ({elements.length}/5)
      </Typography>

      {elements.length === 0 ? (
        <Button
          variant="outlined"
          fullWidth
          className="add-field-button"
          onClick={onAddFieldClick}
          startIcon={<Iconify icon="material-symbols:add" />}
        >
          {t('simpleAIApps.create.formElements.addField')}
        </Button>
      ) : (
        <ReactSortable list={elements} setList={setElements} handle=".drag-handle">
          {elements.map((element) => (
            <Box key={element.id} className="form-element">
              <Box className="element-header">
                <IconButton className="drag-handle">
                  <DragIndicator />
                </IconButton>
                <Box className="type-label">
                  {element.type === 'Short Text' && (
                    <Box className="type-icon"><Iconify icon="mingcute:text-fill" /></Box>
                  )}
                  {element.type === 'Paragraph' && (
                    <Box className="type-icon"><Iconify icon="hugeicons:paragraph" /></Box>
                  )}
                  {element.type === 'Dropdown' && (
                    <Box className="type-icon"><Iconify icon="ri:dropdown-list" /></Box>
                  )}
                  <Typography variant="subtitle2" style={{ color: '#677788', fontWeight: 'bold' }}>
                    {element.type}
                  </Typography>
                </Box>
                <IconButton onClick={() => handleRemoveElement(element.id)} className="remove-button">
                  <Iconify icon="material-symbols:close" />
                </IconButton>
              </Box>
              <Box className="element-content">
                <Box className="input-field-container">
                  <Typography variant="subtitle2" className="field-label">
                    {t('simpleAIApps.create.formElements.inputName')}
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder={t('simpleAIApps.create.formElements.inputNamePlaceholder')}
                    value={element.inputName}
                    onChange={(e) => handleElementChange(element.id, 'inputName', e.target.value)}
                    data-element-id={element.id}
                    autoFocus={element.id === focusedElementId}
                  />
                </Box>
                <Box className="input-field-container">
                  <Typography variant="subtitle2" className="field-label">
                    {t('simpleAIApps.create.formElements.defaultText')}
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder={t('simpleAIApps.create.formElements.defaultTextPlaceholder')}
                    value={element.defaultText}
                    onChange={(e) => handleElementChange(element.id, 'defaultText', e.target.value)}
                    multiline={element.type === 'Paragraph'}
                    minRows={element.type === 'Paragraph' ? 5 : 1}
                    maxRows={element.type === 'Paragraph' ? 10 : 1}
                    sx={{
                      '& .MuiInputBase-root': {
                        '& textarea': {
                          resize: element.type === 'Paragraph' ? 'both' : 'none',
                        },
                      },
                    }}
                  />
                </Box>
              {element.type === 'Dropdown' && (
                <Box className="options-container">
                <Typography variant="subtitle2" className="field-label">
                  {t('simpleAIApps.create.formElements.addOptions')}
                </Typography>
                  {element.options.map((option) => (
                    <Box key={option.id} className="option-item">
                      <TextField
                        value={option.value}
                        onChange={(e) => handleOptionChange(element.id, option.id, e.target.value)}
                        placeholder={t('simpleAIApps.create.formElements.optionPlaceholder')}
                        className="option-input"
                      />
                      <IconButton
                        onClick={() => handleRemoveOption(element.id, option.id)}
                        className="remove-option"
                      >
                        <Iconify icon="material-symbols:close" />
                      </IconButton>
                    </Box>
                  ))}
                  <Button
                    onClick={() => handleAddOption(element.id)}
                    startIcon={<Iconify icon="material-symbols:add" />}
                    className="add-option-button"
                    variant="outlined"
                    style={{ textTransform: 'capitalize' }}
                  >
                    {t('simpleAIApps.create.formElements.addOption')}
                  </Button>
                </Box>
              )}
              </Box>
            </Box>
          ))}
        </ReactSortable>
      )}
    </Paper>
  );
};

FormElements.propTypes = {
  elements: PropTypes.array.isRequired,
  setElements: PropTypes.func.isRequired,
  handleRemoveElement: PropTypes.func.isRequired,
  handleElementChange: PropTypes.func.isRequired,
  handleOptionChange: PropTypes.func.isRequired,
  handleAddOption: PropTypes.func.isRequired,
  handleRemoveOption: PropTypes.func.isRequired,
  focusedElementId: PropTypes.number,
  onAddFieldClick: PropTypes.func.isRequired,
};

export default memo(FormElements);
