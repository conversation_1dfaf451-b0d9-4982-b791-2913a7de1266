@use '../../../../../styles/abstracts/variables' as *;

.form-elements {
  padding: $spacing-4;
  margin-bottom: $spacing-4;
  border-radius: $border-radius-md !important;
  border: 1px solid $border-color !important;
  box-shadow: $shadow-sm !important;
  background: url('@/assets/images/effect.png') no-repeat top -80px right -75px #FFF !important;

  .form-title {
    color: $primary-text-color-dark !important;
    font-weight: $font-weight-bold;
    margin-bottom: $spacing-3;
  }

  .add-field-button {
    padding:$spacing-3 $spacing-3 !important;
    border: 2px dashed $border-color !important;
    text-transform: none !important;
    color: $primary-text-color;
    font-weight: $font-weight-medium;
    border-radius: $border-radius-md !important;
    background-color: $bg-paper !important;
    transition: all 0.3s ease;
    margin:0 !important;

    &:hover {
      border-color: $primary-color !important;
      color: $primary-color !important;
      background-color: rgba($primary-color, 0.04) !important;
    }

    .MuiButton-startIcon {
      margin-right: $spacing-2;
    }
  }

  .form-element {
    border: 1px solid $border-color;
    border-radius: $border-radius-md;
    padding: $spacing-3;
    margin-bottom: $spacing-3;
    background-color: $bg-paper;
    .element-content{
      background-color: $bg-light;
      border-radius: $border-radius-md !important;
      border: 1px solid $border-color !important;
      padding: $spacing-4 !important;
      display:flex;
      flex-direction: column;
      gap: $spacing-3;
      .MuiTextField-root{
        margin:0 !important
      }
    }
    .element-header {
      display: flex;
      align-items: center;
      margin-bottom: $spacing-3;

      .drag-handle {
        cursor: move;
        color: $primary-text-color;
      }

      .type-label {
        display: flex;
        align-items: center;
        gap: $spacing-2;

        .type-icon {
          width: 32px;
          height: 32px;
          background-color: $bg-light;
          border-radius: $border-radius-sm;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: $font-weight-bold;
          border:1px solid $border-color;
          color: $primary-text-color;
          font-size: $font-size-md;
          svg{
            width: 18px;
            height: 18px;
          }
        }
      }

      .remove-button {
        margin-left: auto;
        color: $primary-text-color;
      }
    }

    .input-field {
      margin-bottom: $spacing-3;
    }

    .options-container {
      .field-label {
        color: $primary-text-color;
        font-weight: $font-weight-bold;
        margin-bottom: $spacing-2;
      }
      .option-item {
        display: flex;
        align-items: center;
        margin-bottom: $spacing-2;

        .option-input {
          flex: 1;
          margin-right: $spacing-2;
        }

        .remove-option {
          color: $primary-text-color;
        }
      }

      .add-option-button {
        margin-top: $spacing-2;
      }
    }

    .element-content {
      background-color: $bg-light;
      padding: $spacing-3;
      display: flex;
      flex-direction: column;
      gap: $spacing-3;

      .input-field-container {
        .field-label {
          color: $primary-text-color;
          font-weight: $font-weight-bold;
          margin-bottom: $spacing-2;
        }

        .MuiTextField-root {
          margin: 0 !important;
          
          .MuiOutlinedInput-root {
            background-color: $bg-paper;
          }
        }
      }
    }
  }
} 