import React from 'react';
import PropTypes from 'prop-types';
import { Box, Typography, Tooltip } from '@mui/material';
import { useTranslation } from 'react-i18next';
import Button from '../../../../../components/Button/Button';
import './NavigationButtons.scss';

// Tooltip için stil tanımları
const tooltipStyles = {
  tooltip: {
    backgroundColor: 'rgba(97, 97, 97, 0.9)',
    fontSize: '12px',
    padding: '8px 12px',
    maxWidth: '300px',
  },
  arrow: {
    color: 'rgba(97, 97, 97, 0.9)',
  },
};

const NavigationButtons = ({ currentStep, isFormValid, handleBack, handleContinue }) => {
  const { t } = useTranslation();

  if (currentStep === 2) {
    return null;
  }

  return (
    <Box className="navigation-buttons">
      <Typography variant="caption" className="warning-text">
        {t('simpleAIApps.create.warning')}
      </Typography>

      <Box className="button-group">
        {currentStep > 0 && (
          <Button variant="outlined" onClick={handleBack} className="back-button" color="default">
            {t('simpleAIApps.create.buttons.back')}
          </Button>
        )}
        <Tooltip
          title={!isFormValid() ? t('common.pleaseCompleteRequiredFields') : ''}
          placement="top"
          arrow
          disableHoverListener={isFormValid()}
          classes={tooltipStyles}
        >
          <span>
            <Button
              variant="contained"
              onClick={handleContinue}
              disabled={!isFormValid()}
              color="primary"
            >
              {currentStep === 0
                ? t('simpleAIApps.create.buttons.next')
                : currentStep === 1
                  ? t('simpleAIApps.create.buttons.reviewApp')
                  : t('simpleAIApps.create.buttons.create')}
            </Button>
          </span>
        </Tooltip>
      </Box>
    </Box>
  );
};
NavigationButtons.propTypes = {
  currentStep: PropTypes.number.isRequired,
  isFormValid: PropTypes.func.isRequired,
  handleBack: PropTypes.func.isRequired,
  handleContinue: PropTypes.func.isRequired,
};

export default NavigationButtons;
