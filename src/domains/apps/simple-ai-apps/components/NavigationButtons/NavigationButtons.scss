@use '../../../../../styles/abstracts/variables' as *;

.navigation-buttons {
  display:flex;
  flex-direction: row;
  margin-top: $spacing-4;

  .warning-text {
    width: 70%;
    color: $primary-text-color;
    letter-spacing: 0px !important;
    font-size: $font-size-xs !important;
    display: block;
    margin-bottom: $spacing-4;
  }

  .button-group {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    margin-left:auto;
    gap: $spacing-2;

    .back-button {
      border-color: #71869d;
      color: $primary-text-color;
      background-color:#71869d;
      border:none !important;
      color:#fff;
      &:hover{
        background-color: #607285 !important;
        border-color: #607285 !important;
      }

      &:hover {
        border-color: $primary-color;
        background-color: transparent;
      }
    }

    .MuiButton-contained {
      background-color: $primary-color;
      height: auto !important;

      &:hover {
        background-color: $primary-color-dark;
      }

      &.Mui-disabled {
        background-color: rgba($primary-color, 0.12);
        color: rgba($primary-color, 0.26);
      }
    }
  }
}