import React, { useState, useRef, useEffect } from 'react';
import { Container, Grid, Typography, Box, CircularProgress } from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useTranslation } from 'react-i18next';

import Iconify from '../../../components/Iconify/iconify';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader';
import TextUsecase from '../components/usecases/TextUsecase';
import AppDescription from './components/AppDescription/AppDescription';
import FormElements from './components/FormElements/FormElements';
import InputTypes from './components/InputTypes/InputTypes';
import PromptEditor from './components/PromptEditor/PromptEditor';
import AppSettings from './components/AppSettings/AppSettings';
import NavigationButtons from './components/NavigationButtons/NavigationButtons';
import EditInputs from './components/EditInputs/EditInputs';
import ReviewButtons from './components/ReviewButtons/ReviewButtons';
import NotFoundPage from '../../../components/404/index.jsx';
import {
  useCreateSimpleAppMutation,
  useGetSimpleAppBySlugQuery,
  useUpdateSimpleAppMutation,
} from '../../../redux/services/CreateApp-api';

function CreateSimpleAIApp() {
  const { t } = useTranslation();
  const { slug } = useParams(); // URL'den slug değerini alıyoruz
  const [notFound, setNotFound] = useState(false);

  // API'dan slug'a göre veriyi çekiyoruz
  const {
    data: appData,
    isLoading,
    error,
  } = useGetSimpleAppBySlugQuery(slug, {
    skip: !slug, // Eğer slug yoksa sorguyu atlamak için
    refetchOnMountOrArgChange: true, // Bileşen mount olduğunda veya slug değiştiğinde yeniden çekme
  });

  // Redux store'dan kullanıcı verilerini al
  const { user } = useSelector((state) => state.auth);

  const navigate = useNavigate();
  console.log(appData);
  // Veriyi console'a yazdırıyoruz
  useEffect(() => {
    if (appData && appData.data) {
      // Kullanıcı kimliği kontrolü
      const appUserId = appData.data.userId;
      const currentUserId = user?._id;

      // Eğer kullanıcı ID'leri eşleşmiyorsa 404 sayfasını göster
      if (appUserId && currentUserId && appUserId !== currentUserId) {
        setNotFound(true);
        return;
      }

      // Form alanlarını doldur
      const app = appData.data;

      // Temel bilgiler
      setAppName(app.title);
      setDescription(app.description);
      setPromptContent(app.prompt);

      // Model ve ayarlar
      setSelectedModel(app.model);
      setTemperature(parseFloat(app.temperature) || 1);
      setTopP(parseFloat(app.top_p) || 1);
      setPresencePenalty(parseFloat(app.presence_penalty) || 0);
      setFrequencyPenalty(parseFloat(app.frequency_penalty) || 0);

      // Form elemanlarını dönüştür ve elements state'ine aktar
      if (app.simple_app_form && Array.isArray(app.simple_app_form)) {
        const mappedElements = app.simple_app_form.map((formItem, index) => {
          // API'dan gelen tip değerini UI'da kullanılan tipe dönüştür
          const uiType = mapApiTypeToElementType(formItem.type);

          // Element nesnesini oluştur
          const element = {
            id: formItem._id || `element-${Date.now()}-${index}`,
            type: uiType,
            label: formItem.label || '',
            inputName: formItem.name || formItem.label || '',
            name: formItem.name || formItem.label || '',
            defaultValue: formItem.value || '',
            defaultText: formItem.label || '',
            options: [],
          };

          // Eğer dropdown tipindeyse, choices'ı options dizisine dönüştür
          if (formItem.type === 'select' && formItem.choices) {
            const optionValues = formItem.choices
              .split('\n')
              .map((choice) => choice.trim())
              .filter((choice) => choice);
            element.options = optionValues.map((value, optionIndex) => ({
              id: `option-${Date.now()}-${index}-${optionIndex}`,
              value: value,
            }));
          } else if (formItem.options && Array.isArray(formItem.options)) {
            element.options = [...formItem.options];
          }

          return element;
        });

        setElements(mappedElements);
      }
    }
  }, [appData, user]);

  // API tip değerlerini UI tip değerlerine dönüştürür
  const mapApiTypeToElementType = (apiType) => {
    const typeMapping = {
      input: 'Short text',
      textarea: 'Paragraph',
      select: 'Dropdown',
    };

    return typeMapping[apiType] || 'Short text'; // Varsayılan olarak Short text
  };

  const [appName, setAppName] = useState('');
  const [elements, setElements] = useState([]);
  const maxLength = 50;
  const elementLimit = 5;
  const [currentStep, setCurrentStep] = useState(0);
  const editorRef = useRef(null);
  const [promptContent, setPromptContent] = useState('');
  const maxPromptLength = 2048;
  const [temperature, setTemperature] = useState(1);
  const [topP, setTopP] = useState(1);
  const [frequencyPenalty, setFrequencyPenalty] = useState(0);
  const [presencePenalty, setPresencePenalty] = useState(0);
  const [selectedModel, setSelectedModel] = useState('gpt-4o');
  const [focusedElementId, setFocusedElementId] = useState(null);
  const [description, setDescription] = useState('');
  const [isInputTypesBouncing, setIsInputTypesBouncing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [
    createSimpleApp,
    {
      isLoading: mutationLoading,
      isSuccess: mutationSuccess,
      isError: mutationError,
      error: mutationErrorData,
      data,
    },
  ] = useCreateSimpleAppMutation();
  const [updateSimpleApp] = useUpdateSimpleAppMutation();

  const steps = ['Create Form', 'Prompt writing', 'Review & Create'];

  const handleNameChange = (event) => {
    const value = event.value || event.target.value;
    if (value.length <= maxLength) {
      setAppName(value);
    }
  };

  const handleAddElement = (type) => {
    if (elements.length < elementLimit) {
      setElements([
        ...elements,
        {
          id: Date.now(),
          type: type.name,
          inputName: '',
          defaultText: '',
          label: '',
          options: type.name === 'Dropdown' ? [{ id: Date.now(), value: '' }] : [],
        },
      ]);
    }
  };

  const handleAddOption = (elementId) => {
    setElements(
      elements.map((element) =>
        element.id === elementId
          ? {
              ...element,
              options: [...element.options, { id: Date.now(), value: '' }],
            }
          : element
      )
    );
  };

  const handleRemoveElement = (elementId) => {
    setElements(elements.filter((element) => element.id !== elementId));

    const elementToRemove = elements.find((el) => el.id === elementId);
    if (elementToRemove && editorRef.current) {
      const content = editorRef.current.getContent();
      // Hem strong tag'li hem de normal versiyonları için regex
      const regex = new RegExp(
        `<strong>\\[${elementToRemove.inputName}\\]</strong>|\\[${elementToRemove.inputName}\\]`,
        'g'
      );
      const updatedContent = content.replace(regex, '');
      editorRef.current.setContent(updatedContent);
      setPromptContent(updatedContent);
    }
  };

  const handleElementChange = (elementId, field, value) => {
    setElements(
      elements.map((element) =>
        element.id === elementId ? { ...element, [field]: value } : element
      )
    );
  };

  const handleRemoveOption = (elementId, optionId) => {
    setElements(
      elements.map((element) =>
        element.id === elementId
          ? {
              ...element,
              options: element.options.filter((option) => option.id !== optionId),
            }
          : element
      )
    );
  };

  const handleOptionChange = (elementId, optionId, value) => {
    setElements(
      elements.map((element) =>
        element.id === elementId
          ? {
              ...element,
              options: element.options.map((option) =>
                option.id === optionId ? { ...option, value } : option
              ),
            }
          : element
      )
    );
  };

  const inputTypes = [
    { name: 'Short Text', icon: <Iconify icon="mingcute:text-fill" />, disabled: false },
    { name: 'Paragraph', icon: <Iconify icon="hugeicons:paragraph" />, disabled: false },
    { name: 'Dropdown', icon: <Iconify icon="ri:dropdown-list" />, disabled: false },
    { name: 'Radio', icon: <Iconify icon="formkit:radio" />, disabled: true },
    { name: 'Yes / No', icon: <Iconify icon="line-md:switch-off" />, disabled: true },
    { name: 'Checkbox', icon: <Iconify icon="ri:checkbox-multiple-line" />, disabled: true },
    { name: 'File upload', icon: <Iconify icon="solar:cloud-upload-broken" />, disabled: true },
  ];

  const isFormValid = () => {
    // appName'in tanımlı ve boş olmamasını kontrol et
    if (!appName || !appName.trim()) return false;

    if (!elements.length) return false;
    if (currentStep === 1 && promptContent.replace(/<[^>]*>/g, '').length === 0) return false;

    return elements.every((element) => {
      // inputName'in tanımlı olup olmadığını kontrol et
      const inputName = element.inputName || element.name || element.label || '';
      if (!inputName.trim()) return false;

      if (element.type === 'Dropdown') {
        // options dizisinin tanımlı olup olmadığını kontrol et
        if (!element.options || !Array.isArray(element.options) || element.options.length === 0)
          return false;

        return element.options.every((option) => {
          // option değerinin tanımlı olup olmadığını kontrol et
          const value = option.value || '';
          return value.trim().length > 0;
        });
      }

      return true;
    });
  };

  const handleContinue = () => {
    setCurrentStep(currentStep + 1);
  };

  const handleBack = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleCreate = async () => {
    // If submission is already in progress, prevent another submission
    if (isSubmitting || mutationLoading) {
      return;
    }

    // Set submission state to active
    setIsSubmitting(true);

    // Create form data
    const rawFormData = {
      appName,
      description,
      maxLength,
      elements,
      prompt: promptContent,
      temperature,
      topP,
      frequencyPenalty,
      presencePenalty,
      selectedModel,
    };

    // Function to clean HTML tags
    const stripHtmlTags = (html) => {
      if (!html) return '';
      // Remove HTML tags and resolve HTML entities
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      return tempDiv.textContent || tempDiv.innerText || '';
    };

    // Function to convert element types to API format
    const mapElementTypeToApiType = (type) => {
      const typeMapping = {
        'Short text': 'input',
        Paragraph: 'textarea',
        Dropdown: 'select',
        'Radio button': 'radio',
        Checkbox: 'checkbox',
      };

      return typeMapping[type] || 'input'; // Varsayılan olarak input
    };

    // Convert to the format required by the API
    const simple_app_form = elements
      .map((element) => {
        if (!element) return null; // Geçersiz element ise atla

        const baseField = {
          type: element.type ? mapElementTypeToApiType(element.type) : 'input',
          label: element.defaultText || element.label || element.inputName || '',
          name: element.name || element.inputName || '',
          value: element.defaultText || element.defaultValue || '',
        };

        // If type is Dropdown, add choices as newline-separated string
        if (
          element.type === 'Dropdown' &&
          element.options &&
          Array.isArray(element.options) &&
          element.options.length > 0
        ) {
          baseField.choices = element.options.map((opt) => opt.value).join('\n');
        }
        // If type is Radio button or Checkbox, add options array
        else if (
          ['Radio button', 'Checkbox'].includes(element.type) &&
          element.options &&
          Array.isArray(element.options) &&
          element.options.length > 0
        ) {
          baseField.options = element.options.map((opt) => ({
            value: opt && opt.value ? opt.value : '',
          }));
        }

        return baseField;
      })
      .filter(Boolean); // null değerleri filtrele

    // Data format to be sent to the API
    const apiData = {
      title: appName || '',
      slug: appData && appData.data && appData.data.slug ? appData.data.slug : '', // Var olan slug'ı koru
      simple_app_form: simple_app_form || [],
      temperature: temperature !== undefined ? temperature.toString() : '1',
      top_p: topP !== undefined ? topP.toString() : '1',
      presence_penalty: presencePenalty !== undefined ? presencePenalty.toString() : '0',
      frequency_penalty: frequencyPenalty !== undefined ? frequencyPenalty.toString() : '0',
      prompt: promptContent ? stripHtmlTags(promptContent) : '',
      model: selectedModel || 'gpt-4o',
      description: description ? stripHtmlTags(description) : '',
      userId:
        user && user._id
          ? user._id
          : appData && appData.data && appData.data.userId
            ? appData.data.userId
            : '',
    };

    try {
      // Güncelleme API çağrısı
      const response = await updateSimpleApp({
        id: appData.data._id,
        simpleAppData: apiData,
      }).unwrap();

      // Başarı bildirimi göster ve 3 saniye sonra uygulamaya yönlendir
      toast.success(
        'Application updated successfully! You will be redirected to the application page.',
        {
          position: 'top-right',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: 'light',
          icon: '🎉',
          onClose: () => {
            navigate(`/ai_apps/${response.data.slug}/`);
          },
        }
      );
    } catch (error) {
      console.error('Error details:', error);
      toast.error('An error occurred while updating the application. Please try again.', {
        position: 'top-right',
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: 'light',
      });
      // Process completed, reset submission state
      setIsSubmitting(false);
    }
  };

  const handleDragStart = (e, element) => {
    // Noneditable span içinde input parametresini gönder
    e.dataTransfer.setData(
      'text/plain',
      `<span contenteditable="false" class="mceNonEditable input-param"><b>{${element.inputName}}</b></span>`
    );
  };

  const handleEditorDrop = (e) => {
    e.preventDefault();
    const text = e.dataTransfer.getData('text/plain');
    if (editorRef.current) {
      editorRef.current.execCommand('mceInsertContent', false, text);
    }
  };

  const handleEditorChange = (content, editor) => {
    setDescription(content);
  };
  const handlePromptChange = (content, editor) => {
    setPromptContent(content);
  };

  const handleEditElement = (elementId) => {
    setFocusedElementId(elementId);
    setCurrentStep(0);
  };

  const handleAddFieldClick = () => {
    setIsInputTypesBouncing(true);
    setTimeout(() => setIsInputTypesBouncing(false), 1000);
  };

  useEffect(() => {
    if (focusedElementId && currentStep === 0) {
      const inputElement = document.querySelector(`[data-element-id="${focusedElementId}"]`);
      if (inputElement) {
        inputElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        inputElement.focus();
      }
      setFocusedElementId(null);
    }
  }, [currentStep, focusedElementId]);

  // TextUsecase bileşeni için veri hazırlama fonksiyonu
  const prepareAppDataForTextUsecase = (appData) => {
    if (!appData) return null;

    // HTML etiketlerini temizle
    const cleanDescription = appData.description
      ? appData.description.replace(/<\/?[^>]+(>|$)/g, '')
      : '';

    // Form alanlarını TextUsecase'in beklediği formata dönüştür
    const formFields = (appData.elements || []).map((element) => {
      // Girdi tipini TextUsecase'in beklediği formata dönüştür
      let fieldType;

      switch (element.type) {
        case 'Dropdown':
          fieldType = 'select';
          break;
        case 'Paragraph':
          fieldType = 'textarea';
          break;
        case 'Short Text':
          fieldType = 'text';
          break;
        case 'Yes / No':
          fieldType = 'radio';
          break;
        case 'Checkbox':
          fieldType = 'checkbox';
          break;
        case 'File upload':
          fieldType = 'file';
          break;
        default:
          fieldType = 'text';
      }

      return {
        _id: element.id.toString(),
        type: fieldType,
        label: element.inputName,
        name: element.inputName,
        default_value: element.defaultText || '',
        choices:
          element.type === 'Dropdown' ? element.options.map((opt) => opt.value).join('\n') : '',
      };
    });

    // TextUsecase'in beklediği tam veri yapısını oluştur
    return {
      _id: Date.now().toString(),
      title: appData.appName,
      slug: appData.appName.toLowerCase().replace(/\s+/g, '-'),
      content: cleanDescription,
      api_type: 'stream',
      stream_settings: {
        stream_form: {
          form_fields: formFields,
        },
        stream_prompt: appData.promptContent?.replace(/<\/?[^>]+(>|$)/g, '') || '',
        temperature: appData.temperature?.toString() || '1',
        top_p: appData.topP?.toString() || '1',
        frequency_penalty: appData.frequencyPenalty?.toString() || '0',
        presence_penalty: appData.presencePenalty?.toString() || '0',
        model_selection: appData.selectedModel || 'gpt-4o',
        max_token: '4000',
      },
    };
  };

  return (
    <>
      {isLoading ? (
        <Container
          maxWidth="lg"
          sx={{
            mt: 5,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '50vh',
          }}
        >
          <Box sx={{ textAlign: 'center' }}>
            <CircularProgress />
            <Typography variant="h6" sx={{ mt: 2 }}>
              {t('simpleAIApps.create.loading')}
            </Typography>
          </Box>
        </Container>
      ) : notFound || error ? (
        <NotFoundPage />
      ) : (
        <Container maxWidth="lg" sx={{ mt: 0, mb: 3 }}>
          <ToastContainer
            position="top-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
          />
          <Box sx={{ p: 0 }}>
            <Grid container spacing={0}>
              <Grid item mb={4} xs={12}>
                <WelcomeHeader
                  isPageView
                  title={t('simpleAIApps.create.title')}
                  description={t('simpleAIApps.create.description')}
                />
              </Grid>
              <Grid container spacing={3}>
                <Grid item xs={12} md={12} lg={12}>
                  {elements.length > 0 && (
                    <ReviewButtons
                      currentStep={currentStep}
                      isFormValid={isFormValid}
                      handleBack={handleBack}
                      handleContinue={handleCreate}
                      isSubmitting={isSubmitting || mutationLoading}
                      updateApp={true}
                    />
                  )}
                </Grid>
              </Grid>
              {currentStep === 0 ? (
                <Grid container spacing={3}>
                  <Grid item xs={12} md={8} lg={8}>
                    <AppDescription
                      appName={appName}
                      description={description}
                      maxLength={maxLength}
                      handleNameChange={handleNameChange}
                      handleEditorChange={handleEditorChange}
                    />

                    <FormElements
                      elements={elements}
                      setElements={setElements}
                      handleRemoveElement={handleRemoveElement}
                      handleElementChange={handleElementChange}
                      handleOptionChange={handleOptionChange}
                      handleAddOption={handleAddOption}
                      handleRemoveOption={handleRemoveOption}
                      focusedElementId={focusedElementId}
                      onAddFieldClick={handleAddFieldClick}
                    />
                  </Grid>

                  <Grid item xs={12} md={4} lg={4}>
                    <InputTypes
                      elements={elements}
                      elementLimit={elementLimit}
                      inputTypes={inputTypes}
                      handleAddElement={handleAddElement}
                      isBouncing={isInputTypesBouncing}
                    />
                  </Grid>
                </Grid>
              ) : currentStep === 1 ? (
                <Grid container spacing={3}>
                  <Grid item xs={12} md={8} lg={8}>
                    <PromptEditor
                      elements={elements}
                      promptContent={promptContent}
                      maxPromptLength={maxPromptLength}
                      handlePromptChange={handlePromptChange}
                      handleDragStart={handleDragStart}
                      handleEditorDrop={handleEditorDrop}
                      editorRef={editorRef}
                    />

                    <AppSettings
                      selectedModel={selectedModel}
                      temperature={temperature}
                      topP={topP}
                      frequencyPenalty={frequencyPenalty}
                      presencePenalty={presencePenalty}
                      setSelectedModel={setSelectedModel}
                      setTemperature={setTemperature}
                      setTopP={setTopP}
                      setFrequencyPenalty={setFrequencyPenalty}
                      setPresencePenalty={setPresencePenalty}
                    />
                  </Grid>
                  <Grid item xs={12} md={4} lg={4}>
                    <EditInputs elements={elements} onEditElement={handleEditElement} />
                  </Grid>
                </Grid>
              ) : (
                <Grid item mb={4} xs={12}>
                  <TextUsecase
                    data={prepareAppDataForTextUsecase({
                      appName,
                      description,
                      elements,
                      promptContent,
                      temperature,
                      topP,
                      frequencyPenalty,
                      presencePenalty,
                      selectedModel,
                    })}
                    id={Date.now().toString()}
                    disableFavorites={true}
                    hideTitle={true}
                    onlyShowUsecase={true}
                  />
                </Grid>
              )}
              <Grid container spacing={3}>
                <Grid item xs={12} md={8} lg={8}>
                  {elements.length > 0 && (
                    <NavigationButtons
                      currentStep={currentStep}
                      isFormValid={isFormValid}
                      handleBack={handleBack}
                      handleContinue={handleContinue}
                    />
                  )}
                </Grid>
              </Grid>
            </Grid>
          </Box>
        </Container>
      )}
    </>
  );
}

export default CreateSimpleAIApp;
