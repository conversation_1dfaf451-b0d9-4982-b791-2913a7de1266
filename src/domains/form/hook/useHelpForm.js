import { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '../../../redux/features/auth/authSlice';
import { useFormsQuery } from '../../../redux/services/form-service';
import { useSubmitFormResponseMutation } from '../../../redux/services/form-response';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';
import { HELP_FORM_ID } from '../../../constants/form-constants';

export const useHelpForm = () => {
  const { t, i18n } = useTranslation();
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);
  const [shouldFetchForm, setShouldFetchForm] = useState(false);
  const [helpFormData, setHelpFormData] = useState({});
  const [helpFormErrors, setHelpFormErrors] = useState({});
  const [hasInteracted, setHasInteracted] = useState(false);
  const [resetCounter, setResetCounter] = useState(0);
  const formRenderRef = useRef(null);
  const user = useSelector(selectCurrentUser);

  // Kullanıcının mevcut dil tercihi
  const currentLanguage = i18n.language || user?.onboarding?.language || 'en';

  // Fetch form data only when help menu is clicked
  const {
    data: supportFormData,
    isLoading,
    error,
  } = useFormsQuery(
    shouldFetchForm ? { formId: HELP_FORM_ID, language: currentLanguage } : undefined,
    {
      skip: !shouldFetchForm,
    }
  );

  // Form yanıtlarını göndermek için RTK mutation hook'unu kullan
  const [submitFormResponse, { isLoading: isSubmittingForm }] = useSubmitFormResponseMutation();

  // Form verilerini hazırlayan ayrı bir fonksiyon
  const prepareFormDataWithUserInfo = useCallback(() => {
    if (!user || !supportFormData) return {};

    // Temiz bir nesne ile başla
    const formDataWithUserInfo = {};

    // Form alanlarını belirle
    const topics = supportFormData.topics || [];
    const topic = supportFormData.topic || (topics.length > 0 ? topics[0] : null);
    const fields = topic?.fields || [];

    // Kullanıcının tam adını oluştur (name + surname)
    const fullName = user.surname
      ? `${user.name || ''} ${user.surname || ''}`.trim()
      : user.name || '';

    // Kullanıcı bilgilerini ilgili alanlara ekle
    fields.forEach((field) => {
      if (field.name === 'name') {
        // Name alanına kullanıcının tam adını ekle
        formDataWithUserInfo[field.name] = fullName;
      }

      if (field.name === 'email') {
        formDataWithUserInfo[field.name] = user.email || '';
      }
    });

    return formDataWithUserInfo;
  }, [user, supportFormData]);

  // Form verisinin ve kullanıcı bilgilerinin değişimini izle
  useEffect(() => {
    // Form açıksa ve veriler yüklendiyse
    if (isHelpModalOpen && supportFormData && user) {
      try {
        // Kullanıcı bilgileri ile form verilerini hazırla
        const formValues = prepareFormDataWithUserInfo();

        // Form verilerini ayarla
        if (Object.keys(formValues).length > 0) {
          setHelpFormData(formValues);
        }
      } catch {
        // Hata durumunu sessizce yönet
      }
    }
  }, [isHelpModalOpen, supportFormData, user, prepareFormDataWithUserInfo]);

  // Form alanı değişikliklerini takip eden fonksiyon
  const handleHelpFormChange = (data) => {
    if (!data || Object.keys(data).length === 0) return;

    // Kullanıcı etkileşimini takip et
    if (data.__userInteracted) {
      setHasInteracted(true);
    }

    // __errors özel alanını kontrol et
    if (data.__errors && typeof data.__errors === 'object' && data.__errors !== null) {
      setHelpFormErrors((prevErrors) => {
        const updatedErrors = { ...prevErrors };

        // __errors içindeki her alan için işlem yap - null kontrolü ekledim
        try {
          Object.entries(data.__errors).forEach(([key, value]) => {
            if (value === null) {
              // null değeri, hatanın temizlenmesi gerektiğini gösterir
              delete updatedErrors[key];
            } else {
              // Yeni hata ekle
              updatedErrors[key] = value;
            }
          });
        } catch {
          // Hatayı sessizce ele al
        }

        return updatedErrors;
      });

      // __errors işlendi, diğer alanları işlemeye devam et
      delete data.__errors;
    } else if (data.__errors === null) {
      // Eğer data.__errors null ise, tüm hataları temizle
      setHelpFormErrors({});
      delete data.__errors;
    }

    // __formState tüm form durumunu içeriyor olabilir
    if (data.__formState && typeof data.__formState === 'object') {
      // __formState değerini kullanabiliriz - bu tüm form state'ini içeriyor olabilir
      const formState = data.__formState;

      // Eğer formState varsa ve içinde veriler varsa, tüm state'i güncelleyelim
      if (formState && typeof formState === 'object' && Object.keys(formState).length > 0) {
        setHelpFormData(formState);
      }

      delete data.__formState;
    }

    // __userInteracted flag'ini temizle
    delete data.__userInteracted;

    // Geriye kalan tüm veri alanlarını işle
    const fieldNames = Object.keys(data);

    if (fieldNames.length > 0) {
      // Form datasını güncelle
      setHelpFormData((prevData) => {
        const updatedData = { ...prevData };

        fieldNames.forEach((fieldName) => {
          updatedData[fieldName] = data[fieldName];
        });

        return updatedData;
      });
    }
  };

  // Alanın koşul durumunu kontrol eden fonksiyon
  const checkCondition = (field, formValues) => {
    // Eğer koşul yoksa görünür
    if (!field.condition) return true;

    const { field: conditionField, operator, value } = field.condition;

    // Koşul alanının değerini al
    const fieldValue = formValues[conditionField];

    // Operatör kontrolü
    switch (operator) {
      case 'equals':
        return fieldValue === value;
      case 'not_equals':
        return fieldValue !== value;
      case 'contains':
        return typeof fieldValue === 'string' && fieldValue.includes(value);
      case 'not_contains':
        return typeof fieldValue === 'string' && !fieldValue.includes(value);
      case 'starts_with':
        return typeof fieldValue === 'string' && fieldValue.startsWith(value);
      case 'ends_with':
        return typeof fieldValue === 'string' && fieldValue.endsWith(value);
      case 'greater_than':
        return Number(fieldValue) > Number(value);
      case 'less_than':
        return Number(fieldValue) < Number(value);
      default:
        return true;
    }
  };

  // Formu sıfırdan oluştur gerektiğinde
  const resetFormAndCreateNew = useCallback(() => {
    if (supportFormData && user) {
      try {
        // Temiz bir form oluştur
        const newFormData = prepareFormDataWithUserInfo();

        // State'i güncelle
        setHelpFormData(newFormData);
        setHelpFormErrors({});
        setHasInteracted(false);
      } catch {
        // Hatayı sessizce ele al
      }
    }
  }, [supportFormData, user, prepareFormDataWithUserInfo]);

  // Form modalını resetle ve yeni form oluştur
  const handleResetForm = () => {
    resetFormAndCreateNew();
    setResetCounter((prev) => prev + 1);
  };

  // Form gönderme fonksiyonu
  const handleSubmitHelpForm = async () => {
    try {
      if (!supportFormData) {
        toast.error(t('support.form.errors.unavailable'), {
          toastId: 'header-toast',
          updateId: 'header-toast',
        });
        return false;
      }

      // Form gönderilirken kullanıcının etkileşime girdiğini belirt
      setHasInteracted(true);

      // FormRender bileşeninin validateForm metodunu çağır
      if (formRenderRef.current && typeof formRenderRef.current.validateForm === 'function') {
        formRenderRef.current.validateForm();
      }

      // Gerekli alanları kontrol et
      let hasValidationErrors = false;
      const tempErrors = { ...helpFormErrors };

      // Tüm gerekli alanları topla
      const allFields = [];

      // Form yapısına göre alanları toplama
      if (supportFormData.topic && supportFormData.topic.fields) {
        allFields.push(...supportFormData.topic.fields);
      } else if (supportFormData.topics && supportFormData.topics.length > 0) {
        // İlk topic ile başla (multiple topic durumunda)
        const primaryTopic = supportFormData.topics[0];
        if (primaryTopic && primaryTopic.fields) {
          allFields.push(...primaryTopic.fields);
        }
      } else if (supportFormData.fields) {
        // Doğrudan fields varsa
        allFields.push(...supportFormData.fields);
      }

      // Eğer hiç alan bulunamazsa hata göster
      if (allFields.length === 0) {
        toast.error(t('support.form.errors.noFields'), {
          toastId: 'header-toast',
          updateId: 'header-toast',
        });
        console.error('No fields found in form data:', supportFormData);
        return false;
      }

      // Gerekli alanları kontrol et
      allFields.forEach((field) => {
        // Önce alanın görünür olup olmadığını kontrol et
        const isVisible = checkCondition(field, helpFormData);

        // Alan görünür değilse kontrol etme ve mevcut hatayı temizle
        if (!isVisible) {
          // Eğer bu alan için daha önce hata varsa temizle
          if (tempErrors[field.name]) {
            delete tempErrors[field.name];
          }
          return; // continue yerine return kullanılır çünkü forEach içindeyiz
        }

        // Sadece görünür alanlar için required kontrolü yap
        if (field.required) {
          const fieldName = field.name;
          const fieldValue = helpFormData[fieldName];

          // Boş alan kontrolü - farklı tip boş değerleri kontrol edelim
          const isEmpty =
            fieldValue === undefined ||
            fieldValue === null ||
            fieldValue === '' ||
            (Array.isArray(fieldValue) && fieldValue.length === 0) ||
            (typeof fieldValue === 'object' && Object.keys(fieldValue).length === 0);

          if (isEmpty) {
            tempErrors[fieldName] = t('common.validation.required');
            hasValidationErrors = true;
          } else {
            // Eğer alan doluysa ve hataları temizliyoruz
            if (tempErrors[fieldName] === t('common.validation.required')) {
              delete tempErrors[fieldName];
            }
          }
        }
      });

      // Hataları güncelle
      setHelpFormErrors(tempErrors);

      // Eğer validasyon hataları varsa form gönderilmeyecek
      if (hasValidationErrors) {
        toast.error(t('support.form.errors.requiredFields'), {
          toastId: 'header-toast',
          updateId: 'header-toast',
        });
        return false;
      }

      // Form yanıtlarını hazırla
      const responses = allFields
        .filter((field) => {
          // Görünür alanları kontrol et
          const isVisible = checkCondition(field, helpFormData);
          if (!isVisible) return false;

          const fieldValue = helpFormData[field.name];
          return !(
            fieldValue === undefined ||
            fieldValue === null ||
            fieldValue === '' ||
            (Array.isArray(fieldValue) && fieldValue.length === 0) ||
            (typeof fieldValue === 'object' && Object.keys(fieldValue).length === 0)
          );
        })
        .map((field) => ({
          fieldId: field._id,
          name: field.label || field.name,
          type: field.type,
          value: helpFormData[field.name],
        }));

      // Form yanıt verilerini hazırla
      const requestData = {
        formId: HELP_FORM_ID,
        formType: 'help',
        title: supportFormData?.title || t('support.form.defaultTitle'),
        description: supportFormData?.description || t('support.form.defaultDescription'),
        responses,
      };

      console.log('Form gönderilecek veriler:', requestData);

      try {
        // Form yanıtlarını gönder
        const response = await submitFormResponse(requestData).unwrap();

        if (response && response.status === 'success') {
          toast.success(t('support.form.success.submitted'), {
            toastId: 'header-toast',
            updateId: 'header-toast',
          });

          handleCloseHelpModal();
          return true;
        } else {
          const errorMessage = response?.message || t('support.form.errors.submission');
          toast.error(`${t('common.error')}: ${errorMessage}. ${t('common.tryAgain')}`, {
            toastId: 'header-toast',
            updateId: 'header-toast',
          });
          return false;
        }
      } catch (error) {
        console.error('Form gönderimi hatası:', error);
        toast.error(t('support.form.errors.submit'), {
          toastId: 'header-toast',
          updateId: 'header-toast',
        });
        return false;
      }
    } catch (error) {
      console.error('Form işleme hatası:', error);
      toast.error(t('support.form.errors.submit'), {
        toastId: 'header-toast',
        updateId: 'header-toast',
      });
      return false;
    }
  };

  // Help modal için fonksiyonlar
  const handleOpenHelpModal = () => {
    // Form verilerini temizle
    setHelpFormData({});
    setHelpFormErrors({});
    setHasInteracted(false);

    // Form modalını aç
    setIsHelpModalOpen(true);

    // Form verisini alma işlemini başlat
    setShouldFetchForm(true);
  };

  const handleCloseHelpModal = () => {
    setIsHelpModalOpen(false);
    setShouldFetchForm(false);
    setHelpFormData({});
    setHelpFormErrors({});
  };

  // FormRender bileşenini memo ile optimize edelim
  const getFormRenderComponent = useMemo(() => {
    if (!supportFormData) return null;

    return {
      formData: supportFormData,
      onChange: handleHelpFormChange,
      values: helpFormData,
      defaultValues: helpFormData,
      initialValues: helpFormData,
      formInitialValues: helpFormData,
      disabled: isSubmittingForm,
      errors: helpFormErrors,
      ref: formRenderRef,
    };
  }, [supportFormData, helpFormData, helpFormErrors, isSubmittingForm]);

  return {
    isHelpModalOpen,
    supportFormData,
    isLoading,
    error,
    isSubmittingForm,
    helpFormData,
    helpFormErrors,
    hasInteracted,
    resetCounter,
    formRenderRef,
    handleOpenHelpModal,
    handleCloseHelpModal,
    handleSubmitHelpForm,
    handleHelpFormChange,
    handleResetForm,
    getFormRenderComponent,
  };
};

export default useHelpForm;
