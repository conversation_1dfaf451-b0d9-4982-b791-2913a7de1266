import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import {
  Modal,
  Box,
  Typography,
  Grid,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemText,
  Paper,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import CardWithIcon from '@/components/CardWithIcon/CardWithIcon';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';

const modalStyle = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: { xs: '90%', sm: '80%', md: '60%' },
  maxHeight: '90vh',
  bgcolor: 'background.paper',
  borderRadius: 2,
  boxShadow: 24,
  p: 4,
  overflow: 'auto',
};

const SubmittedIdeaCard = ({ idea }) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const modalRef = useRef(null);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  // Backdrop tıklamasını kontrol etmek için
  const handleBackdropClick = (event) => {
    if (event.target === event.currentTarget) {
      handleClose();
    }
  };

  // Responses array'inden title ve description'ı filtrele
  const getIdeaTitle = () => {
    const titleResponse = idea.responses.find((response) =>
      response.name.toLowerCase().includes('title of your idea')
    );
    return titleResponse?.value || 'Untitled idea';
  };

  const getIdeaDescription = () => {
    const descriptionResponse = idea.responses.find((response) =>
      response.name.toLowerCase().includes('describe your idea')
    );
    return descriptionResponse?.value || 'No description provided';
  };

  const filteredIdeaTitle = getIdeaTitle();
  const filteredIdeaDescription = getIdeaDescription();

  return (
    <Grid item xs={12} md={6}>
      <CardWithIcon
        icon={<LightbulbIcon sx={{ width: 24, height: 24 }} />}
        title={filteredIdeaTitle}
        description={filteredIdeaDescription}
        variant="sales"
        buttonText={t('cockpit.submittedIdeas.viewDetails')}
        buttonType="FUNCTION"
        buttonFunction={handleOpen}
        newTab={false}
        date={idea.createdAt}
        lineClamp={3}
      />

      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
        disableEscapeKeyDown={false}
        onClick={handleBackdropClick}
        // Erişilebilirlik iyileştirmeleri
        keepMounted
        disableEnforceFocus={false}
        disableAutoFocus={false}
        ref={modalRef}
      >
        <Box
          sx={modalStyle}
          onClick={(e) => e.stopPropagation()}
          role="dialog"
          aria-modal="true"
          tabIndex={-1}
        >
          {/* Modal Header */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography id="modal-title" variant="h6" component="h2">
              {filteredIdeaTitle}
            </Typography>
            <IconButton
              aria-label="close modal"
              onClick={handleClose}
              sx={{ color: 'text.secondary' }}
              // Erişilebilirlik için
              tabIndex={0}
            >
              <CloseIcon />
            </IconButton>
          </Box>

          <Divider sx={{ mb: 2 }} />

          {/* Modal Content */}
          <Box id="modal-description">
            {/* Description Section */}
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Description
            </Typography>
            <Typography variant="body1" paragraph>
              {filteredIdeaDescription}
            </Typography>

            {/* Responses Section */}
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Responses
            </Typography>
            <Paper variant="outlined" sx={{ mt: 2 }}>
              <List>
                {idea.responses.map((response, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemText
                        primary={
                          <Typography variant="subtitle2" color="text.primary">
                            {response.name}
                          </Typography>
                        }
                        secondary={
                          <Typography variant="body2" color="text.secondary">
                            {response.value}
                          </Typography>
                        }
                      />
                    </ListItem>
                    {index < idea.responses.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Paper>

            {/* Status and Date Section */}
            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
              {/* <Typography variant="body2" color="text.secondary">
                Status: {idea.status}
              </Typography> */}
              <Typography variant="body2" color="text.secondary"></Typography>
              <Typography variant="body2" color="text.secondary">
                {t('cockpit.submittedIdeas.created')}{' '}
                {(() => {
                  const dateObj = new Date(idea.createdAt);
                  const day = String(dateObj.getUTCDate()).padStart(2, '0');
                  const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0');
                  const year = dateObj.getUTCFullYear();
                  const hours = String(dateObj.getUTCHours()).padStart(2, '0');
                  const minutes = String(dateObj.getUTCMinutes()).padStart(2, '0');
                  return `${day}/${month}/${year} - ${hours}:${minutes}`;
                })()}
              </Typography>
            </Box>
          </Box>
        </Box>
      </Modal>
    </Grid>
  );
};

SubmittedIdeaCard.propTypes = {
  idea: PropTypes.shape({
    id: PropTypes.string,
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    status: PropTypes.string,
    responses: PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string.isRequired,
        value: PropTypes.string.isRequired,
      })
    ).isRequired,
    createdAt: PropTypes.string,
  }).isRequired,
};

export default SubmittedIdeaCard;
