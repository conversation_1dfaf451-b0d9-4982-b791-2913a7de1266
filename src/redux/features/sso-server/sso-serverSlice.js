import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  authorizationCode: null,
  isAuthorized: false,
  error: null,
  loading: false
};

const ssoServerSlice = createSlice({
  name: 'ssoServer',
  initialState,
  reducers: {
    setAuthorizationCode: (state, action) => {
      state.authorizationCode = action.payload;
      state.isAuthorized = !!action.payload;
    },
    clearAuthorizationCode: (state) => {
      state.authorizationCode = null;
      state.isAuthorized = false;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    }
  }
});

export const {
  setAuthorizationCode,
  clearAuthorizationCode,
  setError,
  setLoading
} = ssoServerSlice.actions;

// Selectors
export const selectAuthorizationCode = (state) => state.ssoServer.authorizationCode;
export const selectIsAuthorized = (state) => state.ssoServer.isAuthorized;
export const selectError = (state) => state.ssoServer.error;
export const selectLoading = (state) => state.ssoServer.loading;

export default ssoServerSlice.reducer;