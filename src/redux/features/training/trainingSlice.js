import { createSlice } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

const initialState = {
  selectedLevel: null, // Başlangıçta null olsun, kullanıcının gerçek seviyesi sonradan ayarlanacak
  trainingProgress: {
    isExpanded: false, // Varsayılan olarak kapalı
  },
  showTrainingProgress: true, // TrainingProgress bileşeninin gösterilip gösterilmeyeceği
  globalTrainingProgress: {
    progress: 0, // İlerleme yüzdesi
    totalSteps: 0, // Toplam adım sayısı
    completedSteps: 0, // Tamamlanan adım sayısı
    currentStepTitle: '', // Mevcut adım başlığı
    nextStepUrl: '/training/next-step', // Bir sonraki adımın URL'i
    buttonType: '', // Buton türü (URL, Content, MODAL, Ideation)
    modalContent: null, // Modal içeriği (buttonType Content veya MODAL ise)
    ideationFormId: null, // Ideation form ID'si (buttonType Ideation ise)
  },
};

export const trainingSlice = createSlice({
  name: 'training',
  initialState,
  reducers: {
    setSelectedLevel: (state, action) => {
      state.selectedLevel = action.payload;
    },
    setTrainingProgressExpanded: (state, action) => {
      state.trainingProgress.isExpanded = action.payload;
    },
    setShowTrainingProgress: (state, action) => {
      state.showTrainingProgress = action.payload;
    },
    updateGlobalTrainingProgress: (state, action) => {
      state.globalTrainingProgress = {
        ...state.globalTrainingProgress,
        ...action.payload,
      };
    },
    // Sayfa değişiminde TrainingProgress görünürlüğünü ayarla
    updateTrainingProgressVisibility: (state) => {
      // Her sayfada TrainingProgress'i göster
      state.showTrainingProgress = true;

      /* Eski gizleme koşullarını kaldırıyoruz
      const { pathname } = action.payload;

      // TrainingProgress'i gizlemek istediğimiz sayfalar
      const hideOnPages = ['/', '/train'];

      // Tam eşleşme kontrolü yap
      if (hideOnPages.includes(pathname)) {
        state.showTrainingProgress = false;
        return;
      }

      // Başlangıç kontrolü yap (alt sayfalar için)
      for (const page of hideOnPages) {
        if (pathname.startsWith(page + '/')) {
          state.showTrainingProgress = false;
          return;
        }
      }

      // Diğer tüm sayfalarda göster
      state.showTrainingProgress = true;
      */
    },
  },
});

// Redux persist config
const persistConfig = {
  key: 'training',
  storage,
  whitelist: [
    'trainingProgress',
    'selectedLevel',
    'showTrainingProgress',
    'globalTrainingProgress',
  ], // Hangi state'lerin persist edileceği
};

export const {
  setSelectedLevel,
  setTrainingProgressExpanded,
  setShowTrainingProgress,
  updateGlobalTrainingProgress,
  updateTrainingProgressVisibility,
} = trainingSlice.actions;
export const selectSelectedLevel = (state) => state.training.selectedLevel;
export const selectTrainingProgressExpanded = (state) => state.training.trainingProgress.isExpanded;
export const selectShowTrainingProgress = (state) => state.training.showTrainingProgress;
export const selectGlobalTrainingProgress = (state) => state.training.globalTrainingProgress;

// Persist reducer ile export
export default persistReducer(persistConfig, trainingSlice.reducer);
