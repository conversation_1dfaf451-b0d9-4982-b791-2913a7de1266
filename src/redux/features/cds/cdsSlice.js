import { createSlice } from '@reduxjs/toolkit';
import { cdsApi } from '../../services/cds-api';

const initialState = {
  platformLanguages: [],
  functionLabels: [],
  jobRoles: [],
  managementRoles: [],
  technicalBackground: [],
  aiExperience: [],
  industry: [],
  terms: null,
  courses: [],
  levels: [],
  loading: false,
  error: null,
};

const cdsSlice = createSlice({
  name: 'onboarding',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Platform Languages
      .addMatcher(cdsApi.endpoints.platformLanguages.matchPending, (state) => {
        state.loading = true;
      })
      .addMatcher(cdsApi.endpoints.platformLanguages.matchFulfilled, (state, { payload }) => {
        state.loading = false;
        state.platformLanguages = payload;
      })

      // Function Labels
      .addMatcher(cdsApi.endpoints.functionLabels.matchFulfilled, (state, { payload }) => {
        state.functionLabels = payload;
      })

      // Job Roles
      .addMatcher(cdsApi.endpoints.jobRoles.matchFulfilled, (state, { payload }) => {
        state.jobRoles = payload;
      })

      // Management Roles
      .addMatcher(cdsApi.endpoints.managementRoles.matchFulfilled, (state, { payload }) => {
        state.managementRoles = payload;
      })

      // Technical Background
      .addMatcher(cdsApi.endpoints.technicalBackground.matchFulfilled, (state, { payload }) => {
        state.technicalBackground = payload;
      })

      // AI Experience
      .addMatcher(cdsApi.endpoints.aiExperience.matchFulfilled, (state, { payload }) => {
        state.aiExperience = payload;
      })

      // Industry
      .addMatcher(cdsApi.endpoints.industry.matchFulfilled, (state, { payload }) => {
        state.industry = payload;
      })

      // Terms and Conditions
      .addMatcher(cdsApi.endpoints.terms.matchFulfilled, (state, { payload }) => {
        state.terms = payload;
      })

      // Courses
      .addMatcher(cdsApi.endpoints.courses.matchFulfilled, (state, { payload }) => {
        state.courses = payload;
      })

      // Levels
      .addMatcher(cdsApi.endpoints.levels.matchFulfilled, (state, { payload }) => {
        state.levels = payload;
      })

      // For error states
      .addMatcher(
        (action) => action.type.endsWith('/rejected'),
        (state, action) => {
          state.loading = false;

          // Stream yanıtlarını kontrol et
          if (
            action.payload?.data &&
            typeof action.payload.data === 'string' &&
            action.payload.data.includes('data: {')
          ) {
            // Stream yanıtı olduğu için bu bir hata değil
            state.error = null;
            return;
          }

          // Custom error messages based on HTTP status codes
          if (action.payload?.status === 404) {
            state.error = 'İstenen kaynak bulunamadı';
          } else if (action.payload?.status === 401) {
            state.error = 'Oturumunuz sonlanmış olabilir, lütfen tekrar giriş yapın';
          } else if (action.payload?.status === 403) {
            state.error = 'Bu işlem için yetkiniz bulunmuyor';
          } else if (action.payload?.status === 500) {
            state.error = 'Sunucu hatası oluştu, lütfen daha sonra tekrar deneyin';
          } else {
            state.error =
              action.error?.message ||
              action.payload?.data?.message ||
              'Bilinmeyen bir hata oluştu';
          }

          if (process.env.NODE_ENV === 'development') {
          }
        }
      );
  },
});

// Selector'lar
export const selectPlatformLanguages = (state) => state.onboarding.platformLanguages;
export const selectFunctionLabels = (state) => state.onboarding.functionLabels;
export const selectJobRoles = (state) => state.onboarding.jobRoles;
export const selectLevels = (state) => state.onboarding.levels;
export const selectManagementRoles = (state) => state.onboarding.managementRoles;
export const selectTechnicalBackground = (state) => state.onboarding.technicalBackground;
export const selectAiExperience = (state) => state.onboarding.aiExperience;
export const selectIndustry = (state) => state.onboarding.industry;
export const selectTerms = (state) => state.onboarding.terms;
export const selectCourses = (state) => state.onboarding.courses;
export const selectLoading = (state) => state.onboarding.loading;
export const selectError = (state) => state.onboarding.error;

export default cdsSlice.reducer;
