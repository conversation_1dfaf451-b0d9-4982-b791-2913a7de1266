import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  generalSettings: null,
  userSettings: null,
  securitySettings: null,
  userSegmentation: null,
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setGeneralSettings: (state, action) => {
      state.generalSettings = action.payload;
    },
    setUserSettings: (state, action) => {
      state.userSettings = action.payload;
    },
    setSecuritySettings: (state, action) => {
      state.securitySettings = action.payload;
    },
    setUserSegmentation: (state, action) => {
      state.userSegmentation = action.payload;
    },
  },
});

export const { setGeneralSettings, setUserSettings, setSecuritySettings, setUserSegmentation } = settingsSlice.actions;
export const selectGeneralSettings = (state) => state.settings.generalSettings;
export const selectUserSettings = (state) => state.settings.userSettings;
export const selectSecuritySettings = (state) => state.settings.securitySettings;
export const selectUserSegmentation = (state) => state.settings.userSegmentation;

export default settingsSlice.reducer; 