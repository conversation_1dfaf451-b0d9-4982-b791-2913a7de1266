import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  toolkits: JSON.parse(localStorage.getItem('toolkits')) || null,
};

const ToolkitSlicer = createSlice({
  name: 'toolkit',
  initialState,
  reducers: {
    setToolkits: (state, { payload: { toolkits } }) => {
      state.toolkits = toolkits;
      localStorage.setItem('toolkits', JSON.stringify(toolkits));
    },
    clearToolkits: (state) => {
      state.toolkits = null;
      localStorage.removeItem('toolkits');
    },
  },
});

export const { setToolkits, clearToolkits } = ToolkitSlicer.actions;
export default ToolkitSlicer.reducer;

// Selectors
export const selectCurrentToolkits = (state) => state.toolkit.toolkits;
