import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_URL } from '../../config-global';

// Journey tracking için ayrı bir API tanımlıyoruz

export const appTrackingApi = createApi({
  reducerPath: 'appTrackingApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  endpoints: (builder) => ({
    updateAppTracking: builder.mutation({
      query: (params) => ({
        url: `tracking/app`,
        method: 'PATCH',
        body: {
          userId: params.userId,
          appId: params.appId,
          appType: params.appType,
          year: params.year,
          month: params.month,
          day: params.day,
        },
      }),
    }),
    getAppTrackingDataByUserId: builder.query({
      query: (params) => ({
        url: `tracking/app`,
        method: 'GET',
        params: {
          month: params?.month,
          year: params?.year,
          day: params?.day,
          userId: params?.userId,
        },
      }),
    }),
    getMostFrequentlyUsedApps: builder.query({
      query: (userId) => ({
        url: `tracking/app/most-used/${userId}`,
        method: 'GET',
      }), 
    }),
  }),
});

export const { useGetAppTrackingDataByUserIdQuery, useUpdateAppTrackingMutation, useGetMostFrequentlyUsedAppsQuery } = appTrackingApi;
