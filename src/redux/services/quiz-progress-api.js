import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_URL } from '../../config-global';

export const quizProgressApi = createApi({
  reducerPath: 'quizProgressApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      headers.set('Content-Type', 'application/json');
      headers.set('Accept', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['QuizProgress'],
  endpoints: (builder) => ({
    getQuizProgress: builder.query({
      async queryFn(arg, queryApi, extraOptions, baseQuery) {
        const { courseId, chapterId, topicId } = arg;
        try {
          const result = await baseQuery({
            url: `/quiz-progress/courses/${courseId}/chapters/${chapterId}/topics/${topicId}/quiz-progress`,
            method: 'GET',
          });

          // 404 durumunda başlangıç durumunu dön
          if (result.error?.status === 404) {
            return {
              data: {
                completed: false,
                score: 0,
                answers: {},
                attemptCount: 0,
                lastAttemptDate: null,
              },
            };
          }

          // Diğer hatalar için error döndür
          if (result.error) {
            console.error('Quiz Progress Error:', result.error);
            return { error: result.error };
          }

          return { data: result.data };
        } catch (error) {
          console.error('Quiz Progress Exception:', error);
          return { error: { status: 500, data: error.message } };
        }
      },
      providesTags: ['QuizProgress'],
    }),

    saveQuizProgress: builder.mutation({
      async queryFn(arg, queryApi, extraOptions, baseQuery) {
        const { courseId, chapterId, topicId, ...data } = arg;
        try {
          const result = await baseQuery({
            url: `/quiz-progress/courses/${courseId}/chapters/${chapterId}/topics/${topicId}/quiz-progress`,
            method: 'POST',
            body: data,
          });

          if (result.error) {
            console.error('Save Quiz Progress Error:', result.error);
            return {
              error: {
                status: result.error.status,
                message: 'Quiz ilerlemesi kaydedilemedi. Lütfen tekrar deneyin.',
              },
            };
          }

          return { data: result.data };
        } catch (error) {
          console.error('Save Quiz Progress Exception:', error);
          return { error: { status: 500, data: error.message } };
        }
      },
      invalidatesTags: ['QuizProgress'],
    }),

    getQuizStats: builder.query({
      async queryFn(courseId, queryApi, extraOptions, baseQuery) {
        try {
          const result = await baseQuery({
            url: `/quiz-progress/courses/${courseId}/quiz-stats`,
            method: 'GET',
          });

          if (result.error) {
            console.error('Quiz Stats Error:', result.error);
            return {
              data: {
                averageScore: 0,
                totalAttempts: 0,
                completedQuizzes: 0,
              },
            };
          }

          return { data: result.data };
        } catch (error) {
          console.error('Quiz Stats Exception:', error);
          return { error: { status: 500, data: error.message } };
        }
      },
      providesTags: ['QuizProgress'],
    }),
  }),
});

export const { useGetQuizProgressQuery, useSaveQuizProgressMutation, useGetQuizStatsQuery } =
  quizProgressApi;
