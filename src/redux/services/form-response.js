import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_URL } from '../../config-global';

// Form yanıtları için API servisi
export const formResponseApi = createApi({
  reducerPath: 'formResponseApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['FormResponses'],
  endpoints: (builder) => ({
    // Form yanıtlarını gönder
    submitFormResponse: builder.mutation({
      query: (formData) => ({
        url: '/form-responses',
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: ['FormResponses'],
      transformResponse: (response) => {
        // <PERSON><PERSON>tı dönüştür
        if (response) {
          return {
            status: 'success',
            data: response.data || response,
            message: 'Form başarıyla gönderildi',
          };
        }
        return response;
      },
      transformErrorResponse: (response) => {
        // Hata mesajını yapılandır
        return {
          status: 'error',
          message: response.data?.message || response.error || 'Form gönderilirken bir hata oluştu',
          error: response.data || response,
        };
      },
    }),
 
    getFormList: builder.query({
      query: (params) => {
        const { submittedBy, formType } = params;
        return `/form-responses/?submittedBy=${submittedBy}&formType=${formType}`;
      },
      providesTags: ['FormResponses'],
      refetchOnMountOrArgChange: true,
      refetchOnFocus: true,
      refetchOnReconnect: true,
      transformResponse: (response) => {
        if (response?.data) {
          return response.data;
        }
        return response;
      },
    }),
  }),
});

// Hook'ları export et
export const { useSubmitFormResponseMutation, useGetFormListQuery } = formResponseApi;
