import { api } from './api';

export const authApi = api.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation({
      query: (credentials) => ({
        url: '/auth/sign-in',
        method: 'POST',
        body: credentials,
      }),
    }),
    forgotPassword: builder.mutation({
      query: (email) => ({
        url: '/auth/forgotPassword',
        method: 'POST',
        body: { email },
      }),
    }),
    resetPassword: builder.mutation({
      query: (data) => ({
        url: '/auth/resetPassword',
        method: 'POST',
        body: data,
      }),
    }),
    definePassword: builder.mutation({
      query: ({ password, email }) => ({
        url: '/auth/definePassword',
        method: 'POST',
        body: { newPassword: password, email },
      }),
    }),
    changePassword: builder.mutation({
      query: (credentials) => ({
        url: '/auth/changePassword',
        method: 'POST',
        body: credentials,
      }),
    }),
  }),
});

export const {
  useLoginMutation,
  useDefinePasswordMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useChangePasswordMutation,
} = authApi;
