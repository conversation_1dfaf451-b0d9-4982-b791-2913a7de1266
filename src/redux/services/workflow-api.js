import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { CDS_API_URL, CDS_API_KEY } from '../../config-global';

export const workflowApi = createApi({
  reducerPath: 'workflowApi',
  baseQuery: fetchBaseQuery({
    baseUrl: CDS_API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('x-api-key', CDS_API_KEY);
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getWorkflows: builder.query({
      query: (params) => {
        const queryParams = {};
        if (params?.limit) {
          queryParams.limit = params.limit;
        }
        if (params?.function_label) {
          queryParams.function_label =
            typeof params.function_label === 'object'
              ? params.function_label.slug
              : params.function_label;
        }
        return {
          url: 'workflow',
          method: 'GET',
          params: queryParams,
        };
      },
      transformResponse: (response) => {
        if (!response || !response.data) {
          return [];
        }
        return response.data.workflows;
      },
    }),
  }),
});

export const { useGetWorkflowsQuery } = workflowApi;
