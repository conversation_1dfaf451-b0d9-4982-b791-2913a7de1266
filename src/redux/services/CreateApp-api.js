import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_URL } from '../../config-global';

export const createAppApi = createApi({
  reducerPath: 'createAppApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('Content-Type', 'application/json');
      return headers;
    },
  }),
  endpoints: (builder) => ({
    createSimpleApp: builder.mutation({
      query: (simpleAppData) => ({
        url: '/app-creator/',
        method: 'POST',
        body: JSON.stringify(simpleAppData),
        headers: {
          'Content-Type': 'application/json',
        },
      }),
      transformResponse: (response) => {
        return response;
      },
      transformErrorResponse: (response) => {
        return {
          status: response.status,
          message: response.data?.message || 'Uygulama oluşturulurken bir hata olu<PERSON>'
        };
      },
    }),
    updateSimpleApp: builder.mutation({
      query: ({ id, simpleAppData }) => ({
        url: `/app-creator/${id}`,
        method: 'PATCH',
        body: JSON.stringify(simpleAppData),
        headers: {
          'Content-Type': 'application/json',
        },
      }),
      transformResponse: (response) => {
        return response;
      },
      transformErrorResponse: (response) => {
        return {
          status: response.status,
          message: response.data?.message || 'Uygulama güncellenirken bir hata oluştu'
        };
      },
    }),
    deleteSimpleApp: builder.mutation({
      query: (id) => ({
        url: `/app-creator/${id}`,
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        return response;
      },
      transformErrorResponse: (response) => {
        return {
          status: response.status,
          message: response.data?.message || 'Uygulama silinirken bir hata oluştu'
        };
      },
    }),
    getSimpleAppBySlug: builder.query({
      query: (slug) => ({
        url: `/app-creator/slug/${slug}`,
        method: 'GET',
      }),
      transformResponse: (response) => {
        return response;
      },
    }),
    getSimpleAppsByUserId: builder.query({
      query: (userId) => ({
        url: `/app-creator/?userId=${userId}`,
        method: 'GET',
      }),
      transformResponse: (response) => {
        return response;
      },
    }),
  }),
});

export const { 
  useCreateSimpleAppMutation, 
  useGetSimpleAppsQuery, 
  useGetSimpleAppBySlugQuery,
  useUpdateSimpleAppMutation,
  useDeleteSimpleAppMutation,
  useGetSimpleAppsByUserIdQuery
} = createAppApi; 