import { api } from './api';

export const ssoServerApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getAuthorizationCode: builder.query({
      query: ({ token, params }) => ({
        url: '/sso-server/authorize',
        method: 'GET',
        params,
        headers: {
          Authorization: `Bearer ${token}`
        }
      }),
    }),
    getAuthData: builder.query({
      query: ({ token }) => ({
        url: '/sso-server/auth-data',
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`
        }
      }),
    }),
    checkGenAICard: builder.query({
      query: ({ userId }) => ({
        url: `/sso-server/user-stacks/${userId}`,
        method: 'GET',
      }),
    }),
  }),
});

export const {
  useGetAuthorizationCodeQuery,
  useCheckGenAICardQuery,
  useGetAuthDataQuery,
} = ssoServerApi;
