import { api } from './api';

// Constants
const CACHE_TIME = 600; // 10 minutes
const PREFETCH_TIME = 60; // 1 minute

export const shortcutsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get favorites
    getUserShortcuts: builder.query({
      query: ({ userId, shortcutType }) => ({
        url: `/shortcuts?shortcutType=${shortcutType}&userId=${userId}`,
        method: 'GET',
      }),
      // Optimize edilmiş transformResponse
      transformResponse: (response) => {
        // Yanıt kontrolleri
        if (!response) {
          return [];
        }

        // API, data içinde array döndürüyor
        if (response.data && Array.isArray(response.data)) {
          return response.data;
        }

        // Diğer durumlarda response'un kendisi bir dizi olabilir
        if (Array.isArray(response)) {
          return response;
        }

        // Hiçbir geçerli veri yapısı bulunamadı
        return [];
      },
      // Important: Optimize cache by tagging each record separately
      providesTags: (result = []) => [
        'Shortcuts',
        ...result.map(({ _id, shortcutID }) => ({ type: 'Shortcuts', id: shortcutID || _id })),
      ],
      // Increase cache duration - data stays in cache longer
      keepUnusedDataFor: CACHE_TIME,
      // Query preloading behavior - may come from api module
      serializeQueryArgs: ({ endpointName, queryArgs }) => {
        // Serialize query arguments uniquely
        return `${endpointName}-${queryArgs.userId}-${queryArgs.shortcutType}`;
      },
    }),

    // Add favorite
    addShortcut: builder.mutation({
      query: ({ userId, shortcutType, shortcutID }) => ({
        url: '/shortcuts',
        method: 'PUT',
        body: { userId, shortcutType, shortcutID },
      }),
      // Faster API response transformation
      transformResponse: (response) => {
        if (!response) return {};
        return response.data || response;
      },
      // For optimistic cache updates
      async onQueryStarted(
        { userId, shortcutType, shortcutID },
        { dispatch, queryFulfilled, getState, extra }
      ) {
        // Make optimistic cache update
        const patchResult = dispatch(
          shortcutsApi.util.updateQueryData(
            'getUserShortcuts',
            { userId, shortcutType },
            (draft) => {
              // If record already exists, don't add it
              const exists = draft.some((item) => item.shortcutID === shortcutID);
              if (!exists) {
                // Add new record optimistically
                draft.push({
                  _id: `temp-${Date.now()}`, // Temporary ID
                  userId,
                  shortcutType,
                  shortcutID,
                });
              }
            }
          )
        );

        try {
          // Wait for API result
          const { data } = await queryFulfilled;

          // If API was successful, update cached value with real ID
          if (data && data.data && data.data._id) {
            // API yanıtı data.data içinde bulunuyor
            dispatch(
              shortcutsApi.util.updateQueryData(
                'getUserShortcuts',
                { userId, shortcutType },
                (draft) => {
                  const index = draft.findIndex((item) => item.shortcutID === shortcutID);
                  if (index !== -1) {
                    draft[index]._id = data.data._id;
                  }
                }
              )
            );
          }
        } catch (error) {
          // Roll back optimistic update in case of error
          patchResult.undo();
          console.error('Error adding favorite:', error);
        }
      },
      // Invalidate all shortcuts to ensure all components are updated
      invalidatesTags: (result, error, { shortcutID }) => [
        'Shortcuts',
        { type: 'Shortcuts', id: shortcutID },
      ],
    }),

    // Remove favorite
    removeShortcut: builder.mutation({
      query: ({ userId, _id }) => ({
        url: `/shortcuts/${_id}/${userId}`,
        method: 'DELETE',
      }),
      // Faster API response transformation
      transformResponse: (response) => {
        if (!response) return {};
        return response.data || response;
      },
      // For optimistic deletion
      async onQueryStarted(
        { userId, _id, shortcutID, shortcutType = 'usecase' },
        { dispatch, queryFulfilled, getState }
      ) {
        // Optimistic cache update - update UI immediately
        const patchResult = dispatch(
          shortcutsApi.util.updateQueryData(
            'getUserShortcuts',
            { userId, shortcutType }, // shortcutType'ı parametre olarak kullan
            (draft) => {
              // Remove related record from cache
              const index = draft.findIndex(
                (item) => (shortcutID && item.shortcutID === shortcutID) || item._id === _id
              );
              if (index !== -1) {
                draft.splice(index, 1);
              }
            }
          )
        );

        try {
          // Wait for API result
          await queryFulfilled;
          // Nothing to do on success
        } catch (error) {
          // Roll back optimistic update in case of error
          patchResult.undo();
          console.error('Error removing favorite:', error);
        }
      },
      // Invalidate all shortcuts to ensure all components are updated
      invalidatesTags: (result, error, { shortcutID, _id }) => [
        'Shortcuts',
        { type: 'Shortcuts', id: shortcutID || _id },
      ],
    }),
  }),
});

// Prefetching functions
export const prefetchUserShortcuts = (userId, shortcutType = 'usecase') => {
  return shortcutsApi.util.prefetch(
    'getUserShortcuts',
    { userId, shortcutType },
    { force: false, ifOlderThan: PREFETCH_TIME }
  );
};

export const { useGetUserShortcutsQuery, useAddShortcutMutation, useRemoveShortcutMutation } =
  shortcutsApi;
