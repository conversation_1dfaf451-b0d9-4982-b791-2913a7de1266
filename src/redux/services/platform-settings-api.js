import { api } from './api';

export const platformSettingsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Public Settings
    getPublicSettings: builder.query({
      query: () => ({
        url: '/settings/public',
        method: 'GET',
      }),
    }),
    // General Settings Routes
    getGeneralSettings: builder.query({
      query: () => ({
        url: '/settings/general',
        method: 'GET',
      }),
    }),
    updateGeneralSettings: builder.mutation({
      query: (settings) => ({
        url: '/settings/general',
        method: 'PUT',
        body: settings,
      }),
    }),

    // User Settings Routes
    getUserSettings: builder.query({
      query: () => ({
        url: '/settings/user',
        method: 'GET',
      }),
    }),
    updateUserSettings: builder.mutation({
      query: (settings) => ({
        url: '/settings/user',
        method: 'PUT',
        body: settings,
      }),
    }),

    // Security Settings Routes
    getSecuritySettings: builder.query({
      query: () => ({
        url: '/settings/security',
        method: 'GET',
      }),
    }),
    updateSecuritySettings: builder.mutation({
      query: (settings) => ({
        url: '/settings/security',
        method: 'PUT',
        body: settings,
      }),
    }),

    // Segment Management Routes
    getUserSegmentation: builder.query({
      query: () => ({
        url: '/settings/user-segmentation',
        method: 'GET',
      }),
    }),
    createSegment: builder.mutation({
      query: (segment) => ({
        url: '/settings/user-segmentation/segments',
        method: 'POST',
        body: segment,
      }),
    }),
    getSegmentById: builder.query({
      query: (segmentId) => ({
        url: `/settings/user-segmentation/segments/${segmentId}`,
        method: 'GET',
      }),
    }),
    updateSegment: builder.mutation({
      query: ({ segmentId, segment }) => ({
        url: `/settings/user-segmentation/segments/${segmentId}`,
        method: 'PUT',
        body: segment,
      }),
    }),
    deleteSegment: builder.mutation({
      query: (segmentId) => ({
        url: `/settings/user-segmentation/segments/${segmentId}`,
        method: 'DELETE',
      }),
    }),
  }),
});

export const {
  useGetPublicSettingsQuery,
  useGetGeneralSettingsQuery,
  useUpdateGeneralSettingsMutation,
  useGetUserSettingsQuery,
  useUpdateUserSettingsMutation,
  useGetSecuritySettingsQuery,
  useUpdateSecuritySettingsMutation,
  useGetUserSegmentationQuery,
  useCreateSegmentMutation,
  useGetSegmentByIdQuery,
  useUpdateSegmentMutation,
  useDeleteSegmentMutation,
} = platformSettingsApi;
