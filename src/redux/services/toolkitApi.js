import { api } from './api';

export const toolkitApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getToolkits: builder.query({
      query: () => '/toolkit',
      keepUnusedDataFor: 60,
      providesTags: ['Toolkits'],
      transformResponse: (response) => {
        return response?.data?.[0]?.toolkitItems || [];
      },
    }),
  }),
});

export const { useGetToolkitsQuery } = toolkitApi;
