import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { CDS_API_URL, CDS_API_KEY } from '../../config-global';

// Constants
const CACHE_TIME = 600; // 10 minutes
const PREFETCH_TIME = 60; // 1 minute

export const formApi = createApi({
  reducerPath: 'formApi',
  baseQuery: fetchBaseQuery({
    baseUrl: CDS_API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('x-api-key', CDS_API_KEY);
      return headers;
    },
  }),
  endpoints: (builder) => ({
    forms: builder.query({
      query: (params) => ({
        url: params.formId ? `/forms/list/${params.formId}` : '/forms/list',
        method: 'GET',
        params: { lang: params.language },
      }),
      transformResponse: (response) => {
        if (response?.data) {
          return response.data;
        }
        return null;
      },
      keepUnusedDataFor: CACHE_TIME,
    }),

    // Tek bir formu ID ve dil parametreleriyle çekmek için yeni endpoint
    formById: builder.query({
      query: (params) => ({
        url: `/forms/list/${params.formId}`,
        method: 'GET',
        params: { lang: params.lang || 'en' },
      }),
      transformResponse: (response) => {
        // Yanıt kontrolü
        if (response?.status === 'success') {
          return response;
        }
        return null;
      },
      keepUnusedDataFor: CACHE_TIME,
    }),

    // Form yanıtlarını göndermek için yeni mutation ekle
    submitFormResponses: builder.mutation({
      query: (formData) => ({
        url: '/form-responses',
        method: 'POST',
        body: formData,
      }),
      transformResponse: (response) => {
        if (response?.data) {
          return response.data;
        }
        return response;
      },
    }),
  }),
});

// Prefetching functions
export const prefetchForm = (formId) => {
  return formApi.util.prefetch('forms', formId, { force: false, ifOlderThan: PREFETCH_TIME });
};

export const { useFormsQuery, useFormByIdQuery, useSubmitFormResponsesMutation } = formApi;
