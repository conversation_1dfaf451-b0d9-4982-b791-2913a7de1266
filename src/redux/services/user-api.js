import { api } from './api';

export const userApi = api.injectEndpoints({
  endpoints: (builder) => ({
    updateUserOnboarding: builder.mutation({
      query: ({ userId, onboarding }) => ({
        url: `/users/onboarding/${userId}`,
        method: 'PATCH',
        body: { onboarding },
      }),
    }),
    updateUser: builder.mutation({
      query: ({ userId, name, surname }) => ({
        url: `/users/update-account/${userId}`,
        method: 'PATCH',
        body: { name, surname },
      }),
    }),
    updateUserJourneyLevel: builder.mutation({
      query: ({ userId, journeyLevel }) => ({
        url: `/users/journey-level/${userId}`,
        method: 'PATCH',
        body: { journeyLevel },
      }),
      invalidatesTags: ['User'],
    }),
    deleteOnboarding: builder.mutation({
      query: ({ userId }) => ({
        url: `/users/onboarding/${userId}`,
        method: 'DELETE',
      }),
    }),
    updateOnboardingIndustry: builder.mutation({
      query: ({ userId, industry }) => ({
        url: `/users/onboarding/${userId}/industry`,
        method: 'PATCH',
        body: { industry },
      }),
    }),
    getUserJourneyLevel: builder.query({
      query: (userId) => ({
        url: `/users/journey-level/${userId}`,
        method: 'GET',
      }),
      transformResponse: (response) => {
        if (!response || !response.data) {
          return null;
        }
        return response.data;
      },
    }),
  }),
});

export const {
  useUpdateUserOnboardingMutation,
  useUpdateUserMutation,
  useDeleteOnboardingMutation,
  useUpdateOnboardingIndustryMutation,
  useGetUserJourneyLevelQuery,
  useUpdateUserJourneyLevelMutation,
} = userApi;
