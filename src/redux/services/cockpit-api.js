import { api } from './api';

// Constants
const CACHE_TIME = 600; // 10 minutes
const PREFETCH_TIME = 60; // 1 minute

export const cockpitApi = api.injectEndpoints({ 
  endpoints: (builder) => ({
    // Get favorites
    getStats: builder.query({
      query: ({ userId }) => ({
        url: `/cockpit/stats/${userId}`,
        method: 'GET',
      }),
      // Optimize edilmiş transformResponse
      transformResponse: (response) => {
        
        return response.data;
      }, 
      keepUnusedDataFor: CACHE_TIME, 
    }), 
  }),
});

// Prefetching functions
export const prefetchCockpitStats = (userId) => {
  return cockpitApi.util.prefetch(
    'getStats',
    { userId },
    { force: false, ifOlderThan: PREFETCH_TIME }
  );
};

export const { useGetStatsQuery } = cockpitApi;
