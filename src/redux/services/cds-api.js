import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { CDS_API_URL, CDS_API_KEY } from '../../config-global';

export const cdsApi = createApi({
  reducerPath: 'cdsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: CDS_API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('x-api-key', CDS_API_KEY);
      return headers;
    },
  }),
  endpoints: (builder) => ({
    platformLanguages: builder.query({
      query: () => ({
        url: '/platform-lang/get',
        method: 'GET',
      }),
    }),
    functionLabels: builder.query({
      query: () => ({
        url: '/onboarding/get-functions',
        method: 'GET',
      }),
    }),
    jobRoles: builder.query({
      query: (functionLabel) => ({
        url: 'onboarding/get-jobRoles',
        method: 'GET',
        ...(functionLabel !== 'other' && {
          params: {
            functionLabel: typeof functionLabel === 'object' ? functionLabel.slug : functionLabel,
          },
        }),
      }),
    }),
    managementRoles: builder.query({
      query: (functionLabel) => ({
        url: 'onboarding/get-managementRoles',
        method: 'GET',
        params: {
          functionLabel: typeof functionLabel === 'object' ? functionLabel.slug : functionLabel,
        },
      }),
    }),
    technicalBackground: builder.query({
      query: () => ({
        url: 'onboarding/get-technical-backgrounds',
        method: 'GET',
      }),
    }),
    aiExperience: builder.query({
      query: () => ({
        url: 'onboarding/get-ai-experiences',
        method: 'GET',
      }),
    }),
    industry: builder.query({
      query: () => ({
        url: 'onboarding/get-industries',
        method: 'GET',
      }),
    }),
    terms: builder.query({
      query: () => ({
        url: 'terms-and-conditions/get',
        method: 'GET',
      }),
    }),
    levels: builder.query({
      query: () => ({
        url: 'onboarding/get-levels',
        method: 'GET',
      }),
    }),
    courses: builder.query({
      query: () => ({
        url: 'courses/get',
        method: 'GET',
      }),
    }),
    getCourse: builder.query({
      query: (courseId) => ({
        url: `courses/${courseId}`,
        method: 'GET',
      }),
    }),

    selectedCourses: builder.query({
      query: (courseIds) => ({
        url: 'courses/list',
        method: 'POST',
        body: {
          courseIds: courseIds,
        },
      }),
    }),
  }),
});

export const {
  usePlatformLanguagesQuery,
  useFunctionLabelsQuery,
  useJobRolesQuery,
  useManagementRolesQuery,
  useTechnicalBackgroundQuery,
  useAiExperienceQuery,
  useIndustryQuery,
  useTermsQuery,
  useLevelsQuery,
  useCoursesQuery,
  useSelectedCoursesQuery,
  useGetCourseQuery,
} = cdsApi;
