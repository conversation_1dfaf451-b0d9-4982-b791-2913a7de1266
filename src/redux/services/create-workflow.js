import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_URL, CDS_API_URL, CDS_API_KEY } from '../../config-global';

// Dinamik baseFetchQuery tanımlıyoruz
const getBaseQuery = (url) => {
  return fetchBaseQuery({
    baseUrl: url,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('x-api-key', CDS_API_KEY);
      return headers;
    },
  });
};

export const createWorkflowApi = createApi({
  reducerPath: 'createWorkflowApi',
  baseQuery: getBaseQuery(API_URL), // Varsayılan olarak API_URL kullanıyoruz
  endpoints: (builder) => ({
    // Primary API endpoint - API_URL kullanır
    getWorkflowById: builder.query({
      query: (params) => {
        // Basit string veya obje parametresi kabul edelim
        const slug = typeof params === 'object' ? params.slug : params;
        // Ana API için doğru URL'yi oluştur
        return {
          url: `workflow-creator/slug/${slug}`,
          method: 'GET',
        };
      },
      transformResponse: (response) => {
        console.log('Raw API Response:', response);
        if (!response || !response.data) {
          return null;
        }

        return response;
      },
      // API yanıtını değerlendirme ve gerekirse diğer API'ye geçme
      async onQueryStarted(params, { dispatch, queryFulfilled }) {
        try {
          // İlk API isteğinin sonucunu bekle
          const result = await queryFulfilled;

          // Eğer veri yoksa veya userId yoksa CDS_API'ye istek at
          if (!result.data || !result.data.data || !result.data.data.userId) {
            console.log('userId bulunamadı, CDS_API_URL kullanılacak...');

            // Slug'ı al
            const slug = typeof params === 'object' ? params.slug : params;

            // CDS API'ye istek at
            dispatch(createWorkflowApi.endpoints.getWorkflowByCdsApi.initiate(slug));
          }
        } catch (error) {
          console.error('API hatası, CDS_API_URL deneniyor:', error);
          // Hata durumunda CDS API'ye istek at
          const slug = typeof params === 'object' ? params.slug : params;
          dispatch(createWorkflowApi.endpoints.getWorkflowByCdsApi.initiate(slug));
        }
      },
    }),

    // Secondary API endpoint - CDS_API_URL kullanır
    getWorkflowByCdsApi: builder.query({
      queryFn: async (slug, api, extraOptions) => {
        // CDS API için özel baseQuery oluşturuyoruz
        const customBaseQuery = getBaseQuery(CDS_API_URL);

        try {
          // CDS API'ye istek gönderiyoruz
          const result = await customBaseQuery(
            {
              url: `workflow/slug/${slug}`,
              method: 'GET',
            },
            api,
            extraOptions
          );

          console.log('CDS API Response:', result.data);

          if (result.error) {
            throw new Error(result.error?.data?.message || 'CDS API error');
          }

          // Başarılı yanıtı getWorkflowById'nin önbelleğine manuel olarak ekleyelim
          // Böylece UI bunu görebilir
          if (result.data) {
            try {
              // getWorkflowById'nin önbelleğini güncelle
              // Böylece UI'da veri görüntülenecek
              api.dispatch(
                createWorkflowApi.util.upsertQueryData('getWorkflowById', { slug }, result.data)
              );
            } catch (cacheError) {
              console.error('Önbellek güncelleme hatası:', cacheError);
            }
          }

          // Sonucu döndür
          return { data: result.data };
        } catch (error) {
          console.error('CDS API hatası:', error);
          return {
            error: {
              data: {
                message: error.message || 'CDS API isteği başarısız oldu',
              },
            },
          };
        }
      },
    }),

    getWorkflowsByUser: builder.query({
      query: (userId) => ({
        url: `/workflow-creator/`,
        method: 'GET',
        params: {
          userId: userId,
          limit: 30,
          page: 1,
        },
      }),
      transformResponse: (response) => {
        console.log('User Workflows Response:', response);
        if (response?.data && Array.isArray(response.data)) {
          return response.data;
        } else if (response?.data?.workflows && Array.isArray(response.data.workflows)) {
          return response.data.workflows;
        } else if (response?.results && Array.isArray(response.results)) {
          return response.results;
        } else if (Array.isArray(response)) {
          return response;
        }
        return [];
      },
      transformErrorResponse: (response) => {
        console.log('Error Response:', response);
        return {
          status: response.status,
          message: response.data?.message || 'Kullanıcı iş akışları getirilemedi',
        };
      },
    }),

    saveAndRunWorkflow: builder.mutation({
      query: (workflowData) => {
        // If workflowData has an ID, it's an update operation
        if (workflowData.id) {
          return {
            url: `workflow-creator/${workflowData.id}`,
            method: 'PATCH',
            body: workflowData,
          };
        }

        // Otherwise, it's a create operation
        return {
          url: 'workflow-creator',
          method: 'POST',
          body: workflowData,
        };
      },
      transformResponse: (response) => {
        console.log('Raw API Response:', response);
        if (!response) {
          throw new Error('No response received');
        }
        return response;
      },
      transformErrorResponse: (response) => {
        console.log('Error Response:', response);
        return response;
      },
    }),
  }),
});

export const {
  useGetWorkflowByIdQuery,
  useGetWorkflowByCdsApiQuery,
  useSaveAndRunWorkflowMutation,
  useGetWorkflowsByUserQuery,
} = createWorkflowApi;
