import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { setCredentials, logOut } from '../features/auth/authSlice';
import { API_URL } from '../../config-global';

// İstek timeout değeri
const REQUEST_TIMEOUT = 15000; // 15 saniye

const baseQuery = fetchBaseQuery({
  baseUrl: API_URL,
  credentials: 'include',
  timeout: REQUEST_TIMEOUT, // Timeout ekleyerek askıda kalan istekleri sınırlıyoruz
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.token;
    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }
    
    // Cache kontrol başlıkları ekleyerek browser caching'i optimize ediyoruz
    headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    headers.set('Pragma', 'no-cache');
    headers.set('Expires', '0');
    
    return headers;
  },
});

const baseQueryWithReauth = async (args, api, extraOptions) => {
  let result = await baseQuery(args, api, extraOptions);

  // 401 (Unauthorized) veya 403 (Forbidden) hatası durumunda token yenileme dene
  if (
    result.error?.status === 401 ||
    result.error?.status === 403 ||
    result.error?.originalStatus === 401 ||
    result.error?.originalStatus === 403
  ) {
    console.log('Token yenileme deneniyor...');

    try {
      const refreshResult = await baseQuery('/auth/refresh-token', api, extraOptions);

      if (refreshResult.data) {
        console.log('Token yenilendi');
        const user = api.getState().auth.user;
        api.dispatch(setCredentials({ ...refreshResult.data, user }));

        // Yeni token ile isteği tekrar et
        result = await baseQuery(args, api, extraOptions);
      } else {
        console.log('Token yenilenemedi, oturum kapatılıyor...');
        api.dispatch(logOut());
      }
    } catch (error) {
      console.error('Token yenileme hatası:', error);
      api.dispatch(logOut());
    }
  }

  return result;
};

export const api = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  // Cache davranışını global olarak ayarlıyoruz
  keepUnusedDataFor: 120, // 2 dakika (saniye cinsinden)
  refetchOnMountOrArgChange: false, // Bileşen mount olduğunda otomatik sorgu atmayı devre dışı bırakıyoruz
  refetchOnFocus: false, // Sayfa odaklandığında yeniden sorgu atmayı devre dışı bırakıyoruz 
  refetchOnReconnect: false, // Yeniden bağlandığında sorgu atmayı devre dışı bırakıyoruz
  tagTypes: [
    'Shortcuts', 
    'UserShortcuts',
    'Shortcut',
    'User'
  ],
  endpoints: () => ({}),
});
