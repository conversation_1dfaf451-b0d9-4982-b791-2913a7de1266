import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import enTranslation from '../public/locales/en/translation.json';
import deTranslation from '../public/locales/de/translation.json';

// localStorage'dan kayı<PERSON> dili al, yoksa var<PERSON> o<PERSON> 'en' kullan
const savedLanguage = localStorage.getItem('i18nextLng') || 'en';
 
i18n.use(initReactI18next).init({
  resources: {
    en: {
      translation: enTranslation,
    },
    de: {
      translation: deTranslation,
    },
  },
  lng: savedLanguage, // Başlangıç dilini localStorage'dan al
  fallbackLng: 'en',
  supportedLngs: ['en', 'de'],
  debug: false,
  interpolation: {
    escapeValue: false,
  },
  react: {
    useSuspense: true,
  },
  ns: ['translation'],
  defaultNS: 'translation',
  detection: {
    order: ['localStorage', 'navigator'],
    caches: ['localStorage'],
  },
});

// Dil değişikliklerini localStorage'a kaydet
i18n.on('languageChanged', (lng) => {
  localStorage.setItem('i18nextLng', lng);
});

export default i18n;
