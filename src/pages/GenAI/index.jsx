import { Container, CircularProgress, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader';
import { useGetAuthorizationCodeQuery, useGetAuthDataQuery } from '../../redux/services/sso-server.api';
import { skipToken } from '@reduxjs/toolkit/query';
import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  setAuthorizationCode,
  selectAuthorizationCode,
  selectLoading,
} from '../../redux/features/sso-server/sso-serverSlice';
import { useLocation } from 'react-router-dom';
import { useGetJourneyTrackingQuery } from '../../redux/services/journey-api';
import useUpdateJourneyTracking from '../../domains/journey/utils/updateJourneyTracking'; 

const GenAIPage = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const location = useLocation();

  // Journey card id state'ten al
  const journeyCardId = location.state?.cardId;

  // Auth state'den user ID ve token'ı al
  const userId = useSelector((state) => state.auth.user?._id);
  const token = useSelector((state) => state.auth.token);
  const user = useSelector((state) => state.auth.user);

  // Journey tracking güncellemek için hook'u ve verileri al
  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();
  const { data: trackingData } = useGetJourneyTrackingQuery(userId, {
    skip: !userId,
  });

  // Önce auth data'yı al - token varsa
  const { data: authData, isLoading: isAuthDataLoading } = useGetAuthDataQuery(
    token ? { token } : skipToken
  );
  console.log('authData', authData);

  // Auth data yüklendikten sonra authorization code isteği yap
  const { data, isLoading, error } = useGetAuthorizationCodeQuery(
    token && authData?.data ? {
      
      params: {
        response_type: 'code',
        client_id: authData.data.clientId,
        redirect_uri: authData.data.redirectUris[0],
        state: 'genai',
        userId,
      },
      token,
    } : skipToken
  );

  // Global state'den loading ve authorization code'u al
  const globalLoading = useSelector(selectLoading);
  const authCode = useSelector(selectAuthorizationCode);

  useEffect(() => {
    console.log('Data:', data);
    if (data?.data?.code && data?.data?.redirect_uri) {
      console.log('Redirecting with:', {
        code: data.data.code,
        redirect_uri: data.data.redirect_uri,
      });

      // Store'a kaydet
      dispatch(setAuthorizationCode(data.code));

      // Journey tracking güncellemesi yap - eğer cardId varsa
      if (journeyCardId && userId && user?.journeyLevel?.name && trackingData) {
        try {
          // Kullanıcının journey level'ını kullan
          const userLevel = user.journeyLevel.name.toLowerCase();

          // Journey tracking güncellemesi yap
          updateJourneyTrackingCard({
            userId: userId,
            journeyTrackingData: trackingData,
            userLevel: userLevel,
            cardId: journeyCardId,
          })
            .then(() => {
              console.log(
                'Journey tracking updated successfully for GenAI Playground card:',
                journeyCardId
              );

              // Redux cache'i yenile - localStorage'e bir timestamp kaydediyoruz,
              // Footer bileşeni bunu algılayıp refetch yapacak
              localStorage.setItem('journey_tracking_updated', Date.now().toString());

              // Tracking güncellemesi tamamlandıktan sonra yönlendirme yap
              const redirectUrl = new URL(
                'https://genai-stack.aibusinessschool.com/auth/sso/callback'
              );
              redirectUrl.searchParams.set('code', data.data.code);
              redirectUrl.searchParams.set('auth_server', data.data.redirect_uri);
              window.location.href = redirectUrl.toString();
            })
            .catch((trackingError) => {
              console.error('Failed to update journey tracking:', trackingError);
              // Hata durumunda da yönlendirme yap
              const redirectUrl = new URL(
                'https://genai-stack.aibusinessschool.com/auth/sso/callback'
              );
              redirectUrl.searchParams.set('code', data.data.code);
              redirectUrl.searchParams.set('auth_server', data.data.redirect_uri);
              window.location.href = redirectUrl.toString();
            });
        } catch (trackingError) {
          console.error('Failed to update journey tracking:', trackingError);
          // Hata durumunda yönlendirme yap
          const redirectUrl = new URL('https://genai-stack.aibusinessschool.com/auth/sso/callback');
          redirectUrl.searchParams.set('code', data.data.code);
          redirectUrl.searchParams.set('auth_server', data.data.redirect_uri);
          window.location.href = redirectUrl.toString();
        }
      } else {
        // Journey card id yoksa direkt yönlendirme yap
        const redirectUrl = new URL('https://genai-stack.aibusinessschool.com/auth/sso/callback');
        redirectUrl.searchParams.set('code', data.data.code);
        redirectUrl.searchParams.set('auth_server', data.data.redirect_uri);
        window.location.href = redirectUrl.toString();
      }
    }
  }, [data, dispatch, journeyCardId, userId, user, trackingData, updateJourneyTrackingCard]);

  if (!userId || !token) {
    return (
      <Container maxWidth="xl">
        
        <div>Please login to access GenAI features.</div>
      </Container>
    );
  }

  if (isLoading || globalLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl">
       
      {error && (
        <Box mt={2} color="error.main">
          Error: {error.message || 'Failed to get authorization'}
        </Box>
      )}
      {authCode && (
        <Box mt={2} color="success.main">
          Authorization successful! Redirecting...
        </Box>
      )}
    </Container>
  );
};

export default GenAIPage;
