import { Box, Container, Grid, Typography, CircularProgress } from '@mui/material';
import { useSelector } from 'react-redux';
import { Suspense, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader.jsx';
import CourseCard, { CardContent } from '@/components/CourseCard/CourseCard';
import WhiteContainer from '../../components/WhiteContainer/WhiteContainer.jsx';
import { Innovate } from '../../mockData/Innovate.js';
import { checkAndLockCards } from '../../utils/cardUtils';
import './Innovate.scss';

const InnovateContent = () => {
  const user = useSelector((state) => state.auth.user);
  const { t, i18n } = useTranslation();

  // Kullanıcı rolüne göre innovate verilerini filtrele ve kilitle
  const filteredInnovate = useMemo(() => {
    // Önce Innovate verilerini aynı yapıya dönüştür
    const innovateData = Innovate.map((item) => ({
      type: 'InnovateItem',
      props: { ...item },
    }));

    // Utils fonksiyonu ile kilitleme işlemini uygula
    return checkAndLockCards(innovateData, user, t).map((item) => item.props);
  }, [user, t]);

  // Kartlarda çeviri desteği için yardımcı fonksiyon
  const getTranslatedText = (item, field) => {
    if (item.translations && item.translations[field]) {
      return (
        item.translations[field][i18n.language] || item.translations[field]['en'] || item[field]
      );
    }
    return item[field];
  };

  // Tooltip metni için çeviri desteği
  const getTranslatedTooltip = (item) => {
    if (item.locked) {
      if (item.translations && item.translations.tooltipText) {
        return (
          item.translations.tooltipText[i18n.language] ||
          item.translations.tooltipText['en'] ||
          t('common.lockedTooltip')
        );
      }
      return item.tooltipText || t('common.lockedTooltip');
    }
    return undefined;
  };

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('common.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('innovate.title')}
            description={t('innovate.subtitle')}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer title="" subtitle="" showNavigation={false}>
            <Grid container className="innovate-card-wrapper" spacing={2}>
              {filteredInnovate.map((item) => (
                <Grid item xs={12} md={4} key={item.id}>
                  <CourseCard
                    buttonText={getTranslatedText(item, 'buttonText')}
                    buttonType={item.buttonType}
                    buttonVariant="text"
                    imageSrc={item.imageSrc}
                    locked={item.locked}
                    tooltipText={getTranslatedTooltip(item)}
                    onClick={(e) => {
                      // Kilitli kartlara tıklandığında engelle
                      if (item.locked) {
                        e.preventDefault();
                        return;
                      }

                      // Normal işleme devam et
                      if (item.buttonType === 'URL') {
                        if (item.newTab) {
                          window.open(item.buttonURL, '_blank');
                        } else {
                          window.location.href = item.buttonURL;
                        }
                      }
                    }}
                  >
                    <CardContent
                      title={getTranslatedText(item, 'title')}
                      description={getTranslatedText(item, 'description')}
                      buttonURL={item.buttonURL}
                      newTab={item.newTab}
                      buttonText={getTranslatedText(item, 'buttonText')}
                      buttonType={item.buttonType}
                      imageSrc={item.imageSrc}
                      locked={item.locked}
                      tooltipText={getTranslatedTooltip(item)}
                    />
                  </CourseCard>
                </Grid>
              ))}
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

const InnovatePage = () => {
  return (
    <Suspense
      fallback={
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: 'calc(100vh - 64px)', // Header yüksekliğini çıkarıyoruz
          }}
        >
          <CircularProgress />
        </Box>
      }
    >
      <InnovateContent />
    </Suspense>
  );
};

export default InnovatePage;
