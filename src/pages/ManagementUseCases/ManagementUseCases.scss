@use '../../styles/abstracts/variables' as *;

.apply-back-button {
  position: absolute !important;
  top:-29px;
  left:-1px;
  margin-bottom: $spacing-4;
  
  &.MuiButton-root {
    color: $primary-color;
    border:none !important;
    background-color: $bg-paper !important;
    border-top: 1px solid $border-color !important;
    border-left: 1px solid $border-color !important;
    border-right: 1px solid $border-color !important;
    border-radius: 0 !important;
    border-top-left-radius: $border-radius-md !important;
    border-top-right-radius: $border-radius-md !important;
    color: $text-secondary !important;
    text-transform: none;
    font-weight: $font-weight-medium;
    font-size: calc($font-size-xs - 1px) !important;  
    padding: $spacing-1 $spacing-2 !important;
    
    &:hover {
      border-color: $primary-color-dark;
      background-color: $primary-color-light;
    }
  }
}

.app-card-wrapper {
  padding: 0 !important;
}

.tabs-container {
  border-bottom: 1px solid $divider-color;
  margin-bottom: $spacing-4;

  .MuiTabs-root {
    .MuiTab-root {
      text-transform: none;
      min-width: auto;
      padding: $spacing-3 $spacing-4;
      font-size: $font-size-sm;
      cursor:pointer !important;
      font-weight: $font-weight-medium;
      margin-right: $spacing-2;

      &.Mui-selected {
        color: $primary-color;
      }

      &:hover {
        color: $primary-color-light;
      }
    }

    .MuiTabs-indicator {
      background-color: $primary-color;
      height: 2px;
    }
  }
}

.no-results-container {
  padding: $spacing-4;
  
  .no-results-message {
    background-color: rgba(#e3f2fd, 0.7);
    border: 1px solid #bbdefb;
    border-radius: $border-radius-sm;
    color: #0d47a1;
    font-size: $font-size-md;
    padding: $spacing-3;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    
    .message-icon {
      margin-right: $spacing-2;
      font-size: 20px;
    }
  }
} 