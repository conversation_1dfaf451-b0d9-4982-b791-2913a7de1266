import { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Tabs,
  Tab,
  CircularProgress,
  Button,
} from '@mui/material';
import { useSelector } from 'react-redux';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../components/WhiteContainer/WhiteContainer.jsx';
import AppCard from '@/components/AppCard/AppCard';
import './ITUseCases.scss';
import { useGetUseCasesQuery } from '@/redux/services/use-case-api';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import InfoIcon from '@mui/icons-material/Info';
import NotFoundPage from '../../components/404';

const ITUseCasesPage = () => {
  const user = useSelector((state) => state.auth.user);
  const [selectedType, setSelectedType] = useState('all');
  const navigate = useNavigate();
  const { i18n, t } = useTranslation();

  // Kullanıcı erişim kontrolü
  const canAccessPage = () => {
    // Kullanıcı yoksa sayfaya erişilemez
    if (!user || !user.onboarding) return false;

    const userFunctionLabel = user.onboarding.function_label;
    const userTechnicalBackground = user.onboarding.technical_background_label;

    // Function label'ı kontrol et
    const isFunctionLabelDifferent = 
      typeof userFunctionLabel === 'string' 
        ? userFunctionLabel !== 'it-ai-data'
        : userFunctionLabel?.slug !== 'it-ai-data';

    // Technical background'u kontrol et
    const isITSpecialist = 
      typeof userTechnicalBackground === 'string'
        ? userTechnicalBackground === 'it-specialist'
        : userTechnicalBackground?.slug === 'it-specialist';

    // Her iki koşul da sağlanıyorsa erişime izin ver
    return isFunctionLabelDifferent && isITSpecialist;
  };

  // Sayfa erişimi kontrolü - 404 sayfasını göster
  if (!canAccessPage()) {
    return <NotFoundPage />;
  }

  const {
    data: useCases,
    isLoading,
    error,
  } = useGetUseCasesQuery(
    {
      function_label: 'it-ai-data',
    }
  );

  const getPageTitle = () => {
    return t('itUseCases.title', 'IT related use cases');
  };

  const filterTypes = [
    { value: 'all', label: t('useCases.filters.all', 'All use cases') },
    { value: 'text-generation', label: t('useCases.filters.text', 'Text Generation') },
    { value: 'video-generation', label: t('useCases.filters.video', 'Video Generation') },
    { value: 'image-generation', label: t('useCases.filters.image', 'Image Generation') },
  ];

  const handleTypeChange = (event, newValue) => {
    setSelectedType(newValue);
  };

  const filteredUseCases =
    useCases?.filter((useCase) =>
      selectedType === 'all' ? true : useCase.use_case_type?.includes(selectedType)
    ) || [];

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('common.pleaseLogin', 'Please log in.')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={getPageTitle()}
            description={t(
              'itUseCases.description',
              'Discover carefully selected, relevant IT use cases for your organization.'
            )}
            showProgress={false}
            name={user.name}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer showNavigation={false} showMore={false} variant="backButtonTrue">
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/apply')}
              className="apply-back-button"
            >
              {t('common.backToApply', 'Back to Apply')}
            </Button>
            <Box className="tabs-container">
              <Tabs
                value={selectedType}
                onChange={handleTypeChange}
                variant="scrollable"
                scrollButtons="auto"
              >
                {filterTypes.map((type) => (
                  <Tab key={type.value} label={type.label} value={type.value} />
                ))}
              </Tabs>
            </Box>

            <Grid container className="app-card-wrapper" spacing={2}>
              {isLoading ? (
                <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                  <CircularProgress />
                </Grid>
              ) : error ? (
                <Grid item xs={12}>
                  <Typography color="error">
                    {t('common.errorLoading', 'Error loading use cases. Please try again later.')}
                  </Typography>
                </Grid>
              ) : filteredUseCases.length === 0 ? (
                <Grid item xs={12} className="no-results-container">
                  <Typography className="no-results-message">
                    <InfoIcon className="message-icon" />
                    {t('useCases.noResults', 'No use cases found for the selected filter.')}
                  </Typography>
                </Grid>
              ) : (
                filteredUseCases.map((useCase) => {
                  let title = useCase.title;
                  let languageKey = 'english';

                  if (
                    i18n.language === 'de' ||
                    i18n.language === 'german' ||
                    i18n.language === 'deutsch'
                  ) {
                    languageKey = 'german';
                  }

                  if (useCase.translations && useCase.translations[languageKey]) {
                    title = useCase.translations[languageKey].title;
                  } else if (useCase.translations && useCase.translations.english) {
                    title = useCase.translations.english.title;
                  }

                  return (
                    <Grid item xs={12} md={4} key={useCase._id}>
                      <AppCard
                        title={title}
                        usecase_icon_url={useCase.usecase_icon_url}
                        usedCount={0}
                        appType="usecase"
                        id={useCase._id}
                        onClick={() => navigate(`/usecase/${useCase.slug}`)}
                      />
                    </Grid>
                  );
                })
              )}
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ITUseCasesPage; 