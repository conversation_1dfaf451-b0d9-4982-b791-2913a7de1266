@use '../../styles/abstracts/variables' as *;

.course-landing-page {
  padding:0;
  .progress-bar{
    margin-top:$spacing-4 !important;
    margin-bottom:0 !important;
  }
  .course-header {
    background-color:rgba(0, 102, 255, 0.03) !important;
    border-bottom:1px solid $border-color;
    padding:0;
    margin:0;
    .MuiContainer-maxWidthLg{
        padding:$spacing-6 0;
        position: relative;
        display: flex;
        align-items: center;
        &:not(.has-cover-image) {
            background: url('../../assets/images/learning-bg.png') no-repeat right center !important;
            background-size: 40% !important;
        }
    }
    &__top-grid{
        width: 100%;
        margin: 0 !important;
    }
    &__content-container{
        padding-top:0 !important;
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
    }
    &__image-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: $spacing-4;
    }
    &__cover-image {
        width: 100%;
        max-width: 420px;
        height: auto;
        object-fit: contain;
        border-radius: $border-radius-md;
        box-shadow: $shadow-sm;
    }

  &__title {
    display:block !important;
    font-size: $font-size-xxl !important;
    font-weight: $font-weight-semibold !important;
    margin-bottom: $spacing-2 !important;
    color: $text-primary !important;
  }

  &__description {
    font-size: $font-size-md !important;
    color: $text-secondary !important;
    margin-bottom: $spacing-3 !important;
    max-width: 800px;
    text-align: justify;
    line-height: 1.6 !important;
  }


  &__content {
        position: relative;
        z-index: 1;

        &-provider {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: $spacing-2;
            margin-bottom: $spacing-3;
            img{
              height: 42px;
              object-fit: contain;
            }
        }
    }
  }
  
  .course-details {
      padding-top: $spacing-3;
      &__about-section-grid{
          padding-top: 0 !important;
      }
      &__sticky-header {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          background: $bg-paper;
          z-index: 100;
          padding: $spacing-2;
          box-shadow: $shadow-sm;
          transform: translateY(-100%);
          transition: transform 0.3s ease;
          &.visible {
              transform: translateY(0);
          }

          &-container {
              display: flex;
              align-items: center;
              justify-content: space-between;
          }

          &-left {
              display: flex;
              align-items: center;
              gap: $spacing-2;

              .course-icon {
                  width: 40px;
                  height: 40px;
                  border-radius: $border-radius-sm;
                  background: $primary-color;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  img {
                      width: 24px;
                      height: 24px;
                      object-fit: contain;
                  }
              }

              .course-title {
                  font-size: $font-size-md !important;
                  font-weight: $font-weight-semibold !important;
                  color: $text-primary !important;
              }
          }

          &-right {
              display: flex;
              align-items: center;
              gap: $spacing-3;

              .enroll-button {
                  background: $primary-color !important;
                  color: white !important;
                  padding: $spacing-1 $spacing-3 !important;
                  border-radius: $border-radius-sm !important;
                  font-weight: $font-weight-medium !important;
                  text-transform: none !important;

                  &:hover {
                      background: $primary-color-dark !important;
                  }
              }
          }
      }
  
      &__nav {
          display: flex;
          align-items: center;
          margin-bottom: $spacing-3;
          border: 1px solid $border-color !important;
          border-radius: $border-radius-md !important;
          margin-top:0 !important;
          position: sticky;
          top: 66px;
          background: $bg-paper;
          z-index: 10;
          padding: $spacing-2 $spacing-2;

          &-item {
              text-transform: none !important;
              font-weight: $font-weight-medium !important;
              font-size: $font-size-sm !important;
              color: $text-secondary !important;
              padding: $spacing-2 $spacing-3 !important;
              min-width: auto !important;
              border:1px solid transparent !important;
              margin-right: $spacing-3 !important;
              position: relative;
              &:hover {
                  background-color: rgba($primary-color, 0.05) !important;
                  border:1px solid rgba($primary-color, 0.1) !important;
                  color: $primary-color !important;
              }
              &.active{
                  background-color: rgba($primary-color, 0.05) !important;
                  border:1px solid rgba($primary-color, 0.1) !important;
                  color: $primary-color !important;
                  font-weight: $font-weight-semibold !important;
              }

              &:hover {
                  background-color: transparent;
              }

              &.progress-action {
                  margin-left: auto !important;
                  background-color: $primary-color !important;
                  color: white !important;
                  padding: $spacing-2 $spacing-4 !important;
                  display: flex !important;
                  align-items: center !important;
                  gap: $spacing-2 !important;
                  margin-right: 0 !important;
                  transition: all 0.3s ease !important;
                  border: none !important;

                  .MuiSvgIcon-root {
                      font-size: 18px !important;
                      transition: transform 0.3s ease !important;
                  }

                  &:hover {
                      background-color: $primary-color-dark !important;
                      transform: translateY(-2px);
                      box-shadow: 0 4px 8px rgba($primary-color, 0.2) !important;

                      .MuiSvgIcon-root {
                          transform: translateX(4px);
                      }
                  }
              }
          }

          .completed {
              background-color: $success-color !important;
              color: white !important;
              &:hover {
                  background-color: $success-color-dark !important;
              }
          }

          .certificate-available {
              color: #2e7d32;
              
              &:hover {
                  background-color: rgba(46, 125, 50, 0.08);
              }
          }
          
          .certificate-locked {
              color: #757575;
              
              &:hover {
                  background-color: rgba(117, 117, 117, 0.08);
                  cursor: not-allowed;
              }
          }
      }
  
      &__section {
          scroll-margin-top: 60px;
          margin-bottom: $spacing-4;
          &.about{
              scroll-margin-top: 120px;
              margin-bottom:$spacing-1 !important
          }
          &.courses{
              margin-top:calc($spacing-6 + $spacing-4) !important;
          }
  
          &-title {
              display:block !important;
              font-size: $font-size-md !important;
              font-weight: $font-weight-semibold !important;
              margin-bottom: $spacing-3 !important;
              color: $text-primary !important;
          }
      }
  
      &__cover-image {
          width: 100%;
          max-width: 420px;
          height: auto;
          object-fit: cover;
          border-radius: $border-radius-md;
          box-shadow: $shadow-sm;
      }

      &__image-container {
          position: sticky;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          top: 100px;
      }

      &__program {
        background-color: $bg-paper;
        border:1px solid $border-color;
        border-radius: $border-radius-md;
        padding: $spacing-4;
          margin-bottom: $spacing-4;

          &-description {
              font-size: calc($font-size-md - 1px) !important;
              color: $text-primary !important;
              line-height: 1.8 !important;
              margin-bottom: 0 !important;
              p {
                margin-bottom: 1rem;
              }

              h1, h2, h3, h4, h5, h6 {
                margin-top: 1.5rem;
                margin-bottom: 1rem;
                font-weight: 600;
              }

              ul, ol {
                margin: 1rem 0;
                padding-left: 2rem;
              }

              li {
                margin-bottom: 0.5rem;
              }

              a {
                color: #4A90E2;
                text-decoration: none;
                &:hover {
                  text-decoration: underline;
                }
              }

              img {
                max-width: 100%;
                height: auto;
                margin: 1rem 0;
              }

              blockquote {
                border-left: 4px solid #4A90E2;
                margin: 1rem 0;
                padding: 0.5rem 1rem;
                background-color: rgba(74, 144, 226, 0.1);
              }

              code {
                background-color: #f5f5f5;
                padding: 0.2rem 0.4rem;
                border-radius: 3px;
                font-family: monospace;
              }

              pre {
                background-color: #f5f5f5;
                padding: 1rem;
                border-radius: 4px;
                overflow-x: auto;
                margin: 1rem 0;
              }
          }

          &-info {
              display: flex;
              flex-direction: column;
              gap: $spacing-2;

              &-item {
                  font-size: $font-size-md !important;
                  color: $text-primary !important;
                  
                  strong {
                      font-weight: $font-weight-semibold !important;
                      margin-right: $spacing-1;
                  }
              }
          }
      }
  
      &__section-list {
          display: flex;
          flex-direction: column;
          gap: $spacing-3;
          height: 100%;

          &-item {
              display: flex;
              align-items: flex-start;
              gap: $spacing-2;
              font-size: calc($font-size-md - 2px) !important;
              color: $text-primary !important;
              line-height: 1.6 !important;

              &:before {
                  content: "✓";
                  color: $success-color;
                  font-weight: $font-weight-bold;
                  margin-top: 2px;
              }
          }
      }

      &__skills {
          display: flex;
          flex-wrap: wrap;
          background-color: $bg-paper;
          border:1px solid $border-color;
          border-radius: $border-radius-md;
          padding: $spacing-4;
          gap: $spacing-2;

          &-item {
              display:flex;
              background-color: rgba($primary-color, 0.05);
              border:1px solid rgba($primary-color, 0.1);
              color: #485487 !important;
              padding: $spacing-2 $spacing-2 !important;
              border-radius: $border-radius-sm !important;
              font-size: $font-size-xs !important;
          }
      }
  
      &__details {
          display:flex;
          flex-direction: row;
          flex-wrap:wrap;
          background-color: $bg-paper;
          border:1px solid $border-color;
          border-radius: $border-radius-md;
          padding: $spacing-4;
          gap:0;
          &-item {
            width: 50%;
            max-width: 50%;
              display: flex;
              align-items: flex-start;
              gap: $spacing-2;
              margin-bottom: $spacing-4;
              &.full-width {
                width: 100% !important;
                max-width: 100% !important;
              }
              &:last-child{
                margin-bottom:0 !important;
              }
  
              &-icon {
                  width: 24px;
                  height: 24px;
                  color: $text-secondary;
              }
  
              &-content {
                  &-title {
                      font-weight: $font-weight-semibold !important;
                      color: $text-primary !important;
                      font-size: $font-size-sm !important;
                      margin-bottom: $spacing-1 !important;
                  }
  
                  &-subtitle {
                      color: $text-secondary !important;
                      font-size: $font-size-xs !important;
                      padding-right: $spacing-1 !important;
  
                      &--link {
                          color: $primary-color !important;
                          text-decoration: none !important;
                          cursor: pointer;
  
                          &:hover {
                              text-decoration: underline !important;
                          }
                      }
                  }
              }
          }
      }
  }
}

// Certificate Showcase Styles
.certificate-showcase {
  background-color: $bg-paper;
  border-radius: $border-radius-lg;
  padding: $spacing-4 $spacing-6;
  margin-top: calc($spacing-6) !important;
  margin-bottom: calc($spacing-6 + $spacing-4) !important;
  border:1px solid $border-color;
  overflow: hidden;
  position: relative;


  .certificate-title {
    font-size: $font-size-xxl;
    font-weight: $font-weight-bold;
    margin-bottom: $spacing-4;
    color: $text-primary;
  }

  .certificate-benefits {

    .certificate-benefit-item {
      display: flex;
      align-items: center;
      margin-bottom: $spacing-3;
      font-size: calc($font-size-md - 1px) !important;
      line-height: 1.5;
      color: $text-secondary;
      &:last-child{
        margin-bottom: 0 !important;
      }

      &::before {
        content: '✓';
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
        border-radius: 50%;
        margin-right: $spacing-2;
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
      }
    }
  }

  .enroll-button {
    padding: $spacing-2 $spacing-4;
    font-weight: $font-weight-semibold;
    text-transform: none;
    font-size: $font-size-md;
    border-radius: $border-radius-md;
    background-color: $primary-color;
    transition: all 0.3s ease;

    &:hover {
      background-color: $primary-color-dark;
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba($primary-color, 0.2);
    }
  }

  .certificate-image-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: relative;
    height: 100%;
    min-height: 240px;
  }

  .certificate-image-wrapper {
    position: relative;
    width: 90%;
    max-width: 320px;
    perspective: 1000px;
  }

  .certificate-document {
    width: 100%;
    height: 220px;
    background-color: $bg-paper;
    border-radius: $border-radius-sm;
    box-shadow: $shadow-sm;
    overflow: hidden;
    transform: rotate(0deg);
    position: relative;
    z-index: 1;
    border: 1px solid $border-color;

    .certificate-document-header {
      height: 30px;
      background-color: rgba($primary-color, 0.05);
      border-bottom: 1px solid $border-color;
    }

    .certificate-document-content {
      padding: $spacing-3;
      display: flex;
      flex-direction: column;
      gap: $spacing-2;
    }

    .certificate-document-line {
      height: 6px;
      background-color: $bg-light-dark;
      border-radius: $border-radius-sm;
        
      &.short {
        width: 70%;
      }
      &.gray {
        width: 50%;
        background-color:#939393;
      }
      &.blue{
        margin-top: $spacing-6;
        width: 75%;
        background-color:$primary-color;
      }
    }

    .certificate-document-footer {
      height: 30px;
      background-color: $bg-paper;
      border-top:1px solid $border-color;
      position: absolute;
      bottom: 0;
      width: calc(100% - $spacing-2);
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: $spacing-2;
      padding-right: $spacing-2;
      font-size: calc($font-size-xs - 2px) !important;
      color: rgba($text-secondary, 0.5) !important;

      .certificate-logo {
        height: 20px;
        width: auto;
        object-fit: contain;
        opacity: 0.9;
      }
    }
  }

  .certificate-ribbon {
    position: absolute;
    top: 0;
    right: 20px;
    width: 60px;
    height: 90px;
    background-color: rgba($primary-color, 1);
    z-index: 2;
    clip-path: polygon(0 0, 100% 0, 100% 80%, 50% 100%, 0 80%);
    display: flex;
    align-items: center;
    justify-content: center;
    
    .certificate-ribbon-inner {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
    }

    .certificate-ribbon-icon {
      color: $bg-paper;
      font-size: 28px;
    }
  }
}

@media (max-width: $tablet) {
  .certificate-showcase {
    padding: $spacing-4 $spacing-3;

    .certificate-image-container {
      margin-top: $spacing-6;
    }

    .certificate-ribbon {
      top: 0;
      right: 10px;
      width: 50px;
      height: 100px;
    }
  }
}
.course-instructors-sidebar {
    background-color: $bg-paper;
    border-radius: $border-radius-md;
    padding: $spacing-4;
    border: 1px solid $border-color;
  
    .course-details__section-title {
      margin-bottom: $spacing-3 !important;
    }
  
  
    .instructor-list {
      margin-top:0 !important;
      .instructor-item {
        margin-bottom: $spacing-4;
        display: flex;
        align-items: center;
        border-bottom:1px solid $border-color;
        padding-bottom: $spacing-4;
        gap: $spacing-2;
        &:last-child{
          border-bottom: none;
          margin-bottom: 0;
          padding-bottom: 0;
        }
  
        &:last-child {
          margin-bottom: 0;
        }
  
        .MuiAvatar-root {
          width: 48px;
          height: 48px;
          border-radius: 50%;
        }
  
        h6 {
          font-weight: $font-weight-semibold;
          color: $text-primary;
          margin-bottom: $spacing-1;
          font-size: $font-size-md;
        }
  
        .MuiTypography-body2 {
          color: $text-secondary;
          font-size: $font-size-xs;
        }
      }
    }
  
    .offered-by {
      margin-top: $spacing-6;
      padding-top: $spacing-4;
      border-top: 1px solid $border-color;
  
      h6 {
        font-weight: $font-weight-semibold;
        color: $text-primary;
        margin-bottom: $spacing-2;
        font-size: $font-size-md;
      }
  
      .MuiAvatar-root {
        width: 48px;
        height: 48px;
        background-color: #8c1515;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: $border-radius-sm;
      }
  
      .MuiTypography-subtitle1 {
        font-weight: $font-weight-semibold;
        color: $text-primary;
      }
  
      .MuiTypography-body2 {
        color: $primary-color;
        cursor: pointer;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  
  // Module styles for the course content
  .course-module {
    border: 1px solid $border-color;
    border-radius: $border-radius-sm;
    margin-bottom: $spacing-3;
    overflow: hidden;
    
    &__header {
      padding: $spacing-3 $spacing-4;
      background-color: $bg-light;
      border-bottom: 1px solid $border-color;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &__top-grid{
        display:flex !important;
        flex-direction: row !important; 
        justify-content: space-between !important;
      }
      
      &-title {
        font-weight: $font-weight-semibold !important;
        color: $text-primary !important;
        font-size: $font-size-md !important;
      }
      
      &-meta {
        color: $text-secondary !important;
        font-size: $font-size-sm !important;
      }
    }
    
    &__content {
      padding: $spacing-2 $spacing-4;
    }
  }
.program-course-content{
  background-color: $bg-paper;
  border:1px solid $border-color;
  border-radius: $border-radius-md;
  padding: $spacing-4;
}
