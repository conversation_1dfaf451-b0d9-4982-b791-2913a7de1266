@use '../../styles/abstracts/variables' as *;

.learn-page {
  display: flex;
  flex-direction: column;
  background-color: $bg-default;

  &__grid {
    flex: 1;
    display: flex;
    position: relative;
  }

  &__column {
    padding: $spacing-3;
    transition: all 0.3s ease;

    &--side {
      width: 20%;
      background-color: rgba($bg-paper, 0.8);
      border-color: $divider-color;

      &-left {
        @extend .learn-page__column--side;
        display: flex;
        padding: 0 !important;
        flex-direction: column;
        background-color: #FAFBFD !important;
        box-shadow: inset -36px 0 40px -32px rgba(0, 0, 0, .05) !important;
        min-width: 300px;
      }

      &-right {
        @extend .learn-page__column--side;
        border-left: 1px solid;
        min-width: 300px;
      }
    }

    &--main {
      width: 60%;
      display: flex;
      justify-content: center;
      background-color:#fff !important;
      align-items: center;
    }
  }

  &__course-name {
    font-size: $font-size-lg !important;
    font-weight: $font-weight-bold !important;
    color: $primary-text-color-dark !important;
    margin: 0;
    padding: $spacing-4;
    border-bottom: 1px solid $divider-color;
  }


  &__chapters {
    flex: 1;
    gap:0 !important;
    overflow-y: auto;
    margin-right: -#{$spacing-3}; // Offset parent padding
    padding-right: $spacing-3; // Add padding back for scrollbar
  }

  &__title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $primary-color;
    margin: 0;
    margin-bottom: $spacing-4;
  }

  &__content {
    font-size: $font-size-md;
    font-weight: $font-weight-medium;
    color: $text-primary;
  }

  // Course content specific styles
  &__chapter {
    padding:0;
    margin:0;

    &-title {
      font-size: $font-size-md;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: $spacing-3 $spacing-2;
      transition: all 0.2s ease;
      border-left: 3px solid transparent;

      &:hover {
        background-color: rgba($primary-color, 0.04);
      }

      // Aktif/açık durum stili
      &--active {
        background-color: #F5F6F8;
        font-weight: $font-weight-bold;
        border-left: 3px solid $primary-color;
      }

      .icon {
        margin-right: $spacing-2;
        color: $primary-color;
        transition: transform 0.2s ease;

        &--expanded {
          transform: rotate(90deg);
        }
      }
    }
  }

  &__topics {
    background-color:#f9f9f9 !important;
  }

  &__topic {
    margin-bottom: $spacing-2;
    padding: $spacing-2;
    padding-left: $spacing-6;
    border-radius: $border-radius-sm;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba($primary-color, 0.04);
    }

    &--active {
      background-color: rgba($primary-color, 0.08);
    }

    &-header {
      display: flex;
      align-items: center;
      gap: $spacing-2;
    }

    &-icon {
      color: $text-secondary;
      font-size: $font-size-md;

      &--completed {
        color: $success-color;
      }

      &--active {
        color: $primary-color;
      }
    }

    &-content {
      margin-left: calc(#{$font-size-md} + #{$spacing-2});
      margin-top: $spacing-1;
    }

    &-title {
      font-size: $font-size-sm !important;
      font-weight: $font-weight-medium !important;
      color: $text-primary !important;
    }

    &-meta {
      font-size: $font-size-xs !important;
      color: $text-secondary !important;
      display: flex;
      align-items: center;
      gap: $spacing-2;
    }

    &-type {
      display: flex;
      align-items: center;
      gap: $spacing-1;
    }

    &-duration {
      display: flex;
      align-items: center;
      gap: $spacing-1;
    }
  }

  // Panel toggle butonları için stil
  .MuiIconButton-root {
    width: 24px;
    height: 24px;
    background-color: white;
    border: 1px solid $divider-color;
    
    &:hover {
      background-color: $bg-light;
    }
  }
} 