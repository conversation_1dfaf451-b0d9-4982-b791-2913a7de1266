//course landing page

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CheckIcon from '@mui/icons-material/Check';
import ReactMarkdown from 'react-markdown';
import DOMPurify from 'dompurify';

import aibsLogo from '../../assets/aibs-logo.png';
import {
  Container,
  Grid,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Button,
  Avatar,
} from '@mui/material';
import { Timer as TimerIcon, School as SchoolIcon } from '@mui/icons-material';
import {
  useFetchCourseDetailsQuery,
  selectIsCourseCompleted,
  fetchProgress,
} from '../../redux/features/courses/courseSlice';
import AIHero from '../../components/AIHero/AIHero';
import { CourseContent } from '../../components/LMS/landing';
import CertificateModal from '../../components/CertificateModal/CertificateModal';

import './course.scss';
import ProgressBar from '../../components/ProgressBar';
import { useCheckCertificateQuery } from '../../redux/services/courses-api';
import useCourseProgress from '../../hooks/useCourseProgress';

const CourseLandingPage = () => {
  // 1. Router hooks
  const { courseId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();

  // 2. Redux hooks
  const dispatch = useDispatch();
  const user = useSelector((state) => state.auth.user);
  const isCourseCompleted = useSelector(selectIsCourseCompleted);

  // 3. i18n hook
  const { i18n, t } = useTranslation();

  // 4. State hooks
  const [expandedChapter, setExpandedChapter] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [isHeaderVisible, setIsHeaderVisible] = useState(false);
  const [manualTabSelection, setManualTabSelection] = useState(false);
  const [openCertificateModal, setOpenCertificateModal] = useState(false);

  // 5. Refs
  const aboutRef = useRef(null);
  const outcomesRef = useRef(null);
  const coursesRef = useRef(null);
  const certificatesRef = useRef(null);

  // 6. API hooks
  const { data: course, isLoading, error: fetchError } = useFetchCourseDetailsQuery(courseId);
  const { data: certificate } = useCheckCertificateQuery(
    { userId: user?._id, courseId },
    { skip: !user?._id || !courseId }
  );

  // 7. Memoized values
  const currentLanguageData = useMemo(() => {
    if (!course?.translations) return null;
    const currentLanguage = i18n.language || 'en';
    const data = course.translations[currentLanguage] || course.translations.en || {};

    return {
      ...data,
      title: data.title || '',
      description: data.description || '',
      coverImage: data.coverImage || '',
      providerImage: data.providerImage || '',
      duration: data.duration || '',
      certificateAvailable: data.certificateAvailable || false,
      outcomes: Array.isArray(data?.outcomes) ? data.outcomes : [],
      skills: Array.isArray(data?.skills) ? data.skills : [],
      instructors: Array.isArray(data?.instructors) ? data.instructors : [],
      chapters: Array.isArray(data?.chapters) ? data.chapters : [],
    };
  }, [course, i18n.language]);

  // 8. Custom hooks
  const {
    displayProgress = 0,
    getFirstIncompleteTopicInfo = () => ({ chapterId: null, topicId: null }),
    updateProgress,
    isLoading: isProgressUpdating,
  } = useCourseProgress(
    course && currentLanguageData
      ? {
          ...course,
          translations: {
            [i18n.language]: currentLanguageData,
          },
        }
      : null,
    user?._id
  );

  // 9. Callbacks
  const handleChapterChange = useCallback((panel) => {
    return (event, isExpanded) => {
      if (!panel) return;
      setExpandedChapter(isExpanded ? panel : null);
    };
  }, []);

  const handleOpenCertificateModal = useCallback(() => {
    setOpenCertificateModal(true);
  }, []);

  const handleCloseCertificateModal = useCallback(() => {
    setOpenCertificateModal(false);
  }, []);

  const navigateToTopic = useCallback(
    (chapterId, topicId) => {
      // Journey cardId'sini sessionStorage'a kaydet
      if (location.state?.cardId) {
        sessionStorage.setItem('journeyCardId', location.state.cardId);
      }
      navigate(`/course/learn/${courseId}/${chapterId}/${topicId}`, {
        state: {
          cardId: location.state?.journeyCardId || null,
        },
      });
    },
    [courseId, navigate, location.state?.cardId]
  );

  const handleScroll = useCallback(() => {
    const scrollPosition = window.scrollY;
    const headerHeight = 300;
    setIsHeaderVisible(scrollPosition > headerHeight);

    if (manualTabSelection) return;

    if (scrollPosition < 100) {
      setTabValue(0);
      return;
    }

    const sections = [
      { ref: aboutRef, index: 0 },
      { ref: outcomesRef, index: 1 },
      { ref: coursesRef, index: 2 },
      { ref: certificatesRef, index: 3 },
    ];

    const viewportMiddle = window.innerHeight * 0.4;

    let activeIndex = sections.findIndex(({ ref }) => {
      if (!ref?.current) return false;
      const rect = ref.current.getBoundingClientRect();
      return rect.top <= viewportMiddle && rect.bottom >= viewportMiddle;
    });

    if (activeIndex === -1) {
      let closestDistance = Infinity;
      sections.forEach(({ ref, index }) => {
        if (!ref?.current) return;
        const rect = ref.current.getBoundingClientRect();
        const distance = Math.abs(rect.top);
        if (distance < closestDistance) {
          closestDistance = distance;
          activeIndex = index;
        }
      });
    }

    if (activeIndex !== -1) {
      setTabValue(activeIndex);
    }
  }, [manualTabSelection]);

  // Kursa başlama işlemi
  const handleStartCourse = useCallback(async () => {
    if (displayProgress > 0 && displayProgress < 100) {
      // Devam eden kurs - mevcut ilerlemeyi koru, sadece tamamlanmamış topice git
      const { chapterId, topicId } = getFirstIncompleteTopicInfo();
      if (chapterId && topicId) {
        navigateToTopic(chapterId, topicId);
        return;
      }
    }

    const firstChapter = currentLanguageData?.chapters?.[0];
    const firstTopic = firstChapter?.topics?.[0];
    if (firstChapter && firstTopic) {
      try {
        // Sadece displayProgress 0 olduğunda (kursa ilk kez başlarken) ilerleme verisi oluştur
        if (displayProgress === 0) {
          // İlerleme verisini güncelle
          await updateProgress({
            chapterId: firstChapter._id,
            topicId: firstTopic._id,
            completed: false,
          });
        }

        // Kursa yönlendir
        navigateToTopic(firstChapter._id, firstTopic._id);
      } catch (error) {
        console.error('Kurs başlatma hatası:', error);
      }
    }
  }, [
    courseId,
    currentLanguageData,
    displayProgress,
    getFirstIncompleteTopicInfo,
    navigateToTopic,
    updateProgress, // updateProgress'i yorumdan çıkar
  ]);

  // 10. Effects
  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    handleScroll();
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  useEffect(() => {
    if (manualTabSelection) {
      const timer = setTimeout(() => setManualTabSelection(false), 1000);
      return () => clearTimeout(timer);
    }
  }, [manualTabSelection]);

  // Kurs ilerleme verisini otomatik olarak çek
  useEffect(() => {
    if (courseId && user?._id) {
      dispatch(fetchProgress(courseId));
    }
  }, [courseId, user?._id, dispatch]);

  // 11. Derived values
  const hasCertificate = Boolean(certificate?.data?.certificate?.downloadUrl);

  // 12. Loading and error states
  if (isLoading) {
    return (
      <Box
        sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (fetchError) {
    return (
      <Container maxWidth="sm" sx={{ mt: 12 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {t('course.errors.loading')}
        </Alert>
        <Button variant="contained" onClick={() => window.location.reload()}>
          {t('course.actions.tryAgain')}
        </Button>
      </Container>
    );
  }

  if (!course || !currentLanguageData) {
    return (
      <Container maxWidth="sm" sx={{ mt: 12 }}>
        <Alert severity="error">{t('course.errors.languageNotAvailable')}</Alert>
      </Container>
    );
  }

  return (
    <Box className="course-landing-page">
      {/* Course Header Section */}
      <Box className="course-header">
        <Container
          className={`MuiContainer-maxWidthLg ${currentLanguageData?.coverImage ? 'has-cover-image' : ''}`}
        >
          <Grid container className="course-header__top-grid" spacing={4}>
            <Grid item xs={12} md={6} className="course-header__content-container">
              <Box className="course-header__content">
                <Box className="course-header__content-provider">
                  <Box
                    component="img"
                    src={currentLanguageData?.providerImage}
                    alt="Provider Image"
                  />
                </Box>
                <Typography variant="h1" className="course-header__title">
                  {currentLanguageData?.title}
                </Typography>

                <ProgressBar progress={displayProgress} course={course} courseId={courseId} />
              </Box>
            </Grid>
            {currentLanguageData.coverImage && (
              <Grid item xs={12} md={6} className="course-header__image-container">
                <Box
                  component="img"
                  src={currentLanguageData?.coverImage}
                  alt="Course Cover"
                  className="course-header__cover-image"
                />
              </Grid>
            )}
          </Grid>
        </Container>
      </Box>

      {/* Sticky Header */}
      <Box className={`course-details__sticky-header ${isHeaderVisible ? 'visible' : ''}`}>
        <Container>
          <Box className="course-details__sticky-header-container">
            <Box className="course-details__sticky-header-left">
              <Box className="course-icon">
                <SchoolIcon sx={{ color: 'white' }} />
              </Box>
              <Typography className="course-title">{currentLanguageData?.title}</Typography>
            </Box>
          </Box>
        </Container>
      </Box>

      <Container maxWidth="lg">
        <Box className="course-details">
          {/* Navigation */}
          <Box className="course-details__nav">
            <Button
              className={`course-details__nav-item ${tabValue === 0 ? 'active' : ''}`}
              onClick={() => {
                setTabValue(0);
                setManualTabSelection(true);
                aboutRef.current?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              {t('course.nav.programDetails')}
            </Button>

            {currentLanguageData?.outcomes &&
              currentLanguageData?.outcomes?.length > 0 &&
              currentLanguageData?.outcomes?.filter((outcome) => outcome.text && outcome.imageUrl)
                ?.length > 0 && (
                <Button
                  className={`course-details__nav-item ${tabValue === 1 ? 'active' : ''}`}
                  onClick={() => {
                    setTabValue(1);
                    setManualTabSelection(true);
                    outcomesRef.current?.scrollIntoView({ behavior: 'smooth' });
                  }}
                >
                  {t('course.nav.outcomes')}
                </Button>
              )}

            <Button
              className={`course-details__nav-item ${tabValue === 2 ? 'active' : ''}`}
              onClick={() => {
                setTabValue(2);
                setManualTabSelection(true);
                coursesRef.current?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              {t('course.nav.programContent')}
            </Button>

            {hasCertificate && (
              <Button
                className="course-details__nav-item certificate-available"
                onClick={handleOpenCertificateModal}
              >
                {t('course.nav.certificate')}
              </Button>
            )}

            <Button
              variant="contained"
              onClick={handleStartCourse}
              disabled={isProgressUpdating}
              className={`course-details__nav-item progress-action ${
                displayProgress === 100 && isCourseCompleted ? 'completed' : ''
              }`}
            >
              {isProgressUpdating ? (
                <CircularProgress size={24} />
              ) : displayProgress === 0 ? (
                <>
                  {t('course.actions.start')}
                  <ArrowForwardIcon />
                </>
              ) : displayProgress === 100 && isCourseCompleted ? (
                <>
                  {t('course.actions.completed')}
                  <CheckIcon />
                </>
              ) : (
                <>
                  {t('course.actions.continue')}
                  <ArrowForwardIcon />
                </>
              )}
            </Button>
          </Box>

          {/* Course Content Sections */}
          <Box id="about" ref={aboutRef} className="course-details__section about">
            <Grid container spacing={4}>
              <Grid item xs={12} md={12}>
                <Box className="course-details__program">
                  <div
                    className="course-details__program-description"
                    dangerouslySetInnerHTML={{
                      __html: DOMPurify.sanitize(currentLanguageData?.description),
                    }}
                  />
                </Box>
                <Grid container className="course-details__about-section-grid" spacing={4}>
                  {currentLanguageData?.skills && currentLanguageData?.skills?.length > 0 && (
                    <Grid item xs={12} md={6}>
                      <Typography variant="h2" className="course-details__section-title">
                        {t('course.headers.skillsGain')}
                      </Typography>
                      <Box className="course-details__skills">
                        {currentLanguageData?.skills?.map((skill, index) => (
                          <Typography key={index} className="course-details__skills-item">
                            {skill}
                          </Typography>
                        ))}
                      </Box>
                    </Grid>
                  )}
                  <Grid
                    item
                    xs={12}
                    md={
                      currentLanguageData?.skills && currentLanguageData?.skills?.length > 0
                        ? 6
                        : 12
                    }
                  >
                    <Typography variant="h2" className="course-details__section-title">
                      {t('course.headers.detailsToKnow')}
                    </Typography>
                    <Box className="course-details__details">
                      <Grid container spacing={2}>
                        <Grid
                          item
                          xs={12}
                          md={
                            currentLanguageData?.skills && currentLanguageData?.skills?.length > 0
                              ? 6
                              : 4
                          }
                        >
                          <Box
                            className={`course-details__details-item ${!currentLanguageData?.skills || currentLanguageData?.skills?.length === 0 ? 'full-width' : ''}`}
                          >
                            <TimerIcon sx={{ color: '#4A90E2' }} />
                            <Box>
                              <Typography className="course-details__details-item-content-title">
                                {t('course.details.duration.title')}
                              </Typography>
                              <Typography className="course-details__details-item-content-subtitle">
                                {t('course.details.duration.subtitle', {
                                  duration: currentLanguageData?.duration,
                                })}
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>
                        {currentLanguageData?.certificateAvailable && (
                          <Grid
                            item
                            xs={12}
                            md={
                              currentLanguageData?.skills && currentLanguageData?.skills?.length > 0
                                ? 6
                                : 4
                            }
                          >
                            <Box
                              className={`course-details__details-item ${!currentLanguageData?.skills || currentLanguageData?.skills?.length === 0 ? 'full-width' : ''}`}
                            >
                              <LinkedInIcon sx={{ color: '#0a66c2' }} />
                              <Box>
                                <Typography className="course-details__details-item-content-title">
                                  {t('course.details.certificate.title')}
                                </Typography>
                                <Typography className="course-details__details-item-content-subtitle">
                                  {t('course.details.certificate.subtitle')}
                                </Typography>
                              </Box>
                            </Box>
                          </Grid>
                        )}
                        <Grid
                          item
                          xs={12}
                          md={
                            currentLanguageData?.skills && currentLanguageData?.skills?.length > 0
                              ? 6
                              : 4
                          }
                        >
                          <Box
                            className={`course-details__details-item ${!currentLanguageData?.skills || currentLanguageData?.skills?.length === 0 ? 'full-width' : ''}`}
                          >
                            <EmojiEventsIcon sx={{ color: '#FDBB11' }} />
                            <Box>
                              <Typography className="course-details__details-item-content-title">
                                {t('course.details.aiPoints.title')}
                              </Typography>
                              <Typography className="course-details__details-item-content-subtitle">
                                {t('course.details.aiPoints.subtitle')}
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>

          <Box
            id="outcomes"
            ref={outcomesRef}
            className="course-details__section"
            sx={{
              display: currentLanguageData?.outcomes?.length > 0 ? 'block' : 'none',
            }}
          >
            {currentLanguageData?.outcomes && currentLanguageData?.outcomes?.length > 0
              ? currentLanguageData?.outcomes
                  ?.filter((outcome) => outcome.text && outcome.imageUrl)
                  .map((outcome, index) => (
                    <AIHero
                      key={`outcome-${index}`}
                      title={t('course.headers.outcomes')}
                      description={outcome.text}
                      imageUrl={outcome.imageUrl}
                      smallTitle={true}
                      position={outcome.imagePosition || 'left'}
                    />
                  ))
              : null}
            {currentLanguageData?.certificateAvailable && (
              <Box className="certificate-showcase">
                <Grid container spacing={4} alignItems="center">
                  <Grid item xs={12} md={6}>
                    <Box className="certificate-content">
                      <Typography variant="h3" className="certificate-title">
                        {t('course.certificate.title')}
                      </Typography>
                      <Box className="certificate-benefits">
                        <Typography variant="body1" className="certificate-benefit-item">
                          {t('course.certificate.benefits.linkedIn')}
                        </Typography>
                        <Typography variant="body1" className="certificate-benefit-item">
                          {t('course.certificate.benefits.socialMedia')}
                        </Typography>
                        <Typography variant="body1" className="certificate-benefit-item">
                          {t('course.certificate.benefits.expertise')}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box className="certificate-image-container">
                      <Box className="certificate-image-wrapper">
                        <Box className="certificate-ribbon">
                          <Box className="certificate-ribbon-inner">
                            <EmojiEventsIcon className="certificate-ribbon-icon" />
                          </Box>
                        </Box>
                        <Box className="certificate-document">
                          <Box className="certificate-document-header"></Box>
                          <Box className="certificate-document-content">
                            <Box className="certificate-document-line gray"></Box>
                            <Box className="certificate-document-line short"></Box>
                            <Box className="certificate-document-line"></Box>
                            <Box className="certificate-document-line short"></Box>
                            <Box className="certificate-document-line blue"></Box>
                          </Box>
                          <Box className="certificate-document-footer">
                            {t('course.certificate.certifiedBy')}{' '}
                            <img src={aibsLogo} alt="AIBS Logo" className="certificate-logo" />
                          </Box>
                        </Box>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            )}
          </Box>

          <Box id="courses" ref={coursesRef} className="course-details__section courses">
            <Grid container spacing={4}>
              <Grid
                item
                xs={12}
                sx={{ paddingTop: '0px !important' }}
                md={currentLanguageData?.instructors?.[0]?.name ? 8 : 12}
              >
                <Typography variant="h2" className="course-details__section-title">
                  {t('course.headers.programContent')}
                </Typography>

                <CourseContent
                  cardId={location.state?.cardId}
                  title={currentLanguageData?.title || ''}
                  chapters={currentLanguageData?.chapters || []}
                  expandedChapter={expandedChapter}
                  onChapterChange={handleChapterChange}
                  onTopicClick={navigateToTopic}
                  currentLanguage={i18n.language || 'en'}
                />
              </Grid>
              {currentLanguageData?.instructors?.[0]?.name && (
                <Grid item xs={12} md={4}>
                  <Typography variant="h2" className="course-details__section-title">
                    {t('course.headers.instructors')}
                  </Typography>
                  <Box className="course-instructors-sidebar">
                    <Box className="instructor-list" sx={{ mt: 3 }}>
                      {currentLanguageData?.instructors?.map((instructor, index) => (
                        <Box
                          key={index}
                          className="instructor-item"
                          sx={{ mb: 3, display: 'flex', alignItems: 'flex-start', gap: 2 }}
                        >
                          <Avatar
                            src={instructor.avatar || ''}
                            alt={instructor.name}
                            sx={{ width: 56, height: 56 }}
                          />
                          <Box>
                            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                              {instructor.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                              {instructor.title}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {instructor.role} at {instructor.company}
                            </Typography>
                          </Box>
                        </Box>
                      ))}
                    </Box>
                  </Box>
                </Grid>
              )}
            </Grid>
          </Box>
        </Box>
      </Container>

      <CertificateModal
        open={openCertificateModal}
        onClose={handleCloseCertificateModal}
        certificateUrl={certificate?.data?.certificate?.downloadUrl}
        courseId={courseId}
      />
    </Box>
  );
};

export default React.memo(CourseLandingPage);
