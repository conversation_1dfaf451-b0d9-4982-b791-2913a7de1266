import { useState, useEffect } from 'react';
import { usePara<PERSON>, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  CircularProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Close as CloseIcon,
  Download as DownloadIcon,
  LinkedIn as LinkedInIcon,
  FastForward as FastForwardIcon,
  Check as CheckIcon,
} from '@mui/icons-material';

import TopicView from '../../components/LMS/course-content/TopicView';

import { toast } from 'react-toastify';
import {
  useGenerateCertificateMutation,
  useCheckCertificateQuery,
} from '../../redux/services/courses-api';
import {
  useUpdateJourneyTrackingMutation,
  useGetJourneyTrackingQuery,
  useGetJourneysQuery,
} from '../../redux/services/journey-api';
import { fetchProgress, selectCurrentCardId } from '../../redux/features/courses/courseSlice';
import { API_URL } from '../../config-global';
import i18n from '../../i18n';
import { useNavigate } from 'react-router-dom';
import useJourneyCardHandlers from '../../domains/journey/hooks/useJourneyCardHandlers';
import { developmentLogs } from '../../utils/developmentLogs';

const CourseLearnPage = () => {
  const { courseId, chapterId, topicId } = useParams();
  const location = useLocation();
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Component başında journeyState'i güncelleyelim
  const journeyState = (() => {
    // Eğer location.state varsa ve içi boş değilse onu kullan
    if (location.state && Object.keys(location.state).length > 0) {
      // Yönlendirme sırasında cardId'yi sessionStorage'a kaydet
      if (location.state.cardId) {
        sessionStorage.setItem('journeyCardId', location.state.cardId);
      }
      return location.state;
    }

    // State boş ise sessionStorage'dan al
    const journeyCardId = sessionStorage.getItem('journeyCardId');
    const journeyLevel = sessionStorage.getItem('journeyLevel');

    if (journeyCardId && journeyLevel) {
      return {
        cardId: journeyCardId, // cardId olarak kaydediyoruz
        journeyLevel,
      };
    }

    // Hiçbir veri yoksa boş obje döndür
    return {};
  })();

  const [isCompleteModalOpen, setIsCompleteModalOpen] = useState(false);
  const [isCertificateModalOpen, setIsCertificateModalOpen] = useState(false);
  const [certificateUrl, setCertificateUrl] = useState(null);
  const user = useSelector((state) => state.auth.user);
  const course = useSelector((state) => state.courses.currentCourse);
  const cardId = useSelector(selectCurrentCardId);
  const currentLanguage = i18n.language || 'en';
  const dispatch = useDispatch();

  const [updateJourneyTracking] = useUpdateJourneyTrackingMutation();
  const { data: journeyTrackingData } = useGetJourneyTrackingQuery(user?._id, {
    skip: !user?._id,
  });

  const [generateCertificate, { isLoading: isGeneratingCertificate }] =
    useGenerateCertificateMutation();

  const { data: existingCertificate } = useCheckCertificateQuery(
    { userId: user?.id, courseId },
    { skip: !user?.id || !courseId }
  );

  const { data: journeys } = useGetJourneysQuery({
    function: user?.onboarding?.function_label, // veya dinamik olarak belirlenen function
    levels: journeyState?.journeyLevel || 'beginner',
  });

  // Hook'u kullanmak için gerekli parametreleri hazırlayalım
  const { handleCardClick } = useJourneyCardHandlers({
    trackingData: journeyTrackingData,
    selectedLevel: journeyState?.journeyLevel || 'beginner',
    user: user,
  });

  // useEffect hook içinde recommendedTopic için kontrol yapalım
  useEffect(() => {
    if (courseId) {
      // Kurs tamamlama ve sertifika durumlarını kontrol et
      const completedCourseId = localStorage.getItem('completed_course_id');
      const certificateUrlSaved = localStorage.getItem(`certificate_url_${courseId}`);

      // Eğer localStorage'da bu kurs için bir tamamlama kaydı varsa ve modal kapalıysa
      if (
        completedCourseId === courseId &&
        course?.translations[currentLanguage]?.certificate === true &&
        !isCompleteModalOpen
      ) {
        setIsCompleteModalOpen(true);
      }

      // Eğer localStorage'da bu kurs için bir sertifika kaydı varsa
      if (certificateUrlSaved) {
        setCertificateUrl(certificateUrlSaved);

        // Eğer showCertificate parametresi de varsa modalı aç
        if (localStorage.getItem(`show_certificate_${courseId}`) === 'true') {
          setIsCertificateModalOpen(true);
          // Bir kez gösterdikten sonra flag'i temizle
          localStorage.removeItem(`show_certificate_${courseId}`);
        }
      }
    }
  }, [courseId, isCompleteModalOpen, user, journeyState]);

  const handleCourseComplete = async () => {
    try {
      // Tamamlanan kurs ID'sini localStorage'a kaydet
      localStorage.setItem('completed_course_id', courseId);

      setIsCompleteModalOpen(true);
      if (course?.translations[currentLanguage]?.certificate !== false) {
        handleGenerateCertificate();
      }

      // URL state'inden gelen seviyeyi kullan, yoksa kullanıcının profil seviyesini kullan
      const userLevel =
        journeyState.journeyLevel?.toLowerCase() || user?.journeyLevel?.name?.toLowerCase();

      // Hangi seviyenin kullanıldığını kontrol et

      const userId = user?._id;

      // Kursu API üzerinden completed olarak işaretle
      if (userId && courseId) {
        try {
          const response = await fetch(`${API_URL}/tracking/course/courses/${courseId}/complete`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${user?.token}`,
            },
            body: JSON.stringify({ userId }),
          });

          if (!response.ok) {
            if (import.meta.env.DEV) {
              console.error('Kurs tamamlama hatası:', await response.text());
            }
          } else {
            dispatch(fetchProgress(courseId));
          }
        } catch (error) {
          if (import.meta.env.DEV) {
            console.error('Kurs tamamlama API hatası:', error);
          }
        }
      }

      // Journey tracking ile ilgili mevcut işlemler
      if (userLevel) {
        // Öncelikle sessionStorage'dan alacağız, yoksa state'den, son olarak redux'tan
        const journeyCardId =
          sessionStorage.getItem('journeyCardId') || journeyState.cardId || cardId;

        if (journeyCardId) {
          const currentTracking = journeyTrackingData?.[userLevel] || [];

          // Eğer cardId zaten varsa, tekrar eklemeye gerek yok
          if (!currentTracking.includes(journeyCardId)) {
            const updatedTracking = [...currentTracking, journeyCardId];

            // Journey tracking veritabanını güncelle
            await updateJourneyTracking({
              userId: user?._id,
              journeyTracking: {
                ...journeyTrackingData,
                [userLevel]: updatedTracking,
              },
            });

            // TrainingProgress için localStorage üzerinden bildirim gönder
            localStorage.setItem('journey_tracking_updated', Date.now().toString());

            // TrainingProgress widget'ını güncellemek için bir custom event tetikleyelim
            window.dispatchEvent(
              new CustomEvent('update_training_progress', {
                detail: {
                  source: 'course',
                  cardId: journeyCardId,
                  trackingData: true,
                  timestamp: Date.now(),
                },
              })
            );

            // Kalıcı bir işaretçi ekleyelim - Footer.jsx bunu algılayacak
            localStorage.setItem('tracking_completed_card', journeyCardId);
          }
        }
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Kurs tamamlama işlemi sırasında hata:', error);
      }
    }
  };

  const handleGenerateCertificate = async () => {
    try {
      // Eğer mevcut sertifika varsa, direkt onu göster
      if (existingCertificate?.data?.downloadUrl) {
        setCertificateUrl(existingCertificate.data.downloadUrl);
        // Sertifika URL'ini localStorage'a kaydet
        localStorage.setItem(`certificate_url_${courseId}`, existingCertificate.data.downloadUrl);
        return;
      }

      // Kullanıcı bilgilerini kontrol et
      if (!user?.name || !user?.surname) {
        toast.error(
          t(
            'courseContent.errors.userInfoMissing',
            'User information is missing. Please try again later.'
          )
        );
        return;
      }

      const fullName = `${user.name} ${user.surname}`;

      const response = await generateCertificate({
        userId: user._id,
        courseId,
        fullName,
        certificateName:
          course?.translations.en?.title ||
          t('courseContent.certificate.defaultTitle', 'Course Certificate'),
        organizationName: 'AI Business School',
        instructorName:
          course?.instructors?.[0]?.name ||
          t('courseContent.certificate.defaultInstructor', 'Course Instructor'),
        completionTime: `${course?.duration || 0} minutes`,
        issueDate: new Date().toISOString(),
      }).unwrap();

      if (response.status === 'success') {
        setCertificateUrl(response.data.downloadUrl);
        // Sertifika URL'ini localStorage'a kaydet
        localStorage.setItem(`certificate_url_${courseId}`, response.data.downloadUrl);
        toast.success(
          t('courseContent.certificate.generated', 'Certificate generated successfully!')
        );
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Certificate generation error:', error);
      }
      toast.error(
        t(
          'courseContent.errors.certificateGeneration',
          'Failed to generate certificate. Please try again.'
        )
      );
    }
  };

  const handleDownloadCertificate = () => {
    if (certificateUrl) {
      // Sertifikayı doğrudan indirmek için
      fetch(`${certificateUrl}?token=${user?.token}`)
        .then((response) => response.blob())
        .then((blob) => {
          // Dosyayı indirmek için geçici bir bağlantı oluştur
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          // Dosya adını sertifika adına göre ayarla
          a.download = `${course?.title || 'Certificate'}.pdf`;
          document.body.appendChild(a);
          a.click();

          // Temizlik işlemleri
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        })
        .catch((error) => {
          if (import.meta.env.DEV) {
            console.error('Sertifika indirme hatası:', error);
          }
          toast.error(
            t(
              'courseContent.errors.certificateGeneration',
              'Certificate could not be downloaded. Please try again.'
            )
          );
        });
    }
  };

  const handleShareOnLinkedIn = () => {
    const linkedinUrl = `https://www.linkedin.com/profile/add?startTask=CERTIFICATION_NAME&name=${encodeURIComponent(course?.title || 'Course Certificate')}&organizationId=14025979&issueYear=${new Date().getFullYear()}&issueMonth=${new Date().getMonth() + 1}&certUrl=${encodeURIComponent(certificateUrl)}&certId=${encodeURIComponent(existingCertificate?.data?.certificateNumber || '')}&organization=${encodeURIComponent('AI Business School')}`;
    window.open(linkedinUrl, '_blank', 'width=600,height=600');
  };

  // Sertifika görüntüleme modalını açma işlevi
  const openCertificateModal = () => {
    setIsCompleteModalOpen(false);
    // Burada sertifika göster flag'ini ayarla
    localStorage.setItem(`show_certificate_${courseId}`, 'true');
    setIsCertificateModalOpen(true);
  };

  return (
    <Box
      component="main"
      sx={{
        display: 'flex',
      }}
    >
      <TopicView
        courseId={courseId}
        chapterId={chapterId}
        topicId={topicId}
        onCourseComplete={handleCourseComplete}
      />
      {/* Course Completion Modal */}

      <Dialog
        open={isCompleteModalOpen}
        onClose={() => {
          // Burada hiçbir şey yapmıyoruz - onClose'u devre dışı bırakıyoruz
          setIsCompleteModalOpen(false);
        }}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: 6,
            p: 1,
            overflow: 'hidden',
          },
        }}
        disableEscapeKeyDown // ESC tuşunu devre dışı bırak
        onBackdropClick={(e) => e.stopPropagation()} // Backdrop tıklamasını engelle (MUI v5 için)
        hideBackdrop={false} // Arka planı gizleme
      >
        <DialogTitle
          sx={{
            py: 3,
            textAlign: 'center',
            fontWeight: 'bold',
            borderBottom: '1px solid #eee',
          }}
        >
          {t('course.completionModal.title')}
        </DialogTitle>

        <DialogContent sx={{ py: 4, px: 3 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>
            <Box
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                bgcolor: 'success.light',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mb: 3,
                mt: 3,
              }}
            >
              <CheckIcon sx={{ color: 'white', fontSize: 40 }} />
            </Box>
            {/* <Typography variant="h6" sx={{ fontWeight: 'medium', mb: 2, textAlign: 'center' }}>
              {t('course.completionModal.congratsTitle', 'Congratulations!')}
            </Typography> */}
            <Typography sx={{ textAlign: 'center' }}>
              {t('course.completionModal.content')}
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 1, justifyContent: 'center' }}>
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              justifyContent: 'center',
              gap: 2,
              flexDirection: {
                xs: 'column',
                sm: course?.translations[currentLanguage]?.certificate === true ? 'row' : 'column',
              },
            }}
          >
            {course?.translations[currentLanguage]?.certificate === false && (
              <Button
                onClick={() => setIsCompleteModalOpen(false)}
                variant="outlined"
                sx={{
                  textTransform: 'none',
                  borderRadius: 2,
                  py: 1,
                  flexGrow: 1,
                }}
              >
                {t('course.completionModal.close')}
              </Button>
            )}

            <Button
              variant="contained"
              color="primary"
              onClick={() => {
                setIsCompleteModalOpen(false);
                if (course?.translations[currentLanguage]?.certificate === true) {
                  openCertificateModal();
                } else {
                  // Yönlendirmeyi cardId varlığına veya recommended topic durumuna göre yap
                  // if (journeyState.cardId) {
                  //   // Journey'den geldiyse ve bir sonraki kart varsa ona yönlendir
                  //   if (journeys && journeys.length > 0) {
                  //     const currentCardIndex = journeys.findIndex(
                  //       (card) => card._id === journeyState.cardId
                  //     );

                  //     const nextCard = journeys[currentCardIndex + 1];

                  //     if (nextCard) {
                  //       sessionStorage.setItem(
                  //         'pendingCardClick',
                  //         JSON.stringify({
                  //           cardId: nextCard._id,
                  //           language: currentLanguage,
                  //         })
                  //       );
                  //       // Zaman aşımını arttırarak yönlendirme sonrası işlemlerin tamamlanmasını sağla
                  //       navigate('/');
                  //       setTimeout(() => {
                  //         handleCardClick(nextCard, currentLanguage);
                  //       }, 300); // Süreyi 300ms'ye çıkararak daha güvenli hale getiriyoruz
                  //     } else {
                  //       // Son kartsa journey ana sayfasına yönlendir
                  //       navigate('/');
                  //     }
                  //   }
                  // }
                  // Journey yoksa veya recommended topic ise ana sayfaya yönlendir
                  navigate('/');
                }
              }}
              disabled={isGeneratingCertificate}
              startIcon={isGeneratingCertificate ? <CircularProgress size={20} /> : null}
              sx={{
                textTransform: 'none',
                borderRadius: 2,
                py: 1,
                flexGrow: 1,
                boxShadow: 2,
              }}
            >
              {course?.translations[currentLanguage]?.certificate === true
                ? t('course.completionModal.viewCertificate')
                : t('course.completionModal.backToHome', 'Back to Home')}
            </Button>
          </Box>
        </DialogActions>
      </Dialog>

      {/* Certificate Viewer Modal */}
      <Dialog
        open={isCertificateModalOpen}
        onClose={() => {
          // Burada hiçbir şey yapmıyoruz - onClose'u devre dışı bırakıyoruz
        }}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: 6,
            overflow: 'hidden',
          },
        }}
        disableEscapeKeyDown // ESC tuşunu devre dışı bırak
        onBackdropClick={(e) => e.stopPropagation()} // Backdrop tıklamasını engelle (MUI v5 için)
        hideBackdrop={false} // Arka planı gizleme
      >
        <DialogTitle
          sx={{
            px: 3,
            py: 2,
            borderBottom: '1px solid #eee',
            bgcolor: '#f9f9f9',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Tooltip title={t('common.close', 'Close')}>
                <IconButton
                  onClick={() => {
                    setIsCertificateModalOpen(false);
                    setIsCompleteModalOpen(false); // Tamamlama modalını da kapat

                    // LocalStorage'dan açık olduğunu belirten flag'i de temizleyelim
                    localStorage.removeItem(`show_certificate_${courseId}`);
                    localStorage.removeItem('completed_course_id');
                  }}
                  sx={{
                    color: 'text.secondary',
                    '&:hover': {
                      bgcolor: 'rgba(0, 0, 0, 0.04)',
                    },
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Tooltip>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Tooltip
                  title={t('courseContent.certificate.downloadCertificate', 'Download Certificate')}
                >
                  <Button
                    onClick={handleDownloadCertificate}
                    variant="outlined"
                    color="primary"
                    startIcon={<DownloadIcon />}
                    sx={{
                      textTransform: 'none',
                      borderRadius: 2,
                    }}
                  >
                    {t('courseContent.certificate.download', 'Download')}
                  </Button>
                </Tooltip>

                <Tooltip
                  title={t('courseContent.certificate.shareOnLinkedIn', 'Share on LinkedIn')}
                >
                  <Button
                    onClick={handleShareOnLinkedIn}
                    variant="contained"
                    sx={{
                      bgcolor: '#0A66C2',
                      color: 'white',
                      textTransform: 'none',
                      borderRadius: 2,
                      '&:hover': {
                        bgcolor: '#084e96',
                      },
                    }}
                    startIcon={<LinkedInIcon />}
                  >
                    {t('courseContent.certificate.share', 'Share')}
                  </Button>
                </Tooltip>
              </Box>
              {isGeneratingCertificate && <CircularProgress size={20} />}
            </Box>

            <Button
              onClick={() => {
                // Burada cardId veya recommendedTopic kontrolü ekleyelim
                // if (journeyState.cardId) {
                //   // Journey'leri kontrol et
                //   if (journeys && journeys.length > 0) {
                //     // Mevcut kartın index'ini bul
                //     const currentCardIndex = journeys.findIndex(
                //       (card) => card._id === journeyState.cardId
                //     );
                //     // Bir sonraki kartı bul
                //     const nextCard = journeys[currentCardIndex + 1];

                //     if (nextCard) {
                //       // useJourneyCardHandlers'dan gelen handleCardClick fonksiyonunu kullan
                //       // NextCard bilgilerini sessionStorage'a kaydet
                //       sessionStorage.setItem(
                //         'pendingCardClick',
                //         JSON.stringify({
                //           cardId: nextCard._id,
                //           language: currentLanguage,
                //         })
                //       );
                //       // Zaman aşımını arttırarak yönlendirme sonrası işlemlerin tamamlanmasını sağla
                //       if (nextCard.journeyType !== 'Usecases') {
                //         navigate('/');
                //         setTimeout(() => {
                //           handleCardClick(nextCard, currentLanguage);
                //         }, 300); // Süreyi 300ms'ye çıkararak daha güvenli hale getiriyoruz
                //       } else {
                //         setTimeout(() => {
                //           handleCardClick(nextCard, currentLanguage);
                //         }, 300); // Süreyi 300ms'ye çıkararak daha güvenli hale getiriyoruz
                //       }
                //     } else {
                //       // Son kartsa journey ana sayfasına yönlendir
                //       navigate('/');
                //     }
                //   } else {
                //     // Journey bulunamadıysa ana sayfaya yönlendir
                //     navigate('/');
                //     developmentLogs('No journey found');
                //   }
                // } else {
                //   // Journey'den gelmediyse veya önerilen eğitimse ana sayfaya yönlendir
                //   navigate('/');
                //   developmentLogs('Not from journey or recommended topic, redirecting to home');
                // }
                navigate('/');
              }}
              variant="contained"
              endIcon={journeyState.cardId ? <FastForwardIcon /> : null}
              sx={{
                textTransform: 'none',
                borderRadius: 2,
                px: 4,
                py: 1.5,
                boxShadow: 2,
                minWidth: 250,
                height: 50,
              }}
            >
              {journeyState.cardId
                ? t('course.completionModal.continueJourney')
                : t('course.completionModal.backToHome', 'Back to Home')}
            </Button>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ height: '80vh', p: 0 }}>
          {certificateUrl ? (
            <iframe
              src={`${certificateUrl}?token=${user?.token}`}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
              }}
              title={t('courseContent.certificate.certificatePreview', 'Certificate Preview')}
            />
          ) : (
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
                gap: 2,
              }}
            >
              <CircularProgress />
              <Typography>
                {t('courseContent.certificate.generating', 'Generating certificate...')}
              </Typography>
            </Box>
          )}
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default CourseLearnPage;
