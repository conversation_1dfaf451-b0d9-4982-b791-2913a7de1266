import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import EditIcon from '@mui/icons-material/Edit';
import RefreshIcon from '@mui/icons-material/Refresh';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import SyncIcon from '@mui/icons-material/Sync';
import MenuIcon from '@mui/icons-material/Menu';
import OutputIcon from '@mui/icons-material/Output';
import { getStepTitle } from '../../utils/stepHelpers';

const WorkflowSidebar = ({
  title,
  currentStep,
  steps,
  stepOutputs,
  workflowId,
  isAutoProgressing,
  onRestart,
  showEditButton = false,
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleEditClick = () => {
    // Get the workflow name from the URL
    const workflowName = window.location.pathname.split('/').pop();
    // Log the workflowId for debugging
    console.log('Navigating to edit with workflowId:', workflowId);
    // Pass the workflowId as a query parameter for reference
    navigate(
      `/workflow-creator?workflow_action=edit&workflow_name=${workflowName}&workflow_id=${workflowId}`
    );
  };

  // Sonuç (output) olup olmadığını kontrol et
  const hasOutput = steps.some((step) => step.type === 'output' && stepOutputs?.[step.name]);

  return (
    <div className="workflow-title">
      <div className="workflow-title-inner">
        <MenuIcon sx={{ fontSize: 24 }} />
        <h1>{title}</h1>
      </div>
      <div className="action-buttons">
        {showEditButton && (
          <a className="sidebar-button" onClick={handleEditClick}>
            <EditIcon sx={{ fontSize: 16 }} />
            <span>{t('common.workflows.run.editApp')}</span>
          </a>
        )}
        <a
          className={`sidebar-button ${hasOutput ? '' : 'disabled'}`}
          onClick={hasOutput ? onRestart : undefined}
        >
          <RefreshIcon sx={{ fontSize: 16 }} />
          <span>{t('common.workflows.run.restartApp')}</span>
        </a>
      </div>

      <ul className="workflow-steps">
        {steps.map((step, index) => {
          const stepTitle = getStepTitle(step);
          const isCompleted =
            index < currentStep ||
            (index === currentStep && step.type === 'output' && stepOutputs?.[step.name]);
          const isActive = currentStep === index;
          const isPromptProcessing = isActive && isAutoProgressing && step.type === 'prompt';
          const isActiveOutput = isActive && step.type === 'output';

          return (
            <li
              key={index}
              data-bs-toggle="tooltip"
              data-bs-placement="top"
              data-step={index}
              className={`${isActive ? 'active' : ''} ${isCompleted ? 'completed' : ''} ${isPromptProcessing ? 'processing' : ''}`}
            >
              <div className="step-number">
                {isCompleted ? (
                  <CheckCircleIcon sx={{ fontSize: 18, color: '#10B981' }} />
                ) : isPromptProcessing ? (
                  <SyncIcon sx={{ fontSize: 18 }} className="rotating-icon" />
                ) : isActiveOutput ? (
                  <OutputIcon sx={{ fontSize: 18, color: 'white' }} />
                ) : (
                  index + 1
                )}
              </div>
              {stepTitle}
            </li>
          );
        })}
      </ul>
    </div>
  );
};

WorkflowSidebar.propTypes = {
  title: PropTypes.string.isRequired,
  currentStep: PropTypes.number.isRequired,
  steps: PropTypes.array.isRequired,
  stepOutputs: PropTypes.object,
  workflowId: PropTypes.string,
  isAutoProgressing: PropTypes.bool,
  onRestart: PropTypes.func.isRequired,
  showEditButton: PropTypes.bool,
};

export default WorkflowSidebar;
