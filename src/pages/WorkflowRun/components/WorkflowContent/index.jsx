import PropTypes from 'prop-types';
import {
  Ty<PERSON>graphy,
  Button,
  CircularProgress,
  TextField,
  IconButton,
  Tooltip,
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import './WorkflowContent.scss';
import { getStepTitle } from '../../utils/stepHelpers';
import HtmlRenderer from '@components/HtmlRenderer';

const WorkflowContent = ({
  steps,
  currentStep,
  formData,
  stepOutputs,
  isAutoProgressing,
  onInputChange,
  onNext,
}) => {
  const [copied, setCopied] = useState(false);
  const { t } = useTranslation();

  // Prompt içerisindeki değişkenleri değiştirme
  const replacePromptVariables = (text, currentFormData = {}) => {
    if (!text) return '';

    // HTML içerisindeki değişkenleri bulmak için regex kullanıyoruz
    const regex = /{([^}]+)}/g;

    // custom-label sınıfına sahip a taglarını temizle ve içeriklerini koru
    const cleanedText = text.replace(
      /<a\s+class="custom-label"[^>]*data-key="([^"]*)"[^>]*>({[^}]*})<\/a>/g,
      '$2'
    );

    return cleanedText.replace(regex, (match, key) => {
      // Input adımlarını bul (hem normal hem de normalleştirilmiş key ile)
      const normalizedKey = key.toLowerCase().replace(/\s+/g, '_');

      // Tüm input adımlarını bul
      const inputStep = steps.find(
        (s) =>
          s.type === 'input' &&
          (s.name === key ||
            s.name === normalizedKey ||
            s.label === key ||
            s.label.toLowerCase().replace(/\s+/g, '_') === normalizedKey)
      );

      // Eğer böyle bir input adımı bulunduysa ve value değeri varsa
      if (inputStep && inputStep.value) {
        return inputStep.value;
      }

      // Input değerleri için formData'dan al
      if (currentFormData[normalizedKey]) return currentFormData[normalizedKey];
      if (currentFormData[key]) return currentFormData[key];

      // Önceki adımların çıktılarından al
      if (stepOutputs[normalizedKey]?.sections?.[0]?.content) {
        const content = stepOutputs[normalizedKey].sections[0].content;
        if (Array.isArray(content)) {
          return content.map((item) => item.fullContent || item.content || '').join('');
        }
        return content.fullContent || content.content || '';
      }

      return match; // Eşleşme bulunamazsa değişken adını olduğu gibi bırak
    });
  };

  // Input değişikliklerini yönetmek için yeni bir fonksiyon
  const handleInputChange = (e, stepName) => {
    // Değeri formData'ya kaydet
    onInputChange(e, stepName);
  };

  // HTML içerisindeki özel etiketleri (custom-label) işleyip değişkenleri değiştiren fonksiyon
  const processHtmlLabels = (html) => {
    // DOMParser'ı yalnızca tarayıcı ortamında kullan
    if (typeof window === 'undefined' || !window.DOMParser) {
      console.warn('DOMParser browser dışında kullanılamaz');
      return html;
    }

    try {
      // HTML içindeki custom-label etiketlerini bulup değiştirelim
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // Tüm custom-label sınıfına sahip etiketleri bul
      const labels = doc.querySelectorAll('.custom-label');

      labels.forEach((label) => {
        // Etiketin data-key özniteliğini al
        const key = label.getAttribute('data-key');
        if (key) {
          // Değişken adını bul ve değerini almak için değişken değişimi yap
          const variableName = key;
          const replacement = replacePromptVariables(`{${variableName}}`, formData);

          // Eğer replacement, {variableName} şeklinde değil ise (yani değişim olduysa)
          if (replacement !== `{${variableName}}`) {
            // Etiketin içeriğini değiştir
            label.textContent = replacement;
          }
        }
      });

      // İşlenmiş HTML'i geri döndür
      return doc.body.innerHTML;
    } catch (error) {
      console.error('HTML işleme hatası:', error);
      return html;
    }
  };

  const renderOutput = (output) => {
    if (!output) return null;

    // Output değerlerindeki HTML içinde değişkenleri değiştir
    if (output.sections) {
      output.sections.forEach((section) => {
        if (section.content) {
          if (Array.isArray(section.content)) {
            section.content.forEach((item, index) => {
              if (typeof item === 'object') {
                if (item.fullContent) {
                  item.fullContent = replacePromptVariables(String(item.fullContent), formData);
                }
                if (item.content) {
                  item.content = replacePromptVariables(String(item.content), formData);
                }
              } else if (typeof item === 'string') {
                section.content[index] = replacePromptVariables(item, formData);
              }
            });
          } else if (typeof section.content === 'object') {
            if (section.content.fullContent) {
              section.content.fullContent = replacePromptVariables(
                String(section.content.fullContent),
                formData
              );
            }
            if (section.content.content) {
              section.content.content = replacePromptVariables(
                String(section.content.content),
                formData
              );
            }
          } else if (typeof section.content === 'string') {
            section.content = replacePromptVariables(section.content, formData);
          }
        }
      });
    }

    const handleCopyAllContent = () => {
      // Formatlanmış içeriği topla
      const resultSections = document.querySelectorAll('.result-section');
      let formattedContent = '';

      resultSections.forEach((section) => {
        // Başlığı ekle (varsa)
        const title = section.querySelector('h6');
        if (title) {
          formattedContent += `# ${title.textContent.trim()}\n\n`;
        }

        // Section içindeki tüm metni al
        formattedContent += section.innerText;
        formattedContent += '\n\n';
      });

      // Panoya kopyala
      navigator.clipboard
        .writeText(formattedContent)
        .then(() => {
          setCopied(true);
          setTimeout(() => {
            setCopied(false);
          }, 2000);
        })
        .catch((err) => {
          console.error('Kopyalama hatası:', err);
        });
    };

    const formatMarkdownContent = (content, isHtml) => {
      if (!content) return null;

      // Eğer HTML içerikse, doğrudan HTML olarak göster
      if (isHtml) {
        let htmlContent = content[0];

        return (
          <div className="output-content">
            <HtmlRenderer
              content={htmlContent}
              className="markdown-paragraph"
              sanitize={true}
              formatCodeBlocks={true}
            />
          </div>
        );
      }

      // Eğer içerik bir dizi ise (ki öyle olacak), her bir öğeyi formatlayalım
      if (Array.isArray(content)) {
        return (
          <div className="output-content">
            {content.map((item, index) => {
              // Stream datasından gelen fullContent'i kullan
              let formattedItem = item;
              if (typeof item === 'object') {
                if (item.fullContent) {
                  formattedItem = item.fullContent;
                } else if (item.content) {
                  formattedItem = item.content;
                }
              }

              // HTML içindeki değişkenleri değiştir
              formattedItem = replacePromptVariables(String(formattedItem), formData);

              // HTML içindeki özel etiketleri işle
              try {
                formattedItem = processHtmlLabels(String(formattedItem));
              } catch (error) {
                console.error('HTML etiketlerini işleme hatası:', error);
              }

              // Markdown yapılarını manuel olarak dönüştürelim
              const formattedText = String(formattedItem)
                // Başlıklar
                .replace(/^#### (.*$)/gim, '<h4>$1</h4>')
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                // İtalik ve kalın
                .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/gim, '<em>$1</em>')
                // Listeler
                .replace(/^- (.*$)/gim, '<li>$1</li>')
                // Link
                .replace(
                  /\[(.*?)\]\((.*?)\)/gim,
                  '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>'
                )
                // Paragraflar
                .replace(/\n$/gim, '<br />')
                // --- işaretlerini yatay çizgiye dönüştür
                .replace(/^---$/gim, '<hr />');

              return (
                <div key={index} className="markdown-paragraph">
                  <HtmlRenderer content={formattedText} sanitize={true} formatCodeBlocks={true} />
                </div>
              );
            })}
          </div>
        );
      }

      // String içerik için aynı işlemi yapalım
      let formattedContent = content;
      if (typeof content === 'object') {
        if (content.fullContent) {
          formattedContent = content.fullContent;
        } else if (content.content) {
          formattedContent = content.content;
        }
      }

      // HTML içindeki değişkenleri değiştir
      formattedContent = replacePromptVariables(String(formattedContent), formData);

      // HTML içindeki özel etiketleri işle
      try {
        formattedContent = processHtmlLabels(String(formattedContent));
      } catch (error) {
        console.error('HTML etiketlerini işleme hatası:', error);
      }

      const formattedText = String(formattedContent)
        // Başlıklar
        .replace(/^#### (.*$)/gim, '<h4>$1</h4>')
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        // İtalik ve kalın
        .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/gim, '<em>$1</em>')
        // Listeler
        .replace(/^- (.*$)/gim, '<li>$1</li>')
        // Link
        .replace(
          /\[(.*?)\]\((.*?)\)/gim,
          '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>'
        )
        // Paragraflar
        .replace(/\n$/gim, '<br />')
        // --- işaretlerini yatay çizgiye dönüştür
        .replace(/^---$/gim, '<hr />');

      return (
        <div className="output-content">
          <HtmlRenderer
            content={formattedText}
            className="markdown-paragraph"
            sanitize={true}
            formatCodeBlocks={true}
          />
        </div>
      );
    };

    return (
      <div className="output-result">
        <div className="output-header">
          <Typography variant="h6" className="output-title">
            {output.title || t('common.result')}
          </Typography>
          <Tooltip title={copied ? t('common.copied') : t('common.copy')}>
            <IconButton
              className="copy-button"
              onClick={handleCopyAllContent}
              aria-label={t('common.copy')}
            >
              {copied ? <CheckIcon /> : <ContentCopyIcon />}
            </IconButton>
          </Tooltip>
        </div>

        {output.sections?.map((section, sectionIndex) => (
          <div key={sectionIndex} className="result-section">
            {section.title && (
              <Typography variant="h6" component="h6" sx={{ mb: 1, textTransform: 'uppercase' }}>
                {section.title}
              </Typography>
            )}
            {formatMarkdownContent(section.content, section.isHtml)}
          </div>
        ))}
      </div>
    );
  };

  // İleri butonuna tıklandığında
  const handleNext = () => {
    onNext();
  };

  return (
    <div className="workflow-steps-content">
      {steps.map((step, index) => {
        if (currentStep !== index) return null;

        const stepTitle = getStepTitle(step);
        const processedStepValue = step.value ? replacePromptVariables(step.value, formData) : '';

        return (
          <div
            key={index}
            className={`workflow-step ${index < currentStep ? 'past' : ''} ${index === currentStep ? 'active' : ''}`}
            data-name={step.name}
            data-type={step.type}
            data-step={index}
          >
            {step.type === 'input' && (
              <div className="input-container">
                <TextField
                  id={step.name}
                  name={step.name}
                  label={`${stepTitle}:`}
                  variant="outlined"
                  fullWidth
                  value={formData[step.name] || ''}
                  onChange={(e) => handleInputChange(e, step.name)}
                  margin="normal"
                  className="mui-workflow-input workflow-text-field"
                />
                <div className="next-button-container">
                  <Button variant="contained" color="primary" size="medium" onClick={handleNext}>
                    {t('common.workflows.run.next')}
                  </Button>
                </div>
              </div>
            )}

            {step.type === 'prompt' && (
              <div className="prompt-container">
                {isAutoProgressing ? (
                  <div className="loading-content">
                    <CircularProgress size={40} />
                    <Typography sx={{ mt: 2 }}>Generating {stepTitle}</Typography>
                  </div>
                ) : (
                  <div className="prompt-waiting">
                    <Typography variant="body1">Waiting for system response...</Typography>
                  </div>
                )}
              </div>
            )}

            {step.type === 'output' && (
              <div className="output-container">
                {stepOutputs[step.name] ? (
                  <div className="workflow-content">{renderOutput(stepOutputs[step.name])}</div>
                ) : processedStepValue ? (
                  <div className="processed-html-content">
                    <HtmlRenderer
                      content={processedStepValue.replace(
                        /<a\s+class="custom-label"[^>]*data-key="([^"]*)"[^>]*>({[^}]*})<\/a>/g,
                        '$2'
                      )}
                      sanitize={true}
                      formatCodeBlocks={true}
                      className="markdown-paragraph"
                    />
                  </div>
                ) : (
                  <div className="loading-content">
                    <CircularProgress size={40} />
                    <Typography sx={{ mt: 2 }}>Processing {stepTitle}</Typography>
                  </div>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

WorkflowContent.propTypes = {
  steps: PropTypes.array.isRequired,
  currentStep: PropTypes.number.isRequired,
  formData: PropTypes.object.isRequired,
  stepOutputs: PropTypes.object.isRequired,
  isAutoProgressing: PropTypes.bool.isRequired,
  onInputChange: PropTypes.func.isRequired,
  onNext: PropTypes.func.isRequired,
};

export default WorkflowContent;
