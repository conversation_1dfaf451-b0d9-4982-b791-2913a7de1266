export const getStepTitle = (step) => {
  if (!step) return '';

  // Prompt tipi ve label "Prompt" ise name kullan
  if (step.type === 'prompt' && step.label === 'Prompt' && step.name) {
    return step.name;
  }

  // Öncelikle label kullan, yoksa name'e bak
  if (step.label && step.label !== 'Prompt') {
    return step.label;
  }

  // Label yoksa name'i kullan
  if (step.name) {
    // Name'i formatlayarak g<PERSON> (snake_case -> Title Case)
    return step.name
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Tip bazlı fallback (hem label hem name yoksa)
  if (step.type === 'input') {
    return 'Input';
  } else if (step.type === 'prompt') {
    return 'Prompt';
  } else if (step.type === 'output') {
    return 'Output';
  }

  // Son çare
  return 'Step';
};
