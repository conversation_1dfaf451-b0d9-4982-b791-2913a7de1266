import { useState, useEffect, useCallback, useRef } from 'react';
import { Container, CircularProgress } from '@mui/material';
import { useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
  useGetWorkflowByIdQuery,
  useGetWorkflowByCdsApiQuery,
} from '@/redux/services/create-workflow';
import { useUseAzureOpenAIConnectorsMutation } from '@/redux/services/connectors-api';
import WorkflowSidebar from './components/WorkflowSidebar';
import WorkflowContent from './components/WorkflowContent';
import './WorkflowRun.scss';

const WorkflowRun = () => {
  const { workflowName } = useParams();
  const slug = workflowName?.split('/').pop();
  const { t } = useTranslation();

  // Oturum açmış kullanıcının userId'sini al
  const currentUserId = useSelector((state) => state.auth.user?._id);

  // Ana API isteği
  const {
    data: mainData,
    isLoading: isMainLoading,
    error: mainError,
  } = useGetWorkflowByIdQuery({ slug });

  // CDS API isteği - gerektiğinde onQueryStarted tarafından tetiklenecek
  const { data: cdsData, isLoading: isCdsLoading } = useGetWorkflowByCdsApiQuery(slug, {
    // Bu sorgu otomatik olarak çalışmayacak, sadece gerektiğinde tetiklenecek
    skip: true,
  });

  // İki API'den gelen verileri ve durumları birleştir
  const data = cdsData || mainData;
  const isLoading = isMainLoading || isCdsLoading;

  // Eğer veri varsa hata mesajını null yap, yoksa uygun hata mesajını göster
  const error =
    data && data.data
      ? null
      : mainError
        ? mainError?.data?.message || t('common.workflows.run.errorLoading')
        : null;

  // Hata gösterimi için zamanlayıcı ekliyoruz
  const [showErrorMessage, setShowErrorMessage] = useState(false);

  // Hata durumunda 3 saniye sonra hata mesajını göster
  useEffect(() => {
    let timer;
    if (!isLoading && !data && error) {
      timer = setTimeout(() => {
        setShowErrorMessage(true);
      }, 3000); // 3 saniye sonra hata mesajını göster
    } else {
      setShowErrorMessage(false);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isLoading, data, error]);

  const finalData = data;

  const [triggerAzureOpenAI] = useUseAzureOpenAIConnectorsMutation();

  const [formData, setFormData] = useState({});
  const [currentStep, setCurrentStep] = useState(0);
  const [isAutoProgressing, setIsAutoProgressing] = useState(false);
  const [stepOutputs, setStepOutputs] = useState({});
  const [promptResults, setPromptResults] = useState({});
  const [abortController, setAbortController] = useState(null);

  // Stream'den gelen içeriği toplamak için bir ref kullanıyoruz
  const contentRef = useRef('');

  // Anahtar normalizasyon fonksiyonu - özel karakterleri ve boşlukları işler
  const normalizeKey = (key) => {
    if (!key || typeof key !== 'string') return '';

    return key
      .toLowerCase()
      .replace(/[äöüßÄÖÜẞ]/g, (match) => {
        // Almanca karakterleri ASCII karakterlere dönüştür
        const mapping = {
          ä: 'ae',
          ö: 'oe',
          ü: 'ue',
          ß: 'ss',
          Ä: 'ae',
          Ö: 'oe',
          Ü: 'ue',
          ẞ: 'ss',
        };
        return mapping[match] || match;
      })
      .replace(/[-\s]+/g, '_') // Hem tireler hem de boşlukları alt çizgiye dönüştür
      .replace(/[^a-z0-9_]+/g, '_'); // Diğer özel karakterleri alt çizgiye dönüştür
  };

  const createInitialFormData = (workflowForm) => {
    const initialFormData = {};
    workflowForm?.forEach((step) => {
      if (step.type === 'input') {
        initialFormData[step.name] = step.value || '';

        const normalizedName = normalizeKey(step.name);
        if (normalizedName !== step.name) {
          initialFormData[normalizedName] = step.value || '';
        }

        // Label da ekleyerek erişim sağla
        if (step.label) {
          const normalizedLabel = normalizeKey(step.label);
          initialFormData[normalizedLabel] = step.value || '';
        }
      }
    });
    return initialFormData;
  };

  useEffect(() => {
    if (finalData?.data?.workflow_form) {
      setFormData(createInitialFormData(finalData.data.workflow_form));
    }
  }, [finalData]);

  const handleInputChange = (e, stepName) => {
    const { value } = e.target;

    const currentStep = finalData?.data?.workflow_form?.find((step) => step.name === stepName);

    setFormData((prev) => {
      const updates = {
        ...prev,
        [stepName]: value, // Original name
      };

      const normalizedName = normalizeKey(stepName);
      if (normalizedName !== stepName) {
        updates[normalizedName] = value;
      }

      if (currentStep?.label) {
        const normalizedLabel = normalizeKey(currentStep.label);
        updates[normalizedLabel] = value;
      }

      return updates;
    });
  };

  // Prompt içerisindeki değişkenleri değiştirme
  const replacePromptVariables = (promptText) => {
    if (!promptText) return '';

    // HTML içerisindeki değişkenleri bulmak için regex kullanıyoruz
    const regex = /{([^}]+)}/g;
    let cleanPromptText = promptText.replace(/<[^>]*>?/gm, '');

    return cleanPromptText.replace(regex, (match, key) => {
      // Değişken adını normalize et (küçük harf, boşlukları alt çizgi ile değiştir, özel karakterleri kaldır)
      const normalizedKey = normalizeKey(key);

      // Input değerleri için formData'dan al
      if (formData[normalizedKey]) return formData[normalizedKey];

      // Normalizasyon işe yaramazsa orijinal anahtarı dene
      if (formData[key]) return formData[key];

      // Önceki promptlardan gelen sonuçlar için promptResults'dan al
      if (promptResults[normalizedKey]) return promptResults[normalizedKey];

      // Normalizasyon işe yaramazsa orijinal anahtarı dene
      if (promptResults[key]) return promptResults[key];

      return match; // Eşleşme bulunamazsa değişken adını olduğu gibi bırak
    });
  };

  // API isteği gönderme
  const sendPromptToAPI = useCallback(
    async (step) => {
      try {
        setIsAutoProgressing(true);

        const controller = new AbortController();
        setAbortController(controller);

        // Referans değerini sıfırla
        contentRef.current = '';

        const promptValue = replacePromptVariables(step.value);

        // API isteği için parametreler
        const requestParams = {
          prompt: promptValue,
          temperature: parseFloat(finalData?.data?.temperature || 1),
          frequency: parseFloat(finalData?.data?.frequency_penalty || 0),
          presence: parseFloat(finalData?.data?.presence_penalty || 0),
          stream: false,
          html: true,
          abortController: controller,
          onUpdate: (chunk) => {
            try {
              // Stream modunda değilsek ve direkt JSON içeriği alıyorsak
              if (typeof chunk === 'string' && chunk.startsWith('data: ')) {
                const jsonData = chunk.slice(5).trim();
                try {
                  const parsed = JSON.parse(jsonData);
                  if (parsed.content) {
                    contentRef.current = parsed.content;
                  }
                } catch {
                  // JSON ayrıştırma hatası durumunda
                  contentRef.current = jsonData;
                }
              } else if (typeof chunk === 'string' && chunk.startsWith('{')) {
                // API'den doğrudan JSON nesnesi geliyorsa
                try {
                  const data = JSON.parse(chunk);
                  if (data.data && data.data.content) {
                    contentRef.current = data.data.content;
                  }
                } catch {
                  // JSON ayrıştırma hatası durumunda
                  contentRef.current = chunk;
                }
              } else if (chunk && typeof chunk === 'string') {
                // Direkt metin gelirse onu kullan
                contentRef.current = chunk.trim();
              }
            } catch {
              // Error processing chunk
            }
          },
        };

        // API isteği gönderme
        const response = await triggerAzureOpenAI(requestParams);

        if (response.error) {
          throw new Error(response.error.data || 'API request failed');
        }

        // Referanstan alınan içeriği kullan
        const processedContent = contentRef.current;

        // Sonuçları sakla - normalize edilmiş adı kullan
        const stepName = normalizeKey(step.name);
        setPromptResults((prev) => ({
          ...prev,
          [stepName]: processedContent,
        }));

        // API yanıtı olarak true geldiyse veya içerik HTML etiketleri içeriyorsa HTML içeriği olarak işaretle
        const containsHtmlTags = (text) => {
          if (!text || typeof text !== 'string') return false;

          // HTML etiketini daha kesin kontrol et - tam etiket formatı aranıyor
          const htmlTagPattern =
            /<\/?(?:div|p|span|h[1-6]|ul|ol|li|table|tr|td|th|section|article|header|footer|nav|strong|em|a|img|br|hr|code|pre)[^>]*>/i;
          return htmlTagPattern.test(text);
        };

        // İçeriği pre-processing'den geçir - genel HTML içeriği kontrolü
        let finalContent = processedContent;

        // HTML içeriği olup olmadığını kontrol et
        const isHtmlContent = finalData?.data?.html === true || containsHtmlTags(finalContent);

        // Çıktı formatını oluştur
        const formattedOutput = {
          sections: [
            {
              title: step.name,
              content: isHtmlContent
                ? // HTML içerikse bölme yapma, doğrudan kullan
                  [finalContent]
                : // Normal markdown içerik, paragraflar halinde böl
                  finalContent
                    .split('\n\n')
                    .map((item) => item.trim())
                    .filter((item) => item),
              isHtml: isHtmlContent,
            },
          ],
        };

        // Çıktıyı kaydet
        setStepOutputs((prev) => ({
          ...prev,
          [step.name]: formattedOutput,
        }));

        setIsAutoProgressing(false);
        // Sonraki adıma geç
        setCurrentStep((prev) => prev + 1);
      } catch {
        // Error sending prompt to API
        setIsAutoProgressing(false);
      }
    },
    [finalData, formData, promptResults, triggerAzureOpenAI]
  );

  // Output işleme
  const processOutput = useCallback(
    (outputStep) => {
      try {
        setIsAutoProgressing(true);

        // HTML içeriğini temizleme - bunun yerine sadece custom-label sınıfına sahip a taglarını işle
        let outputValue = outputStep.value;

        // HTML entity'leri decode et (ör. &auml; -> ä)
        const decodeHtmlEntities = (text) => {
          const textArea = document.createElement('textarea');
          textArea.innerHTML = text;
          return textArea.value;
        };

        // Önce custom-label içindeki data-key değerlerini normalize et
        outputValue = outputValue.replace(
          /<a\s+[^>]*class="custom-label"[^>]*data-key="([^"]*)"[^>]*>({[^}]*})<\/a>/g,
          (match, dataKey) => {
            // HTML entity'leri decode et
            const decodedKey = decodeHtmlEntities(dataKey);
            // Normalize edilmiş anahtarı oluştur
            const normalizedKey = normalizeKey(decodedKey);

            // data-key değerini normalize edilmiş değerle değiştir
            return match.replace(`data-key="${dataKey}"`, `data-key="${normalizedKey}"`);
          }
        );

        // Şimdi değişkenleri içerikle değiştir
        outputValue = outputValue.replace(
          /<span\s+[^>]*class="custom-label"[^>]*data-key="([^"]*)"[^>]*>({[^}]*})<\/span>/g,
          (match, dataKey, variableName) => {
            // Değişken içindeki adı çıkart (ör. {kundenengagement-taktiken} -> kundenengagement-taktiken)
            const variableNameWithoutBraces = variableName ? variableName.replace(/[{}]/g, '') : '';

            // Önce form verilerinden değeri bul (input değerleri için)
            if (formData[dataKey]) {
              return formData[dataKey];
            }

            // Önce normalize edilmiş anahtar ile promptResults'tan değeri bul
            if (promptResults[dataKey]) {
              return promptResults[dataKey];
            }

            // Değişken adını normalize et ve tekrar dene
            const normalizedVariableName = normalizeKey(variableNameWithoutBraces);
            if (promptResults[normalizedVariableName]) {
              return promptResults[normalizedVariableName];
            }

            // Aynı şekilde formData'dan da normalize edilmiş anahtarla kontrol et
            if (formData[normalizedVariableName]) {
              return formData[normalizedVariableName];
            }

            // Orijinal değişken adını dene
            if (promptResults[variableNameWithoutBraces]) {
              return promptResults[variableNameWithoutBraces];
            }

            // Orijinal değişken adını formData'da dene
            if (formData[variableNameWithoutBraces]) {
              return formData[variableNameWithoutBraces];
            }

            // Hiçbir eşleşme bulunamazsa orijinal değişken adını göster
            return variableName;
          }
        );

        // Aynı işlemi <a> etiketleri için de yapalım
        outputValue = outputValue.replace(
          /<a\s+[^>]*class="custom-label"[^>]*data-key="([^"]*)"[^>]*>({[^}]*})<\/a>/g,
          (match, dataKey, variableName) => {
            // Değişken içindeki adı çıkart (ör. {kundenengagement-taktiken} -> kundenengagement-taktiken)
            const variableNameWithoutBraces = variableName ? variableName.replace(/[{}]/g, '') : '';

            // Önce form verilerinden değeri bul (input değerleri için)
            if (formData[dataKey]) {
              return formData[dataKey];
            }

            // Önce normalize edilmiş anahtar ile promptResults'tan değeri bul
            if (promptResults[dataKey]) {
              return promptResults[dataKey];
            }

            // Değişken adını normalize et ve tekrar dene
            const normalizedVariableName = normalizeKey(variableNameWithoutBraces);
            if (promptResults[normalizedVariableName]) {
              return promptResults[normalizedVariableName];
            }

            // Aynı şekilde formData'dan da normalize edilmiş anahtarla kontrol et
            if (formData[normalizedVariableName]) {
              return formData[normalizedVariableName];
            }

            // Orijinal değişken adını dene
            if (promptResults[variableNameWithoutBraces]) {
              return promptResults[variableNameWithoutBraces];
            }

            // Orijinal değişken adını formData'da dene
            if (formData[variableNameWithoutBraces]) {
              return formData[variableNameWithoutBraces];
            }

            // Hiçbir eşleşme bulunamazsa orijinal değişken adını göster
            return variableName;
          }
        );

        // Kalan değişkenleri değiştir (a etiketleri dışında kalan {variable} şeklindeki değişkenler)
        const regex = /{([^}]+)}/g;
        outputValue = outputValue.replace(regex, (match, key) => {
          // HTML entity'leri decode et
          const decodedKey = decodeHtmlEntities(key);

          // Normalize edilmiş anahtar
          const normalizedKey = normalizeKey(decodedKey);

          // Önce formData'dan kontrol et (Text input değerleri)
          if (formData[normalizedKey]) return formData[normalizedKey];
          if (formData[key]) return formData[key];
          if (formData[decodedKey]) return formData[decodedKey];

          // Önceki promptlardan gelen sonuçlar
          if (promptResults[normalizedKey]) return promptResults[normalizedKey];

          // Normalizasyon işe yaramazsa orijinal anahtarı dene
          if (promptResults[key]) return promptResults[key];

          // Orijinal decode edilmiş anahtarı dene
          if (promptResults[decodedKey]) return promptResults[decodedKey];

          return match; // Eşleşme bulunamazsa değişken adını olduğu gibi bırak
        });

        // Daha kapsamlı temizleme işlemi - <span class="custom-label"> elementlerini temizle
        // İçinde herhangi bir içerik olmayan veya sadece boşluk olan custom-label span'lerini kaldır
        outputValue = outputValue.replace(
          /<span\s+[^>]*class="custom-label"[^>]*>(\s*)<\/span>/g,
          ''
        );

        // İçinde sadece data-* özellikleri olan ama görünür içerik olmayan custom-label span'lerini kaldır
        outputValue = outputValue.replace(
          /<p>\s*<span\s+[^>]*class="custom-label"[^>]*>[^<]*<\/span>\s*<\/p>/g,
          ''
        );

        // İçi boş p taglarını kaldır (tüm kombinasyonlar)
        outputValue = outputValue.replace(/<p[^>]*>\s*<\/p>/g, '');

        // Boş paragrafları temizle (tekrar kontrolü)
        outputValue = outputValue.replace(/<p[^>]*>(\s|&nbsp;)*<\/p>/g, '');

        // Ardışık boş paragrafların temizliği için
        outputValue = outputValue.replace(
          /(<br\s*\/?>|\s|&nbsp;)*<p[^>]*>(\s|&nbsp;)*<\/p>(\s|&nbsp;)*/g,
          ''
        );

        // custom-label sınıfını içeren tüm span etiketlerini temizle
        // Bu son çare olarak kullanılabilir
        if (outputValue.includes('<span class="custom-label"')) {
          const parser = new DOMParser();
          const doc = parser.parseFromString(outputValue, 'text/html');

          // custom-label sınıfına sahip tüm span'leri seç
          const labelSpans = doc.querySelectorAll('span.custom-label');

          // Bu span'leri içeren tüm p etiketlerini kaldır
          labelSpans.forEach((span) => {
            const parentP = span.closest('p');
            if (parentP && parentP.textContent.trim() === '') {
              parentP.parentNode?.removeChild(parentP);
            } else {
              // Sadece span'i kaldır
              span.parentNode?.removeChild(span);
            }
          });

          // Temizlenmiş HTML'i al
          outputValue = doc.body.innerHTML;
        }

        // Çıktıyı kaydet - tüm içeriği tek bir bölüm olarak işle
        const output = {
          sections: [
            {
              content: [outputValue], // HTML içeriği olarak al, bölme
              isHtml: true, // HTML içeriği olarak işaretle
            },
          ],
        };

        // Çıktıyı kaydet
        setStepOutputs((prev) => ({
          ...prev,
          [outputStep.name]: output,
        }));

        setIsAutoProgressing(false);
      } catch (error) {
        console.error('Error processing output:', error);
        // Error processing output
        setIsAutoProgressing(false);
      }
    },
    [promptResults, formData]
  );

  const handleNext = useCallback(async () => {
    if (currentStep < finalData?.data?.workflow_form.length - 1) {
      const currentElement = finalData?.data?.workflow_form[currentStep];
      const nextElement = finalData?.data?.workflow_form[currentStep + 1];

      if (currentElement.type === 'input') {
        setCurrentStep((prev) => prev + 1);

        if (nextElement.type === 'prompt') {
          // Bir sonraki adım prompt ise, otomatik olarak işlenecek
        }
      }
    }
  }, [currentStep, finalData?.data?.workflow_form]);

  // Prompt adımlarını işlemek için useEffect
  useEffect(() => {
    const currentElement = finalData?.data?.workflow_form?.[currentStep];
    if (currentElement?.type === 'prompt') {
      sendPromptToAPI(currentElement);
    } else if (currentElement?.type === 'output') {
      processOutput(currentElement);
    }
  }, [currentStep, finalData?.data?.workflow_form, sendPromptToAPI, processOutput]);

  // Component unmount olduğunda veya adım değiştiğinde abort controller'ı temizle
  useEffect(() => {
    return () => {
      if (abortController) {
        abortController.abort();
      }
    };
  }, [currentStep, abortController]);

  const handleRestart = () => {
    // State'leri başlangıç değerlerine sıfırla
    setCurrentStep(0);
    setIsAutoProgressing(false);
    setStepOutputs({});
    setPromptResults({});
    setFormData(createInitialFormData(finalData?.data?.workflow_form));
  };

  // Workflow'a ait userId ve oturum açmış kullanıcının userId'sine göre edit butonunu gösterip göstermeme kontrolü
  const showEditButton = () => {
    if (!data || !data.data) return false;

    // Eğer workflow'un userId'si yoksa edit butonunu gösterme
    if (!data.data.userId) return false;

    // Eğer workflow'un userId'si varsa ve oturum açmış kullanıcının userId'si ile eşleşiyorsa edit butonunu göster
    return data.data.userId === currentUserId;
  };

  return (
    <div className="workflow-run-container">
      <Container maxWidth="lg" className="workflow-run-content">
        {isLoading ? (
          <div className="loading-wrapper">
            <CircularProgress />
          </div>
        ) : showErrorMessage && error ? (
          <Container className="error-container">
            <h1 className="error-message">
              {t('common.workflows.run.errorLoading')}: {error}
            </h1>
          </Container>
        ) : (
          <div className="workflow-run-inner">
            {/* Left Column - Sidebar */}
            <div className="sidebar-container">
              <div className="sidebar-sticky-wrapper">
                <WorkflowSidebar
                  title={data?.data?.title || 'Workflow'}
                  currentStep={currentStep}
                  steps={data?.data?.workflow_form || []}
                  stepOutputs={stepOutputs}
                  workflowId={data?.data?._id}
                  isAutoProgressing={isAutoProgressing}
                  onRestart={handleRestart}
                  showEditButton={showEditButton()}
                />
              </div>
            </div>

            {/* Right Column - Content */}
            <div className="workflow-content-container">
              <WorkflowContent
                currentStep={currentStep}
                steps={finalData?.data?.workflow_form || []}
                formData={formData}
                onInputChange={handleInputChange}
                onNext={handleNext}
                onRestart={handleRestart}
                stepOutputs={stepOutputs}
                isAutoProgressing={isAutoProgressing}
              />
            </div>
          </div>
        )}
      </Container>
    </div>
  );
};

export default WorkflowRun;
