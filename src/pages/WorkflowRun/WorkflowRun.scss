.workflow-run {
  padding: 2rem 0;

  .workflow-content {
    background-color: #fff;
    box-shadow: none;
    width: 100%;
  }

  .workflow-textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 1rem;
    resize: vertical;
    min-height: 120px;

    &:focus {
      outline: none;
      border-color: #2196f3;
    }
  }

  .output-section {
    min-height: 200px;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
  }
}

.workflow-run-container {
  padding: 1rem 0;
  background-color: #f9fafb;
  min-height: calc(100vh - 64px);
}

.workflow-run-content {
  padding: 0;
}

.workflow-run-inner {
  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
}

/* Sidebar container için gerekli stiller */
.sidebar-container {
  position: relative;
  height: auto;
  min-height: 100%;
  width: 25%;
}

.sidebar-sticky-wrapper {
  position: sticky;
  top: 16px;
  height: auto;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.workflow-title {
  display: flex;
  flex-direction: column;
  background: #fff;
  padding: 1.25rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e0e0e0;
  overflow-y: auto;

  .workflow-title-inner {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;

    .app-icon {
      width: 52px;
      height: 52px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #ECF3FF;
      border-radius: 8px;
    }

    h1 {
      margin: 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #000;
    }
  }

  .action-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #E5E7EB;
  }

  .sidebar-button {
    display: flex;
    align-items: center;
    gap: 0.35rem;
    color: #6B7280;
    text-decoration: none;
    font-size: 0.75rem;
    padding: 0.35rem 0.5rem;
    border-radius: 4px;
    background-color: #F9FAFB;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      color: #2563EB;
      background-color: #F3F4F6;
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }
  }

  .workflow-steps {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      color: #6B7280;
      padding: 0.5rem 0.5rem;
      font-weight: 500;
      border-radius: 4px;
      transition: all 0.2s ease;
      margin-bottom: 0.35rem;

      .step-number {
        width: 24px;
        height: 24px;
        min-width: 24px;
        border-radius: 50%;
        background-color: #F3F4F6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.5rem;
        font-size: 0.825rem;
        transition: background-color 0.3s ease;
        color: #6B7280;
      }

      &.completed {
        color: #10B981;
        
        .step-number {
          background-color: #D1FAE5;
          color: #10B981;
        }
      }

      &.active {
        color: #3B82F6;
        background-color: #EFF6FF;

        .step-number {
          background-color: #3B82F6;
          color: white;
        }
      }

      &.active[data-step="5"] {
        .step-number {
          background-color: #3B82F6;
          color: white;
          
          .css-yo767a-MuiSvgIcon-root {
            fill: white;
          }
        }
      }

      &.processing {
        .step-number {
          background-color: #3B82F6;

          .rotating-icon {
            animation: rotating 1.5s linear infinite;
            color: white;
          }
        }
      }
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.workflow-steps-content {
  width: 100%;
  padding: 20px;
  display: flex;
  align-items: center;
  background-color: #fafafa;
  background-image: linear-gradient(rgba(80, 80, 80, 0.02) 1px, transparent 1px), 
    linear-gradient(90deg, rgba(80, 80, 80, 0.02) 1px, transparent 1px), 
    linear-gradient(rgba(80, 80, 80, 0.01) 0.5px, transparent 0.5px), 
    linear-gradient(90deg, rgba(80, 80, 80, 0.01) 0.5px, transparent 0.5px);
  background-size: 20px 20px, 20px 20px, 10px 10px, 10px 10px;
  background-position: -1px -1px, -1px -1px, -1px -1px, -1px -1px;
    

  .workflow-step {
    margin-bottom: 1rem;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    overflow: hidden;
    

    label {
      font-weight: 500;
      color: #333;
      display: block;
      margin-bottom: 10px;
      align-self: flex-start;
      width: 100%;
    }
  }

  .loading-content {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .loading-dots {
    display: inline-block;
    &:after {
      content: '...';
      animation: dots 1.5s steps(4, end) infinite;
    }
  }

  .next-button-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    width: 100%;

    button {
      padding: 10px 24px;
    }
  }
}

@keyframes dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60% { content: '...'; }
  80%, 100% { content: ''; }
}

.workflow-input {
  width: 80%;
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #2196f3;
  }
}

.result-content {
  text-align: left;
  width: 100%;
  
  .result-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    
    h5 {
      margin-bottom: 0;
    }
    
    .copy-button {
      background-color: rgba(59, 130, 246, 0.1);
      border-radius: 4px;
      transition: all 0.2s ease;
      
      &:hover {
        background-color: rgba(59, 130, 246, 0.2);
      }
    }
  }
  
  h2 {
    color: #2196f3;
    margin-bottom: 1rem;
  }

  h3 {
    color: #333;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
  }

  em {
    color: #666;
    font-size: 0.9rem;
  }

  ul {
    margin: 1rem 0;
    padding-left: 1.5rem;

    li {
      margin-bottom: 0.5rem;
      color: #444;
    }
  }

  .result-section {
    margin-bottom: 2rem;
  }
}

.workflow-content-container {
  width: 75%;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  padding: 20px;
}

.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.error-message {
  color: #d32f2f;
  text-align: center;
  padding: 2rem;
  font-weight: 500;
}

.mui-workflow-input {
  margin-bottom: 1rem;
  width: 100%;
  
  .MuiInputBase-input {
    font-size: 16px !important;
  }
  
  .MuiOutlinedInput-notchedOutline {
    legend {
      font-size: 0.85rem;
    }
  }
}

.input-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.output-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

.prompt-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.prompt-waiting {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 2rem;
}

// Responsive düzenlemeler
@media (max-width: 992px) {
  .workflow-run-inner {
    flex-direction: column;
  }
  
  .sidebar-container,
  .workflow-content-container {
    width: 100%;
  }
  
  .sidebar-container {
    margin-bottom: 1.5rem;
  }
}

.workflow-text-field {
  .MuiOutlinedInput-root {
    border-radius: 6px;
    
    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      border-color: #3B82F6;
      border-width: 2px;
    }
  }
  
  .MuiInputLabel-root {
    font-weight: 500;
    
    &.Mui-focused {
      color: #3B82F6;
    }
  }
}

.output-result {
  text-align: left;
  width: 100%;
  
  .output-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-left: 1rem;
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    
    &:hover {
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
      border-color: #cbd5e1;
    }
    
    .output-title {
      margin-bottom: 0;
      color: #1e293b;
      font-weight: 600;
    }
    
    .copy-button {
      background-color: #f8fafc;
      border-radius: 6px;
      transition: all 0.2s ease;
      padding: 8px;
      
      &:hover {
        background-color: #f1f5f9;
        color: #3b82f6;
      }
    }
  }
  
  h2 {
    color: #2196f3;
    margin-bottom: 1rem;
  }

  h3 {
    color: #333;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
  }

  em {
    color: #666;
    font-size: 0.9rem;
  }

  ul {
    margin: 1rem 0;
    padding-left: 1.5rem;

    li {
      margin-bottom: 0.5rem;
      color: #444;
    }
  }

  .result-section {
    margin-bottom: 2rem;
  }
} 