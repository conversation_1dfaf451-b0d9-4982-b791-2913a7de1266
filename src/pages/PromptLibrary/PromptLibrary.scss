@use '../../styles/abstracts/variables' as *;

.prompt-library {
  &__tabs {
    margin-bottom: $spacing-4;
    border-bottom: 1px solid $border-color;

    .MuiTabs-root {
      min-height: 48px;
    }

    .MuiTab-root {
      text-transform: none;
      font-weight: $font-weight-medium;
      min-height: 48px;
      padding: $spacing-2 $spacing-3;
    }
  }

  &__filters {
    background-color: $bg-paper;
    padding: $spacing-4;
    border-radius: $border-radius-md;
    box-shadow: $shadow-sm;
    margin-bottom: $spacing-4;

    .MuiFormControl-root {
      flex: 1;
    }
  }

  &__cards {
    margin-top: $spacing-4;
    margin-bottom: 2rem;
  }

  // Sayfalama stilleri
  .MuiPagination-root {
    .MuiPaginationItem-root {
      &.Mui-selected {
        background-color: var(--primary-color);
        color: white;
        
        &:hover {
          background-color: var(--primary-color-dark);
        }
      }
    }
  }
}

.prompt-modal {
  &__content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  &__prompt {
    background-color: $bg-default;
    padding: 24px;
    border-radius: $border-radius-md;

    &-box {
      background-color: $bg-paper;
      padding: 24px;
      border-radius: $border-radius-md;
      border: 1px solid $border-color;
    }
  }

  &__make-it-your-own {
    h2 {
      font-size: $font-size-lg;
      font-weight: $font-weight-bold;
      margin-bottom: $spacing-3;
      color: $text-primary;
    }

    b {
      color: $text-primary;
      display: block;
      margin-top: $spacing-3;
      margin-bottom: $spacing-1;
    }
  }
} 