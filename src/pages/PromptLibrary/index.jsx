import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  Container, 
  Grid, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem,
  Tabs,
  Tab,
  Box,
  Pagination,
  CircularProgress,
  Alert,
  Typography
} from '@mui/material';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader.jsx';
import PromptCard from '../../components/PromptCard/PromptCard';
import PromptModal from '../../components/PromptModal/PromptModal';
import { 
  providers, 
  getAppsForProvider,
  getProviderUrl,
  getProviderFromUrl,
  getAppUrl,
  getAppFromUrl
} from '../../mockData/promptLibraryData';
import { useGetPromptsQuery } from '../../redux/services/prompt-library-api';
import { useGetUserShortcutsQuery } from '../../redux/services/shortcuts-api';
import { selectCurrentUser } from '../../redux/features/auth/authSlice';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import './PromptLibrary.scss';

// Statik fonksiyon listesi
const functions = [
  { value: 'Human Resources', label: 'Human Resources' },
  { value: 'Marketing', label: 'Marketing' },
  { value: 'Communications', label: 'Communications' },
  { value: 'Finance', label: 'Finance' },
  { value: 'Sales', label: 'Sales' },
  { value: 'Information Technology', label: 'Information Technology' },
  { value: 'Customer Service', label: 'Customer Service' },
  { value: 'Legal', label: 'Legal' }
];

// Tüm uygulamaları almak için yardımcı fonksiyon
const getAllApps = () => {
  const allApps = [];
  
  // Her sağlayıcı için uygulamaları toplama
  providers.forEach(provider => {
    const providerApps = getAppsForProvider(provider.value);
    
    // Yeni uygulamaları ekle, duplicate'leri önle
    providerApps.forEach(app => {
      if (!allApps.some(existingApp => existingApp.value === app.value)) {
        allApps.push(app);
      }
    });
  });
  
  return allApps;
};

const PromptLibraryPage = () => {
  const navigate = useNavigate();
  const { provider: urlProvider, app: urlApp, function: urlFunction } = useParams();
  const { t, i18n } = useTranslation();
  const [selectedProvider, setSelectedProvider] = useState('all');
  const [selectedApp, setSelectedApp] = useState('all');
  const [selectedFunction, setSelectedFunction] = useState('all');
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedPrompt, setSelectedPrompt] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [page, setPage] = useState(1);
  const itemsPerPage = 9;
  const [availableApps, setAvailableApps] = useState([]);
  const currentUser = useSelector(selectCurrentUser);
  const userId = currentUser ? (currentUser._id || currentUser.id) : null;
  const [favoritePrompts, setFavoritePrompts] = useState([]);
  const [useFallbackLanguage, setUseFallbackLanguage] = useState(false);
  const [promptsData, setPromptsData] = useState(null);

  // API'dan seçilen dildeki promptları çekme
  const { 
    data: currentLanguagePromptsData, 
    error: currentLanguagePromptsError, 
    isLoading: currentLanguagePromptsLoading, 
    isFetching: currentLanguagePromptsFetching 
  } = useGetPromptsQuery({
    page,
    limit: itemsPerPage,
    provider: selectedProvider,
    app: selectedApp,
    function_label: selectedFunction,
    language: i18n.language
  }, { skip: selectedTab === 1 });

  // Fallback için İngilizce promptları çekme
  const { 
    data: fallbackPromptsData, 
    isLoading: fallbackPromptsLoading, 
    isFetching: fallbackPromptsFetching 
  } = useGetPromptsQuery({
    page,
    limit: itemsPerPage,
    provider: selectedProvider,
    app: selectedApp,
    function_label: selectedFunction,
    language: 'en'
  }, { skip: selectedTab === 1 || !useFallbackLanguage });

  // Mevcut dilde prompt yoksa İngilizce promptlara geç
  useEffect(() => {
    if (selectedTab === 0) {
      if (currentLanguagePromptsData) {
        if (currentLanguagePromptsData.prompts.length === 0 && i18n.language !== 'en') {
          setUseFallbackLanguage(true);
        } else {
          setUseFallbackLanguage(false);
          setPromptsData(currentLanguagePromptsData);
        }
      }
    }
  }, [currentLanguagePromptsData, i18n.language, selectedTab]);

  // Fallback dilde veri geldiğinde state'i güncelle
  useEffect(() => {
    if (useFallbackLanguage && fallbackPromptsData) {
      setPromptsData(fallbackPromptsData);
    }
  }, [fallbackPromptsData, useFallbackLanguage]);

  // Favori promptları çekme
  const { 
    data: userShortcutsResponse, 
    error: userShortcutsError, 
    isLoading: userShortcutsLoading
  } = useGetUserShortcutsQuery({
    userId: userId,
    shortcutType: 'prompt'
  }, { skip: !userId || selectedTab === 0 });

  // Favori promptların detaylarını ayarlama
  useEffect(() => {
    if (userShortcutsResponse && Array.isArray(userShortcutsResponse) && userShortcutsResponse.length > 0 && selectedTab === 1) {
      const favoritePromptsList = userShortcutsResponse.map(shortcut => {
        // apiData içindeki favori prompt bilgilerini alıyoruz
        const promptData = shortcut.apiData || {};
        
        return {
          _id: shortcut.shortcutID,
          DisplayText: promptData.DisplayText || 'Prompt Title',
          DisplayDescription: promptData.DisplayDescription || 'Description not available',
          ProviderName: promptData.ProviderName || 'Unknown Provider',
          Products: promptData.Products || [],
          Function: promptData.Function || promptData.function || [],
          showOnWork: promptData.showOnWork || false,
          Title: promptData.Title || promptData.DisplayText || 'Prompt Title' // Title alanını da ekleyelim
        };
      });
      setFavoritePrompts(favoritePromptsList);
    } else if (selectedTab === 1) {
      setFavoritePrompts([]);
    }
  }, [userShortcutsResponse, selectedTab]);

  // Sayfa yüklendiğinde ve URL parametreleri değiştiğinde çalışır
  useEffect(() => {
    const updateStates = async () => {
      if (urlProvider) {
        const provider = getProviderFromUrl(urlProvider);
        setSelectedProvider(provider);
        
        // Eğer belirli bir provider seçilmişse, sadece o provider'ın app'lerini göster
        if (provider !== 'all') {
          const apps = getAppsForProvider(provider);
          setAvailableApps(apps);
        } else {
          // Provider 'all' ise tüm app'leri göster
          setAvailableApps(getAllApps());
        }
        
        if (urlApp) {
          const app = getAppFromUrl(provider, urlApp);
          setSelectedApp(app);
          
          if (urlFunction) {
            // URL'den gelen fonksiyonu düzeltme - tireleri boşluklara çevir
            const normalizedUrlFunction = urlFunction.toLowerCase().replace(/-/g, ' ');
            
            // Düzeltilmiş fonksiyonu bul
            const foundFunction = functions.find(f => f.value.toLowerCase() === normalizedUrlFunction);
            if (foundFunction) {
              setSelectedFunction(foundFunction.value);
            }
          }
        }
      } else {
        setSelectedProvider('all');
        setSelectedApp('all');
        setSelectedFunction('all');
        // Provider seçilmediğinde de tüm app'leri göster
        setAvailableApps(getAllApps());
      }
    };

    updateStates();
  }, [urlProvider, urlApp, urlFunction]);

  const handleProviderChange = (event) => {
    const value = event.target.value;
    setSelectedProvider(value);
    setSelectedApp('all');
    setSelectedFunction('all');
    setPage(1);

    // Provider değiştiğinde uygun app listesini ayarla
    if (value !== 'all') {
      const apps = getAppsForProvider(value);
      setAvailableApps(apps);
    } else {
      // 'All' seçildiğinde tüm app'leri göster
      setAvailableApps(getAllApps());
    }

    const providerUrl = getProviderUrl(value);
    if (providerUrl) {
      navigate(`/prompt-library/${providerUrl}`);
    } else {
      navigate('/prompt-library');
    }
  };

  const handleAppChange = (event) => {
    const value = event.target.value;
    setSelectedApp(value);
    setPage(1);

    const providerUrl = getProviderUrl(selectedProvider);
    const appUrl = getAppUrl(value);
    
    if (providerUrl && appUrl) {
      navigate(`/prompt-library/${providerUrl}/${appUrl}`);
    } else if (providerUrl) {
      navigate(`/prompt-library/${providerUrl}`);
    } else {
      navigate('/prompt-library');
    }
  };

  const handleFunctionChange = (event) => {
    const value = event.target.value;
    setSelectedFunction(value);
    setPage(1);

    const providerUrl = getProviderUrl(selectedProvider);
    const appUrl = getAppUrl(selectedApp);
    
    if (providerUrl && appUrl && value !== 'all') {
      // URL için fonksiyon adını küçük harfe çevir ve boşlukları tire ile değiştir
      const functionUrl = value.toLowerCase().replace(/\s+/g, '-');
      navigate(`/prompt-library/${providerUrl}/${appUrl}/${functionUrl}`);
    } else if (providerUrl && appUrl) {
      navigate(`/prompt-library/${providerUrl}/${appUrl}`);
    } else if (providerUrl) {
      navigate(`/prompt-library/${providerUrl}`);
    } else {
      navigate('/prompt-library');
    }
  };

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
    setPage(1); // Sekme değiştiğinde sayfa numarasını sıfırla
  };

  const handleCardClick = (prompt) => {
    setSelectedPrompt(prompt);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPrompt(null);
  };

  const handlePageChange = (event, value) => {
    setPage(value);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const renderTitle = (text) => {
    const parts = text.split(/(<placeholder>.*?<\/placeholder>)/g);
    return (
      <Typography variant="body1" className="prompt-card__title">
        {parts.map((part, index) => {
          if (part.startsWith('<placeholder>') && part.endsWith('</placeholder>')) {
            const content = part.replace('<placeholder>', '').replace('</placeholder>', '').trim();
            return (
              <Box
                component="span"
                key={index}
                className="prompt-card__title-highlight"
              >
                {content ? `{${content}}` : '<placeholder>'}
              </Box>
            );
          }
          return part;
        })}
      </Typography>
    );
  };

  // Favoriler tab'ında eğer favoriler boşsa mesaj gösterme örneği
  const renderNoFavoritesMessage = () => {
    if (selectedTab === 1 && favoritePrompts.length === 0 && !userShortcutsLoading) {
      return (
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          width: '100%',
          minHeight: '200px',
          textAlign: 'center',
          p: 3
        }}>
          <Typography variant="body1">
            {t('promptLibrary.noFavorites')}
          </Typography>
        </Box>
      );
    }
    return null;
  };

  // Arayüz yükleniyor durumu kontrolü
  const isLoading = selectedTab === 0 && (currentLanguagePromptsLoading || (useFallbackLanguage && fallbackPromptsLoading));
  const isFetching = selectedTab === 0 && (currentLanguagePromptsFetching || (useFallbackLanguage && fallbackPromptsFetching));
  const hasError = selectedTab === 0 && currentLanguagePromptsError && (!useFallbackLanguage || !fallbackPromptsData);

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('promptLibrary.title')}
            description={t('promptLibrary.description')}
          />
        </Grid>

        <Grid item xs={12}>
          <Box className="prompt-library__tabs">
            <Tabs value={selectedTab} onChange={handleTabChange}>
              <Tab label={t('promptLibrary.allPrompts')} />
              <Tab label={t('promptLibrary.yourFavoritePrompts')} />
            </Tabs>
          </Box>

          {selectedTab === 0 && (
            <Box className="prompt-library__filters">
              <Box sx={{ display: 'flex', gap: 2 }}>
                <FormControl fullWidth>
                  <InputLabel>{t('promptLibrary.selectProvider')}</InputLabel>
                  <Select
                    value={selectedProvider || 'all'}
                    label={t('promptLibrary.selectProvider')}
                    onChange={handleProviderChange}
                  >
                    <MenuItem value="all">{t('promptLibrary.allProviders')}</MenuItem>
                    {providers.map((provider) => (
                      <MenuItem key={provider.value} value={provider.value}>
                        {provider.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <FormControl fullWidth>
                  <InputLabel>{t('promptLibrary.selectApp')}</InputLabel>
                  <Select
                    value={selectedApp || 'all'}
                    label={t('promptLibrary.selectApp')}
                    onChange={handleAppChange}
                  >
                    <MenuItem value="all">{t('promptLibrary.allApps')}</MenuItem>
                    {availableApps.map((app) => (
                      <MenuItem key={app.value} value={app.value}>
                        {app.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <FormControl fullWidth>
                  <InputLabel>{t('promptLibrary.selectFunction')}</InputLabel>
                  <Select
                    value={selectedFunction || 'all'}
                    label={t('promptLibrary.selectFunction')}
                    onChange={handleFunctionChange}
                  >
                    <MenuItem value="all">{t('promptLibrary.allFunctions')}</MenuItem>
                    {functions.map((func) => (
                      <MenuItem key={func.value} value={func.value}>
                        {func.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Box>
          )}

          {selectedTab === 0 ? (
            // Tüm promptları göster
            hasError ? (
              <Alert severity="error" sx={{ mt: 2 }}>
                {t('promptLibrary.errorLoadingPrompts')}
              </Alert>
            ) : isLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                
                {!promptsData || promptsData.prompts.length === 0 ? (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    {t('promptLibrary.noPromptsFound')}
                  </Alert>
                ) : (
                  <Grid container spacing={3} className="prompt-library__cards">
                    {promptsData.prompts.map((prompt) => (
                      <Grid item xs={12} sm={6} md={4} key={prompt._id}>
                        <PromptCard 
                          prompt={{
                            id: prompt._id,
                            title: prompt.DisplayText,
                            description: prompt.DisplayText,
                            makeItYourOwn: prompt.DisplayDescription,
                            createdBy: prompt.ProviderName,
                            app: prompt.Products,
                            function: prompt.Function || prompt.function || [],
                            tryInMicrosoft: prompt.showOnWork
                          }}
                          onCardClick={() => handleCardClick({
                            ...prompt,
                            id: prompt._id,
                            Title: prompt.DisplayText,
                            description: prompt.DisplayText,
                            makeItYourOwn: prompt.DisplayDescription,
                            createdBy: prompt.ProviderName,
                            app: prompt.Products,
                            function: prompt.Function || prompt.function || [],
                            tryInMicrosoft: prompt.showOnWork
                          })}
                        />
                      </Grid>
                    ))}
                  </Grid>
                )}

                {promptsData?.pagination.totalPages > 1 && (
                  <Box sx={{ 
                    display: 'flex', 
                    justifyContent: 'center', 
                    mt: 4, 
                    mb: 4,
                    position: 'relative' 
                  }}>
                    {isFetching && (
                      <CircularProgress 
                        size={24} 
                        sx={{ 
                          position: 'absolute', 
                          top: '50%', 
                          left: '50%', 
                          marginTop: '-12px',
                          marginLeft: '-12px'
                        }} 
                      />
                    )}
                    <Pagination 
                      count={promptsData.pagination.totalPages}
                      page={page}
                      onChange={handlePageChange}
                      color="primary"
                      size="large"
                      showFirstButton 
                      showLastButton
                      disabled={isFetching}
                    />
                  </Box>
                )}
              </>
            )
          ) : (
            // Favori promptları göster
            userShortcutsError ? (
              <Alert severity="error" sx={{ mt: 2 }}>
                {t('promptLibrary.errorLoadingFavorites')}
              </Alert>
            ) : userShortcutsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                <CircularProgress />
              </Box>
            ) : favoritePrompts.length === 0 ? (
              <Alert severity="info" sx={{ mt: 2 }}>
                {t('promptLibrary.noFavorites')}
              </Alert>
            ) : (
              <Grid container spacing={3} className="prompt-library__cards">
                {favoritePrompts.map((prompt) => (
                  <Grid item xs={12} sm={6} md={4} key={prompt._id}>
                    <PromptCard 
                      prompt={{
                        id: prompt._id,
                        title: prompt.DisplayText,
                        description: prompt.DisplayText,
                        makeItYourOwn: prompt.DisplayDescription,
                        createdBy: prompt.ProviderName,
                        app: prompt.Products,
                        function: prompt.Function || [],
                        tryInMicrosoft: prompt.showOnWork
                      }}
                      onCardClick={() => handleCardClick({
                        ...prompt,
                        id: prompt._id,
                        Title: prompt.Title || prompt.DisplayText,
                        description: prompt.DisplayText,
                        makeItYourOwn: prompt.DisplayDescription,
                        createdBy: prompt.ProviderName,
                        app: prompt.Products,
                        function: prompt.Function || [],
                        tryInMicrosoft: prompt.showOnWork
                      })}
                    />
                  </Grid>
                ))}
              </Grid>
            )
          )}
        </Grid>
      </Grid>

      <PromptModal
        open={isModalOpen}
        onClose={handleCloseModal}
        prompt={selectedPrompt}
      />
    </Container>
  );
};

export default PromptLibraryPage;