import React, { useState, useEffect, useMemo } from 'react';
import { Container, Grid, Typography, Box, AppBar, Toolbar, CircularProgress } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import WhiteContainer from '../../components/WhiteContainer/WhiteContainer.jsx';
import Carousel from '../../components/Carousel/Carousel.jsx';
import CourseCard, { CardContent } from '@/components/CourseCard/CourseCard';
import CardWithIcon from '@/components/CardWithIcon/CardWithIcon';
import copilotLogo from '../../assets/images/copilot-logo.svg';
import msLogo from '../../assets/images/microsoftlogo.svg';
import { copilotPlaygrounds } from '../../mockData/copilotPlaygrounds';
import { getRecommendedCopilotTrainings } from '../../mockData/recommendedCopilotTrainings';
import './MicrosoftCopilot.scss';
import { useGetJourneysQuery } from '../../redux/services/journey-api';
import { fetchCourseDetails } from '../../redux/features/courses/courseSlice';

const MicrosoftCopilotPage = () => {
  const user = useSelector((state) => state.auth.user);
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const [courseDetails, setCourseDetails] = useState({});
  const [isCourseDetailsLoading, setIsCourseDetailsLoading] = useState(false);
  

  // Journey verilerini çekiyoruz
  const {
    data: journeyData,
    isLoading: isJourneyLoading,
  } = useGetJourneysQuery({
    function: user?.onboarding?.function_label,
    levels: user?.journeyLevel?.name || 'beginner',
    provider: 'microsoft'
  }, {
    skip: !user?._id
  });

  // Kursların ID'lerini toplayalım ve detaylarını çekelim
  useEffect(() => {
    if (journeyData && journeyData.length > 0) {
      setIsCourseDetailsLoading(true);
      
      // Kurstan çevirilere göre chapters alacak yardımcı fonksiyon
      const getLocalizedChapters = (courseData) => {
        const currentLanguage = i18n.language || 'en';
        
        // Çevirileri kontrol et
        if (courseData.translations && courseData.translations[currentLanguage]?.chapters) {
          // Mevcut dilde çeviri varsa kullan
          return courseData.translations[currentLanguage].chapters;
        } else if (courseData.translations && courseData.translations.en?.chapters) {
          // Mevcut dilde çeviri yoksa İngilizce'yi kullan
          return courseData.translations.en.chapters;
        } else {
          // Hiç çeviri yoksa, orijinal chapters'ı kullan
          return courseData.chapters;
        }
      };
      
      const fetchCourseDetailsForJourneys = async () => {
        const courses = {};
        
        // Her journey için kurs ID'si olanları işle
        for (const journey of journeyData) {
          if (journey.course) {
            try {
              // Redux thunk'ı çalıştır ve promise'ı bekle
              const result = await dispatch(fetchCourseDetails(journey.course));
              
              // Eğer istek başarılıysa
              if (result.payload && !result.error) {
                const courseData = result.payload;
                
                // Doğru chaptersCount değerini hesapla
                let chaptersCount = 0;
                
                if (courseData.topics && Array.isArray(courseData.topics)) {
                  // topics bir dizi ise doğrudan uzunluğunu al
                  chaptersCount = courseData.topics.length;
                } else if (courseData.chapters && Array.isArray(courseData.chapters)) {
                  // Çevrilmiş chapters'ları al
                  const localizedChapters = getLocalizedChapters(courseData);
                  // chapters bir dizi ise doğrudan uzunluğunu al
                  chaptersCount = localizedChapters?.length || 0;
                } else {
                  // Hiçbiri yoksa veya uygun değilse, 0 göster
                  chaptersCount = 0;
                }
                
                // Kurs başlığı için çeviriyi kontrol et
                const currentLanguage = i18n.language || 'en';
                const courseTitle = 
                  (courseData.translations && courseData.translations[currentLanguage]?.title) ||
                  (courseData.translations && courseData.translations.en?.title) ||
                  courseData.title || "";
                
                courses[journey.course] = {
                  duration: courseData.duration || "N/A",
                  chaptersCount: chaptersCount,
                  title: courseTitle,
                };
              }
            } catch (error) {
              console.error(`Error fetching course details for ${journey.course}:`, error);
            }
          }
        }
        
        setCourseDetails(courses);
        setIsCourseDetailsLoading(false);
      };
      
      fetchCourseDetailsForJourneys();
    }
  }, [journeyData, dispatch, i18n.language]);

  // Tüm veriler yükleniyor mu kontrolü
  const isLoading = isJourneyLoading || isCourseDetailsLoading;

  // Kullanıcının iş fonksiyonunu al
  const getUserBusinessFunction = () => {
    if (!user?.onboarding?.function_label) return 'Sales';
    
    // function_label bir obje ise
    if (typeof user.onboarding.function_label === 'object') {
      // Öncelikle çevirilerine bakalım, yoksa slug'ı kullanalım
      return user.onboarding.function_label.translations?.en || 
             user.onboarding.function_label.slug || 'Sales';
    }
    
    // function_label bir string ise
    return user.onboarding.function_label;
  };

  // Kullanıcının function_label'ını al
  const getUserFunctionLabel = () => {
    if (!user?.onboarding?.function_label) return null;
    
    return user.onboarding.function_label;
  };

  // Filter copilotPlaygrounds based on user's function_label
  const filteredCopilotPlaygrounds = useMemo(() => {
    const businessFunction = getUserBusinessFunction();
    
    // If user is not Sales, filter out cards with variant 'sales'
    if (businessFunction !== 'Sales') {
      return copilotPlaygrounds.filter(playground => playground.variant !== 'sales');
    }
    
    return copilotPlaygrounds;
  }, [user?.onboarding?.function_label]);

  // Araç kartlarında çeviri desteği için yardımcı fonksiyon
  const getTranslatedText = (item, field) => {
    let translatedText = '';
    
    if (item.translations && item.translations[field]) {
      translatedText = item.translations[field][i18n.language] || 
                       item.translations[field]['en'] || 
                       item[field];
    } else {
      translatedText = item[field];
    }
    
    // {businessFunction} placeholder'ını gerçek iş fonksiyonu ile değiştir
    return translatedText ? translatedText.replace(/{businessFunction}/g, businessFunction) : '';
  };

  const businessFunction = getUserBusinessFunction();
  const functionLabel = getUserFunctionLabel();
  
  // Dinamik recommended kursları al
  const recommendedCopilotTrainings = getRecommendedCopilotTrainings(functionLabel);

  // Kart tıklandığında çalışacak fonksiyon
  const handleJourneyCardClick = (item) => {
    // Course parametresi varsa, kurs sayfasına yönlendir
    if (item.course) {
      if (item.newTab) {
        window.open(`/course/${item.course}`, '_blank');
      } else {
        navigate(`/course/${item.course}`);
      }
      return;
    }
    
    // Course yoksa ve URL varsa, URL'e yönlendir
    if (item.url) {
      if (item.newTab) {
        window.open(item.url, '_blank');
      } else {
        window.location.href = item.url;
      }
    }
  };

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('common.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  // Kurs kartları için süre ve bölüm bilgilerini alma fonksiyonu
  const getCourseInfo = (courseId) => {
    if (courseId && courseDetails[courseId]) {
      return {
        duration: courseDetails[courseId].duration,
        chaptersCount: courseDetails[courseId].chaptersCount,
      };
    }
    
    // Varsayılan değerler
    return {
      duration: "N/A",
      chaptersCount: 0,
    };
  };

  // Veri yükleniyor ise loading ekranı göster
  if (isLoading) {
    return (
      <Container>
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          flexDirection: 'column',
          minHeight: '50vh', 
          py: 8 
        }}>
          <CircularProgress size={60} thickness={4} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            {t('common.loading')}
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <>
      <AppBar position="static" className="copilot-app-bar">
        <Toolbar className="toolbar-container">
          <Container>
            <div className="content-wrapper">
              <img 
                src={copilotLogo} 
                alt="Copilot Logo" 
                className="copilot-logo"
              />
              <Typography
                variant="h6"
                component="span"
                className="header-title"
              >
                Microsoft 365 Copilot for {businessFunction}
              </Typography>
              <img 
                src={msLogo} 
                alt="Microsoft Logo" 
                className="microsoft-logo"
              />
            </div>
          </Container>
        </Toolbar>
      </AppBar>
      <Container>
      <Grid container className="copilot-container" spacing={4} mt={0}>
        {/* Journey Kartları */}
        {journeyData && journeyData.length > 0 && (
          <Grid item xs={12} mt={4}>
            <WhiteContainer
              title={`Recommended Copilot courses for ${businessFunction} professionals`}
              showNavigation={true}
              variant="transparent"
            >
              <Carousel
                swiperProps={{
                  slidesPerView: 'auto',
                  spaceBetween: 24,
                }}
                breakpoints={{
                  320: { slidesPerView: 1, spaceBetween: 24 },
                  768: { slidesPerView: 2, spaceBetween: 24 },
                  1024: { slidesPerView: 3, spaceBetween: 24 },
                  1440: { slidesPerView: 3, spaceBetween: 24 },
                }}
              >
                {journeyData.map((item, index) => {
                  // Çeviri için doğru anahtar belirleme
                  const translationKey = i18n.language === 'de' ? 'german' : 'english';
                  
                  // Çevirileri translations objesi içinden çekiyoruz
                  const cardTitle = item.translations?.[translationKey]?.title || item.title_english || item.title || '';
                  const cardDescription = item.translations?.[translationKey]?.description || item.description || '';
                  const buttonText = item.translations?.[translationKey]?.buttonText || item.buttonText || 'View';
                  
                  // Eğer bir kurs ID'si varsa, kurs bilgilerini alalım
                  let courseInfo = {
                    duration: "N/A",
                    chaptersCount: 0
                  };
                  
                  if (item.course) {
                    courseInfo = getCourseInfo(item.course);
                  }

                  // API'dan gelen closedCard değerini kontrol et
                  const isLocked = item.closedCard === true;

                  return (
                    <CourseCard
                      key={item._id}
                      buttonText={buttonText}
                      buttonType="URL"
                      buttonVariant="text"
                      cardNumber={index + 1}
                      courseNumber={courseInfo.chaptersCount}
                      duration={courseInfo.duration}
                      chaptersCount={courseInfo.chaptersCount}
                      locked={isLocked}
                      tooltipText={isLocked ? t('common.lockedTooltip') : ''}
                    >
                      <CardContent
                        title={cardTitle}
                        description={cardDescription}
                        buttonURL={item.course ? `/course/${item.course}` : (item.url || '#')}
                        newTab={item.course ? false : item.newTab}
                        buttonText={buttonText}
                        buttonType="URL"
                        cardNumber={index + 1}
                        courseNumber={courseInfo.chaptersCount}
                        duration={courseInfo.duration}
                        chaptersCount={courseInfo.chaptersCount}
                        locked={isLocked}
                        tooltipText={isLocked ? t('common.lockedTooltip') : ''}
                        onClick={() => handleJourneyCardClick(item)}
                      />
                    </CourseCard>
                  );
                })}
              </Carousel>
            </WhiteContainer>
          </Grid>
        )}

          <Grid item xs={12}>
            <WhiteContainer
              title="Playground areas"
              variant="transparent"
            >
              <Grid container spacing={3}>
                {filteredCopilotPlaygrounds.map((playground) => (
                  <Grid item xs={12} mt={2} md={4} key={playground.id}>
                    <CardWithIcon
                      icon={<playground.icon />}
                      title={getTranslatedText(playground, 'title')}
                      description={getTranslatedText(playground, 'description')}
                      variant={playground.variant}
                      locked={playground.locked}
                      buttonType={playground.buttonType}
                      buttonURL={playground.buttonURL}
                      newTab={playground.newTab}
                      tooltipText={playground.locked ? t('common.lockedTooltip') : ''}
                      onClick={() => !playground.locked && window.open(playground.buttonURL, playground.newTab ? '_blank' : '_self')}
                    />
                  </Grid>
                ))}
              </Grid>
            </WhiteContainer>
          </Grid>

          <Grid item xs={12}>
            <WhiteContainer
              title="More advanced and technical Copilot courses"
              showNavigation={true}
              variant="transparent"
            >
              <Carousel
                swiperProps={{
                  slidesPerView: 'auto',
                  spaceBetween: 24,
                }}
                breakpoints={{
                  320: { slidesPerView: 1, spaceBetween: 24 },
                  768: { slidesPerView: 2, spaceBetween: 24 },
                  1024: { slidesPerView: 3, spaceBetween: 24 },
                  1440: { slidesPerView: 3, spaceBetween: 24 },
                }}
              >
                {recommendedCopilotTrainings.map((item) => (
                  <CourseCard
                    key={item.id}
                    buttonText={item.buttonText}
                    buttonType={item.buttonType}
                    buttonVariant="text"
                    imageSrc={item.imageSrc}
                    locked={item.locked}
                    tooltipText={item.locked ? t('common.lockedTooltip') : item.tooltipText}
                  >
                    <CardContent
                      title={item.title}
                      description={item.description}
                      buttonURL={item.buttonURL}
                      newTab={item.newTab}
                      buttonText={item.buttonText}
                      buttonType={item.buttonType}
                      imageSrc={item.imageSrc}
                      locked={item.locked}
                      tooltipText={item.locked ? t('common.lockedTooltip') : item.tooltipText}
                    />
                  </CourseCard>
                ))}
              </Carousel>
            </WhiteContainer>
          </Grid>
        </Grid>
      </Container>
      </>
  );
};

export default MicrosoftCopilotPage;