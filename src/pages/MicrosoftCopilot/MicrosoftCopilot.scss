@use '../../styles/abstracts/variables' as *;
.copilot-container {
  & > .MuiGrid-root:first-child{
    padding-top:0 !important;
    margin-top:0 !important;
  }
  margin-bottom: $spacing-6;
  .carousel-card{
    min-height: 260px;
  }
}
.copilot-app-bar {
    width: 100% !important;
  background: linear-gradient(90deg, #4B9EF9 0%, #9C3FE4 50%, #F65164 100%) !important;
  box-shadow: none !important;
  margin-bottom: $spacing-6;
  padding: $spacing-3 0 !important;
  position: relative !important;
  left: 0;
  right: 0;

  .toolbar-container {
    padding: 0 !important;
    min-height: auto !important;
  }

  .content-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    padding: $spacing-2 0;
  }

  .copilot-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-right: $spacing-2;
  }

  .header-title {
    color: white;
    font-weight: 600;
    -webkit-text-stroke: 1px #8764E8;
    font-size: 24px;
  }

  .microsoft-logo {
    width: 120px;
    height: auto;
    margin-left: auto;
    background-color: rgba(255, 255, 255, 0.55);
    padding:$spacing-3 $spacing-3;
    border-radius: $border-radius-sm;
  }
}

.copilot-features {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    padding: $spacing-2 0;
    border-bottom: 1px solid var(--divider-color);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);

    &:last-child {
      border-bottom: none;
    }
  }
}

.copilot-resources {
  ul {
    list-style: none;
    padding: 0;
    margin: $spacing-3 0;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: $spacing-3;

    li {
      padding: $spacing-3;
      background: var(--bg-light);
      border-radius: var(--border-radius-md);
      color: var(--text-secondary);
      font-size: var(--font-size-sm);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: $shadow-sm;
        background: var(--bg-paper);
        color: var(--text-primary);
      }
    }
  }
} 