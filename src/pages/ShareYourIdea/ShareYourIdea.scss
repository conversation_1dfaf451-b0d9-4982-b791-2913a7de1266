@use '../../styles/abstracts/variables' as *;


.idea-card {
  height: 100%;
  border: none;
  border-radius: $border-radius-lg;
  padding: $spacing-4;
  background-color: $bg-paper;
  box-shadow: rgba(145, 158, 171, 0.2) 0px 0px 2px 0px,
              rgba(145, 158, 171, 0.12) 0px 12px 24px -4px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  gap: $spacing-3;

  &:hover {
    box-shadow: rgba(145, 158, 171, 0.3) 0px 0px 2px 0px,
                rgba(145, 158, 171, 0.2) 0px 20px 32px -4px;
    transform: translateY(-4px);
  }
  
  .idea-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-primary;
    line-height: 1.4;
    margin: 0;
    
    &:after {
      content: '';
      display: block;
      width: 40px;
      height: 3px;
      background: $primary-color;
      margin-top: $spacing-2;
      border-radius: 2px;
    }
  }

  .idea-content {
    color: $text-secondary;
    font-size: $font-size-md;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
    margin: 0;
  }
  
  .idea-submission-date {
    color: $text-disabled;
    font-size: $font-size-sm;
    display: flex;
    align-items: center;
    margin: 0;
    
    &:before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      background: currentColor;
      border-radius: 50%;
      margin-right: $spacing-2;
    }
  }

  .MuiButton-root {
    align-self: flex-start;
    min-width: 140px;
    margin-top: $spacing-2;
    
    &:hover {
      background-color: rgba($primary-color, 0.08);
    }
  }
}