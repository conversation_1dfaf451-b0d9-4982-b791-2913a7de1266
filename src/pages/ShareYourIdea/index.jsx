import { Box, Container, Grid, Typography, CircularProgress } from '@mui/material';
import { useSelector } from 'react-redux';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../components/WhiteContainer/WhiteContainer.jsx';
import Button from '../../components/Button/Button.jsx';
import FormModal from '../../components/FormModal/FormModal.jsx';
import FormRender from '../../components/FormRender/FormRender.jsx';
import './ShareYourIdea.scss';
import { useTranslation } from 'react-i18next';
import { useState, useEffect, useReducer } from 'react';
import { useFormByIdQuery } from '../../redux/services/form-service';
import { useSubmitFormResponseMutation } from '../../redux/services/form-response';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import useIdeationList from '@/domains/form/hook/useIdeationList';
import SubmittedIdeaCard from '@/domains/form/components/SubmittedIdeaCard';
import InfoIcon from '@mui/icons-material/Info';
import { IDEA_FORM_ID } from '../../constants/form-constants';

// Form yanıtları için tüm kullanıcılar için ideation listesini burada çekelim
// Bu sayede hook koşullu çağrı hatası giderilmiş olacak
const useIdeationData = (userId, formType) => {
  const { ideationList, isIdeationLoading, isIdeationError } = useIdeationList(userId, formType);
  return { ideationList, isIdeationLoading, isIdeationError };
};

// Bir formun tüm alanlarını toplayan yardımcı fonksiyon
const getAllFields = (formData) => {
  if (!formData) return [];

  let allFields = [];

  // Ana fields dizisinden alanları ekle
  if (formData.fields && Array.isArray(formData.fields)) {
    allFields = [...formData.fields];
  }

  // topics dizisindeki fields'ları ekle
  if (formData.topics && Array.isArray(formData.topics)) {
    formData.topics.forEach((topic) => {
      if (topic.fields && Array.isArray(topic.fields)) {
        allFields = [...allFields, ...topic.fields];
      }
    });
  }

  return allFields;
};

const ShareYourIdeaPage = () => {
  const user = useSelector((state) => state.auth.user);
  const { t, i18n } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({});
  const [descriptionFields, setDescriptionFields] = useState([]);
  const [classificationFields, setClassificationFields] = useState([]);
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [skipValidationOnce, setSkipValidationOnce] = useState(true);
  const [hasInteracted, setHasInteracted] = useState(false);

  // Mevcut dil seçimini al
  const currentLang = i18n.language || 'en';

  // useIdeationList hook'unu koşullu çağırmak yerine, ayrı bir fonksiyonda çağırıyoruz
  const { ideationList, isIdeationLoading, isIdeationError } = useIdeationData(
    user ? user._id : '',
    'ideation'
  );

  // React'in yeniden render olması için basit bir forceUpdate fonksiyonu
  const [, forceUpdate] = useReducer((x) => x + 1, 0);

  // Form verilerini çek - kullanıcının dil seçimine göre
  const {
    data: apiFormData,
    isLoading,
    error,
  } = useFormByIdQuery(
    {
      formId: IDEA_FORM_ID,
      lang: currentLang,
    },
    {
      refetchOnMountOrArgChange: true, // Dil değişiminde yeniden çek
    }
  );

  // Form yanıtlarını göndermek için RTK mutation hook'unu kullan
  const [submitFormResponse, { isLoading: isSubmittingForm }] = useSubmitFormResponseMutation();

  // Form yanıtı gönderilirken state güncellemesi için takip
  useEffect(() => {
    setIsSubmitting(isSubmittingForm);
  }, [isSubmittingForm]);

  useEffect(() => {}, [formData]);

  useEffect(() => {}, [formErrors]);

  useEffect(() => {
    if (isModalOpen) {
      setFormData({});
      setFormErrors({});
      setActiveStep(0);

      setTimeout(() => {
        const allFields = [...descriptionFields, ...classificationFields];

        const initialValues = {};

        allFields
          .filter((field) => field.type === 'select' && field.required)
          .forEach((field) => {
            initialValues[field.name] = null;
          });

        setFormData(initialValues);

        setFormErrors({});

        forceUpdate();
      }, 100);
    }
  }, [isModalOpen, descriptionFields, classificationFields]);

  useEffect(() => {
    if (apiFormData) {
      // Tüm form alanlarını topla - hem fields hem de topics dizilerinden
      let allFields = [];

      // Ana fields dizisinden alanları ekle
      if (apiFormData?.data?.fields && apiFormData.data.fields.length > 0) {
        allFields = [...apiFormData.data.fields];
      } else if (apiFormData?.fields && apiFormData.fields.length > 0) {
        allFields = [...apiFormData.fields];
      } else if (apiFormData?.data?.forms && apiFormData.data.forms[0]?.fields) {
        allFields = [...apiFormData.data.forms[0].fields];
      }

      // topics dizisindeki fields'ları ekle
      if (apiFormData?.data?.topics && apiFormData.data.topics.length > 0) {
        apiFormData.data.topics.forEach((topic) => {
          if (topic.fields && topic.fields.length > 0) {
            allFields = [...allFields, ...topic.fields];
          }
        });
      } else if (apiFormData?.topics && apiFormData.topics.length > 0) {
        apiFormData.topics.forEach((topic) => {
          if (topic.fields && topic.fields.length > 0) {
            allFields = [...allFields, ...topic.fields];
          }
        });
      }

      // Başlık ve açıklama alanlarını filtrele
      const filteredDescriptionFields = allFields.filter((field) => {
        const name = field.name?.toLowerCase().trim();
        return (
          name === 'title' ||
          name === 'description' ||
          name.includes('describe') ||
          name.includes('title')
        );
      });

      const descFields =
        filteredDescriptionFields.length > 0 ? filteredDescriptionFields : allFields.slice(0, 2);

      setDescriptionFields(descFields);

      // Diğer alanları filtrele
      const filteredClassificationFields = allFields.filter((field) => {
        const name = field.name?.toLowerCase().trim();
        return (
          name !== 'title' &&
          name !== 'description' &&
          !name.includes('describe') &&
          !name.includes('title')
        );
      });

      const classFields =
        filteredClassificationFields.length > 0 ? filteredClassificationFields : allFields.slice(2);

      setClassificationFields(classFields);
    }
  }, [apiFormData]);

  const handleOpenModal = () => {
    setIsModalOpen(true);
    setSkipValidationOnce(true);
    setHasInteracted(false);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setActiveStep(0);
    setFormData({});
  };

  // Form alanlarını temizleme yardımcı fonksiyonu
  const cleanHiddenFieldErrors = (currentFields, formState) => {
    // Tüm alanları kontrol et ve gizli olanların hatalarını temizle
    const updatedErrors = { ...formErrors };
    let hasChanges = false;

    currentFields.forEach((field) => {
      const isVisible = checkCondition(field, formState);
      if (!isVisible && updatedErrors[field.name]) {
        console.log(`Clearing error for hidden field: ${field.name}`);
        delete updatedErrors[field.name];
        hasChanges = true;
      }
    });

    // Eğer hata listesi güncellendiyse state'i güncelle
    if (hasChanges) {
      setFormErrors(updatedErrors);
      return updatedErrors;
    }

    return formErrors;
  };

  const handleSubmit = async () => {
    const allFields = [...descriptionFields, ...classificationFields];

    // Önce gizli alanların hatalarını temizle
    const currentErrors = cleanHiddenFieldErrors(allFields, formData);

    let missingRequiredField = false;
    const missingFields = [];

    allFields.forEach((field) => {
      // Önce alanın görünür olup olmadığını kontrol et
      const isVisible = checkCondition(field, formData);

      // Alan görünür değilse kontrol etme ve ilgili hatayı temizle
      if (!isVisible) {
        console.log(`Field "${field.label || field.name}" is hidden, skipping validation`);
        // Eğer bu alan için bir hata varsa temizleyelim
        if (currentErrors[field.name]) {
          delete currentErrors[field.name];
        }
        return; // continue yerine return kullanılır çünkü forEach içindeyiz
      }

      // Sadece görünür alanlar için required kontrolü yap
      if (field.required && checkIfEmpty(formData[field.name])) {
        missingRequiredField = true;
        missingFields.push(field.label || field.name);
        currentErrors[field.name] = 'This field is required';
      }
    });

    // Güncellenen hataları set et
    setFormErrors(currentErrors);

    if (missingRequiredField) {
      console.log('Required fields are missing:', missingFields);
      toast.error(t('ideation.form.errors.requiredFields'), {
        toastId: 'idea-form-error',
      });
      return false;
    }

    try {
      const responses = allFields
        .filter((field) => {
          // Görünür alanları kontrol et
          const isVisible = checkCondition(field, formData);
          if (!isVisible) return false;

          // Görünür alanların dolu olanlarını filtrele
          return !checkIfEmpty(formData[field.name]);
        })
        .map((field) => ({
          fieldId: field._id,
          name: field.label.toLowerCase(),
          type: field.type,
          value: formData[field.name],
        }));

      const requestData = {
        formId: IDEA_FORM_ID,
        formType: 'ideation',
        title: apiFormData?.data?.title || apiFormData?.title || t('ideation.form.defaultTitle'),
        description:
          apiFormData?.data?.description ||
          apiFormData?.description ||
          t('ideation.form.defaultDescription'),
        responses,
      };

      console.log('Submitting form data:', requestData);
      const response = await submitFormResponse(requestData).unwrap();

      if (response && response.status === 'success') {
        toast.success(t('ideation.form.success.submitted'), {
          toastId: 'idea-form-success',
        });

        handleCloseModal();
        return true;
      } else {
        const errorMessage = response?.message || t('ideation.form.errors.submission');
        console.error('Form submission failed:', errorMessage);
        toast.error(`${t('common.error')}: ${errorMessage}. ${t('common.tryAgain')}`, {
          toastId: 'idea-form-submission-error',
        });
        return false;
      }
    } catch (error) {
      const errorMessage =
        error.data?.message || error.message || t('ideation.form.errors.generic');

      console.error('Form submission error:', error);
      toast.error(`${t('common.error')}: ${errorMessage}. ${t('common.tryAgain')}`, {
        toastId: 'idea-form-catch-error',
      });
      return false;
    }
  };

  const handleFormChange = (newData) => {
    if (!newData || Object.keys(newData).length === 0) {
      return;
    }

    // Kullanıcı form ile etkileşime girdiğinde flag'i true yap
    if (newData.__userInteracted) {
      setHasInteracted(true);
    }

    if (newData.__errors) {
      const updatedErrors = { ...formErrors };

      Object.entries(newData.__errors).forEach(([key, value]) => {
        if (value === null) {
          // Hata temizleniyor
          delete updatedErrors[key];
        } else {
          // Yeni hata ekleniyor
          updatedErrors[key] = value;
        }
      });

      // Validasyon hatalarını güncelle
      setFormErrors(updatedErrors);
      delete newData.__errors;
    }

    // Form sayfasındaki veriler
    if (newData.__formState) {
      // Bu durumda, doğrudan tüm form verilerini güncelleyebiliriz
      setFormData(newData.__formState);
      delete newData.__formState;
    } else {
      // __userInteracted ve __formState özel alanlarını temizle
      delete newData.__userInteracted;

      // Form verilerini güncelle
      const updatedFields = Object.keys(newData).filter((k) => k !== '__errors');
      if (updatedFields.length > 0) {
        const updatedFormData = { ...formData };

        updatedFields.forEach((fieldName) => {
          updatedFormData[fieldName] = newData[fieldName];

          // Eğer alan doldurulmuşsa ve bir hata varsa, hatayı temizle
          if (!checkIfEmpty(newData[fieldName]) && formErrors[fieldName]) {
            const updatedErrors = { ...formErrors };
            delete updatedErrors[fieldName];
            setFormErrors(updatedErrors);
          }
        });

        setFormData(updatedFormData);

        // Formu güncelle ve debug için veriyi logla
        setTimeout(() => {
          console.log('Updated form data:', updatedFormData);
          forceUpdate();
        }, 10);
      }
    }
  };

  // Alanın koşul durumunu kontrol eden fonksiyon
  const checkCondition = (field, formValues) => {
    // Eğer koşul yoksa görünür
    if (!field.condition && !field.conditional_logic) return true;

    // Koşul bilgilerini debug için logla
    console.log(`Checking condition for field "${field.name || field.label}"`);

    // API'den gelen "condition" property'si
    if (field.condition) {
      console.log('Using condition property:', field.condition);
      const { field: conditionField, operator, value } = field.condition;

      // Koşul alanının değerini al
      const fieldValue = formValues[conditionField];
      console.log(`Field value: ${fieldValue}`);

      // Operatör kontrolü
      let result = false;
      switch (operator) {
        case 'equals':
          result = fieldValue === value;
          break;
        case 'not_equals':
          result = fieldValue !== value;
          break;
        case 'contains':
          result = typeof fieldValue === 'string' && fieldValue.includes(value);
          break;
        case 'not_contains':
          result = typeof fieldValue === 'string' && !fieldValue.includes(value);
          break;
        case 'starts_with':
          result = typeof fieldValue === 'string' && fieldValue.startsWith(value);
          break;
        case 'ends_with':
          result = typeof fieldValue === 'string' && fieldValue.endsWith(value);
          break;
        case 'greater_than':
          result = Number(fieldValue) > Number(value);
          break;
        case 'less_than':
          result = Number(fieldValue) < Number(value);
          break;
        default:
          result = true;
          break;
      }

      console.log(`Condition result for "${field.name || field.label}": ${result}`);
      return result;
    }

    // FormRender bileşenine uygun "conditional_logic" property'si
    if (field.conditional_logic && field.conditional_logic.enabled) {
      console.log('Using conditional_logic property:', field.conditional_logic);

      // Koşul gruplarını değerlendir (her grup OR ilişkisiyle değerlendirilir)
      return field.conditional_logic.rules.some((ruleGroup) => {
        // Her gruptaki kuralları değerlendir (her kural AND ilişkisiyle değerlendirilir)
        return ruleGroup.every((rule) => {
          // Boş alan adı varsa, kuralı atla
          if (!rule.field) {
            return true;
          }

          const fieldValue = formValues[rule.field];
          console.log(
            `Evaluating rule: ${rule.field} ${rule.operator} ${rule.value}, Current value: ${fieldValue}`
          );

          switch (rule.operator) {
            case 'equals':
            case '=':
            case '==':
              return fieldValue === rule.value;
            case 'not_equals':
            case '!=':
              // For empty value comparison, check if field has any value
              if (rule.value === '') {
                return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
              }
              return fieldValue !== rule.value;
            case 'has_any_value':
              return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
            case 'is_empty':
              return fieldValue === undefined || fieldValue === null || fieldValue === '';
            case '>':
            case 'greater_than':
              return parseFloat(fieldValue) > parseFloat(rule.value);
            case '<':
            case 'less_than':
              return parseFloat(fieldValue) < parseFloat(rule.value);
            case 'contains':
              return typeof fieldValue === 'string' && fieldValue.includes(rule.value);
            case 'starts_with':
              return typeof fieldValue === 'string' && fieldValue.startsWith(rule.value);
            case 'ends_with':
              return typeof fieldValue === 'string' && fieldValue.endsWith(rule.value);
            default:
              return true;
          }
        });
      });
    }

    // Varsayılan olarak alanı görünür kabul et
    return true;
  };

  const checkIfEmpty = (value) => {
    if (value === undefined || value === null) {
      return true;
    }

    if (typeof value === 'string') {
      const isEmpty = value.trim() === '';
      return isEmpty;
    }

    if (Array.isArray(value)) {
      return value.length === 0;
    }

    if (typeof value === 'object' && !Array.isArray(value)) {
      return Object.keys(value).length === 0;
    }

    return false;
  };

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">Please log in.</Typography>
        </Box>
      </Container>
    );
  }

  // Mevcut form verilerinin yapısını FormRender bileşenine uygun şekilde hazırla
  const prepareFormData = (formId, title, description, fields) => {
    return {
      _id: formId,
      title: title,
      description: description,
      type: 'form',
      fields: fields,
    };
  };

  const formSteps = [
    {
      title: t('ideation.form.steps.describe.title'),
      label: t('ideation.form.steps.describe.label'),
      onChange: (data) => {
        handleFormChange(data);
      },
      content: (
        <FormRender
          hideTitle={false}
          hideDescription={false}
          formTitle={t('ideation.form.steps.describe.title')}
          formData={prepareFormData(
            apiFormData?.data?._id || apiFormData?._id || 'idea-form',
            apiFormData?.data?.title || apiFormData?.title || t('ideation.form.title'),
            apiFormData?.data?.description || apiFormData?.description || '',
            descriptionFields.length > 0 ? descriptionFields : []
          )}
          onChange={(data) => {
            handleFormChange(data);
          }}
          values={formData}
          errors={formErrors}
          disabled={isSubmitting}
        />
      ),
    },
    {
      title: t('ideation.form.steps.classify.title'),
      label: t('ideation.form.steps.classify.label'),
      onChange: (data) => {
        handleFormChange(data);
      },
      content: (
        <FormRender
          hideTitle={false}
          hideDescription={true}
          formTitle={t('ideation.form.steps.classify.title')}
          formData={prepareFormData(
            apiFormData?.data?._id || apiFormData?._id || 'idea-form',
            apiFormData?.data?.title || apiFormData?.title || t('ideation.form.title'),
            apiFormData?.data?.description || apiFormData?.description || '',
            classificationFields.length > 0 ? classificationFields : []
          )}
          data={{}}
          onChange={(data) => {
            handleFormChange(data);
          }}
          disabled={isSubmitting}
        />
      ),
    },
  ];

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('shareYourIdea.title')}
            description={t('shareYourIdea.description')}
            showProgress={false}
          />
          <Box mt={3}>
            <Button variant="contained" color="primary" size="medium" onClick={handleOpenModal}>
              {t('shareYourIdea.buttons.NewIdea')}
            </Button>
          </Box>
        </Grid>
        <Grid item xs={12}>
          <WhiteContainer
            title={t('ideation.submissions.title')}
            subtitle={t('ideation.submissions.subtitle')}
          >
            <Grid container spacing={3}>
              {isIdeationLoading ? (
                <Grid item xs={12}>
                  <Box display="flex" justifyContent="center" p={3}>
                    <CircularProgress />
                  </Box>
                </Grid>
              ) : isIdeationError ? (
                <Grid item xs={12}>
                  <Typography color="error">
                    {t('common.error')}: {isIdeationError.message}
                  </Typography>
                </Grid>
              ) : ideationList && ideationList.length > 0 ? (
                ideationList.map((idea, index) => <SubmittedIdeaCard key={index} idea={idea} />)
              ) : (
                <Grid item xs={12}>
                  <Box className="state-message--info">
                    <InfoIcon className="message-icon" />
                    {t('ideation.submissions.noIdeas')}
                  </Box>
                </Grid>
              )}
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
      <FormModal
        open={isModalOpen}
        onClose={handleCloseModal}
        title={t('shareYourIdea.modal.title')}
        onSubmit={handleSubmit}
        maxWidth="md"
        activeStep={activeStep}
        steps={formSteps}
        onNext={() => {
          // Eğer kullanıcı henüz form ile etkileşime girmediyse validasyonu atla
          // Bu sadece ilk adım -> ikinci adım geçişinde çalışır
          if (skipValidationOnce && !hasInteracted) {
            setSkipValidationOnce(false);
            console.log('Skipping validation on first interaction');
            return true;
          }

          console.log('Validating current step fields...');

          // Mevcut adımda gösterilen form alanlarını al
          const currentFields = activeStep === 0 ? descriptionFields : classificationFields;

          // Gizli alanların hatalarını temizle
          const cleanedErrors = cleanHiddenFieldErrors(currentFields, formData);

          // Tüm zorunlu alanların dolu olup olmadığını kontrol et
          let allValid = true;
          const newErrors = {};

          // Her required alan için, boş olup olmadığını kontrol et
          for (const field of currentFields) {
            // Önce alanın görünür olup olmadığını kontrol et
            const isVisible = checkCondition(field, formData);

            // Alan görünür değilse kontrol etme
            if (!isVisible) {
              console.log(`Field "${field.label || field.name}" is hidden, skipping validation`);
              continue;
            }

            // Sadece görünür alanlar için required kontrolü yap
            if (field.required) {
              const fieldValue = formData[field.name];
              const isEmpty = checkIfEmpty(fieldValue);

              if (isEmpty) {
                console.log(`Required field "${field.label || field.name}" is empty`);
                newErrors[field.name] = t('common.validation.required');
                allValid = false;
              }
            }
          }

          // Eğer required alanlardan biri boşsa, hataları set et ve false döndür
          if (!allValid) {
            console.log('Validation failed, required fields missing:', newErrors);
            setFormErrors({
              ...cleanedErrors,
              ...newErrors,
            });
            // False döndürürse, FormModal bir sonraki adıma geçmeyecek
            return false;
          }

          console.log('Validation passed, all required fields are filled');
          return true;
        }}
        showReview={true}
        formErrors={formErrors}
        loading={isSubmitting}
        skipInitialValidation={true}
        values={formData}
      >
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error">Form not found. Please try again later.</Typography>
        ) : isSubmitting ? (
          <Box sx={{ position: 'relative' }}>
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                zIndex: 2,
              }}
            >
              <Box sx={{ textAlign: 'center' }}>
                <CircularProgress size={60} />
                <Typography sx={{ mt: 2 }}>Submitting your idea...</Typography>
              </Box>
            </Box>
            {formSteps && formSteps.length > 0 ? (
              formSteps[activeStep < formSteps.length ? activeStep : 0].content
            ) : (
              <Typography>No form steps defined</Typography>
            )}
          </Box>
        ) : formSteps && formSteps.length > 0 ? (
          formSteps[activeStep < formSteps.length ? activeStep : 0].content
        ) : (
          <Typography>No form steps defined</Typography>
        )}
      </FormModal>
    </Container>
  );
};

ShareYourIdeaPage.propTypes = {
  data: PropTypes.object,
  onChange: PropTypes.func,
};

export default ShareYourIdeaPage;
