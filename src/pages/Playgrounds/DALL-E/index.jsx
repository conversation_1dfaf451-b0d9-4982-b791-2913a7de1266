import { Box, Container, Grid } from '@mui/material';
import { useRef } from 'react';
import AIHero from '../../../components/AIHero/AIHero';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import { DallEPlaygroundCards } from '../../../mockData/DallEPlaygroundCards';
import PlaygroundCard from '../../../components/PlaygroundCard/PlaygroundCard';
import DallEPlayground from '../../../components/DallEPlayground/DallEPlayground';
import '../Playgrounds.scss';
import { useTranslation } from 'react-i18next';

// Dil bazlı görselleri import et
import VideoThumbnailEN from '../../../assets/images/dall-e-video-thumbnail.png';
import VideoThumbnailDE from '../../../assets/images/dall-e-video-thumbnail-de.png';

const DALLEPlayground = () => {
  const welcomeHeaderRef = useRef(null);
  const { t, i18n } = useTranslation();

  // Çeviri dosyasından dile göre video URL'sini al
  const videoUrl = t('playgrounds.dallE.videoUrl');

  // Dile göre doğru thumbnail'i seç
  const currentLanguage = i18n.language || 'en';
  const videoThumbnail = currentLanguage === 'de' ? VideoThumbnailDE : VideoThumbnailEN;

  const handleTryItClick = () => {
    welcomeHeaderRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  };

  return (
    <Container>
      <AIHero
        title={t('playgrounds.dallE.title')}
        description={t('playgrounds.dallE.description')}
        buttonText={t('playgrounds.dallE.tryItButton')}
        onButtonClick={handleTryItClick}
        videoUrl={videoUrl}
        videoThumbnail={videoThumbnail}
      />

      <Box className="features-section">
        <Grid container spacing={3}>
          {DallEPlaygroundCards().map((feature, index) => (
            <Grid item xs={12} md={12 / DallEPlaygroundCards().length} key={index}>
              <PlaygroundCard {...feature} />
            </Grid>
          ))}
        </Grid>
      </Box>

      <Box ref={welcomeHeaderRef}>
        <WelcomeHeader
          isPageView={true}
          title={t('playgrounds.dallE.tryItYourself')}
          description={t('playgrounds.dallE.tryItDescription')}
          showProgress={false}
        />
      </Box>

      <DallEPlayground
        initialPrompt={t('playgrounds.dallE.samplePrompt')}
        onBack={() => console.log('Back clicked')}
        disableBack={true}
      />
    </Container>
  );
};

export default DALLEPlayground;
