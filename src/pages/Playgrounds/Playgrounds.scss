@use '../../styles/abstracts/variables' as *;

.features-section {
    padding: $spacing-6 0 $spacing-6 0;
    margin-bottom: $spacing-6;
    border-bottom: 1px solid $divider-color;
    &.border-none {
      border-bottom: none !important;
      margin-bottom: $spacing-6 !important;
      padding-bottom: 0 !important;
    }
  
    .feature-box {
      position: relative;
      background: url('@/assets/images/effect.png') no-repeat top -80px right -75px $bg-paper;
      border-radius: $border-radius-lg;
      padding: $spacing-4 $spacing-4;
      border: 1px solid $border-color;
      display: flex;
      flex-direction: column;
      gap: $spacing-2;
  
  
      .feature-tag {
      position:absolute;
      top:$spacing-4;
      right:$spacing-4;
        display: inline-block;
        width: max-content;
        padding: $spacing-2 $spacing-2;
        color: $text-secondary !important;
        background-color: #F7F7F8 !important;
        border:1px solid $border-color;
        border-radius: $border-radius-sm;
        font-size: $font-size-xs;
        font-weight: $font-weight-medium;
        margin-bottom: $spacing-2;
      }
  
      .feature-icon {
        font-size: 32px;
        border:1px solid $border-color;
        width: max-content;
        width: 64px;
        display:flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 64px;
        border-radius: $border-radius-lg;
        margin-bottom: $spacing-3;
        
        svg {
          width: 32px;
          height: 32px;
          color: $primary-color;
        }
      }
  
      .feature-title {
        font-size: $font-size-lg;
        font-weight: $font-weight-bold;
        color: $text-primary;
        padding:0 !important;
        margin:0 !important;
      }
  
      .feature-description {
        color: $text-secondary;
        font-size: calc($font-size-md - 2px);
        line-height: 1.6;
        margin-bottom:$spacing-3;
      }
  
      .feature-button {
        text-transform: none;
        
        &:hover {
          background: rgba($primary-color, 0.05);
        }
      }
    }
  }

.state-message {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $spacing-3;
  border-radius: $border-radius-md;
  font-size: $font-size-xs !important;
  font-weight: $font-weight-medium;
}

.state-message--empty {
  @extend .state-message;
  background-color: rgba($bg-paper, 0.5);
  border: 1px dashed $border-color;
  color: $text-secondary;
  transition: all 0.3s ease;
  justify-content: flex-start !important;
  
  &:hover {
    background-color: rgba($bg-paper, 0.7);
    border-color: $primary-color;
  }
}

.state-message--error {
  @extend .state-message;
  background-color: rgba(#ffebee, 0.5);
  border: 1px solid #ffcdd2;
  justify-content: flex-start !important;
  color: #b71c1c;
}

.state-message--info {
  @extend .state-message;
  background-color: rgba(#e3f2fd, 0.7);
  border: 1px solid #bbdefb;
  justify-content: flex-start !important;
  color: #0d47a1;
  position: relative;

  .message-icon {
    margin-right: $spacing-2;
    font-size: 20px;
  }
}