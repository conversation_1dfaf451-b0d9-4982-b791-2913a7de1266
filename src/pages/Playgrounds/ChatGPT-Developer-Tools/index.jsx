import { Box, Container, Grid } from '@mui/material';
import { useRef, useState } from 'react';
import AIHero from '../../../components/AIHero/AIHero';
import PlaygroundCard from '../../../components/PlaygroundCard/PlaygroundCard';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader';
import { ChatGPTDeveloperToolsPlaygroundCards } from '../../../mockData/ChatGPTDevelopersPlaygroundCards';
import ChatGPTPlaygroundTry from '../../../components/ChatGPTPlayground/ChatGPTPlayground';
import '../Playgrounds.scss';
import { useTranslation } from 'react-i18next';

// Dil bazlı görselleri import et
import VideoThumbnailEN from '../../../assets/images/chatgpt-developer-tools-video-thumbnail.png';
import VideoThumbnailDE from '../../../assets/images/chatgpt-developer-tools-video-thumbnail-de.png';

const ChatGPTDeveloperToolsPlayground = () => {
  const welcomeHeaderRef = useRef(null);
  const [temperature, setTemperature] = useState(1.0);
  const [topP, setTopP] = useState(1.0);
  const [frequencyPenalty, setFrequencyPenalty] = useState(0);
  const [presencePenalty, setPresencePenalty] = useState(0);
  const { t, i18n } = useTranslation();

  // Çeviri dosyasından dile göre video URL'sini al
  const videoUrl = t('playgrounds.chatGPTDeveloperTools.videoUrl');

  // Dile göre doğru thumbnail'i seç
  const currentLanguage = i18n.language || 'en';
  const videoThumbnail = currentLanguage === 'de' ? VideoThumbnailDE : VideoThumbnailEN;

  const handleTryItClick = () => {
    welcomeHeaderRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  };

  return (
    <Container>
      <AIHero
        title={t('playgrounds.chatGPTDeveloperTools.title')}
        description={t('playgrounds.chatGPTDeveloperTools.description')}
        buttonText={t('playgrounds.chatGPTDeveloperTools.tryItButton')}
        onButtonClick={handleTryItClick}
        videoUrl={videoUrl}
        videoThumbnail={videoThumbnail}
      />

      <Box className="features-section">
        <Grid container spacing={3}>
          {ChatGPTDeveloperToolsPlaygroundCards().map((feature, index) => (
            <Grid item xs={12} md={12 / ChatGPTDeveloperToolsPlaygroundCards().length} key={index}>
              <PlaygroundCard {...feature} />
            </Grid>
          ))}
        </Grid>
      </Box>

      <Box ref={welcomeHeaderRef}>
        <WelcomeHeader
          isPageView={true}
          title={t('playgrounds.chatGPTDeveloperTools.tryItYourself')}
          description={t('playgrounds.chatGPTDeveloperTools.tryItDescription')}
          showProgress={false}
        />
      </Box>

      <ChatGPTPlaygroundTry
        initialPrompt={t('playgrounds.chatGPTDeveloperTools.samplePrompt')}
        temperature={temperature}
        setTemperature={setTemperature}
        topP={topP}
        setTopP={setTopP}
        frequencyPenalty={frequencyPenalty}
        setFrequencyPenalty={setFrequencyPenalty}
        presencePenalty={presencePenalty}
        setPresencePenalty={setPresencePenalty}
        onBack={() => console.log('Back clicked')}
        disableBack={true}
      />
    </Container>
  );
};

export default ChatGPTDeveloperToolsPlayground;
