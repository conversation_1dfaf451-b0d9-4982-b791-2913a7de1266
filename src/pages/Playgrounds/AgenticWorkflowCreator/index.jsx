import { Box, Container, Grid, CircularProgress } from '@mui/material';
import { useRef, useState } from 'react';
import AIHero from '../../../components/AIHero/AIHero.jsx';
import PlaygroundCard from '../../../components/PlaygroundCard/PlaygroundCard.jsx';
import { CardContent } from '@/components/CourseCard/CourseCard';
import AppCard from '@/components/AppCard/AppCard';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import VideoThumbnailEN from '@/assets/images/agentic-workflow-creator-video-thumbnail.png';
import VideoThumbnailDE from '@/assets/images/agentic-workflow-creator-video-thumbnail-de.png';
import { AgenticWorkflowCreatorPlaygroundCards } from '../../../mockData/AgenticWorkflowCreatorPlaygroundCards.js';
import '../Playgrounds.scss';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useGetWorkflowsByUserQuery } from '../../../redux/services/create-workflow';
import { useTranslation } from 'react-i18next';
import InfoIcon from '@mui/icons-material/Info';
import ErrorIcon from '@mui/icons-material/Error';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';

const AgenticWorkflowCreatorPlayground = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const {
    data: userWorkflows,
    isLoading,
    isError,
  } = useGetWorkflowsByUserQuery(user?._id, {
    skip: !user?._id,
  });
  const { t, i18n } = useTranslation();

  const videoUrl = t('playgrounds.agenticWorkflowCreator.videoUrl');

  const currentLanguage = i18n.language || 'en';
  const videoThumbnail = currentLanguage === 'de' ? VideoThumbnailDE : VideoThumbnailEN;

  const handleButtonClick = () => {
    navigate('/workflow-creator/');
  };

  // userWorkflows değişkeninin yapısını kontrol edip doğru array'i alalım
  let workflowsArray = [];

  if (userWorkflows && typeof userWorkflows === 'object') {
    if (Array.isArray(userWorkflows)) {
      workflowsArray = userWorkflows;
    } else if (userWorkflows.data && Array.isArray(userWorkflows.data)) {
      workflowsArray = userWorkflows.data;
    } else if (userWorkflows.workflows && Array.isArray(userWorkflows.workflows)) {
      workflowsArray = userWorkflows.workflows;
    } else if (
      userWorkflows.data &&
      userWorkflows.data.workflows &&
      Array.isArray(userWorkflows.data.workflows)
    ) {
      workflowsArray = userWorkflows.data.workflows;
    } else if (userWorkflows.results && Array.isArray(userWorkflows.results)) {
      workflowsArray = userWorkflows.results;
    }
  }

  const hasWorkflows = workflowsArray.length > 0;

  const renderUserWorkflows = () => {
    if (isLoading) {
      return (
        <Box className="state-message--empty">
          <CircularProgress />
        </Box>
      );
    }

    if (isError) {
      return (
        <Box className="state-message--error">
          <ErrorIcon className="message-icon" />
          {t('playgrounds.agenticWorkflowCreator.loadError')}
        </Box>
      );
    }

    if (!hasWorkflows) {
      return (
        <Box className="state-message--info">
          <InfoIcon className="message-icon" />
          {t('playgrounds.agenticWorkflowCreator.noWorkflowsYet')}
        </Box>
      );
    }

    return (
      <Grid container className="app-card-wrapper" spacing={2}>
        {workflowsArray.map((workflow) => (
          <Grid
            item
            xs={12}
            md={4}
            key={workflow._id || workflow.id || `workflow-${Date.now()}-${Math.random()}`}
          >
            <AppCard
              title={workflow.title || ''}
              usecase_icon_url={workflow.usecase_icon_url || ''}
              usedCount={workflow.usedCount || 0}
              appType={'workflow'}
              id={workflow._id || workflow.id || ''}
              onClick={() => navigate(`/ai_workflows/${workflow.slug}`)}
            />
          </Grid>
        ))}
      </Grid>
    );
  };

  return (
    <Container>
      <AIHero
        title={t('playgrounds.agenticWorkflowCreator.title')}
        description={t('playgrounds.agenticWorkflowCreator.description')}
        buttonText={t('playgrounds.agenticWorkflowCreator.createAppButton')}
        onButtonClick={handleButtonClick}
        videoUrl={videoUrl}
        videoThumbnail={videoThumbnail}
      />

      <Box className="features-section border-none">
        <Grid container spacing={3}>
          {AgenticWorkflowCreatorPlaygroundCards().map((feature, index) => (
            <Grid item xs={12} md={12 / AgenticWorkflowCreatorPlaygroundCards().length} key={index}>
              <PlaygroundCard {...feature} />
            </Grid>
          ))}
        </Grid>
      </Box>

      <Grid item xs={12}>
        <WhiteContainer
          title={t('playgrounds.agenticWorkflowCreator.myAppsTitle')}
          subtitle={t('playgrounds.agenticWorkflowCreator.myAppsSubtitle')}
          showNavigation={false}
        >
          {renderUserWorkflows()}
        </WhiteContainer>
      </Grid>
    </Container>
  );
};

export default AgenticWorkflowCreatorPlayground;
