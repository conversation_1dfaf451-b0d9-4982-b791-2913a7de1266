import { Box, Container, Grid } from '@mui/material';
import { useRef, useState } from 'react';
import AIHero from '../../../components/AIHero/AIHero';
import PlaygroundCard from '../../../components/PlaygroundCard/PlaygroundCard';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader';
import ChatGPTPlaygroundTry from '../../../components/ChatGPTPlayground/ChatGPTPlayground';
import { ChatGPTPlaygroundCards } from '../../../mockData/ChatGPTPlaygroundCards';
import '../Playgrounds.scss';
import { useTranslation } from 'react-i18next';
import VideoThumbnailEN from '../../../assets/images/chatgpt-video-thumbnail.png';
import VideoThumbnailDE from '../../../assets/images/chatgpt-video-thumbnail-de.png';
// Import language-specific thumbnails

const ChatGPTPlayground = () => {
  const welcomeHeaderRef = useRef(null);
  const [temperature, setTemperature] = useState(1.0);
  const [topP, setTopP] = useState(1.0);
  const [frequencyPenalty, setFrequencyPenalty] = useState(0);
  const [presencePenalty, setPresencePenalty] = useState(0);
  const { t, i18n } = useTranslation();

  // Get video URL from translation file based on current language
  const videoUrl = t('playgrounds.chatGPT.videoUrl');

  // Select correct thumbnail based on language
  const currentLanguage = i18n.language || 'en';
  const videoThumbnail = currentLanguage === 'de' ? VideoThumbnailDE : VideoThumbnailEN;

  const handleTryItClick = () => {
    welcomeHeaderRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  };

  return (
    <Container>
      <AIHero
        title={t('playgrounds.chatGPT.title')}
        description={t('playgrounds.chatGPT.description')}
        buttonText={t('playgrounds.chatGPT.tryItButton')}
        onButtonClick={handleTryItClick}
        videoUrl={videoUrl}
        videoThumbnail={videoThumbnail}
      />

      <Box className="features-section">
        <Grid container spacing={3}>
          {ChatGPTPlaygroundCards().map((feature, index) => (
            <Grid item xs={12} md={12 / ChatGPTPlaygroundCards().length} key={index}>
              <PlaygroundCard {...feature} />
            </Grid>
          ))}
        </Grid>
      </Box>

      <Box ref={welcomeHeaderRef}>
        <WelcomeHeader
          isPageView={true}
          title={t('playgrounds.chatGPT.tryItYourself')}
          showProgress={false}
        />
      </Box>

      <ChatGPTPlaygroundTry
        initialPrompt={t('playgrounds.chatGPT.samplePrompt')}
        temperature={temperature}
        setTemperature={setTemperature}
        topP={topP}
        setTopP={setTopP}
        frequencyPenalty={frequencyPenalty}
        setFrequencyPenalty={setFrequencyPenalty}
        presencePenalty={presencePenalty}
        setPresencePenalty={setPresencePenalty}
        onBack={() => console.log('Back clicked')}
        disableBack={true}
      />
    </Container>
  );
};

export default ChatGPTPlayground;
