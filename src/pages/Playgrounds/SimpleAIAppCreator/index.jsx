import { Box, Container, Grid } from '@mui/material';
import { useRef, useEffect } from 'react';
import AIHero from '../../../components/AIHero/AIHero';
import PlaygroundCard from '../../../components/PlaygroundCard/PlaygroundCard';
import AppCard from '@/components/AppCard/AppCard';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import { SimpleAIAppCreatorPlaygroundCards } from '../../../mockData/SimpleAIAppCreatorPlaygroundCards';
import '../Playgrounds.scss';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useGetSimpleAppsByUserIdQuery } from '../../../redux/services/CreateApp-api';
import { CircularProgress } from '@mui/material';
import { useTranslation } from 'react-i18next';
import InfoIcon from '@mui/icons-material/Info';
import ErrorIcon from '@mui/icons-material/Error';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';

// Dil bazlı görselleri import et
import VideoThumbnailEN from '@/assets/images/simple-ai-app-creator-video-thumbnail.png';
import VideoThumbnailDE from '@/assets/images/simple-ai-app-creator-video-thumbnail-de.png';
console.log(VideoThumbnailDE);
const SimpleAIAppCreatorPlayground = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const {
    data: userApps,
    isLoading,
    isError,
  } = useGetSimpleAppsByUserIdQuery(user?._id, {
    skip: !user?._id,
  });
  const { t, i18n } = useTranslation();

  // Çeviri dosyasından dile göre video URL'sini al
  const videoUrl = t('playgrounds.simpleAIAppCreator.videoUrl');

  // Dile göre doğru thumbnail'i seç
  const currentLanguage = i18n.language || 'en';
  const videoThumbnail = currentLanguage === 'de' ? VideoThumbnailDE : VideoThumbnailEN;

  const handleButtonClick = () => {
    navigate('/simple-ai-app/create');
  };

  // userApps değişkeninin yapısını kontrol edip doğru array'i alalım
  let appsArray = [];
  let useCasesArray = [];

  // Sadeleştirilmiş veri kontrolü
  if (
    userApps &&
    userApps.data &&
    userApps.data.useCases &&
    Array.isArray(userApps.data.useCases)
  ) {
    useCasesArray = userApps.data.useCases;
  }

  // Öncelikle useCases verilerini, yoksa boş array kullan
  const displayArray = useCasesArray;
  const hasApps = displayArray.length > 0;

  const renderUserApps = () => {
    if (isLoading) {
      return (
        <Box className="state-message--empty">
          <CircularProgress />
        </Box>
      );
    }

    if (isError) {
      return (
        <Box className="state-message--error">
          <ErrorIcon className="message-icon" />
          {t('playgrounds.simpleAIAppCreator.loadError')}
        </Box>
      );
    }

    if (!hasApps) {
      return (
        <Box className="state-message--info">
          <InfoIcon className="message-icon" />
          {t('playgrounds.simpleAIAppCreator.noAppsYet')}
        </Box>
      );
    }

    return (
      <Grid container className="app-card-wrapper" spacing={2}>
        {displayArray.map((item) => (
          <Grid
            item
            xs={12}
            md={4}
            key={item._id || item.id || `app-${Date.now()}-${Math.random()}`}
          >
            <AppCard
              title={item.title || item.name || 'İsimsiz Uygulama'}
              usecase_icon_url={item.usecase_icon_url || item.iconUrl || ''}
              usedCount={item.usedCount || 0}
              appType={'ai_app'}
              id={item._id || item.id || ''}
              onClick={() => navigate(`/ai_apps/${item.slug}`)}
            />
          </Grid>
        ))}
      </Grid>
    );
  };

  return (
    <Container>
      <AIHero
        title={t('playgrounds.simpleAIAppCreator.title')}
        description={t('playgrounds.simpleAIAppCreator.description')}
        buttonText={t('playgrounds.simpleAIAppCreator.createAppButton')}
        onButtonClick={handleButtonClick}
        videoUrl={videoUrl}
        videoThumbnail={videoThumbnail}
      />

      <Box className="features-section border-none">
        <Grid container spacing={3}>
          {SimpleAIAppCreatorPlaygroundCards().map((feature) => (
            <Grid
              item
              xs={12}
              md={12 / SimpleAIAppCreatorPlaygroundCards().length}
              key={feature.title}
            >
              <PlaygroundCard {...feature} />
            </Grid>
          ))}
        </Grid>
      </Box>

      <Grid item xs={12}>
        <WhiteContainer
          title={t('playgrounds.simpleAIAppCreator.myAppsTitle')}
          subtitle={t('playgrounds.simpleAIAppCreator.myAppsSubtitle')}
          showNavigation={false}
        >
          {renderUserApps()}
        </WhiteContainer>
      </Grid>
    </Container>
  );
};

export default SimpleAIAppCreatorPlayground;
