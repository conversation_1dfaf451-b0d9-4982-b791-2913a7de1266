import { Box, Container, Grid } from '@mui/material';
import { useRef } from 'react';
import AIHero from '../../../components/AIHero/AIHero.jsx';
import { AdvancedGenAIPlaygroundCards } from '../../../mockData/AdvancedGenAIPlaygroundCards.js';
import PlaygroundCard from '../../../components/PlaygroundCard/PlaygroundCard.jsx';
import '../Playgrounds.scss';
import { useTranslation } from 'react-i18next';

// Dil bazlı görselleri import et
import VideoThumbnailEN from '../../../assets/images/genai-video-thumbnail.png';
import VideoThumbnailDE from '../../../assets/images/genai-video-thumbnail-de.png';

const AdvancedGenAICreator = () => {
  const welcomeHeaderRef = useRef(null);
  const { t, i18n } = useTranslation();

  // Çeviri dosyasından dile göre video URL'sini al
  const videoUrl = t('playgrounds.advancedGenAI.videoUrl');

  // Dile göre doğru thumbnail'i seç
  const currentLanguage = i18n.language || 'en';
  const videoThumbnail = currentLanguage === 'de' ? VideoThumbnailDE : VideoThumbnailEN;

  const handleTryItClick = () => {
    welcomeHeaderRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  };

  return (
    <Container>
      <AIHero
        title={t('playgrounds.advancedGenAI.title')}
        description={t('playgrounds.advancedGenAI.description')}
        onButtonClick={handleTryItClick}
        videoUrl={videoUrl}
        videoThumbnail={videoThumbnail}
      />

      <Box className="features-section">
        <Grid container spacing={3}>
          {AdvancedGenAIPlaygroundCards().map((feature, index) => (
            <Grid item xs={12} md={12 / AdvancedGenAIPlaygroundCards().length} key={index}>
              <PlaygroundCard {...feature} />
            </Grid>
          ))}
        </Grid>
      </Box>
    </Container>
  );
};

export default AdvancedGenAICreator;
