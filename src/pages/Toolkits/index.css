.toolkit-container {
  padding: var(--spacing-3);
  max-width: 1200px;
  margin: 0 auto;
}

.toolkit-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-3);
  margin-top: var(--spacing-3);
}

.toolkit-card {
  background: var(--bg-paper);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-3);
  box-shadow: var(--shadow-sm);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
}

.toolkit-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.toolkit-card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.toolkit-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.toolkit-card h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  flex: 1;
}

.toolkit-slug {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  text-transform: capitalize;
}

.toolkit-card-content {
  padding-left: 44px; /* icon width + gap */
}

.toolkit-tooltip {
  position: absolute;
  top: calc(100% + var(--spacing-2));
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-dark);
  color: var(--text-light);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  box-shadow: var(--shadow-md);
}

.toolkit-tooltip::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: var(--spacing-1) solid transparent;
  border-bottom-color: var(--bg-dark);
}

.toolkit-card:hover .toolkit-tooltip {
  opacity: 1;
  visibility: visible;
}

/* Microsoft Toolkit Styles */
.login-message {
  padding: var(--spacing-5) 0;
}

.tab-container {
  border-bottom: 1px solid var(--divider-color);
  margin-bottom: 0;
}

.tab-panel {
  padding-top: var(--spacing-4);
}

.pdf-container {
  width: 100%;
  padding: 0 !important;
  margin: 0 !important;
  height: calc(100vh - 180px);
  min-height: 600px;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: var(--border-radius-md);
} 