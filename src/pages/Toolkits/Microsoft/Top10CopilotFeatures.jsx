import { Box, Container, Grid, Typo<PERSON>, Button } from '@mui/material';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import '../index.css';

const Top10CopilotFeatures = () => {
  const user = useSelector((state) => state.auth.user);
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const pdfUrl =
    'https://aibs-content.s3.eu-central-2.amazonaws.com/Top-10-to-try+first+with+Copilot.pdf';
  if (!user) {
    return (
      <Container>
        <Box className="login-message">
          <Typography variant="h4">{t('common.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container className="tutorial-container">
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('tools.microsoft.top10Copilot.title')}
            description={t('tools.microsoft.top10Copilot.description')}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer title="" subtitle="" showNavigation={false} variant="backButtonTrue">
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/toolkits/microsoft/')}
              className="apply-back-button"
            >
              {t('common.backToToolkit')}
            </Button>

            <Grid container spacing={0}>
              <Grid item xs={12}>
                <Box className="pdf-container">
                  <iframe
                    src={pdfUrl}
                    title={t('tools.microsoft.top10Copilot.title')}
                    className="pdf-iframe"
                  />
                </Box>
              </Grid>
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Top10CopilotFeatures;
