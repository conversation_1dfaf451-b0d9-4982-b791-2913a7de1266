import { Box, Container, Grid, <PERSON>po<PERSON>, <PERSON><PERSON>, Tabs, Tab } from '@mui/material';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import '../index.css';

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`copilot-guidelines-tabpanel-${index}`}
      aria-labelledby={`copilot-guidelines-tab-${index}`}
      className="tab-panel"
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const Microsoft365ToolGuidelinesForCopilot = () => {
  const user = useSelector((state) => state.auth.user);
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);

  const pdfUrls = {
    teams:
      'https://aibs-content.s3.eu-central-2.amazonaws.com/GetStarted_Microsoft365CopilotinTeams.pdf',
    word: 'https://aibs-content.s3.eu-central-2.amazonaws.com/GetStarted_Microsoft365CopilotinWord.pdf',
    excel:
      'https://aibs-content.s3.eu-central-2.amazonaws.com/GetStarted_Microsoft365CopilotinExcel.pdf',
    powerpoint:
      'https://aibs-content.s3.eu-central-2.amazonaws.com/GetStarted_Microsoft365CopilotinPowerPoint.pdf',
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  if (!user) {
    return (
      <Container>
        <Box className="login-message">
          <Typography variant="h4">{t('common.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container className="tutorial-container">
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('tools.microsoft.microsoft365ToolGuidelinesForCopilot.title')}
            description={t('tools.microsoft.microsoft365ToolGuidelinesForCopilot.description')}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer title="" subtitle="" showNavigation={false} variant="backButtonTrue">
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/toolkits/microsoft/')}
              className="apply-back-button"
              sx={{ mb: 3 }}
            >
              {t('common.backToToolkit')}
            </Button>

            <Box className="tab-container">
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                aria-label="copilot guidelines tabs"
                variant="fullWidth"
              >
                <Tab
                  label="Copilot in Teams"
                  id="copilot-guidelines-tab-0"
                  aria-controls="copilot-guidelines-tabpanel-0"
                />
                <Tab
                  label="Copilot in Word"
                  id="copilot-guidelines-tab-1"
                  aria-controls="copilot-guidelines-tabpanel-1"
                />
                <Tab
                  label="Copilot in Excel"
                  id="copilot-guidelines-tab-2"
                  aria-controls="copilot-guidelines-tabpanel-2"
                />
                <Tab
                  label="Copilot in PowerPoint"
                  id="copilot-guidelines-tab-3"
                  aria-controls="copilot-guidelines-tabpanel-3"
                />
              </Tabs>
            </Box>

            <TabPanel value={activeTab} index={0}>
              <Box className="pdf-container">
                <iframe
                  src={pdfUrls.teams}
                  title={`${t('tools.microsoft.microsoft365ToolGuidelinesForCopilot.title')} - Teams`}
                  className="pdf-iframe"
                />
              </Box>
            </TabPanel>

            <TabPanel value={activeTab} index={1}>
              <Box className="pdf-container">
                <iframe
                  src={pdfUrls.word}
                  title={`${t('tools.microsoft.microsoft365ToolGuidelinesForCopilot.title')} - Word`}
                  className="pdf-iframe"
                />
              </Box>
            </TabPanel>

            <TabPanel value={activeTab} index={2}>
              <Box className="pdf-container">
                <iframe
                  src={pdfUrls.excel}
                  title={`${t('tools.microsoft.microsoft365ToolGuidelinesForCopilot.title')} - Excel`}
                  className="pdf-iframe"
                />
              </Box>
            </TabPanel>

            <TabPanel value={activeTab} index={3}>
              <Box className="pdf-container">
                <iframe
                  src={pdfUrls.powerpoint}
                  title={`${t('tools.microsoft.microsoft365ToolGuidelinesForCopilot.title')} - PowerPoint`}
                  className="pdf-iframe"
                />
              </Box>
            </TabPanel>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Microsoft365ToolGuidelinesForCopilot;
