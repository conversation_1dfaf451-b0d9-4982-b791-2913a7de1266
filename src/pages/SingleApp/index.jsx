// get the app id from the url || Soner
// eğer simple app veya workflow ise id ile get request gönder || Soner
// eğer diğer usecaseler ise slug ile get request gönder || Soner
// Route to the app page || Soner
// get the app data from the backend || Şükrü
//if app is usercreated get app data from adoption-v2-api else get app data from content-api || Şükrü
// get component by api_type || Burak
// render the component || Burak

import { useParams } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';
import PropTypes from 'prop-types';
import TextUsecase from '../../domains/apps/components/usecases/TextUsecase.jsx';
import AssistantUsecase from '../../domains/apps/components/usecases/AssistantUsecase.jsx';
import ImageUsecase from '../../domains/apps/components/usecases/ImageUsecase.jsx';
import VideoUsecase from '../../domains/apps/components/usecases/VideoUsecase.jsx';
import { useGetUseCaseBySlugQuery } from '../../redux/services/use-case-api';
import React, { useCallback } from 'react';
import { Container } from '@mui/material';

const SingleApp = ({ slug: propSlug, onGenerate, isCompleted, onlyShowUsecase = false }) => {
  const { slug: paramSlug } = useParams();
  const slug = propSlug || paramSlug;

  const {
    data: useCase,
    isLoading,
    error,
  } = useGetUseCaseBySlugQuery(slug, {
    skip: !slug,
    refetchOnMountOrArgChange: true,
  });

  const handleGenerate = useCallback(async () => {
    // Eğer tamamlanmışsa veya onGenerate fonksiyonu yoksa işlemi engelle
    if (isCompleted || !onGenerate) {
      return;
    }

    try {
      await onGenerate();
    } catch (error) {
      console.error('Topic tamamlama hatası:', error);
    }
  }, [onGenerate, isCompleted]);

  // api_type'a göre uygun bileşeni seçen yardımcı fonksiyon
  const renderUsecaseComponent = useCallback(
    (useCaseData) => {
      const { api_type } = useCaseData;

      const commonProps = {
        id: useCaseData._id,
        data: useCaseData,
        onGenerate: handleGenerate,
        onlyShowUsecase: onlyShowUsecase,
      };

      switch (api_type) {
        case 'completions':
        case 'stream':
          return <TextUsecase {...commonProps} />;
        case 'assistants':
          return <AssistantUsecase {...commonProps} />;
        case 'dalle':
          return <ImageUsecase {...commonProps} />;
        case 'heygen':
          return <VideoUsecase {...commonProps} />;
        default:
          console.warn(`Unknown api_type: ${api_type}, using TextUsecase.`);
          return <TextUsecase {...commonProps} />;
      }
    },
    [handleGenerate, onlyShowUsecase]
  );

  if (!slug) {
    return (
      <Container maxWidth="lg" sx={{ p: 3, mx: 'auto' }}>
        <div>Error: No slug provided</div>
      </Container>
    );
  }

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ p: 3, mx: 'auto' }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ p: 3, mx: 'auto' }}>
        <div>Error loading use case: {error.message}</div>
      </Container>
    );
  }

  if (!useCase) {
    return (
      <Container maxWidth="lg" sx={{ p: 3, mx: 'auto' }}>
        <div>Use case not found</div>
      </Container>
    );
  }

  // Assistant type için Container kullanmıyoruz
  if (useCase.api_type === 'assistants') {
    return renderUsecaseComponent(useCase);
  }

  // Diğer tipler için Container kullanıyoruz
  return <Container maxWidth="lg">{renderUsecaseComponent(useCase)}</Container>;
};

SingleApp.propTypes = {
  slug: PropTypes.string,
  onGenerate: PropTypes.func,
  isCompleted: PropTypes.bool,
  onlyShowUsecase: PropTypes.bool,
};

export default React.memo(SingleApp);
