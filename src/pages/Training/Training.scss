@use '../../styles/abstracts/variables' as *;

.journey-page {
  padding: $spacing-5 0;
  background-color: $bg-light;
  min-height: calc(100vh - 64px); // header height'ı çıkarıyoruz

  .page-header {
    margin-bottom: $spacing-5;
    
    h1 {
      margin-bottom: $spacing-2;
      color: $text-primary;
    }
  }

  .journey-section {
    padding: $spacing-5;
    background: $bg-paper;
    border-radius: $border-radius-lg;
    border: 1px solid $border-color;
    margin: 0 auto;
    max-width: 1200px;

    h2 {
      color: $text-primary;
    }

    .carousel-wrapper {
      margin: 0 -#{$spacing-4}; // Carousel'in container padding'ini sıfırlıyoruz
    }

    @media (max-width: $tablet) {
      padding: $spacing-3;
      margin: 0 $spacing-3;
      border-radius: $border-radius-md;

      .carousel-wrapper {
        margin: 0 -#{$spacing-3};
      }
    }
  }
} 