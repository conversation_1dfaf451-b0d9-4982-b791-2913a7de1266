// get the app id from the url || Soner
// Route to the app page || Soner
// get the app data from the backend || Şükrü

import { useParams, useNavigate } from 'react-router-dom';
import { Box, CircularProgress, Container, Grid } from '@mui/material';
import PropTypes from 'prop-types';
import TextUsecase from '../../domains/apps/components/usecases/TextUsecase.jsx';
import {
  useGetSimpleAppBySlugQuery,
  useDeleteSimpleAppMutation,
} from '../../redux/services/CreateApp-api.js';
import React, { useCallback, useState } from 'react';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader';
import { useSelector } from 'react-redux';
import Iconify from '../../components/Iconify/iconify';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import NotFoundPage from '../../components/404/index.jsx';
import './SingleAIAppCreator.scss';

const SingleAIAppCreator = ({ slug: propSlug, onGenerate, isCompleted }) => {
  const { slug: paramSlug } = useParams();
  const slug = propSlug || paramSlug;
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useSelector((state) => state.auth);
  const [deleteSimpleApp] = useDeleteSimpleAppMutation();

  const {
    data: appData,
    isLoading,
    error,
  } = useGetSimpleAppBySlugQuery(slug, {
    skip: !slug,
    refetchOnMountOrArgChange: true,
  });

  const handleGenerate = useCallback(async () => {
    // Eğer tamamlanmışsa veya onGenerate fonksiyonu yoksa işlemi engelle
    if (isCompleted || !onGenerate) {
      return;
    }

    try {
      await onGenerate();
    } catch (error) {
      console.error('Topic tamamlama hatası:', error);
    }
  }, [onGenerate, isCompleted]);

  // Düzenleme işlevi
  const handleEditApp = () => {
    navigate(`/simple-ai-app/edit/${slug}/`);
  };

  // Silme işlevi
  const handleDeleteApp = async () => {
    if (
      window.confirm(
        'Are you sure you want to delete this application? This action cannot be undone.'
      )
    ) {
      try {
        setIsSubmitting(true);
        // API çağrısı
        await deleteSimpleApp(appData._id).unwrap();

        // Başarılı silme durumunda bildirim göster ve AI Apps sayfasına yönlendir
        toast.success(
          'Application deleted successfully. You will be redirected to the playground page.',
          {
            autoClose: 3000,
            onClose: () => {
              window.location.href = '/playgrounds/simple-ai-app-creator';
            },
          }
        );
      } catch (error) {
        console.error('Error deleting application:', error);
        toast.error('An error occurred while deleting the application. Please try again.', {
          position: 'top-right',
          autoClose: 5000,
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };
  // Butonların kullanıcı rolüne göre görünürlüğünü kontrol et
  const canEditApp = () => {
    // If user is admin, they can edit regardless of who created it
    if (user && user.admin === true) {
      return true;
    }

    // If user is not admin, they can only edit if they are the creator
    return user && appData && user._id === appData.data.userId;
  };

  const canDeleteApp = () => {
    // Only admin users can delete apps
    return user && user.admin === true;
  };

  // App verisini TextUsecase bileşenine uygun formata dönüştürme
  const prepareAppDataForTextUsecase = (rawResponse) => {
    if (!rawResponse) return null;

    // API yanıtı, data içerisinde sarmalanmış uygulama verilerini içerir
    // Örnek: { status: "success", message: "Use Case retrieved", data: { _id: "...", ... } }
    const appData = rawResponse.data || rawResponse;

    // Eğer veri yoksa, null döndür
    if (!appData) return null;

    // HTML etiketlerini temizle
    const cleanDescription = appData.description
      ? appData.description.replace(/<\/?[^>]+(>|$)/g, '')
      : '';

    // Form alanlarını, TextUsecase'in beklediği formata dönüştür
    const formFields = (appData.simple_app_form || []).map((field) => {
      // Girdi tipini TextUsecase'in beklediği formata dönüştür
      let fieldType = field.type || 'text';

      // API'den gelen tipi, TextUsecase'in anladığı tipe dönüştür
      if (fieldType === 'input') fieldType = 'text';
      if (fieldType === 'textarea') fieldType = 'textarea';
      if (fieldType === 'select') fieldType = 'select';

      return {
        _id: field._id,
        type: fieldType,
        label: field.name,
        name: field.name,
        default_value: field.label || '',
        choices: field.choices || '',
      };
    });
    // TextUsecase'in beklediği tam veri yapısını oluştur
    return {
      _id: appData._id,
      title: appData.title,
      slug: appData.slug,
      content: cleanDescription,
      api_type: 'stream',
      stream_settings: {
        stream_form: {
          form_fields: formFields,
        },
        stream_prompt: appData.prompt || '',
        temperature: appData.temperature || '1',
        top_p: appData.top_p || '1',
        frequency_penalty: appData.frequency_penalty || '0',
        presence_penalty: appData.presence_penalty || '0',
        model_selection: appData.model || 'gpt-4o',
        max_token: '4000',
      },
    };
  };

  if (!slug) {
    return (
      <Container maxWidth="lg" sx={{ p: 3, mx: 'auto' }}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <div>Error: No slug provided</div>
          </Grid>
        </Grid>
      </Container>
    );
  }

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ p: 3, mx: 'auto' }}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
              <CircularProgress />
            </Box>
          </Grid>
        </Grid>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ p: 3, mx: 'auto' }}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <NotFoundPage />
          </Grid>
        </Grid>
      </Container>
    );
  }

  if (!appData) {
    return (
      <Container maxWidth="lg" sx={{ p: 3, mx: 'auto' }}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <NotFoundPage />
          </Grid>
        </Grid>
      </Container>
    );
  }

  // API'den gelen veriyi TextUsecase için uygun formata dönüştür
  const formattedData = prepareAppDataForTextUsecase(appData);

  // TextUsecase bileşenini container içinde render et
  return (
    <Container maxWidth="lg" className="single-ai-app-creator">
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
      <Grid container spacing={1}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView
            title={formattedData.title}
            description={formattedData.content}
            buttons={[
              {
                label: 'Edit',
                onClick: handleEditApp,
                variant: 'outlined',
                color: 'primary',
                className: 'app-edit-button',
                isVisible: canEditApp,
              },
              {
                label: 'Delete',
                onClick: handleDeleteApp,
                variant: 'outlined',
                color: 'error',
                className: 'app-delete-button',
                isVisible: canDeleteApp,
              },
            ]}
          />
        </Grid>
        <Grid item xs={12}>
          <TextUsecase
            id={formattedData._id}
            data={formattedData}
            onGenerate={handleGenerate}
            onlyShowUsecase={true}
            hideTitle={true}
            type="ai_app"
          />
        </Grid>
      </Grid>
    </Container>
  );
};

SingleAIAppCreator.propTypes = {
  slug: PropTypes.string,
  onGenerate: PropTypes.func,
  isCompleted: PropTypes.bool,
};

export default React.memo(SingleAIAppCreator);
