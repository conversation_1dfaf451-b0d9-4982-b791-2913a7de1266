import { Box, Container, Grid, Typography } from '@mui/material';
import { useSelector } from 'react-redux';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader.jsx';
import HeygenVideoCreator from '../../components/HeygenVideoCreator/index.jsx';
import { useTranslation } from 'react-i18next';

const HeygenVideoCreatorPage = () => {
  const user = useSelector((state) => state.auth.user);
  const { t } = useTranslation();

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">Please log in.</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('heygenVideoCreator.welcome.title')}
            description={t('heygenVideoCreator.welcome.description')}
            showProgress={false}
          />
        </Grid>
        <HeygenVideoCreator />
      </Grid>
    </Container>
  );
};

export default HeygenVideoCreatorPage;
