import { Box, Container, Grid, Typography, Button } from '@mui/material';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useNavigate } from 'react-router-dom';

const DALLECheatSheetPage = () => {
  const user = useSelector((state) => state.auth.user);
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleBackClick = () => {
    // Playground sayfasına geri dön
    navigate('/playgrounds/dall-e');
  };
  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('common.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('sheets.dalle.title').replace('playground', 'Prompt sheet')}
            description={`<p>${t('sheets.dalle.description')}</p><p>${t('sheets.dalle.reminder')}</p>`}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer title="" subtitle="" showNavigation={false} variant="backButtonTrue">
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={handleBackClick}
              className="tutorial-back-button"
            >
              {t('common.backToPlayground')}
            </Button>
            <Grid container spacing={0}>
              <iframe
                src={t('sheets.dalle.pdfUrl')}
                style={{
                  width: '100%',
                  height: '100vh', // Tam ekran yüksekliği
                  border: 'none',
                }}
                title="DALL·E Cheat Sheet"
              />
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

export default DALLECheatSheetPage;
