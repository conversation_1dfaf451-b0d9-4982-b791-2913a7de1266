import { Box, Container, Grid, Typography, Button } from '@mui/material';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useNavigate } from 'react-router-dom';

const ChatGPTCheatSheetPage = () => {
  const { t } = useTranslation();
  const user = useSelector((state) => state.auth.user);
  const navigate = useNavigate();

  const handleBackClick = () => {
    // Playground sayfasına geri dön
    navigate('/playgrounds/chatgpt');
  };

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('common.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Box sx={{ mt: 3 }}>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleBackClick}
          className="back-to-apply-button"
        >
          {t('tutorials.agenticWorkflowCreator.backToPlayground', 'Back to Playground')}
        </Button>
      </Box>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('sheets.chatgpt.title')}
            description={`<p>${t('sheets.chatgpt.description')}</p><p>${t('sheets.chatgpt.reminder')}</p>`}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer title="" subtitle="" showNavigation={false} variant="backButtonTrue">
            <Grid container spacing={0}>
              <iframe
                src={t('sheets.chatgpt.pdfUrl')}
                style={{
                  width: '100%',
                  height: '100vh', // Tam ekran yüksekliği
                  border: 'none',
                }}
                title={t('sheets.chatgpt.title')}
              />
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ChatGPTCheatSheetPage;
