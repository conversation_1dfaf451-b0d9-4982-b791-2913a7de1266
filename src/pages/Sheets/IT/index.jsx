import { Box, Container, Grid, Typo<PERSON>, Button } from '@mui/material';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import '../sheets.scss';

const ChatGPTCheatSheetPage = () => {
  const { t } = useTranslation();
  const user = useSelector((state) => state.auth.user);
  const navigate = useNavigate();
  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('common.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('sheets.it.title')}
            description={`<p>${t('sheets.it.description')}</p><p>${t('sheets.it.reminder')}</p>`}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer title="" subtitle="" showNavigation={false} variant="backButtonTrue">
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/playgrounds/chatgpt-developer-tools/')}
              className="apply-back-button"
            >
              {t('common.backToPlayground', 'Back to Playground')}
            </Button>
            <Box className="sheet__container">
              <Typography className="sheet__title">
                {t('sheets.it.title1', 'ChatGPT cheat sheet for data science')}
              </Typography>
              <Box className="sheet__iframe-wrapper">
                <iframe
                  src={t('sheets.it.pdfUrl1')}
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none',
                  }}
                  title={t('sheets.it.title1')}
                />
              </Box>
            </Box>
          </WhiteContainer>
          <WhiteContainer title="" subtitle="" showNavigation={false}>
            <Box className="sheet__container">
              <Typography className="sheet__title">
                {t('sheets.it.title2', 'Quality in IT projects cheat sheet')}
              </Typography>
              <Box className="sheet__iframe-wrapper">
                <iframe
                  src={t('sheets.it.pdfUrl2')}
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none',
                  }}
                  title={t('sheets.it.title2')}
                />
              </Box>
            </Box>
          </WhiteContainer>
          <WhiteContainer title="" subtitle="" showNavigation={false}>
            <Box className="sheet__container">
              <Typography className="sheet__title">
                {t('sheets.it.title3', 'Systems development life cycle cheat sheet')}
              </Typography>
              <Box className="sheet__iframe-wrapper">
                <iframe
                  src={t('sheets.it.pdfUrl3')}
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none',
                  }}
                  title={t('sheets.it.title3')}
                />
              </Box>
            </Box>
          </WhiteContainer>
          <WhiteContainer title="" subtitle="" showNavigation={false}>
            <Box className="sheet__container">
              <Typography className="sheet__title">
                {t('sheets.it.title4', 'Software design patterns and methodologies cheat sheet')}
              </Typography>
              <Box className="sheet__iframe-wrapper">
                <iframe
                  src={t('sheets.it.pdfUrl4')}
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none',
                  }}
                  title={t('sheets.it.title')}
                />
              </Box>
            </Box>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ChatGPTCheatSheetPage;
