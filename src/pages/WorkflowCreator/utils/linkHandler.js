/**
 * Global link click handler to fix navigation issues
 * This utility handles link clicks to ensure proper navigation in the application
 * Only triggers for navigation from workflow-creator pages to other pages
 */

export const initGlobalLinkHandler = () => {
  if (typeof window === 'undefined') return;

  // Check if we're on a workflow-creator page
  const isWorkflowCreatorPage = () => {
    const path = window.location.pathname;
    return path.includes('/workflow-creator');
  };

  // Only initialize if we're on a workflow-creator page
  if (!isWorkflowCreatorPage()) return;

  // Store the original pushState function
  const originalPushState = window.history.pushState;

  // Override pushState to force navigation after URL change
  window.history.pushState = function () {
    const result = originalPushState.apply(this, arguments);
    window.dispatchEvent(new Event('popstate'));
    return result;
  };

  // Add a global click handler for all links
  document.addEventListener(
    'click',
    (e) => {
      // Find if the click was on a link
      let target = e.target;
      while (target && target.tagName !== 'A') {
        target = target.parentNode;
        if (!target) break;
      }

      // If it's a link with href that's not # or javascript:
      if (
        target &&
        target.tagName === 'A' &&
        target.href &&
        !target.href.startsWith('#') &&
        !target.href.startsWith('javascript:') &&
        !target.classList.contains('custom-label') &&
        !target.hasAttribute('contenteditable')
      ) {
        // Get the URL
        const url = new URL(target.href);

        // Only handle navigation from workflow-creator pages to non-workflow pages
        if (
          isWorkflowCreatorPage() &&
          url.origin === window.location.origin &&
          !url.pathname.includes('/workflow-creator')
        ) {
          e.preventDefault();

          // Force a hard navigation to the new URL
          window.location.href = url.href;

          // If for some reason the above doesn't work, try this as a fallback
          setTimeout(() => {
            if (window.location.href !== url.href) {
              window.location.replace(url.href);
            }
          }, 100);
        }
      }
    },
    true
  );

  // Also handle popstate events to ensure proper navigation
  window.addEventListener('popstate', () => {
    // Only reload if we're on a workflow-creator page
    if (isWorkflowCreatorPage()) {
      window.location.reload();
    }
  });
};

export default initGlobalLinkHandler;
