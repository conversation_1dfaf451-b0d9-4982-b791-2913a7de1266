import PropTypes from 'prop-types';
import { Box, TextField, Button, Tooltip } from '@mui/material';
import { useTranslation } from 'react-i18next';
import '../WorkflowCreator.scss';

const WorkflowHeader = ({
  workflowName,
  onWorkflowNameChange,
  onLoadSample,
  onSaveDraft,
  onSaveAndRun,
  isEditMode = false,
}) => {
  const { t } = useTranslation();
  const isAppNameEmpty = !workflowName.trim();

  return (
    <Box component="section" className="workflow-header">
      <Box className="workflow-header-container">
        <Box className="workflow-header-input-section">
          <TextField
            id="appName"
            name="appName"
            placeholder={t('create.workflowCreator.header.workflowNamePlaceholder')}
            value={workflowName}
            onChange={(e) => onWorkflowNameChange(e.target.value)}
            error={isAppNameEmpty}
            size="small"
            sx={{
              '& .MuiOutlinedInput-root': {
                height: '44px',
                '&.Mui-error fieldset': {
                  borderColor: 'error.main',
                },
              },
            }}
          />
        </Box>
        <Box className="workflow-header-button-group">
          {!isEditMode && (
            <Tooltip
              title={t('create.workflowCreator.header.loadSampleTooltip')}
              placement="bottom"
              arrow
            >
              <Button
                variant="outlined"
                className="workflow-header-outline-button"
                onClick={onLoadSample}
              >
                {t('create.workflowCreator.header.loadSample')}
              </Button>
            </Tooltip>
          )}
          <Button
            variant="outlined"
            className="workflow-header-outline-button"
            onClick={onSaveDraft}
            size="small"
            data-save-message={t('create.workflowCreator.header.saveDraftMessage')}
            data-error-message={t('create.workflowCreator.header.saveDraftError')}
          >
            {t('create.workflowCreator.header.saveDraft')}
          </Button>
          <Button
            variant="contained"
            className="workflow-header-contained-button"
            color="primary"
            onClick={onSaveAndRun}
            size="small"
          >
            {t('create.workflowCreator.header.saveAndRun')}
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

WorkflowHeader.propTypes = {
  workflowName: PropTypes.string.isRequired,
  onWorkflowNameChange: PropTypes.func.isRequired,
  onLoadSample: PropTypes.func.isRequired,
  onSaveDraft: PropTypes.func.isRequired,
  onSaveAndRun: PropTypes.func.isRequired,
  isEditMode: PropTypes.bool,
};

export default WorkflowHeader;
