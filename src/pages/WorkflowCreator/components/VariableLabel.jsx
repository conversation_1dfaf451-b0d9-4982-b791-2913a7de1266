import { Box } from '@mui/material';
import PropTypes from 'prop-types';

const VariableLabel = ({ variable, onDragStart }) => (
  <Box
    component="span"
    href="#"
    draggable="true"
    className="prompt-label"
    data-target="prompt"
    data-key={variable.key}
    onDragStart={(e) => onDragStart(e, variable.value)}
    sx={{
      backgroundColor: '#f8f9fa',
      border: '1px solid #e9ecef',
      borderRadius: '4px',
      padding: '4px 8px',
      fontSize: '0.875rem',
      color: '#495057',
      textDecoration: 'none',
      cursor: 'move',
      '&:hover': {
        backgroundColor: '#e9ecef',
      },
    }}
  >
    {`{${variable.value.replace(/\s+/g, '_').toLowerCase()}}`}
  </Box>
);

VariableLabel.propTypes = {
  variable: PropTypes.shape({
    key: PropTypes.string.isRequired,
    value: PropTypes.string.isRequired,
  }).isRequired,
  onDragStart: PropTypes.func.isRequired,
};

export default VariableLabel;
