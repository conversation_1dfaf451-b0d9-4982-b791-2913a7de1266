import PropTypes from 'prop-types';
import { Box, Typography, IconButton, Tooltip, Paper, Slider } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useTranslation } from 'react-i18next';
import '../WorkflowCreator.scss';

const ParameterSettings = ({ settings, onSettingChange }) => {
  const { t } = useTranslation();

  const handleSettingChange = (setting, value) => {
    onSettingChange(setting, value);
  };

  return (
    <Paper className=" container container--paper" elevation={0}>
      <Typography variant="h6" className="text--title" gutterBottom>
        {t('create.workflowCreator.settings.title')}
      </Typography>

      <Box className="settings-container">
        {/* Temperature */}
        <Box className="settings-box">
          <Box className="settings-title">
            <Box component="span" className="settings-label">
              {t('create.workflowCreator.settings.temperature')}
              <Tooltip
                title={t('create.workflowCreator.settings.tooltips.temperature')}
                placement="top"
                arrow
              >
                <IconButton
                  size="small"
                  sx={{
                    ml: 0.5,
                    p: 0,
                    transition: 'color 0.2s ease',
                    '&:hover': {
                      color: '#1976d2',
                    },
                  }}
                >
                  <InfoOutlinedIcon sx={{ fontSize: '14px', color: 'text.secondary' }} />
                </IconButton>
              </Tooltip>
            </Box>
            <Box className="number-display">{settings.temperature}</Box>
          </Box>
          <Box className="parameter-settings__slider">
            <Slider
              value={parseFloat(settings.temperature)}
              onChange={(e, value) => handleSettingChange('temperature', value.toString())}
              min={0}
              max={2}
              step={0.1}
              valueLabelDisplay="auto"
            />
          </Box>
        </Box>

        {/* Top P */}
        <Box className="settings-box">
          <Box className="settings-title">
            <Box component="span" className="settings-label">
              {t('create.workflowCreator.settings.topP')}
              <Tooltip
                title={t('create.workflowCreator.settings.tooltips.topP')}
                placement="top"
                arrow
              >
                <IconButton
                  size="small"
                  sx={{
                    ml: 0.5,
                    p: 0,
                    transition: 'color 0.2s ease',
                    '&:hover': {
                      color: '#1976d2',
                    },
                  }}
                >
                  <InfoOutlinedIcon sx={{ fontSize: '14px', color: 'text.secondary' }} />
                </IconButton>
              </Tooltip>
            </Box>
            <Box className="number-display">{settings.topP}</Box>
          </Box>
          <Box className="parameter-settings__slider">
            <Slider
              value={parseFloat(settings.topP)}
              onChange={(e, value) => handleSettingChange('topP', value.toString())}
              min={0}
              max={1}
              step={0.1}
              valueLabelDisplay="auto"
            />
          </Box>
        </Box>

        {/* Frequency Penalty */}
        <Box className="settings-box">
          <Box className="settings-title">
            <Box component="span" className="settings-label">
              {t('create.workflowCreator.settings.frequencyPenalty')}
              <Tooltip
                title={t('create.workflowCreator.settings.tooltips.frequencyPenalty')}
                placement="top"
                arrow
              >
                <IconButton
                  size="small"
                  sx={{
                    ml: 0.5,
                    p: 0,
                    transition: 'color 0.2s ease',
                    '&:hover': {
                      color: '#1976d2',
                    },
                  }}
                >
                  <InfoOutlinedIcon sx={{ fontSize: '14px', color: 'text.secondary' }} />
                </IconButton>
              </Tooltip>
            </Box>
            <Box className="number-display">{settings.frequencyPenalty}</Box>
          </Box>
          <Box className="parameter-settings__slider">
            <Slider
              value={parseFloat(settings.frequencyPenalty)}
              onChange={(e, value) => handleSettingChange('frequencyPenalty', value.toString())}
              min={0}
              max={2}
              step={0.1}
              valueLabelDisplay="auto"
            />
          </Box>
        </Box>

        {/* Presence Penalty */}
        <Box className="settings-box">
          <Box className="settings-title">
            <Box component="span" className="settings-label">
              {t('create.workflowCreator.settings.presencePenalty')}
              <Tooltip
                title={t('create.workflowCreator.settings.tooltips.presencePenalty')}
                placement="top"
                arrow
              >
                <IconButton
                  size="small"
                  sx={{
                    ml: 0.5,
                    p: 0,
                    transition: 'color 0.2s ease',
                    '&:hover': {
                      color: '#1976d2',
                    },
                  }}
                >
                  <InfoOutlinedIcon sx={{ fontSize: '14px', color: 'text.secondary' }} />
                </IconButton>
              </Tooltip>
            </Box>
            <Box className="number-display">{settings.presencePenalty}</Box>
          </Box>
          <Box className="parameter-settings__slider">
            <Slider
              value={parseFloat(settings.presencePenalty)}
              onChange={(e, value) => handleSettingChange('presencePenalty', value.toString())}
              min={0}
              max={2}
              step={0.1}
              valueLabelDisplay="auto"
            />
          </Box>
        </Box>
      </Box>
    </Paper>
  );
};

ParameterSettings.propTypes = {
  settings: PropTypes.shape({
    temperature: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    topP: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    frequencyPenalty: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    presencePenalty: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  }).isRequired,
  onSettingChange: PropTypes.func.isRequired,
};

export default ParameterSettings;
