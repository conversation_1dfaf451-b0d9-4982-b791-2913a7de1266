import PropTypes from 'prop-types';
import { Box } from '@mui/material';

import WorkflowDropArea from './WorkflowDropArea';
import '../WorkflowCreator.scss';

const WorkflowColumn = (props) => {
  return (
    <Box
      className="workflow-column"
      sx={{
        width: '100%',
        transition: 'all 0.3s ease',
        '&:hover .drop-area': {
          borderColor: 'rgba(25, 118, 210, 0.4)',
        },
      }}
    >
      <WorkflowDropArea {...props} />
    </Box>
  );
};

WorkflowColumn.propTypes = {
  droppedElements: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      title: PropTypes.string.isRequired,
      icon: PropTypes.string.isRequired,
      input: PropTypes.string,
      detail: PropTypes.string,
      outputName: PropTypes.string,
      promptContent: PropTypes.string,
    })
  ).isRequired,
  onDrop: PropTypes.func.isRequired,
  onDragOver: PropTypes.func.isRequired,
  onElementDragStart: PropTypes.func.isRequired,
  onElementDragOver: PropTypes.func.isRequired,
  onElementDragLeave: PropTypes.func.isRequired,
  onDeleteElement: PropTypes.func.isRequired,
  onElementInputChange: PropTypes.func.isRequired,
  onElementDetailChange: PropTypes.func.isRequired,
  onOutputNameChange: PropTypes.func.isRequired,
};

export default WorkflowColumn;
