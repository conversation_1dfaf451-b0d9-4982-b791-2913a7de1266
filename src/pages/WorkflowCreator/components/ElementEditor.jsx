import { Editor } from '@tinymce/tinymce-react';
import PropTypes from 'prop-types';

const ElementEditor = ({ element, onContentChange, editorRef }) => {
  const editorConfig = {
    // Move all editor configuration here
    height: 300,
    branding: false,
    // ... rest of the config
  };

  return (
    <Editor
      id={`${element.type}_${element.id}`}
      tinymceScriptSrc="/tinymce/tinymce.min.js"
      init={editorConfig}
      onInit={(_, editor) => editorRef(editor, element.id)}
      value={element.type === 'prompt' ? element.promptContent : element.detail}
      onEditorChange={(content) => onContentChange(element.id, content, element.type)}
    />
  );
};

ElementEditor.propTypes = {
  element: PropTypes.shape({
    id: PropTypes.string.isRequired,
    type: PropTypes.string.isRequired,
    promptContent: PropTypes.string,
    detail: PropTypes.string,
  }).isRequired,
  onContentChange: PropTypes.func.isRequired,
  editorRef: PropTypes.func.isRequired,
};

export default ElementEditor;
