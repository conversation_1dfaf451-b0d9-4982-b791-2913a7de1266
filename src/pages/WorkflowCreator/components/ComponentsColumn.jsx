import PropTypes from 'prop-types';
import { Box, Typography, Paper, Tooltip, IconButton } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useTranslation } from 'react-i18next';
import '../WorkflowCreator.scss';

const ComponentsColumn = ({ onDragStart }) => {
  const { t } = useTranslation();

  return (
    <Box sx={{ width: '260px' }}>
      <Paper
        id="components"
        elevation={0}
        sx={{
          backgroundColor: '#fff',
          p: 2,
          borderRadius: 2,
          border: '1px solid #e0e0e0',
          transition: 'box-shadow 0.3s ease',
          '&:hover': {
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.05)',
          },
        }}
      >
        <Typography
          className="element-title"
          sx={{
            fontSize: '12px',
            fontWeight: 600,
            mb: 1.5,
            color: '#677788',
          }}
        >
          {t('create.workflowCreator.componentsColumn.title')}
        </Typography>
        <Box className="item-list">
          <Box
            component="a"
            className="component component-element"
            draggable="true"
            onDragStart={(e) => onDragStart(e, 'text')}
            data-template="text"
          >
            <Box
              component="img"
              className="component-img"
              src="https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/text-input.svg"
              alt={t('create.workflowCreator.components.textInput')}
              sx={{
                width: '24px',
                height: '24px',
                marginBottom: '8px',
                transition: 'all 0.3s ease',
                position: 'relative',
                zIndex: 1,
              }}
            />
            <Typography
              className="component-text"
              sx={{
                fontSize: '12px',
                textAlign: 'center',
                fontWeight: 500,
                position: 'relative',
                zIndex: 1,
                transition: 'color 0.3s ease',
                '.component:hover &': {
                  color: '#1976d2',
                },
              }}
            >
              {t('create.workflowCreator.components.textInput')}
            </Typography>
            <Tooltip
              title={t('create.workflowCreator.componentsColumn.tooltips.textInput')}
              placement="right"
              arrow
            >
              <IconButton
                size="small"
                sx={{
                  position: 'absolute',
                  top: 4,
                  right: 4,
                  p: 0,
                  transition: 'color 0.2s ease',
                  '&:hover': {
                    color: '#1976d2',
                  },
                }}
              >
                <InfoOutlinedIcon sx={{ fontSize: '12px' }} />
              </IconButton>
            </Tooltip>
          </Box>

          <Box
            component="a"
            className="component component-element"
            draggable="true"
            onDragStart={(e) => onDragStart(e, 'output')}
            data-template="output"
          >
            <Box
              component="img"
              className="component-img"
              src="https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/output.svg"
              alt={t('create.workflowCreator.components.display')}
              sx={{
                width: '24px',
                height: '24px',
                marginBottom: '8px',
                transition: 'all 0.3s ease',
                position: 'relative',
                zIndex: 1,
              }}
            />
            <Typography
              className="component-text"
              sx={{
                fontSize: '12px',
                textAlign: 'center',
                fontWeight: 500,
                position: 'relative',
                zIndex: 1,
                transition: 'color 0.3s ease',
                '.component:hover &': {
                  color: '#1976d2',
                },
              }}
            >
              {t('create.workflowCreator.components.display')}
            </Typography>
            <Tooltip
              title={t('create.workflowCreator.componentsColumn.tooltips.display')}
              placement="right"
              arrow
            >
              <IconButton
                size="small"
                sx={{
                  position: 'absolute',
                  top: 4,
                  right: 4,
                  p: 0,
                  transition: 'color 0.2s ease',
                  '&:hover': {
                    color: '#1976d2',
                  },
                }}
              >
                <InfoOutlinedIcon sx={{ fontSize: '12px' }} />
              </IconButton>
            </Tooltip>
          </Box>

          <Box
            component="a"
            className="component full-width component-element"
            draggable="true"
            onDragStart={(e) => onDragStart(e, 'prompt')}
            data-template="prompt"
          >
            <Box
              component="img"
              className="component-img"
              src="https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/prompt.svg"
              alt={t('create.workflowCreator.components.prompt')}
              sx={{
                width: '24px',
                height: '24px',
                marginBottom: '8px',
                transition: 'all 0.3s ease',
                position: 'relative',
                zIndex: 1,
              }}
            />
            <Typography
              className="component-text"
              sx={{
                fontSize: '12px',
                textAlign: 'center',
                fontWeight: 500,
                position: 'relative',
                zIndex: 1,
                transition: 'color 0.3s ease',
                '.component:hover &': {
                  color: '#1976d2',
                },
              }}
            >
              {t('create.workflowCreator.components.prompt')}
            </Typography>
            <Tooltip
              title={t('create.workflowCreator.componentsColumn.tooltips.prompt')}
              placement="right"
              arrow
            >
              <IconButton
                size="small"
                sx={{
                  position: 'absolute',
                  top: 4,
                  right: 4,
                  p: 0,
                  transition: 'color 0.2s ease',
                  '&:hover': {
                    color: '#1976d2',
                  },
                }}
              >
                <InfoOutlinedIcon sx={{ fontSize: '12px' }} />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

ComponentsColumn.propTypes = {
  onDragStart: PropTypes.func.isRequired,
};

export default ComponentsColumn;
