import { useState, useEffect, useRef, useCallback } from 'react';
import { Box, Container, Dialog, DialogContent, CircularProgress, Typography } from '@mui/material';
import { useLocation } from 'react-router-dom';
import { initGlobalLinkHandler } from './utils/linkHandler';
import { useSelector } from 'react-redux';
import { useGetJourneyTrackingQuery } from '../../redux/services/journey-api';
import useUpdateJourneyTracking from '../../domains/journey/utils/updateJourneyTracking';
import { useTranslation } from 'react-i18next';
// import { driver } from 'driver.js';
// import 'driver.js/dist/driver.css';

import './WorkflowCreator.scss';
import WorkflowHeader from './components/WorkflowHeader';
import ComponentsColumn from './components/ComponentsColumn';
import WorkflowColumn from './components/WorkflowColumn';
import ParameterSettings from './components/ParameterSettings';
import AlertSnackbar from './components/AlertSnackbar';

import { useWorkflowState } from '../../hooks/useWorkflowState';
import {
  useSaveAndRunWorkflowMutation,
  useGetWorkflowByIdQuery,
} from '../../redux/services/create-workflow';
import { getSampleElements } from './sample-workflow';
import { developmentLogs } from '../../utils/developmentLogs';

// Initialize the global link handler

const WorkflowCreatorPage = () => {
  const { droppedElements, setDroppedElements, settings, setSettings } = useWorkflowState();
  const location = useLocation();
  const { t, i18n } = useTranslation();

  // Redux store'dan kullanıcı verilerini al
  const user = useSelector((state) => state.auth.user);

  // Journey card id state'ten al
  const journeyCardId = location.state?.cardId;

  // Journey tracking güncellemek için hook'u ve verileri al
  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();
  const { data: trackingData } = useGetJourneyTrackingQuery(user?._id, {
    skip: !user?._id,
  });

  // Initialize the global link handler when the component mounts
  useEffect(() => {
    initGlobalLinkHandler();
  }, []);

  // Parse URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const isEditMode = queryParams.get('workflow_action') === 'edit';
  const workflowName = queryParams.get('workflow_name');
  const workflowId = queryParams.get('workflow_id');

  // Fetch workflow data if in edit mode
  const { data: workflowData, isLoading: isLoadingWorkflow } = useGetWorkflowByIdQuery(
    workflowName,
    {
      skip: !isEditMode || !workflowName,
    }
  );

  const [alert, setAlert] = useState({
    open: false,
    message: '',
    severity: 'warning',
  });

  const [appName, setAppName] = useState(() => {
    const saved = localStorage.getItem('appName');
    return saved ? JSON.parse(saved) : '';
  });

  const [saveAndRunWorkflow] = useSaveAndRunWorkflowMutation();

  const [isCreating, setIsCreating] = useState(false);
  // const driverRef = useRef(null);

  // Tour için driver.js konfigürasyonu
  // const initTour = useCallback(() => {
  //   driverRef.current = driver({
  //     showProgress: true,
  //     showButtons: ['next', 'previous', 'close'],
  //     steps: [
  //       {
  //         element: '#appName',
  //         popover: {
  //           title: t('create.workflowCreator.tour.steps.appName.title'),
  //           description: t('create.workflowCreator.tour.steps.appName.content'),
  //           side: 'bottom',
  //           align: 'start',
  //         },
  //       },
  //       {
  //         element: '.workflow-header-button-group',
  //         popover: {
  //           title: t('create.workflowCreator.tour.steps.headerButtons.title'),
  //           description: t('create.workflowCreator.tour.steps.headerButtons.content'),
  //           side: 'bottom',
  //           align: 'start',
  //         },
  //       },
  //       {
  //         element: '.MuiPaper-root#components',
  //         popover: {
  //           title: t('create.workflowCreator.tour.steps.componentsArea.title'),
  //           description: t('create.workflowCreator.tour.steps.componentsArea.content'),
  //           side: 'right',
  //           align: 'start',
  //         },
  //       },
  //       {
  //         element: '.drop-area',
  //         popover: {
  //           title: t('create.workflowCreator.tour.steps.dropArea.title'),
  //           description: t('create.workflowCreator.tour.steps.dropArea.content'),
  //           side: 'left',
  //           align: 'start',
  //         },
  //       },
  //     ],
  //     nextBtnText: t('modal.next'),
  //     prevBtnText: t('modal.previous'),
  //     doneBtnText: t('modal.done'),
  //     closeBtnText: t('modal.close'),
  //     onReset: () => {
  //       localStorage.setItem('workflowCreatorTourShown', 'true');
  //       if (driverRef.current) {
  //         driverRef.current.destroy();
  //       }
  //     },
  //     // Tur tamamlandığında veya kapatıldığında localStorage'a kaydet
  //     onDestroyStarted: () => {
  //       localStorage.setItem('workflowCreatorTourShown', 'true');
  //       if (driverRef.current) {
  //         driverRef.current.destroy();
  //       }
  //     },
  //   });
  // }, [t]);

  // Sayfa yüklendiğinde tur için driver.js'i başlat
  // useEffect(() => {
  //   // localStorage'da tur gösterilip gösterilmediğini kontrol et
  //   const tourShown = localStorage.getItem('workflowCreatorTourShown');

  //   // Eğer tur daha önce gösterilmediyse
  //   if (!tourShown && !isEditMode) {
  //     initTour();

  //     // Sayfa tamamen yüklendikten sonra turu başlat
  //     const timer = setTimeout(() => {
  //       if (driverRef.current) {
  //         driverRef.current.drive();
  //       }
  //     }, 1000);

  //     return () => clearTimeout(timer);
  //   }
  // }, [initTour, isEditMode]);

  // Load workflow data when in edit mode
  useEffect(() => {
    if (isEditMode && workflowData?.data) {
      // Set app name
      setAppName(workflowData.data.title || '');
      localStorage.setItem('appName', JSON.stringify(workflowData.data.title || ''));

      // Transform workflow_form to droppedElements format
      const elements = workflowData.data.workflow_form.map((item, index) => {
        const elementType = item.type;
        const elementId = `${elementType}_${Date.now() + index}`;

        return {
          id: elementId,
          type: elementType,
          title:
            elementType === 'input'
              ? t('create.workflowCreator.components.textInput')
              : elementType === 'output'
                ? t('create.workflowCreator.components.display')
                : t('create.workflowCreator.components.prompt'),
          icon: `https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/${
            elementType === 'output'
              ? 'output.svg'
              : elementType === 'prompt'
                ? 'prompt.svg'
                : 'text-input.svg'
          }`,
          input: item.label || '',
          detail: elementType === 'input' || elementType === 'output' ? item.value || '' : '',
          outputName: elementType === 'prompt' ? item.name || '' : '',
          promptContent: elementType === 'prompt' ? item.value || '' : '',
          inputs:
            elementType === 'input'
              ? [
                  { inputType: 'text', name: 'input-key', value: item.label || '' },
                  { inputType: 'text', name: 'input-value', value: item.value || '' },
                ]
              : [],
        };
      });

      setDroppedElements(elements);
      localStorage.setItem('dropElementsData', JSON.stringify(elements));

      // Set settings if available
      if (
        workflowData.data.temperature ||
        workflowData.data.top_p ||
        workflowData.data.presence_penalty ||
        workflowData.data.frequency_penalty
      ) {
        setSettings({
          temperature: workflowData.data.temperature || '1',
          topP: workflowData.data.top_p || '1',
          presencePenalty: workflowData.data.presence_penalty || '0',
          frequencyPenalty: workflowData.data.frequency_penalty || '0',
        });
      }
    }
  }, [isEditMode, workflowData, setDroppedElements, setSettings, t]);

  useEffect(() => {
    if (Array.isArray(droppedElements)) {
      localStorage.setItem('dropElementsData', JSON.stringify(droppedElements));
    }
  }, [droppedElements]);

  useEffect(() => {
    if (!isEditMode) {
      localStorage.setItem('appName', JSON.stringify(''));
    }
  }, [isEditMode]);

  const handleDragStart = (e, template) => {
    e.dataTransfer.setData('template', template);
  };

  const handleDrop = useCallback(
    (e) => {
      e.preventDefault();
      const template = e.dataTransfer.getData('template');
      const elementId = e.dataTransfer.getData('elementId');

      // Check if there's already a display element
      const hasDisplayElement = droppedElements.some((element) => element.type === 'output');

      if (template === 'output' && hasDisplayElement) {
        setAlert({
          open: true,
          message: t('create.workflowCreator.alerts.maxDisplay'),
          severity: 'warning',
        });
        return;
      }

      if (elementId) {
        const sourceIndex = droppedElements.findIndex((el) => el.id === elementId);
        // Find the target element that we're dropping onto
        const targetElement = e.target.closest('.workflow-element');
        if (targetElement) {
          const targetId = targetElement.id;
          const targetIndex = droppedElements.findIndex((el) => el.id === targetId);

          if (sourceIndex === targetIndex) return;

          const newElements = [...droppedElements];
          const [removed] = newElements.splice(sourceIndex, 1);
          newElements.splice(targetIndex, 0, removed);
          setDroppedElements(newElements);
        }
        return;
      }

      if (template) {
        const newElement = {
          id: `${template}_${Date.now()}`,
          type: template === 'text' ? 'input' : template,
          title:
            template === 'text'
              ? t('create.workflowCreator.components.textInput')
              : template === 'output'
                ? t('create.workflowCreator.components.display')
                : t('create.workflowCreator.components.prompt'),
          icon: `https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/${
            template === 'output'
              ? 'output.svg'
              : template === 'prompt'
                ? 'prompt.svg'
                : 'text-input.svg'
          }`,
          input: '',
          detail: '',
          outputName: '',
          promptContent: '',
          inputs:
            template === 'text'
              ? [
                  { inputType: 'text', name: 'input-key', value: '' },
                  { inputType: 'text', name: 'input-value', value: '' },
                ]
              : [],
        };

        // Find the target element that we're dropping onto
        const targetElement = e.target.closest('.workflow-element');
        if (targetElement) {
          const targetId = targetElement.id;
          const targetIndex = droppedElements.findIndex((el) => el.id === targetId);
          const newElements = [...droppedElements];
          newElements.splice(targetIndex, 0, newElement);
          setDroppedElements(newElements);
        } else {
          // If not dropping onto an element, add to the end
          setDroppedElements((prev) => [...prev, newElement]);
        }
      }
    },
    [droppedElements, setDroppedElements, t]
  );

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDeleteElement = (id) => {
    const newElements = droppedElements.filter((element) => element.id !== id);
    setDroppedElements(newElements);
  };

  const handleElementInputChange = (id, value) => {
    const newElements = droppedElements.map((element) =>
      element.id === id ? { ...element, input: value } : element
    );
    setDroppedElements(newElements);
  };

  const handleElementDetailChange = (id, value) => {
    const newElements = droppedElements.map((element) =>
      element.id === id
        ? {
            ...element,
            [element.type === 'prompt' ? 'promptContent' : 'detail']: value,
          }
        : element
    );
    setDroppedElements(newElements);
  };

  // Add new handler for output name changes
  const handleOutputNameChange = (id, value) => {
    const newElements = droppedElements.map((element) =>
      element.id === id ? { ...element, outputName: value } : element
    );
    setDroppedElements(newElements);
  };

  // Add handlers for new buttons
  const handleLoadSample = () => {
    // Prevent loading samples in edit mode
    if (isEditMode) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.noEditSample'),
        severity: 'warning',
      });
      return;
    }

    if (Array.isArray(droppedElements) && droppedElements.length > 0) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.removeExisting'),
        severity: 'warning',
      });
      return;
    }

    clearStorage();

    try {
      // Mevcut dile göre örnek iş akışını al
      const currentLanguage = i18n.language;
      const elements = getSampleElements(currentLanguage);

      // localStorage'a kaydet
      localStorage.setItem('appName', JSON.stringify('Sample Workflow'));
      localStorage.setItem('tinymce-custom-colors-forecolor', JSON.stringify([]));
      localStorage.setItem('tinymce-custom-colors-hilitecolor', JSON.stringify([]));
      localStorage.setItem('workflow-tour', JSON.stringify(true));
      localStorage.setItem('dropElementsData', JSON.stringify(elements));

      // State'i güncelle
      setDroppedElements(elements);
    } catch (error) {
      console.error('Error loading sample:', error);
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.sampleLoadError'),
        severity: 'error',
      });
    }
  };

  const handleSaveDraft = () => {
    if (droppedElements.length === 0) {
      // Show error message: "You must enter the workflow name and create a flow before you can save it."
      return;
    }
    // Implement save draft logic
    // Show success message: "Saved as a draft."
  };

  // Add handler for workflow name change
  const handleWorkflowNameChange = (value) => {
    setAppName(value);
    localStorage.setItem('appName', JSON.stringify(value));
  };

  // Add helper function to check for variables in prompt content
  const hasVariablesInPrompt = (promptContent) => {
    if (!promptContent) return false;

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = promptContent;
    return tempDiv.querySelectorAll('.custom-label').length > 0;
  };

  // Add helper function to check for empty input fields
  const hasEmptyInputs = (elements) => {
    return elements.some(
      (element) => element.type === 'input' && (!element.input || element.input.trim() === '')
    );
  };

  // Add helper function to check for empty output names in prompts
  const hasEmptyPromptOutputNames = (elements) => {
    return elements.some(
      (element) =>
        element.type === 'prompt' && (!element.outputName || element.outputName.trim() === '')
    );
  };

  // Update handleSaveAndRun to check for empty inputs
  const handleSaveAndRun = async () => {
    if (!appName.trim()) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.enterAppName'),
        severity: 'warning',
      });
      return;
    }

    if (droppedElements.length === 0) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.createWorkflow'),
        severity: 'error',
      });
      return;
    }

    // Check if there's a Display component at the end of the workflow
    const hasDisplayElement = droppedElements.some((element) => element.type === 'output');
    if (!hasDisplayElement) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.includeDisplay'),
        severity: 'warning',
      });
      return;
    }

    // Check for empty input fields
    if (hasEmptyInputs(droppedElements)) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.fillInputs'),
        severity: 'warning',
      });
      return;
    }

    // Check for empty output names in prompts
    if (hasEmptyPromptOutputNames(droppedElements)) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.fillOutputNames'),
        severity: 'warning',
      });
      return;
    }

    // Check if any Prompt component is empty or has no variables
    const invalidPrompt = droppedElements.find(
      (element) =>
        element.type === 'prompt' &&
        (!element.promptContent ||
          element.promptContent.trim() === '' ||
          !hasVariablesInPrompt(element.promptContent))
    );

    if (invalidPrompt) {
      setAlert({
        open: true,
        message: t('create.workflowCreator.alerts.includeVariable'),
        severity: 'warning',
      });
      return;
    }

    try {
      setIsCreating(true); // Start loading

      // Transform droppedElements into workflow_form format
      const workflow_form = droppedElements.map((element) => ({
        type: element.type,
        label: element.input || element.title,
        name: element.outputName || element.input?.toLowerCase().replace(/\s+/g, '_'),
        value: element.detail || element.promptContent,
      }));

      const payload = {
        title: appName,
        workflow_form,
        temperature: settings.temperature || '1',
        top_p: settings.topP || '1',
        presence_penalty: settings.presencePenalty || '0',
        frequency_penalty: settings.frequencyPenalty || '0',
        userId: user._id,
      };

      // If in edit mode, include the workflow ID
      if (isEditMode) {
        // First priority: use the _id from the fetched data
        if (workflowData?.data?._id) {
          payload.id = workflowData.data._id;
        }
        // Second priority: use the workflowId from query params if available
        else if (workflowId) {
          payload.id = workflowId;
        }

        if (!payload.id) {
          throw new Error('No workflow ID available for update operation');
        }
      }

      const response = await saveAndRunWorkflow(payload).unwrap();

      // Journey tracking güncellemesi yap - eğer cardId varsa
      if (journeyCardId && user?._id && user?.journeyLevel?.name && trackingData) {
        try {
          // Kullanıcının journey level'ını kullan
          const userLevel = user.journeyLevel.name.toLowerCase();

          // Journey tracking güncellemesi yap
          await updateJourneyTrackingCard({
            userId: user._id,
            journeyTrackingData: trackingData,
            userLevel: userLevel,
            cardId: journeyCardId,
          });

          // Redux cache'i yenile - localStorage'e bir timestamp kaydediyoruz,
          // Footer bileşeni bunu algılayıp refetch yapacak
          localStorage.setItem('journey_tracking_updated', Date.now().toString());
        } catch (trackingError) {
          developmentLogs('Failed to update journey tracking:', trackingError);

          // Journey güncelleme başarısız olsa bile devam et
        }
      }

      // Add artificial delay
      await new Promise((resolve) => setTimeout(resolve, 3000));

      if (response && !response.error) {
        clearStorage();
        window.location.href = `/ai_workflows/${response.data.slug}`;
      } else {
        throw new Error(response?.error || 'Failed to create workflow');
      }
    } catch (error) {
      console.error('Error details:', error);
      setAlert({
        open: true,
        message: error?.data?.message || error.message || 'Failed to save and run workflow',
        severity: 'error',
      });
    } finally {
      setIsCreating(false);
    }
  };

  // Add handler for settings changes
  const handleSettingChange = (setting, value) => {
    setSettings((prev) => ({
      ...prev,
      [setting]: value,
    }));
  };

  // Add handlers for element reordering
  const handleElementDragStart = (e, elementId) => {
    e.dataTransfer.setData('elementId', elementId);
  };

  const handleElementDragOver = (e) => {
    e.preventDefault();
    const draggingElement = e.target.closest('.workflow-element');
    if (draggingElement) {
      draggingElement.style.borderTop = '2px solid #1976d2';
    }
  };

  const handleElementDragLeave = (e) => {
    e.preventDefault();
    const draggingElement = e.target.closest('.workflow-element');
    if (draggingElement) {
      draggingElement.style.borderTop = '';
    }
  };

  // Add close handler for alert
  const handleCloseAlert = () => {
    setAlert((prev) => ({ ...prev, open: false }));
  };

  const clearStorage = () => {
    // In edit mode, we don't want to clear everything
    if (isEditMode) {
      // Only clear specific items
      localStorage.removeItem('dropElementsData');
      return;
    }

    // In create mode, clear everything
    localStorage.removeItem('dropElementsData');
    localStorage.removeItem('appName');
    localStorage.removeItem('tinymce-custom-colors-forecolor');
    localStorage.removeItem('tinymce-custom-colors-hilitecolor');
    localStorage.removeItem('workflow-tour');
    setDroppedElements([]);
  };

  const workflowHeaderProps = {
    onLoadSample: handleLoadSample,
    onSaveDraft: handleSaveDraft,
    onSaveAndRun: handleSaveAndRun,
    workflowName: appName,
    onWorkflowNameChange: handleWorkflowNameChange,
    isEditMode: isEditMode,
  };

  const workflowColumnProps = {
    droppedElements: Array.isArray(droppedElements) ? droppedElements : [],
    onDrop: handleDrop,
    onDragOver: handleDragOver,
    onElementDragStart: handleElementDragStart,
    onElementDragOver: handleElementDragOver,
    onElementDragLeave: handleElementDragLeave,
    onDeleteElement: handleDeleteElement,
    onElementInputChange: handleElementInputChange,
    onElementDetailChange: handleElementDetailChange,
    onOutputNameChange: handleOutputNameChange,
  };

  return (
    <Box>
      <WorkflowHeader {...workflowHeaderProps} />
      {/* Workflow Creator Main Section */}
      {isLoadingWorkflow ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
          }}
        >
          <CircularProgress />
        </Box>
      ) : (
        <Box component="section" className="workflow-creator" sx={{ overflow: 'hidden' }}>
          <Container
            maxWidth="xl"
            className="workflow-creator-container"
            sx={{
              backgroundColor: 'transparent',
              boxShadow: 'none',
              '& .MuiPaper-root': {
                backgroundColor: '#fff',
              },
            }}
          >
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(6, 1fr)', gap: 3 }}>
              <Box sx={{ gridColumn: '1 / 2' }}>
                <ComponentsColumn onDragStart={handleDragStart} />
              </Box>
              <Box sx={{ gridColumn: '2 / 7' }}>
                <WorkflowColumn {...workflowColumnProps} />
              </Box>
              <Box sx={{ gridColumn: '1 / 2' }}>{/* Empty space */}</Box>
              <Box sx={{ gridColumn: '2 / 7' }}>
                <ParameterSettings settings={settings} onSettingChange={handleSettingChange} />
              </Box>
            </Box>
          </Container>
        </Box>
      )}

      {/* Loading Dialog */}
      <Dialog open={isCreating} fullWidth maxWidth="sm">
        <DialogContent sx={{ textAlign: 'center', py: 4 }}>
          <CircularProgress size={60} sx={{ mb: 3 }} />
          <Typography variant="h6" gutterBottom>
            {isEditMode
              ? t('create.workflowCreator.dialog.updating')
              : t('create.workflowCreator.dialog.creating')}
          </Typography>
          <Typography color="text.secondary">
            {isEditMode
              ? t('create.workflowCreator.dialog.updatingMessage')
              : t('create.workflowCreator.dialog.buildingMessage')}
          </Typography>
        </DialogContent>
      </Dialog>

      <AlertSnackbar
        open={alert.open}
        message={alert.message}
        severity={alert.severity}
        onClose={handleCloseAlert}
      />
    </Box>
  );
};

export default WorkflowCreatorPage;
