// Base sample elements
const baseSampleElements = [
  {
    id: 'text_1',
    type: 'input',
    title: 'Text Input',
    icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/text-input.svg',
    input: 'Product Type',
    detail: 'Smartphone',
    outputName: '',
    promptContent: '',
    inputs: [
      { inputType: 'text', name: 'input-key', value: 'Product Type' },
      { inputType: 'text', name: 'input-value', value: 'Smartphone' },
    ],
  },
  {
    id: 'text_2',
    type: 'input',
    title: 'Text Input',
    icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/text-input.svg',
    input: 'Target Market',
    detail: 'Europe',
    outputName: '',
    promptContent: '',
    inputs: [
      { inputType: 'text', name: 'input-key', value: 'Target Market' },
      { inputType: 'text', name: 'input-value', value: 'Europe' },
    ],
  },
  {
    id: 'prompt_3',
    type: 'prompt',
    title: 'Prompt',
    icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/prompt.svg',
    input: '',
    detail: '',
    outputName: 'Strategy Ideas',
    promptContent:
      '<p dir="ltr">Generate a list of innovative product launch strategies that focus on the<span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="product_type" data-uid="id-1722848731866-7x2ux1wvr">{product_type}</span> within the <span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="target_market" data-uid="id-1722848739462-nqmj7yof5">{target_market}</span>.</p>',
  },
  {
    id: 'prompt_4',
    type: 'prompt',
    title: 'Prompt',
    icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/prompt.svg',
    input: '',
    detail: '',
    outputName: 'Customer Engagement Tactics',
    promptContent:
      '<p>&nbsp;For each<span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="strategy_ideas" data-uid="id-1722847648873-0xcl3sare">{strategy_ideas}</span> , suggest customer engagement tactics that can maximize the impact of each strategy.&nbsp; Include specific activities, platforms, and engagement metrics.&nbsp;</p>',
  },
  {
    id: 'prompt_5',
    type: 'prompt',
    title: 'Prompt',
    icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/prompt.svg',
    input: '',
    detail: '',
    outputName: 'Success Metrics and Improvement Plans',
    promptContent:
      '<p>For each <span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="customer_engagement_tactics" data-uid="id-1722847700451-1iz83dir1">{customer_engagement_tactics}</span> provided, propose success metrics to measure effectiveness and suggest improvement plans to optimize the launch strategy.</p>',
  },
  {
    id: 'output_6',
    type: 'output',
    title: 'Display',
    icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/output.svg',
    input: '',
    detail:
      '<p><strong>STRATEGY IDEAS</strong></p>\n<p><span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="strategy_ideas" data-uid="id-1722847715961-c7huudsws">{strategy_ideas}</span></p>\n<p><strong>ENGAGEMENT TACTICS</strong></p>\n<p><span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="customer_engagement_tactics" data-uid="id-1722847717909-ptrv6pzoo">{customer_engagement_tactics}</span></p>\n<p><strong>SUCCESS METRICS</strong><span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="success_metrics_and_improvement_plans" data-uid="id-1722847719109-eslvvzo4w">{success_metrics_and_improvement_plans}</span></p>',
    outputName: '',
    promptContent: '',
  },
];

// Dil bazlı örnekler
const sampleWorkflowsByLanguage = {
  en: baseSampleElements,
  de: [
    {
      id: 'text_1',
      type: 'input',
      title: 'Texteingabe',
      icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/text-input.svg',
      input: 'Produkttyp',
      detail: 'Smartphone',
      outputName: '',
      promptContent: '',
      inputs: [
        { inputType: 'text', name: 'input-key', value: 'Produkttyp' },
        { inputType: 'text', name: 'input-value', value: 'Smartphone' },
      ],
    },
    {
      id: 'text_2',
      type: 'input',
      title: 'Texteingabe',
      icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/text-input.svg',
      input: 'Zielmarkt',
      detail: 'Europa',
      outputName: '',
      promptContent: '',
      inputs: [
        { inputType: 'text', name: 'input-key', value: 'Zielmarkt' },
        { inputType: 'text', name: 'input-value', value: 'Europa' },
      ],
    },
    {
      id: 'prompt_3',
      type: 'prompt',
      title: 'Prompt',
      icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/prompt.svg',
      input: '',
      detail: '',
      outputName: 'Strategische Ideen',
      promptContent:
        '<p dir="ltr">Erstellen Sie eine Liste innovativer Produkteinführungsstrategien, die sich auf den <span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="produkttyp" data-uid="id-1722848731866-7x2ux1wvr">{produkttyp}</span> innerhalb des <span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="zielmarkt" data-uid="id-1722848739462-nqmj7yof5">{zielmarkt}</span> konzentrieren.</p>',
    },
    {
      id: 'prompt_4',
      type: 'prompt',
      title: 'Prompt',
      icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/prompt.svg',
      input: '',
      detail: '',
      outputName: 'Kundenengagement-Taktiken',
      promptContent:
        '<p>Schlagen Sie für jede <span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="strategische_ideen" data-uid="id-1722847648873-0xcl3sare">{strategische_ideen}</span> Kundenengagement-Taktiken vor, die die Wirkung jeder Strategie maximieren können. Fügen Sie spezifische Aktivitäten, Plattformen und Engagement-Metriken hinzu.</p>',
    },
    {
      id: 'prompt_5',
      type: 'prompt',
      title: 'Prompt',
      icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/prompt.svg',
      input: '',
      detail: '',
      outputName: 'Erfolgskennzahlen und Verbesserungspläne',
      promptContent:
        '<p>Schlagen Sie für jede bereitgestellte <span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="kundenengagement-taktiken" data-uid="id-1722847700451-1iz83dir1">{kundenengagement-taktiken}</span> Erfolgskennzahlen zur Messung der Effektivität vor und empfehlen Sie Verbesserungspläne zur Optimierung der Einführungsstrategie.</p>',
    },
    {
      id: 'output_6',
      type: 'output',
      title: 'Anzeige',
      icon: 'https://aitrainer.aibusinessschool.com/resources/extentions/ai-workflow-creator/assets/img/output.svg',
      input: '',
      detail:
        '<p><strong>STRATEGISCHE IDEEN</strong></p>\n<p><span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="strategische_ideen" data-uid="id-1722847715961-c7huudsws">{strategische_ideen}</span></p>\n<p><strong>ENGAGEMENT-TAKTIKEN</strong></p>\n<p><span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="kundenengagement-taktiken" data-uid="id-1722847717909-ptrv6pzoo">{kundenengagement-taktiken}</span></p>\n<p><strong>ERFOLGSKENNZAHLEN</strong><span class="custom-label" style="background-color: #dddddd; border: 1px solid rgb(204, 204, 204); padding: 2px 5px; display: inline-block; cursor: move; margin: 0px 2px; border-radius: 4px; text-decoration: none; color: #333333;" contenteditable="false" draggable="true" href="#" data-target="prompt" data-key="erfolgskennzahlen_und_verbesserungspläne" data-uid="id-1722847719109-eslvvzo4w">{erfolgskennzahlen_und_verbesserungspläne}</span></p>',
      outputName: '',
      promptContent: '',
    },
  ],
};

// Dile göre örnek iş akışını döndüren fonksiyon
export const getSampleElements = (language = 'en') => {
  try {
    const elements = sampleWorkflowsByLanguage[language] || sampleWorkflowsByLanguage.en;
    return Array.isArray(elements) ? elements : baseSampleElements;
  } catch (error) {
    console.error('Error in getSampleElements:', error);
    return baseSampleElements;
  }
};

// Geriye uyumluluk için
export const sampleElements = baseSampleElements;
