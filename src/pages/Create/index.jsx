import { Box, Container, Grid, Typography, CircularProgress } from '@mui/material';
import { useSelector } from 'react-redux';
import { Suspense, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader.jsx';
import CourseCard, { CardContent } from '@/components/CourseCard/CourseCard';
import WhiteContainer from '../../components/WhiteContainer/WhiteContainer.jsx';
import { creatorTools } from '../../mockData/creatorTools.js';
import { AdvancedCreatorTools } from '../../mockData/AdvancedCreatorTools.js';
import { checkAndLockCards } from '../../utils/cardUtils';
import './Create.scss';

const CreateContent = () => {
  const user = useSelector((state) => state.auth.user);
  const { t, i18n } = useTranslation();

  // <PERSON>llanıcı rolüne göre creator tools verilerini filtrele ve kilitle
  const filteredCreatorTools = useMemo(() => {
    // Önce creatorTools verilerini aynı yapıya dönüştür
    const toolsData = creatorTools.map((tool) => ({
      type: 'CreatorTool',
      props: { ...tool },
    }));

    // Utils fonksiyonu ile kilitleme işlemini uygula
    return checkAndLockCards(toolsData, user, t).map((item) => item.props);
  }, [user, t]);

  // Kullanıcı rolüne göre advanced creator tools verilerini filtrele ve kilitle
  const filteredAdvancedCreatorTools = useMemo(() => {
    // Önce AdvancedCreatorTools verilerini aynı yapıya dönüştür
    const toolsData = AdvancedCreatorTools.map((tool) => ({
      type: 'AdvancedCreatorTool',
      props: { ...tool },
    }));

    // Utils fonksiyonu ile kilitleme işlemini uygula
    return checkAndLockCards(toolsData, user, t).map((item) => item.props);
  }, [user, t]);

  // Araç kartlarında çeviri desteği için yardımcı fonksiyon
  const getTranslatedText = (item, field) => {
    if (item.translations && item.translations[field]) {
      return (
        item.translations[field][i18n.language] || item.translations[field]['en'] || item[field]
      );
    }
    return item[field];
  };

  // Tooltip metni için çeviri desteği
  const getTranslatedTooltip = (item) => {
    if (item.locked) {
      if (item.translations && item.translations.tooltipText) {
        return (
          item.translations.tooltipText[i18n.language] ||
          item.translations.tooltipText['en'] ||
          t('common.lockedTooltip')
        );
      }
      return item.tooltipText || t('common.lockedTooltip');
    }
    return undefined;
  };

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('common.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('create.title')}
            description={t('create.subtitle')}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer
            title={t('create.simpleTools.title')}
            subtitle={t('create.simpleTools.subtitle')}
            showNavigation={false}
          >
            <Grid container className="create-card-wrapper" spacing={2}>
              {filteredCreatorTools.map((item) => (
                <Grid item xs={12} md={4} key={item.id}>
                  <CourseCard
                    buttonText={getTranslatedText(item, 'buttonText')}
                    buttonType={item.buttonType}
                    buttonVariant="text"
                    imageSrc={item.imageSrc}
                    locked={item.locked}
                    tooltipText={getTranslatedTooltip(item)}
                    onClick={(e) => {
                      // Kilitli kartlara tıklandığında engelle
                      if (item.locked) {
                        e.preventDefault();
                        return;
                      }

                      // Normal işleme devam et
                      if (item.buttonType === 'URL') {
                        if (item.newTab) {
                          window.open(item.buttonURL, '_blank');
                        } else {
                          window.location.href = item.buttonURL;
                        }
                      }
                    }}
                  >
                    <CardContent
                      title={getTranslatedText(item, 'title')}
                      description={getTranslatedText(item, 'description')}
                      buttonURL={item.buttonURL}
                      newTab={item.newTab}
                      buttonText={getTranslatedText(item, 'buttonText')}
                      buttonType={item.buttonType}
                      imageSrc={item.imageSrc}
                      locked={item.locked}
                      tooltipText={getTranslatedTooltip(item)}
                    />
                  </CourseCard>
                </Grid>
              ))}
            </Grid>
          </WhiteContainer>
          <WhiteContainer
            title={t('create.advancedTools.title')}
            subtitle={t('create.advancedTools.subtitle')}
            showNavigation={false}
          >
            <Grid container className="create-card-wrapper" spacing={2}>
              {filteredAdvancedCreatorTools.map((item) => (
                <Grid item xs={12} md={4} key={item.id}>
                  <CourseCard
                    buttonText={getTranslatedText(item, 'buttonText')}
                    buttonType={item.buttonType}
                    buttonVariant="text"
                    imageSrc={item.imageSrc}
                    locked={item.locked}
                    tooltipText={getTranslatedTooltip(item)}
                    onClick={(e) => {
                      // Kilitli kartlara tıklandığında engelle
                      if (item.locked) {
                        e.preventDefault();
                        return;
                      }

                      // Normal işleme devam et
                      if (item.buttonType === 'URL') {
                        if (item.newTab) {
                          window.open(item.buttonURL, '_blank');
                        } else {
                          window.location.href = item.buttonURL;
                        }
                      }
                    }}
                  >
                    <CardContent
                      title={getTranslatedText(item, 'title')}
                      description={getTranslatedText(item, 'description')}
                      buttonURL={item.buttonURL}
                      newTab={item.newTab}
                      buttonText={getTranslatedText(item, 'buttonText')}
                      buttonType={item.buttonType}
                      imageSrc={item.imageSrc}
                      locked={item.locked}
                      tooltipText={getTranslatedTooltip(item)}
                    />
                  </CourseCard>
                </Grid>
              ))}
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

const CreatePage = () => {
  return (
    <Suspense
      fallback={
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: 'calc(100vh - 64px)', // Header yüksekliğini çıkarıyoruz
          }}
        >
          <CircularProgress />
        </Box>
      }
    >
      <CreateContent />
    </Suspense>
  );
};

export default CreatePage;
