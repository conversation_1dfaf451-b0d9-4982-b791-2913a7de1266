import { Box, Container, Grid, Typo<PERSON>, Button } from '@mui/material';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import ReactPlayer from 'react-player';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import '../Tutorials.scss';
// Dil bazlı görselleri import et
import VideoThumbnailEN from '@/assets/images/tutorials/simple-ai-app-creator.jpg';
import VideoThumbnailDE from '@/assets/images/tutorials/simple-ai-app-creator.jpg';

const SimpleAIAppCreatorTutorialPage = () => {
  const user = useSelector((state) => state.auth.user);
  const { t, i18n } = useTranslation();
  const [isPlaying, setIsPlaying] = useState(false);
  const navigate = useNavigate();

  // Çeviri dosyasından dile göre video URL'sini al
  const videoUrl = t('tutorials.simpleAIAppCreator.videoUrl');
  console.log('Video URL:', videoUrl);

  // Dile göre doğru thumbnail'i seç
  const currentLanguage = i18n.language || 'en';
  const videoThumbnail = currentLanguage === 'de' ? VideoThumbnailDE : VideoThumbnailEN;

  const handlePlayClick = () => {
    setIsPlaying(true);
  };

  const handleBackClick = () => {
    // Alternatif olarak sabit bir URL'ye yönlendirme
    navigate('/playgrounds/simple-ai-app-creator');
  };

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('tutorials.simpleAIAppCreator.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container className="tutorial-container">
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('tutorials.simpleAIAppCreator.title')}
            description={t('tutorials.simpleAIAppCreator.description')}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer title="" subtitle="" showNavigation={false} variant="backButtonTrue">
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={handleBackClick}
              className="tutorial-back-button"
            >
              {t('tutorials.simpleAIAppCreator.backToPlayground')}
            </Button>
            <Grid container spacing={0}>
              <Box className="video-container">
                {!isPlaying ? (
                  <>
                    <img
                      src={videoThumbnail}
                      alt={t('tutorials.simpleAIAppCreator.title')}
                      className="video-thumbnail"
                    />
                    <Box className="play-button" onClick={handlePlayClick}>
                      <PlayArrowIcon />
                    </Box>
                  </>
                ) : (
                  <ReactPlayer
                    url={videoUrl}
                    className="react-player"
                    width="100%"
                    height="100%"
                    playing={true}
                    controls={true}
                    config={{
                      file: {
                        forceHLS: true,
                        hlsOptions: {
                          maxBufferLength: 30,
                        },
                      },
                    }}
                    onEnded={() => console.log(t('tutorials.simpleAIAppCreator.completedMessage'))}
                  />
                )}
              </Box>
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

export default SimpleAIAppCreatorTutorialPage;
