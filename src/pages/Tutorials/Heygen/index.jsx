import { <PERSON>, Container, Grid, <PERSON>po<PERSON>, Button, CircularProgress } from '@mui/material';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import ReactPlayer from 'react-player';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import './heygen.scss';

const HeygenTutorialPage = () => {
  const user = useSelector((state) => state.auth.user);
  const { t, i18n } = useTranslation();
  const [isPlaying, setIsPlaying] = useState(false);

  // HeyGen videosu için sabit URL
  const heygenVideoUrl = 'https://www.youtube.com/watch?v=yzmEkeodGZY';
  const heygenVideoUrl2 = 'https://www.youtube.com/watch?v=Y3225dV0lyQ';
  const heygenVideoUrl3 = 'https://www.youtube.com/watch?v=j7PQ73RctD4';

  if (!user) {
    return (
      <Container>
        <Box className="heygen-tutorial__login-message">
          <Typography variant="h4">{t('tutorials.heygen.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container className="heygen-tutorial__container">
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('tutorials.heygen.title')}
            description={t('tutorials.heygen.description')}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <Box className="heygen-tutorial__white-container">
            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} md={7} className="heygen-tutorial__content-col">
                <Typography variant="h5" component="h2" className="heygen-tutorial__title">
                  {t('tutorials.heygen.howToUseAvatars')}
                </Typography>
                <Typography variant="body1" className="heygen-tutorial__description">
                  {t('tutorials.heygen.welcomeText')}
                </Typography>
              </Grid>
              <Grid item xs={12} md={5}>
                <Box className="heygen-tutorial__video-container">
                  <ReactPlayer
                    url={heygenVideoUrl}
                    width="100%"
                    height="100%"
                    controls={true}
                    light={false}
                    playing={isPlaying}
                    onPlay={() => setIsPlaying(true)}
                    onPause={() => setIsPlaying(false)}
                    style={{ position: 'absolute', top: 0, left: 0 }}
                  />
                </Box>
              </Grid>
            </Grid>
          </Box>
          <Box className="heygen-tutorial__white-container">
            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} md={7} className="heygen-tutorial__content-col">
                <Typography variant="h5" component="h2" className="heygen-tutorial__title">
                  {t('tutorials.heygen.meetNewestAvatars')}
                </Typography>
                <Typography variant="body1" className="heygen-tutorial__description">
                  {t('tutorials.heygen.meetHeygenText')}
                </Typography>
              </Grid>
              <Grid item xs={12} md={5}>
                <Box className="heygen-tutorial__video-container">
                  <ReactPlayer
                    url={heygenVideoUrl2}
                    width="100%"
                    height="100%"
                    controls={true}
                    light={false}
                    playing={isPlaying}
                    onPlay={() => setIsPlaying(true)}
                    onPause={() => setIsPlaying(false)}
                    style={{ position: 'absolute', top: 0, left: 0 }}
                  />
                </Box>
              </Grid>
            </Grid>
          </Box>
          <Box className="heygen-tutorial__white-container">
            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} md={7} className="heygen-tutorial__content-col">
                <Typography variant="h5" component="h2" className="heygen-tutorial__title">
                  {t('tutorials.heygen.howWeCreated')}
                </Typography>
                <Typography variant="body1" className="heygen-tutorial__description">
                  {t('tutorials.heygen.howWeCreatedText')}
                </Typography>
              </Grid>
              <Grid item xs={12} md={5}>
                <Box className="heygen-tutorial__video-container">
                  <ReactPlayer
                    url={heygenVideoUrl3}
                    width="100%"
                    height="100%"
                    controls={true}
                    light={false}
                    playing={isPlaying}
                    onPlay={() => setIsPlaying(true)}
                    onPause={() => setIsPlaying(false)}
                    style={{ position: 'absolute', top: 0, left: 0 }}
                  />
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </Container>
  );
};

export default HeygenTutorialPage;
