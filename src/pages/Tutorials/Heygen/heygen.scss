@use "../../../styles/abstracts/variables" as *;

.heygen-tutorial {
  &__container {
    margin-bottom: $spacing-5;
  }

  &__white-container {
    background-color: $bg-paper;
    border-radius: $border-radius-md;
    box-shadow: $shadow-sm;
    padding: 0 0 0 $spacing-4 !important;
    margin-bottom: $spacing-4;
    border: 1px solid $border-color;
  }

  &__title {
    color: $text-primary !important;
    font-weight: $font-weight-bold !important;
    font-size: $font-size-md !important;
    margin-bottom: $spacing-3 !important;
  }

  &__description {
    color: $text-secondary !important;
    font-size: $font-size-md !important;
    margin-bottom: $spacing-4 !important;
  }

  &__content {
    color: $text-secondary !important;
    font-size: $font-size-md !important;
    margin-bottom: 0 !important;
  }


  &__video-container {
    position: relative !important;
    width: 100%;
    box-shadow: none !important;
    height: 100%;
    min-height: 240px;
    border-radius: $border-radius-md;
    overflow: hidden;
  }

  &__login-message {
    padding: $spacing-4 0;
    text-align: center;
    color: $text-primary;
    font-weight: $font-weight-medium;
  }

  &__content-col {
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    
    p {
      &:last-child {
        margin-bottom: 0 !important;
      }
    }
  }
}
