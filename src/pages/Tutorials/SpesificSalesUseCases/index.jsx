import { Box, Container, Grid, Typography, Button } from '@mui/material';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import ReactPlayer from 'react-player';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import '../Tutorials.scss';
// Dil bazlı görselleri import et
import VideoThumbnailEN from '@/assets/images/tutorials/advanced-genai-creator.jpg';
import VideoThumbnailDE from '@/assets/images/tutorials/advanced-genai-creator.jpg';

const SpesificSalesUseCasesPage = () => {
  const user = useSelector((state) => state.auth.user);
  const { t, i18n } = useTranslation();
  const [isPlaying, setIsPlaying] = useState(false);
  const navigate = useNavigate();

  // Çeviri dosyasından dile göre video URL'sini al
  const videoUrl = t('tutorials.advancedGenAICreator.videoUrl');

  // Dile göre doğru thumbnail'i seç
  const currentLanguage = i18n.language || 'en';
  const videoThumbnail = currentLanguage === 'de' ? VideoThumbnailDE : VideoThumbnailEN;

  const handlePlayClick = () => {
    setIsPlaying(true);
  };

  const handleBackClick = () => {
    // Playground sayfasına geri dön
    navigate('/microsoft-copilot/');
  };

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('tutorials.advancedGenAICreator.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container className="tutorial-container">
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('tutorials.spesific_sales.title')}
            description={t('tutorials.spesific_sales.description')}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer title="" subtitle="" showNavigation={false} variant="backButtonTrue">
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={handleBackClick}
              className="tutorial-back-button"
            >
              {t('tutorials.advancedGenAICreator.backToPlayground')}
            </Button>
            <Grid container spacing={0}>
              <iframe
                src={t('tutorials.spesific_sales.pdfUrl')}
                style={{
                  width: '100%',
                  height: '100vh', // Tam ekran yüksekliği
                  border: 'none',
                }}
                title="DALL·E Cheat Sheet"
              />
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

export default SpesificSalesUseCasesPage;
