@use '../../styles/abstracts/variables' as *;

.tutorial-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;

  .video-container {
    width: 100% !important;
    max-width: 100% !important;
    border-radius: $border-radius-md;
    overflow: hidden;
    position: relative;
    height: 0;
    padding-top: 56.25%; /* 16:9 aspect ratio */
    background-color: #000;
    
    .react-player {
      position: absolute;
      top: 0;
      left: 0;
      width: 100% !important;
      height: 100% !important;
    }
  }
  
  .video-thumbnail {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 2;
    
    &:hover {
      transform: translate(-50%, -50%) scale(1.1);
      background-color: rgba(255, 255, 255, 0.9);
      
      & + .video-thumbnail {
        transform: scale(1.05);
      }
    }
    
    svg {
      font-size: 40px;
      color: #0d47a1;
    }
  }
  
  .video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
.tutorial-back-button {
  position: absolute !important;
  top:-29px;
  left:-1px;
  margin-bottom: $spacing-4;
  
  &.MuiButton-root {
    color: $primary-color;
    border:none !important;
    background-color: $bg-paper !important;
    border-top: 1px solid $border-color !important;
    border-left: 1px solid $border-color !important;
    border-right: 1px solid $border-color !important;
    border-radius: 0 !important;
    border-top-left-radius: $border-radius-md !important;
    border-top-right-radius: $border-radius-md !important;
    color: $text-secondary !important;
    text-transform: none;
    font-weight: $font-weight-medium;
    font-size: calc($font-size-xs - 1px) !important;  
    padding: $spacing-1 $spacing-2 !important;
    
    &:hover {
      border-color: $primary-color-dark;
      background-color: $primary-color-light;
    }
  }
}
 