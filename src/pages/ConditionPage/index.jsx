import { useSelector } from 'react-redux';
import { Container, Typography, Box, Paper, CircularProgress } from "@mui/material";
import { personalizeApi } from '../../middleware/personalize-api';
import { platformSettingsApi } from '../../redux/services/platform-settings-api';
import { segmentEvaluator } from '../../middleware/segment-evaluator';
import { puckContent as mockPageData } from "../../mockData/mockPageData";

// Platform Settings API'den genel ayarları çekiyoruz
const { useGetGeneralSettingsQuery, useGetUserSegmentationQuery } = platformSettingsApi;


const blockComponents = {
  HeadingBlock: ({ title, subtitle }) => (
    <Box mb={3}>
      <Typography variant="h4">{title}</Typography>
      {subtitle && <Typography variant="subtitle1">{subtitle}</Typography>}
    </Box>
  ),
  
  UseCaseBlock: ({ title, useCases }) => (
    <Paper sx={{ p: 2, mb: 2 }}>
      <Typography variant="h5" mb={2}>{title}</Typography>
      {useCases?.map((useCase) => (
        <Box key={useCase.id} mb={2}>
          <Typography variant="h6">{useCase.title}</Typography>
          <Typography>{useCase.description}</Typography>
        </Box>
      ))}
    </Paper>
  ),

  CardsBlock: ({ title, description, children }) => (
    <Paper sx={{ p: 2, mb: 2 }}>
      <Typography variant="h5" mb={2}>{title}</Typography>
      {description && <Typography mb={2}>{description}</Typography>}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        {children?.map((card) => (
          <Box key={card.props.id} mb={2}   sx={{ flex: '0 1 calc(33.33% - 20px)' }}>
            <Typography variant="h6">{card.props.title}</Typography>


            <Typography>{card.props.description}</Typography>
            {card.props.image && (
              <Box component="img" 
                src={card.props.image} 
                alt={card.props.title}
                sx={{ maxWidth: '100%', height: 'auto' }}
              />
            )}
          </Box>
        ))}
      </Box>
    </Paper>
  )
};

const ConditionPage = () => {
  const user = useSelector((state) => state.auth.user);
  
  const { 
    data: userSegmentation, 
    error: userSegmentationError, 
    isLoading: userSegmentationLoading 
  } = useGetUserSegmentationQuery();

  // User'ın segmentini bul
  const userSegment = segmentEvaluator.findUserSegment(userSegmentation?.data?.segments, {
    function_label: 'it-ai-data',
    technical_background_label: 'Power User',
    ai_knowledge_label: 3
  });

  if (userSegmentationLoading) {
    return <CircularProgress />;
  }

  if (userSegmentationError) {
    return <Typography color="error">
      Error fetching data: {userSegmentationError?.message}
    </Typography>;
  }

  const userData = {
    name: user?.name || 'Guest',
    function_label: user?.onboarding?.function_label, 
    language: user?.onboarding?.language, 
    technical_background_label: 'Power User',
    ai_experience: user?.onboarding?.ai_experience,
    ai_knowledge_label: 3,
    industry: user?.onboarding?.industry,
    management_role: user?.onboarding?.management_role,
    management_role_label: user?.onboarding?.management_role_label,
    segment: userSegment
  };
 

  const filteredContent = personalizeApi.filterPageContent(mockPageData, userData);

  const renderBlock = (block) => {
    const Component = blockComponents[block.type];
    
    if (!Component) {
      console.warn(`Unknown block type: ${block.type}`);
      return null;
    }

   // console.log('Rendering block:', block);
    const processedProps = personalizeApi.processProps(block.props, userData);
   // console.log('Processed props:', processedProps);
    
    return <Component key={block.props.id} {...processedProps} />;
  };

  return (
    <>
    <Container sx={{ py: 4 }}>
      {filteredContent.map(block => renderBlock(block))}
    </Container>
    <Container sx={{ py: 4 }}>
        <pre>{JSON.stringify(userSegmentation, null, 2)}</pre>
    </Container>
    </>
  );
};

export default ConditionPage;

