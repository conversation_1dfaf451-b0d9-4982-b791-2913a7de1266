import { Box, Container, Grid, Typography } from '@mui/material';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import CourseCard, { CardContent } from '@/components/CourseCard/CourseCard';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import { OpenAITools } from '../../../mockData/OpenAITools.js';

const OpenAIPage = () => {
  const { t, i18n } = useTranslation();
  const user = useSelector((state) => state.auth.user);
  const currentLanguage = i18n.language || 'en';

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('Please log in.')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('tools.openai.title')}
            description={t('tools.openai.description')}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer title="" subtitle="" variant="transparent" showNavigation={false}>
            <Grid container spacing={2}>
              {OpenAITools.map((item) => {
                const localizedContent = item.translations[currentLanguage] || item.translations.en;

                return (
                  <Grid item xs={12} md={4} key={item.id}>
                    <CourseCard
                      buttonText={localizedContent.buttonText}
                      buttonType={item.buttonType}
                      buttonVariant="text"
                      imageSrc={item.imageSrc}
                      locked={item.locked}
                      tooltipText={item.locked ? localizedContent.tooltipText : undefined}
                    >
                      <CardContent
                        title={localizedContent.title}
                        description={localizedContent.description}
                        buttonURL={item.buttonURL}
                        newTab={item.newTab}
                        buttonText={localizedContent.buttonText}
                        buttonType={item.buttonType}
                        imageSrc={item.imageSrc}
                        locked={item.locked}
                        tooltipText={item.locked ? localizedContent.tooltipText : undefined}
                      />
                    </CourseCard>
                  </Grid>
                );
              })}
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

export default OpenAIPage;
