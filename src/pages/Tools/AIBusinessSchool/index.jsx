import { Box, Container, Grid, Typography } from '@mui/material';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import CourseCard, { CardContent } from '@/components/CourseCard/CourseCard';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import { AIBusinessSchoolToolCards } from '../../../mockData/AIBusinessSchoolToolCards.js';

const AIBusinessSchoolPage = () => {
  const { t } = useTranslation();
  const user = useSelector((state) => state.auth.user);

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('common.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  const toolCards = AIBusinessSchoolToolCards();

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('tools.aiBusinessSchool.title')}
            description={t('tools.aiBusinessSchool.description')}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer title="" subtitle="" variant="transparent" showNavigation={false}>
            <Grid container spacing={2}>
              {toolCards.map((item) => (
                <Grid item xs={12} md={6} key={item.id}>
                  <CourseCard
                    buttonText={item.buttonText}
                    buttonType={item.buttonType}
                    buttonVariant="text"
                    variant="bigThumbnail"
                    imageSrc={item.imageSrc}
                    locked={item.locked}
                    tooltipText={item.locked ? item.tooltipText : undefined}
                  >
                    <CardContent
                      title={item.title}
                      description={item.description}
                      buttonURL={item.buttonURL}
                      newTab={item.newTab}
                      buttonText={item.buttonText}
                      buttonType={item.buttonType}
                      imageSrc={item.imageSrc}
                      locked={item.locked}
                      tooltipText={item.locked ? item.tooltipText : undefined}
                    />
                  </CourseCard>
                </Grid>
              ))}
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

export default AIBusinessSchoolPage;
