import { Box, Container, Grid, Typography } from '@mui/material';
import { useRef } from 'react';
import AIHero from '../../../components/AIHero/AIHero';
import { GithubCopilotToolCards } from '../../../mockData/GithubCopilotToolCards.js';
import PlaygroundCard from '../../../components/PlaygroundCard/PlaygroundCard.jsx';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer';
import './GithubCopilot.scss';
import { useTranslation } from 'react-i18next';

const GithubCopilot = () => {
  const welcomeHeaderRef = useRef(null);
  const { t, i18n } = useTranslation();

  // Dile göre doğru resmi seç - şu an için aynı resmi kullanıyoruz
  const heroImage =
    'https://trainerdev.aibusinessschool.com/resources/uploads/2025/01/githubvideo-replacement-1024x538.png';

  const handleTryItClick = () => {
    welcomeHeaderRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  };

  return (
    <Container>
      <AIHero
        title={t('tools.githubCopilot.title')}
        description={t('tools.githubCopilot.description')}
        imageUrl={heroImage}
      />

      <Box className="features-section">
        <Grid container spacing={3}>
          {GithubCopilotToolCards().map((feature, index) => (
            <Grid item xs={12} md={12 / GithubCopilotToolCards().length} key={index}>
              <PlaygroundCard {...feature} />
            </Grid>
          ))}
        </Grid>
      </Box>

      <WhiteContainer title={t('tools.githubCopilot.benefits.title')}>
        <Box className="content-wrapper">
          <Box className="section-group">
            <Typography variant="h3" className="group-title">
              {t('tools.githubCopilot.benefits.beginners.title')}
            </Typography>
            <Typography variant="body1">
              {t('tools.githubCopilot.benefits.beginners.description')}
            </Typography>
          </Box>

          <Box className="section-group">
            <Typography variant="h3" className="group-title">
              {t('tools.githubCopilot.benefits.advanced.title')}
            </Typography>
            <Typography variant="body1">
              {t('tools.githubCopilot.benefits.advanced.description1')}
            </Typography>
            <Typography variant="body1">
              {t('tools.githubCopilot.benefits.advanced.description2')}
            </Typography>
          </Box>
        </Box>
      </WhiteContainer>

      <WhiteContainer title={t('tools.githubCopilot.highlights.title')}>
        <Typography variant="body1" className="section-intro">
          {t('tools.githubCopilot.highlights.intro')}
        </Typography>

        <Typography variant="body1" className="section-intro">
          {t('tools.githubCopilot.highlights.setup')}
        </Typography>

        <Box className="activation-steps">
          <ul>
            <li>
              <Typography variant="body1">
                {t('tools.githubCopilot.highlights.activation.step1')}
              </Typography>
            </li>
            <li>
              <Typography variant="body1">
                {t('tools.githubCopilot.highlights.activation.step2')}
              </Typography>
            </li>
            <li>
              <Typography variant="body1">
                {t('tools.githubCopilot.highlights.activation.step3')}
              </Typography>
            </li>
          </ul>
        </Box>

        <Typography variant="body1" className="section-intro">
          {t('tools.githubCopilot.highlights.with')}
        </Typography>

        <Box className="content-wrapper">
          <Box className="section-group">
            <Typography variant="h3" className="group-title">
              {t('tools.githubCopilot.highlights.completions.title')}
            </Typography>
            <Typography variant="body1">
              {t('tools.githubCopilot.highlights.completions.description')}
            </Typography>
          </Box>

          <Box className="section-group">
            <Typography variant="h3" className="group-title">
              {t('tools.githubCopilot.highlights.chat.title')}
            </Typography>
            <Typography variant="body1">
              {t('tools.githubCopilot.highlights.chat.description')}
            </Typography>
          </Box>

          <Box className="section-group">
            <Typography variant="h3" className="group-title">
              {t('tools.githubCopilot.highlights.model.title')}
            </Typography>
            <Typography variant="body1">
              {t('tools.githubCopilot.highlights.model.description')}
            </Typography>
          </Box>
        </Box>
      </WhiteContainer>

      <WhiteContainer title={t('tools.githubCopilot.inAction.title')}>
        <Box className="content-wrapper">
          <Box className="section-group">
            <Box className="image-wrapper">
              <img
                src="https://aibs-content.s3.eu-central-2.amazonaws.com/Screenshot+2025-02-06+at+16.25.44.png"
                alt={t('tools.githubCopilot.inAction.imageAlt')}
                className="feature-image"
              />
            </Box>
          </Box>
        </Box>
      </WhiteContainer>
      <AIHero
        title={t('tools.githubCopilot.environment.title')}
        description={t('tools.githubCopilot.environment.description')}
        onButtonClick={handleTryItClick}
        imageUrl={'https://trainerdev.aibusinessschool.com/resources/uploads/2025/01/github1.png'}
      />
      <Box mt={6} mb={6}>
        <AIHero
          title={t('tools.githubCopilot.challenges.title')}
          description={t('tools.githubCopilot.challenges.description')}
          position="left"
          onButtonClick={handleTryItClick}
          imageUrl={'https://trainerdev.aibusinessschool.com/resources/uploads/2025/01/github1.png'}
        />
      </Box>
    </Container>
  );
};

export default GithubCopilot;
