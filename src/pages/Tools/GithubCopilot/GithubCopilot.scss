@use '../../../styles/abstracts/variables' as *;
.white-container{
    margin-bottom: $spacing-4;
}
.benefits-section, 
.highlights-section {
  margin-bottom: $spacing-5;
    
  .section-title {
    font-size: $font-size-xxl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-3;
  }
}

.content-wrapper {
  .section-group {
    margin-bottom: $spacing-4;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .group-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      margin-bottom: $spacing-2;
    }
  }
}

.section-intro {
  margin-bottom: $spacing-3 !important;
  color: $text-primary;
  line-height: 1.6;
}

.activation-steps {
  margin-bottom: $spacing-5 !important;
  
  ul {
    display:flex;
    flex-direction: column;
    gap: $spacing-2;
    padding-left: $spacing-3;
    margin-top: $spacing-2;
    margin-bottom: $spacing-3;
    
    li {
      line-height: 1.6;
      color: $text-primary;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.image-wrapper {
  margin-top: $spacing-2;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  overflow: hidden;

  .feature-image {
    width: 100%;
    height: auto;
    display: block;
    border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
    border:1px solid $border-color;
    overflow: hidden;
  }
}

@media (max-width: $tablet) {
  .benefits-section, 
  .highlights-section {
    padding: $spacing-4 0;
    
    .section-title {
      font-size: 24px;
    }
  }
}
  
  