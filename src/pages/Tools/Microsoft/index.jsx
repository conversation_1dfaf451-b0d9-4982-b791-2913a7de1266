import { Box, Container, Grid, Typography } from '@mui/material';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import CourseCard, { CardContent } from '@/components/CourseCard/CourseCard';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import { MicrosoftTools, CopilotStudioCourses } from '../../../mockData/MicrosoftTools.js';
import CourseModal from '../../../components/CourseModal/CourseModal.jsx';

const MicrosoftPage = () => {
  const { t, i18n } = useTranslation();
  const user = useSelector((state) => state.auth.user);
  const currentLanguage = i18n.language || 'en';
  const [openCopilotStudioModal, setOpenCopilotStudioModal] = useState(false);
  const navigate = useNavigate();

  // Kart tıklamaları için işleyiciler
  const handleCardClick = (item) => {
    if (item.buttonType === 'MODAL' && item.id === 'copilot-studio') {
      setOpenCopilotStudioModal(true);
    } else if (item.buttonURL) {
      if (item.newTab) {
        window.open(item.buttonURL, '_blank');
      } else {
        navigate(item.buttonURL);
      }
    }
  };

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('Please log in.')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('tools.microsoft.title')}
            description={t('tools.microsoft.description')}
            showProgress={false}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer title="" subtitle="" variant="transparent" showNavigation={false}>
            <Grid container spacing={2}>
              {MicrosoftTools.map((item) => {
                const localizedContent = item.translations[currentLanguage] || item.translations.en;

                return (
                  <Grid item xs={12} md={4} key={item.id}>
                    <CourseCard
                      buttonText={localizedContent.buttonText}
                      buttonType={item.buttonType}
                      buttonVariant="text"
                      imageSrc={item.imageSrc}
                      locked={item.locked}
                      tooltipText={item.locked ? localizedContent.tooltipText : undefined}
                      onClick={() => !item.locked && handleCardClick(item)}
                    >
                      <CardContent
                        title={localizedContent.title}
                        description={localizedContent.description}
                        buttonURL={item.buttonURL}
                        newTab={item.newTab}
                        buttonText={localizedContent.buttonText}
                        buttonType={item.buttonType}
                        imageSrc={item.imageSrc}
                        locked={item.locked}
                        tooltipText={item.locked ? localizedContent.tooltipText : undefined}
                      />
                    </CourseCard>
                  </Grid>
                );
              })}
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>

      <CourseModal
        open={openCopilotStudioModal}
        onClose={() => setOpenCopilotStudioModal(false)}
        title="Microsoft Copilot Studio Trainings"
        courses={CopilotStudioCourses}
      />
    </Container>
  );
};

export default MicrosoftPage;
