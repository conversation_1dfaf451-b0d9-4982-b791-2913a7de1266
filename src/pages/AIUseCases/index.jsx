import { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Ta<PERSON>,
  Tab,
  CircularProgress,
  Button,
} from '@mui/material';
import { useSelector } from 'react-redux';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../components/WhiteContainer/WhiteContainer.jsx';
import AppCard from '@/components/AppCard/AppCard';
import './AIUseCases.scss';
import { useGetUseCasesQuery } from '@/redux/services/use-case-api';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { NavigateBefore } from '@mui/icons-material';
import InfoIcon from '@mui/icons-material/Info';

const AIUseCasesPage = () => {
  const user = useSelector((state) => state.auth.user);
  const [selectedType, setSelectedType] = useState('all');
  const navigate = useNavigate();
  const { i18n, t } = useTranslation();

  const {
    data: useCases,
    isLoading,
    error,
  } = useGetUseCasesQuery(
    {
      function_label: user?.onboarding?.function_label,
      limit: 100,
    },
    {
      skip: !user?.onboarding?.function_label,
    }
  );

  const getFunctionLabel = () => {
    const label = user?.onboarding?.function_label;
    if (!label) return '';
    if (typeof label === 'string') return label;
    return label.translations?.[i18n.language] || label.translations?.en || label.slug || '';
  };

  const functionLabel = getFunctionLabel();

  const getPageTitle = () => {
    if (!functionLabel) return t('useCases.title.default', 'Use Cases');
    return t('useCases.title.withFunction', '{{function}} specific use cases', {
      function: functionLabel,
    });
  };

  const filterTypes = [
    { value: 'all', label: t('useCases.filters.all', 'All use cases') },
    { value: 'text-generation', label: t('useCases.filters.text', 'Text Generation') },
    { value: 'video-generation', label: t('useCases.filters.video', 'Video Generation') },
    { value: 'image-generation', label: t('useCases.filters.image', 'Image Generation') },
  ];

  const handleTypeChange = (event, newValue) => {
    setSelectedType(newValue);
  };

  const filteredUseCases =
    useCases?.filter((useCase) =>
      selectedType === 'all' ? true : useCase.use_case_type?.includes(selectedType)
    ) || [];

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('common.pleaseLogin', 'Please log in.')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Box sx={{ mt: 3 }}>
        <Button
          variant="outlined"
          startIcon={<NavigateBefore />}
          onClick={() => navigate('/apply')}
          className="back-to-apply-button"
        >
          {t('common.backToApply', 'Back to Apply')}
        </Button>
      </Box>

      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={getPageTitle()}
            description={t(
              'useCases.description',
              'Discover carefully selected, relevant use cases for your business function and specific job area.'
            )}
            showProgress={false}
            name={user.name}
            functionLabel={functionLabel}
          />
        </Grid>

        <Grid item xs={12}>
          <WhiteContainer showNavigation={false} showMore={false} variant="backButtonTrue">
            <Box className="tabs-container">
              <Tabs
                value={selectedType}
                onChange={handleTypeChange}
                variant="scrollable"
                scrollButtons="auto"
              >
                {filterTypes.map((type) => (
                  <Tab key={type.value} label={type.label} value={type.value} />
                ))}
              </Tabs>
            </Box>

            <Grid container className="app-card-wrapper" spacing={2}>
              {isLoading ? (
                <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                  <CircularProgress />
                </Grid>
              ) : error ? (
                <Grid item xs={12}>
                  <Typography color="error">
                    {t('common.errorLoading', 'Error loading use cases. Please try again later.')}
                  </Typography>
                </Grid>
              ) : filteredUseCases.length === 0 ? (
                <Grid item xs={12} className="no-results-container">
                  <Typography className="no-results-message">
                    <InfoIcon className="message-icon" />
                    {t('useCases.noResults', 'No use cases found for the selected filter.')}
                  </Typography>
                </Grid>
              ) : (
                filteredUseCases.map((useCase) => {
                  let title = useCase.title;
                  let languageKey = 'english';

                  if (
                    i18n.language === 'de' ||
                    i18n.language === 'german' ||
                    i18n.language === 'deutsch'
                  ) {
                    languageKey = 'german';
                  }

                  if (useCase.translations && useCase.translations[languageKey]) {
                    title = useCase.translations[languageKey].title;
                  } else if (useCase.translations && useCase.translations.english) {
                    title = useCase.translations.english.title;
                  }

                  return (
                    <Grid item xs={12} md={4} key={useCase._id}>
                      <AppCard
                        title={title}
                        usecase_icon_url={useCase.usecase_icon_url}
                        usedCount={0}
                        appType="usecase"
                        id={useCase._id}
                        onClick={() => navigate(`/usecase/${useCase.slug}`)}
                      />
                    </Grid>
                  );
                })
              )}
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

export default AIUseCasesPage;
