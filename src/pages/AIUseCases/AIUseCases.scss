@use '../../styles/abstracts/variables' as *;

.back-to-apply-button {
  
  &.MuiButton-root {
    border: 1px solid $primary-color !important;
    color: $primary-color !important;
    background-color: transparent !important;
    border-radius: $border-radius-sm !important;
    text-transform: none !important;
    font-weight: $font-weight-medium;
    font-size: $font-size-sm !important;
    padding: $spacing-1 $spacing-2 !important;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    
    .MuiSvgIcon-root {
      font-size: 20px;
      transition: transform 0.3s ease;
    }
    
    &:hover {
      border-color: $primary-color !important;
      background-color: rgba($primary-color, 0.04);
      
      .MuiSvgIcon-root {
        transform: translateX(-2px);
      }
    }
    
    &:active {
      transform: scale(0.98);
    }
  }
}

.app-card-wrapper {
  padding: 0 !important;
}

.tabs-container {
  border-bottom: 1px solid $divider-color;
  margin-bottom: $spacing-4;

  .MuiTabs-root {
    .MuiTab-root {
      text-transform: none;
      min-width: auto;
      padding: $spacing-3 $spacing-4;
      font-size: $font-size-sm;
      cursor:pointer !important;
      font-weight: $font-weight-medium;
      margin-right: $spacing-2;

      &.Mui-selected {
        color: $primary-color;
      }

      &:hover {
        color: $primary-color-light;
      }
    }

    .MuiTabs-indicator {
      background-color: $primary-color;
      height: 2px;
    }
  }
}

.no-results-container {
  padding: $spacing-4;
  
  .no-results-message {
    background-color: rgba(#e3f2fd, 0.7);
    border: 1px solid #bbdefb;
    border-radius: $border-radius-sm;
    color: #0d47a1;
    font-size: $font-size-md;
    padding: $spacing-3;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    
    .message-icon {
      margin-right: $spacing-2;
      font-size: 20px;
    }
  }
} 