import { Box, Typography, Grid, CircularProgress } from '@mui/material';
import PropTypes from 'prop-types';
import AppCard from '../../../../components/AppCard/AppCard';
import styles from '../../Cockpit.module.scss';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useGetMostFrequentlyUsedAppsQuery } from '@/redux/services/app-tracking-api';
import { useGetUsecasesListByIdsQuery } from '@/redux/services/use-case-api';

const UseCaseApps = ({ title, subtitle }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const user = useSelector((state) => state.auth.user);
  const { data: apiResponse, isLoading: isMostUsedAppsLoading } = useGetMostFrequentlyUsedAppsQuery(user?._id, {
    skip: !user?._id
  });

  // Extract the array from the response based on the actual structure
  // {success: true, data: [{appId: "...", appType: "...", totalCount: 1}, ...]}
  const mostUsedApps = apiResponse?.success && Array.isArray(apiResponse.data)
    ? apiResponse.data
    : [];

  // MongoDB ObjectId'leri 24 karakter uzunluğundadır
  const isValidObjectId = (id) => {
    return id && typeof id === 'string' && id.length === 24 && /^[0-9a-fA-F]{24}$/.test(id);
  };

  // Get usecase IDs only from the usecase type apps
  const usecaseIds = mostUsedApps
    .filter(app => app && app.appType === 'usecase')
    .map(app => app.appId)
    .filter(id => isValidObjectId(id)); // Sadece geçerli ObjectId'leri filtrele


  const usecaseIdsBody = { usecaseIds: usecaseIds };

  // Call the hook unconditionally to avoid React Hook rules violation
  const { data: usecasesList = [], isLoading: isUsecasesListLoading } = useGetUsecasesListByIdsQuery(
    usecaseIdsBody,
    { skip: usecaseIds.length === 0 }
  );

  // Wait until both data sources are loaded
  if (isMostUsedAppsLoading || isUsecasesListLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Create enriched apps with all app types, but only enrich usecase types with additional data
  const enrichedApps = mostUsedApps.map(app => {
    // For usecase type apps, enrich with usecase data
    if (app.appType === 'usecase') {
      const usecase = usecasesList.data?.find(usecase => usecase._id === app.appId);
      if (usecase?.title && usecase?.slug) {
        return {
          _id: app.appId,
          shortcutType: 'usecase',
          shortcutID: app.appId,
          totalCount: app.totalCount,
          apiData: {
            title: usecase?.title || 'Untitled App',
            slug: usecase?.slug,
            usecase_icon_url: usecase?.usecase_icon_url,
            usecase_id: app.appId,
            usecase: usecase,
            translations: usecase?.translations
          }
        };
      }
      return null; // Explicitly return null if usecase doesn't have title or slug
    }
    if (app.appType === 'playground') {
      let title = app.appId.split('/').pop() || 'Playground';

      if (app.appId === 'playgrounds/chatgpt') {
        title = 'ChatGPT Playground';
      }
      if (app.appId === 'playgrounds/dall-e') {
        title = 'Dall-E Playground';
      }

      return {
        _id: app.appId,
        shortcutType: 'playground',
        shortcutID: app.appId,
        totalCount: app.totalCount,
        apiData: {
          title: title,
          slug: app.appId,
        }
      };
    }
    // For other app types, keep the original structure but adapt to the expected format
    return {
      _id: app.appId,
      shortcutType: app.appType,
      shortcutID: app.appId,
      totalCount: app.totalCount,
      apiData: {
        title: app.title || app.appId.split('/').pop() || 'App',
        slug: app.appId,
      }
    };
  })
    .filter(app => app !== null) // Filter out null values before sorting
    .sort((a, b) => b.totalCount - a.totalCount); 
  const getShortcutUrl = (shortcutData) => {
    if (!shortcutData?.apiData) return '#';

    switch (shortcutData.shortcutType) {
      case 'usecase':
        return `/usecase/${shortcutData.apiData.slug}`;
      case 'ai_app':
        return `/ai_apps/${shortcutData.apiData.slug}`;
      case 'workflow':
        return `/ai_workflows/${shortcutData.apiData.slug}`;
      case 'playground':
        return `/${shortcutData.apiData.slug}`;
      default:
        return '#';
    }
  };

  const handleShortcutClick = (shortcutData) => {
    if (!shortcutData || !shortcutData.apiData) {
      console.warn('Shortcut data is incomplete:', shortcutData);
      return;
    }

    const url = getShortcutUrl(shortcutData);
    navigate(url);
  };

  return (
    <Box sx={{ flexGrow: 1, width: '100%' }} className={styles.appCardsContainer}>
      <Typography variant="h6" sx={{ mb: 1 }}>
        {title}
      </Typography>
      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 3 }}>
        {subtitle}
      </Typography>

      {enrichedApps.length > 0 ? (
        <Grid container className="app-card-wrapper" spacing={2}>
          {enrichedApps.map((usecase) => (
            <Grid item xs={12} md={4} key={usecase._id || `app-${Math.random()}`}>
              <AppCard
                title={usecase.apiData?.title || 'Untitled App'}
                usecase_icon_url={usecase.apiData?.usecase_icon_url}
                appType={usecase.shortcutType}
                id={usecase.shortcutID}
                onClick={() => handleShortcutClick(usecase)}
              />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          {t('cockpit.common.noMostUsedApps')}
        </Typography>
      )}
    </Box>
  );
};

UseCaseApps.propTypes = {
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string.isRequired
};

export default UseCaseApps;
