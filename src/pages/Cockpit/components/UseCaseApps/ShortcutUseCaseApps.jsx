import { Box, Typography, Grid, CircularProgress } from '@mui/material';
import PropTypes from 'prop-types';
import AppCard from '../../../../components/AppCard/AppCard';
import styles from '../../Cockpit.module.scss';
import { useGetUserShortcutsQuery } from '@/redux/services/shortcuts-api';
import { useSelector } from 'react-redux';
import WhiteContainer from '../../../../components/WhiteContainer/WhiteContainer'; 
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const ShortcutUseCaseApps = ({ title, subtitle, useCases }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const user = useSelector((state) => state.auth.user);
  const { data: userShortcuts, isLoading: isShortcutsLoading } = useGetUserShortcutsQuery(
    {
      userId: user?._id,
      shortcutType: ''
    },
    { skip: !user?._id }
  );

  const getShortcutUrl = (shortcutData) => {
    if (!shortcutData.apiData) return '#';
    
    switch (shortcutData.shortcutType) {
      case 'usecase':
        return `/usecase/${shortcutData.apiData.slug}`;
      case 'ai_app':
        return `/ai_apps/${shortcutData.apiData.slug}`;
      case 'workflow':
        return `/ai_workflows/${shortcutData.apiData.slug}`;
      default:
        return '#';
    }
  };
  const handleShortcutClick = (shortcutData) => {
    if (!shortcutData || !shortcutData.apiData) {
      console.warn('Shortcut data is incomplete:', shortcutData);
      return;
    }
    
    const url = getShortcutUrl(shortcutData);
    navigate(url);
  };
  const validShortcuts = userShortcuts ? userShortcuts.filter(shortcut => {
    if (!shortcut.apiData) {
      return false;
    }
    
    if (shortcut.apiError) {
      return false;
    }
    
    if (!shortcut.apiData.title) {
      return false;
    }
    
    return true;
  }) : [];

  

  return (
    <Box sx={{ flexGrow: 1, width: '100%' }} className={styles.appCardsContainer}>
      <Typography variant="h6" sx={{ mb: 1 }}>
        {title}
      </Typography>
      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 3 }}>
        {subtitle}
      </Typography>
      
      {isShortcutsLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
          <CircularProgress />
        </Box>
      ) : validShortcuts.length > 0 ? (
        <Grid container className="app-card-wrapper" spacing={2}>
          {validShortcuts.map((shortcut) => (
            <Grid item xs={12} md={4} key={shortcut._id}>
              <AppCard
                title={shortcut.apiData.title}
                usecase_icon_url={shortcut.apiData.usecase_icon_url}
                appType={shortcut.shortcutType}
                id={shortcut.shortcutID}
                onClick={() => handleShortcutClick(shortcut)}
              />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          {t('cockpit.common.noFavorites')}
        </Typography>
      )}
    </Box>
  );
};
 
export default ShortcutUseCaseApps;
