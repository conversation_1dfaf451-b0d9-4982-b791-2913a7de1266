@use '../../../../styles/abstracts/variables' as *;
@use "sass:color";

.scoreCard {
  background-color: $bg-paper;
  border-radius: $border-radius-lg;
  padding: $spacing-2;
  box-shadow: $shadow-sm;
  margin-top: $spacing-4;
}

.contentContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.infoSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0;
  margin-left: 20px;
}

.scoreWrapper {
  display: flex;
  align-items: baseline;
  gap: $spacing-1;
}

.scoreValue {
  font-size: 24px !important;
  font-weight: 800 !important;
  color: $text-primary;
}

.awardLogoContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  overflow: hidden;
  margin: 0 10px;
  background: rgba(255, 255, 255, 0.1);
}

.awardLogo {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.awardVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.congratsSection {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
}

.congratsText {
  color: $text-primary !important;
  font-size: $font-size-xl !important;
  font-weight: initial !important;
  display: flex;
  gap: $spacing-2;

  &:global(.MuiTypography-root) {
    font-weight: $font-weight-bold;
  }
}

.levelText {
  color: $text-secondary !important;
  font-size: $font-size-md !important;
}

.capitalizedLevel {
  text-transform: capitalize;
}
