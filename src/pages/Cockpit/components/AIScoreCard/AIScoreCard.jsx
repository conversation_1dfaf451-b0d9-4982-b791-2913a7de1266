import { Box, Typography, Tooltip } from '@mui/material';
import { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import styles from './AIScoreCard.module.scss';
import awardVideo from '/src/assets/videos/award-video.mp4';

const AIScoreCard = ({ score, level }) => {
  const videoRef = useRef(null);
  const { t, i18n } = useTranslation();
  const [tooltipMessage, setTooltipMessage] = useState('');

  // Çeviri için useEffect kullanarak dil değişiminde ve bileşen yüklendiğinde çeviriyi güncelleyelim
  useEffect(() => {
    // Çeviri mevcut mu kontrol edelim
    const key = 'cockpit.levelUp.message';

    if (i18n.exists(key)) {
      setTooltipMessage(t(key));
    } else {
      // Yedek mesaj
      setTooltipMessage(
        'To gain more points & level up, apply & create more use cases, workflows, share more ideas!'
      );
    }
  }, [i18n.language, t, i18n]); // Dil değiştiğinde tekrar çalıştır

  useEffect(() => {
    const video = videoRef.current;

    const playVideo = async () => {
      try {
        if (video) {
          video.currentTime = 0;
          await video.play();
        }
      } catch {
        // Hata durumunda sessizce devam et
      }
    };

    // İlk oynatma için biraz gecikme ekleyelim
    const initialPlay = setTimeout(playVideo, 100);

    // 5 saniye sonra başlayacak interval
    const interval = setTimeout(() => {
      const periodicPlay = setInterval(playVideo, 5000);
      return () => clearInterval(periodicPlay);
    }, 9000);

    // Sayfa görünür olmadığında video'yu durdur
    const handleVisibilityChange = () => {
      if (document.hidden && video) {
        video.pause();
      } else {
        playVideo();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearTimeout(initialPlay);
      clearTimeout(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (video) {
        video.pause();
      }
    };
  }, []);

  return (
    <Tooltip title={tooltipMessage || 'Loading message...'} arrow placement="top">
      <Box className={styles.scoreCard}>
        <Box className={styles.contentContainer}>
          <Box className={styles.awardLogoContainer}>
            <video ref={videoRef} muted playsInline preload="auto" className={styles.awardVideo}>
              <source src={awardVideo} type="video/mp4" />
            </video>
          </Box>
          <Box className={styles.infoSection}>
            <Box className={styles.scoreWrapper}>
              <Typography component="span" className={styles.scoreValue} fontWeight={800}>
                {score}
              </Typography>
              <Typography component="span"> {t('common.points')}</Typography>
            </Box>
            <Box>
              <Typography
                className={`${styles.levelText} ${styles.capitalizedLevel}`}
                variant="body2"
              >
                {level}
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Tooltip>
  );
};

AIScoreCard.propTypes = {
  score: PropTypes.number.isRequired,
  level: PropTypes.string.isRequired,
};

export default AIScoreCard;
