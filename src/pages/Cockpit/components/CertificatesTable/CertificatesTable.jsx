import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Grid,
  CircularProgress,
  Button,
} from '@mui/material';
import PropTypes from 'prop-types';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { useTranslation } from 'react-i18next';
import styles from '../../Cockpit.module.scss';
import { useGetCertificatesByUserIdQuery } from '../../../../redux/services/courses-api';
import CertificateModal from '../../../../components/CertificateModal/CertificateModal';
import { useState } from 'react';

const CertificatesTable = ({ userId }) => {
  const { t } = useTranslation();
  const { data: certificates, isLoading } = useGetCertificatesByUserIdQuery(userId);
  
  const [open, setOpen] = useState(false);
  const [selectedCertificate, setSelectedCertificate] = useState(null);

  const handleOpenModal = (certificate) => {
    setSelectedCertificate(certificate);
    setOpen(true);
  };

  const handleCloseModal = () => {
    setOpen(false);
    setSelectedCertificate(null);
  };
  const handleShareOnLinkedIn = (certificate) => {
    const linkedinUrl = `https://www.linkedin.com/profile/add?startTask=CERTIFICATION_NAME&name=${encodeURIComponent(certificate?.certificateName || 'Course Certificate')}&organizationId=14025979&issueYear=${new Date().getFullYear()}&issueMonth=${new Date().getMonth() + 1}&certUrl=${encodeURIComponent(certificate?.downloadUrl)}&certId=${encodeURIComponent(certificate?.certificateNumber || '')}&organization=${encodeURIComponent('AI Business School')}`;
    window.open(linkedinUrl, '_blank', 'width=600,height=600');
  };

  return (
    <Box sx={{ flexGrow: 1, width: '100%' }} className={styles.appCardsContainer}>
      <Typography variant="h6" sx={{ mb: 1 }}>
        {t('cockpit.certificates.title')}
      </Typography>
      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 3 }}>
        {t('cockpit.certificates.subtitle')}
      </Typography>
      <TableContainer component={Paper} sx={{ mt: 2 }}>
        <Table sx={{ minWidth: 650 }} aria-label="certificates table">
          <TableHead>
            <TableRow>
              <TableCell>{t('cockpit.certificates.training')}</TableCell>
              <TableCell>{t('cockpit.certificates.date')}</TableCell>
              <TableCell align="center">{t('cockpit.certificates.view')}</TableCell>
              <TableCell align="center">{t('cockpit.certificates.share')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
          {isLoading ? (
           
            <TableRow>
              <TableCell colSpan={4} align="center">
                <CircularProgress />
              </TableCell>
            </TableRow>
         
          
        ) : (
          certificates?.data?.certificates?.length > 0 ? (
            certificates?.data?.certificates?.map((certificate, index) => (
              <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
              <TableCell component="th" scope="row">
                {certificate.certificateName}
              </TableCell>
              <TableCell>
                {new Date(certificate.createdAt)
                  .toLocaleString('tr-TR', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false,
                  })
                  .replace(/\./g, '/')}
              </TableCell>
              <TableCell align="center">
                <IconButton
                  color="primary"
                  onClick={() => handleOpenModal(certificate)}
                >
                  <VisibilityIcon />
                </IconButton>
              </TableCell>
              <TableCell align="center">
                <IconButton
                  color="primary"
                  onClick={() => handleShareOnLinkedIn(certificate)}
                >
                  <LinkedInIcon />
                </IconButton>
              </TableCell>
            </TableRow>

            ))
          ) : (
            <TableRow>
              <TableCell colSpan={4} align="center">
                {t('cockpit.certificates.noCertificates')}
              </TableCell>
            </TableRow>
          )
      )}

          </TableBody>
        </Table>
      </TableContainer>
      {selectedCertificate && (
        <CertificateModal
          open={open}
          onClose={handleCloseModal}
          certificateUrl={selectedCertificate.downloadUrl}
          courseId={selectedCertificate.courseId}
          certificateData={selectedCertificate}
        />
      )}
    </Box>
  );
};

CertificatesTable.propTypes = {
  userId: PropTypes.string.isRequired,
};

export default CertificatesTable;
