@use '../../../../styles/abstracts/variables' as *;
@use "sass:color";

.container {
  background-color: $bg-paper;
  border-radius: $border-radius-lg;
  padding: $spacing-4;
  box-shadow: $shadow-sm;
  height: auto;
  display: flex;
  flex-direction: column;
}

.titleContainer {
  display: flex;
  align-items: center;
  gap: $spacing-2;
  margin-bottom: $spacing-3;
  flex-shrink: 0;
}

.titleIcon {
  font-size: $font-size-lg;
  color: $primary-color;
}

.title {
  color: $text-primary !important;
  font-weight: $font-weight-bold !important;
  font-size: $font-size-lg !important;
  letter-spacing: 0.5px !important;

  &:global(.MuiTypography-root) {
    font-weight: $font-weight-bold;
  }
}

.list {
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
  height: 300px;
  overflow-y: auto;
  padding-right: $spacing-2;

  /* <PERSON><PERSON> scrollbar stili */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba($primary-color, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba($primary-color, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba($primary-color, 0.3);
    }
  }
}

.item {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 13px !important;
  border-radius: $border-radius-lg !important;
  transition: all 0.2s ease !important;
  flex-shrink: 0 !important;
  margin: 0 !important;
  border: 1px solid rgba($primary-color, 0.08) !important;
  background-color: #fbfbfb !important;
  border-radius: $border-radius-md !important;

  &::before {
    content: '•';
    color: $primary-color;
    margin-right: $spacing-3;
    font-size: 20px;
    position: absolute;
    left: 8px;
  }
}

.itemLeft {
  padding-left: $spacing-4 !important;
  flex: 1 !important;

  :global(.MuiTypography-root) {
    color: $text-primary;
    font-weight: $font-weight-medium;
    font-size: $font-size-sm;
  }
}

.itemRight {
  display: flex;
  align-items: center;
  gap: $spacing-2;
  margin-left: $spacing-2;
}

.badge {
  padding: $spacing-1 $spacing-3;
  background-color: $primary-color;
  color: $bg-paper;
  border-radius: 12px;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  text-transform: uppercase;
}

.usageCount {
  color: $text-secondary;
  font-size: $font-size-sm;
  background-color: rgba($primary-color, 0.1);
  padding: $spacing-1 $spacing-2;
  border-radius: 12px;
}

.actionButton {
  padding: $spacing-1 $spacing-2;
  background-color: rgba($primary-color, 0.1);
  color: $primary-color;
  border: none;
  border-radius: 12px;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba($primary-color, 0.2);
  }
}

.itemLink {
  text-decoration: none;
  color: inherit;
  display: block;
  width: 100%;
  transition: all 0.2s ease;

  &:hover {
    text-decoration: none;
    
    .item {
      background-color: rgba($primary-color, 0.04) !important;
      border-color: rgba($primary-color, 0.15) !important;
      transform: translateY(-1px);
      box-shadow: $shadow-sm;
    }

    :global(.MuiTypography-root) {
      color: $primary-color;
    }
  }
} 