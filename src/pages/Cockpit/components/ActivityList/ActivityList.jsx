import { Suspense } from 'react';
import { Box, Typography, CircularProgress, List, ListItem, ListItemText } from '@mui/material';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import styles from './ActivityList.module.scss';

const ActivityListContent = ({ title, items, icon }) => {
  const { t } = useTranslation();

  const getTranslatedText = (key, defaultText) => {
    const translated = t(key, { defaultValue: defaultText });
    return translated === key ? defaultText : translated;
  };

  return (
    <Box className={styles.container}>
      <Box className={styles.titleContainer}>
        <span className={styles.titleIcon}>{icon}</span>
        <Typography variant="h6" className={styles.title} fontWeight={700}>
          {title}
        </Typography>
      </Box>
      <List className={styles.list} disablePadding>
        {items.map((item, index) => (
          <a key={index} href={item.url} className={styles.itemLink}>
            <ListItem component="div" className={styles.item} disableGutters>
              <ListItemText
                primary={
                  <Typography variant="body1" className={styles.itemTitle}>
                    {getTranslatedText(`cockpit.items.${item.title}`, item.title)}
                  </Typography>
                }
                className={styles.itemLeft}
              />
              <div className={styles.itemRight}>
                {item.badge && (
                  <span className={styles.badge}>
                    {getTranslatedText(`cockpit.badges.${item.badge}`, item.badge)}
                  </span>
                )}
                {item.usageCount && (
                  <span className={styles.usageCount}>
                    {item.usageCount} {t('cockpit.common.times', 'times')}
                  </span>
                )}
                {item.action && (
                  <button className={styles.actionButton}>
                    {getTranslatedText(`cockpit.actions.${item.action}`, item.action)}
                  </button>
                )}
              </div>
            </ListItem>
          </a>
        ))}
      </List>
    </Box>
  );
};

ActivityListContent.propTypes = {
  title: PropTypes.string.isRequired,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      url: PropTypes.string.isRequired,
      badge: PropTypes.string,
      usageCount: PropTypes.number,
      action: PropTypes.string,
    })
  ).isRequired,
  icon: PropTypes.string.isRequired,
};

const ActivityList = (props) => {
  return (
    <Suspense
      fallback={
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 2 }}>
          <CircularProgress size={24} />
        </Box>
      }
    >
      <ActivityListContent {...props} />
    </Suspense>
  );
};

ActivityList.propTypes = {
  ...ActivityListContent.propTypes,
};

export default ActivityList;
