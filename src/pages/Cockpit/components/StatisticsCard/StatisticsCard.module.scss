@use '../../../../styles/abstracts/variables' as *;

.card {
  background-color: #fbfbfb;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 200px;
  position: relative;
  transition: all 0.3s ease;
  height: 50px;
  
  &.lockedCard {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: rgba(#fbfbfb, 0.5);
    border: 1px dashed rgba($primary-color, 0.3);
    transition: all 0.3s ease;

    &:hover {
      opacity: 0.8;
      background-color: rgba($primary-color, 0.02);
      transform: translateY(-2px);
    }

    .lockedContent {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
      padding: 16px;
    }

    .comingSoonText {
      color: rgba($primary-color, 0.7);
      font-size: $font-size-xs;
      letter-spacing: 2px;
      font-weight: 500;
    }
  }
  
  .cardContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
  }

  .valueSection {
    flex: 0 0 30%;
    display: flex;
    align-items: center;
    justify-content: center;

    :global(.MuiChip-label) {
      padding: 0;
    }
  }

  .valueChip {
    background-color: $bg-paper;
    border: 1px solid #666;
    padding: 4px;
    height: 40px;
    width: 40px;
    position: relative;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .labelSection {
    flex: 0 0 60%;
    padding-left: 16px;
    overflow: hidden;
    
    .label {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #666;
      line-height: 1.4;
    }
  }

  .value {
    font-weight: 600;
    color: #666;
    margin: 0;
    font-size: 14px;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
} 