import { Box, Typography, Chip } from '@mui/material';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import styles from './StatisticsCard.module.scss';

const StatisticsCard = ({ value = null, label = null, isLocked = false }) => {
  const { t } = useTranslation();

  if (isLocked) {
    return (
      <Box className={`${styles.card} ${styles.lockedCard}`}>
        <Box className={styles.cardContent}>
          <Box className={styles.lockedContent}>
            <Typography className={styles.comingSoonText}>Coming Soon</Typography>
          </Box>
        </Box>
      </Box>
    );
  }

  return (
    <Box className={styles.card}>
      <Box className={styles.cardContent}>
        <Box className={styles.valueSection}>
          <Chip
            label={
              <Typography variant="h5" className={styles.value}>
                {value}
              </Typography>
            }
            className={styles.valueChip}
          />
        </Box>
        <Box className={styles.labelSection}>
          <Typography variant="body2" className={styles.label}>
            {t(label)}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

StatisticsCard.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  label: PropTypes.string,
  isLocked: PropTypes.bool,
};

export default StatisticsCard;
