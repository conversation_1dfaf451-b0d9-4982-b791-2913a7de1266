import { Box, Typography, Grid } from '@mui/material';
import PropTypes from 'prop-types';
import AppCard from '../../../../components/AppCard/AppCard';
import { useTranslation } from 'react-i18next';
import styles from '../../Cockpit.module.scss';
import { useGetSimpleAppsByUserIdQuery } from '../../../../redux/services/CreateApp-api';
import { CircularProgress } from '@mui/material';
import { useGetWorkflowsByUserQuery } from '../../../../redux/services/create-workflow';
const CreatedApps = ({ userId }) => {
  const { t } = useTranslation();
  const { data: simpleApps, isLoading } = useGetSimpleAppsByUserIdQuery(userId);
  const { data: workflows, isWorkflowLoading } = useGetWorkflowsByUserQuery(userId);
  return (
    <Box sx={{ flexGrow: 1, width: '100%' }} className={styles.appCardsContainer}>
      <Typography variant="h6" sx={{ mb: 1 }}>
        {t('cockpit.createdApps.title')}
      </Typography>
      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 3 }}>
        {t('cockpit.createdApps.subtitle')}
      </Typography>
      <Grid container spacing={2} sx={{ mt: 2 }}>
        {isLoading ? (
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          </Grid>
        ) : (
          simpleApps?.data?.useCases.map((app, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <AppCard
                title={app.title}
                usecase_icon_url="https://cdn.aibusinessschool.com/usecases/2024-06-03_11-05-09-cloud-1168.svg"
                onClick={() => window.open(`/ai_apps/${app.slug}`, '_blank')}
                disableTooltip={true}
                id={app._id || `created-app-${index}`}
                appType="usecase"
              />
            </Grid>
          ))
        )}
      </Grid>
      <Grid container spacing={2} sx={{ mt: 2 }}>
        {isWorkflowLoading ? (
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          </Grid>
        ) : (
          workflows?.map((workflow, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <AppCard
                title={workflow.title}
                onClick={() => window.open(`/ai_workflows/${workflow.slug}`, '_blank')}
                disableTooltip={true}
                id={workflow._id || `created-workflow-${index}`}
                appType="workflow"
              />
            </Grid>
          ))
        )}
      </Grid>
    </Box>
  );
};

CreatedApps.propTypes = {
  userId: PropTypes.string.isRequired,
};

export default CreatedApps;
