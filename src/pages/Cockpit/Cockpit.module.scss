@use 'sass:color';
@use '../../styles/abstracts/variables' as *;

/* <PERSON><PERSON> fade-in animasyonu */
@keyframes fadeIn {
  from {
    opacity: 0.8;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mainContent {
  padding: $spacing-4;

  :global {
    .MuiGrid-root.MuiGrid-item.MuiGrid-grid-xs-12.MuiGrid-grid-md-3 {
      padding-left: 0;
    }

    .MuiPaper-root.MuiPaper-elevation.MuiPaper-rounded.MuiPaper-elevation1 {
      padding: 0px;
    }
  }
}

.tabsGrid {
  padding-left: 0 !important;
  border-radius: 12px;
  overflow: visible;
  margin-top: 24px !important;
}

.tabsWrapper {
  display: flex;
  min-height: 400px;
  height: auto;
  position: relative;

  :global {
    .MuiTabs-root {
      background-color: #fff;
      border-left: 1px solid #e0e0e0;
      border-right: 1px solid #e0e0e0;
      border-top: 1px solid #e0e0e0;
      min-width: 220px;
      position: sticky;
      top: 20px;
      height: fit-content;
      z-index: 10;
    }

    .MuiTab-root {
      min-width: 220px;
      padding: 20px;
      text-transform: none;
      text-align: left;
      font-weight: 500;
      color: #666;
      opacity: 1;
      transition: all 0.3s ease;
      border-bottom: 1px solid #e0e0e0;
      justify-content: flex-start;
      gap: 12px;

      .MuiSvgIcon-root {
        color: #5e5e5e;
        font-size: 24px;
      }

      .MuiTab-wrapper {
        flex-direction: row;
        justify-content: flex-start;
      }

      &.Mui-selected {
        color: #5e5e5e;
        background-color: #e9e9e9;
        box-shadow: $shadow-sm;
        .MuiSvgIcon-root {
          color: $primary-color;
        }
      }

      &:hover:not(.Mui-selected) {
        background-color: rgba(92, 95, 201, 0.08);
      }
    }

    .MuiTabPanel-root {
      padding: 24px;
      background-color: #fff;
    }
  }
}

.verticalTabPanel {
  :global {
    .MuiBox-root {
      padding: 0 !important;
    }
  }
}

.tabPanelContainer {
  height: auto;
  min-height: 100%;
  scroll-margin-top: 20px;
  transition: all 0.3s ease;
  scroll-behavior: smooth;
  position: relative;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  box-shadow: $shadow-sm;

  &#vertical-tabpanel-0 {
    padding: 0;

    .MuiBox-root {
      padding: 0;
    }
  }
  
  .tabPanelBox {
    margin: 20px;
  }
}

/* Uygulama kartlarını içeren grid container için stil tanımı */
.appCardsContainer {
  margin-top: 0px !important;
}

.innerTabsContainer {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;

  :global {
    .MuiTabs-root {
      min-height: 48px;
    }

    .MuiTab-root {
      text-transform: none;
      font-size: 14px;
      font-weight: 500;
      color: #666;
      min-width: 120px;
      min-height: 48px;

      &.Mui-selected {
        color: #5C5FC9;
      }
    }

    .MuiTabs-indicator {
      background-color: #5C5FC9;
    }
  }
}

.innerTabPanel {
  padding: $spacing-2;
  height: auto;
  flex-grow: 1;
}

.statisticsContainer {
  padding: 10px;
  background-color: #e9e9e9;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  margin-top: $spacing-4;
  position: relative;

  :global {
    .swiper {
      margin: 0;
      padding: 0;
      width: 100%;
      position: unset;
    }

    .swiper-wrapper {
      display: flex;
      margin-left: 0;
      gap: 0;
    }

    .swiper-slide {
      width: 200px !important;
      height: auto;
      padding: 0 12px;
      box-sizing: border-box;
      margin-right: 0px !important;

      & > div {
        height: 100%;
        width: 100%;
      }
    }

    .swiper-button-next,
    .swiper-button-prev {
      width: 32px;
      height: 32px;
      background-color: #fff;
      border-radius: 50%;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      color: $primary-color;
      position: absolute;
      top: 50%;
      z-index: 10;

      &:after {
        font-size: 16px;
        font-weight: bold;
      }

      &:hover {
        background-color: $primary-color;
        color: #fff;
      }

      &.swiper-button-disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .swiper-button-prev {
      left: -15px;
    }

    .swiper-button-next {
      right: -15px;
    }
  }
}

.sliderItem {
  height: 100%;
  width: 100%;
}

.sliderCard {
  padding: 10px !important; 
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #fff !important;
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.cardHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.cardLabel {
  font-size: 14px !important;
  color: #666;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-align: center;
  width: 100%;
  height: 40px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  line-height: 1.5;
  overflow: hidden;
}

.cardValue {
  font-size: 18px !important;
  color: #333;
  margin-top: 4px;
  text-align: center;
  width: 100%;
  position: relative;
  z-index: 1;
}

.cardTitle {
  text-transform: capitalize;
  font-size: 13px;
  color: #666;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-align: center;
  width: 100%;
}

.courseCard {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: $shadow-sm;
  display: flex;
  margin-bottom: 16px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: $shadow-md;
    transform: translateY(-2px);
  }

  :global {
    .MuiCardContent-root {
      padding: 24px;
      &:last-child {
        padding-bottom: 24px;
      }
    }
  }
}

.courseInfo {
  flex: 1;
  display: flex;
  gap: 24px;
  padding-bottom: 0 !important;
}

.courseLeft {
  width: 240px;
  height: 190px;
  position: relative;
  overflow: hidden;
}

.courseRight {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.courseHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.courseActions {
  margin-top: 16px;
}

.chapterInfo {
  h4 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    margin-bottom: 8px;
    color: #2A2B3F;
    text-transform: capitalize !important;
    letter-spacing: 0.5px;
  }

  :global {
    .MuiTypography-h4 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      margin-bottom: 8px;
      color: #2A2B3F;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .MuiTypography-body1 {
      font-size: 13px;
      font-weight: 400;
      margin: 0;
      color: #666;
      line-height: 1.5;
      opacity: 0.8;
    }
  }
}

.progressInfo {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.continueButton {
  background: #2A2B3F;
  color: #fff;
  padding: 8px 24px;
  border-radius: 20px;
  font-weight: 500;
  text-transform: none !important;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;

  &::first-letter {
    text-transform: uppercase !important;
  }

  :global {
    .MuiButton-startIcon {
      margin-right: 4px;
      margin-left: -4px;
    }

    .MuiButton-endIcon {
      margin-left: 4px;
      margin-right: -4px;
    }

    svg {
      font-size: 18px;
      transition: transform 0.3s ease;
    }
  }

  &:hover {
    background: color.adjust(#2A2B3F, $lightness: 10%);

    :global {
      svg {
        transform: translateX(4px);
      }
    }
  }

  &.successButton {
    background: #2e7d32;
    pointer-events: none;
    cursor: default;

    &:hover {
      background: #2e7d32;
    }

    :global {
      svg {
        transform: none;
        font-size: 20px;
      }

      .MuiButton-startIcon {
        margin-right: 8px;
        margin-left: 0;
      }
    }
  }
}
