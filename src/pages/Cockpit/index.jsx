import { Suspense, useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  CircularProgress,
  Paper,
  Tabs,
  Tab,
  Divider,
} from '@mui/material';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import Carousel from '../../components/Carousel/Carousel.jsx';
import AIScoreCard from './components/AIScoreCard/AIScoreCard';
import UseCaseApps from './components/UseCaseApps/UseCaseApps';
import ShortcutUseCaseApps from './components/UseCaseApps/ShortcutUseCaseApps';
import TrainingTabs from './components/TrainingTabs/TrainingTabs';
import CreatedApps from './components/CreatedApps/CreatedApps';
import SubmittedIdeas from './components/SubmittedIdeas/SubmittedIdeas';
import CertificatesTable from './components/CertificatesTable/CertificatesTable';
import { cockpitData } from '../../mockData/cockpit';
import SchoolIcon from '@mui/icons-material/School';
import AppsIcon from '@mui/icons-material/Apps';
import BuildIcon from '@mui/icons-material/Build';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import MicrosoftLogo from '../../assets/images/logos/microsoft.svg';
import AibsLogo from '../../assets/images/logos/aibs-logo.svg';
import styles from './Cockpit.module.scss';
import { useGetStatsQuery } from '../../redux/services/cockpit-api';

// Tab Panel bileşeni
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`vertical-tabpanel-${index}`}
      aria-labelledby={`vertical-tab-${index}`}
      {...other}
      className={`${styles.tabPanelContainer} ${value === index ? styles.activePanel : ''}`}
    >
      {value === index && (
        <Box className={index !== 0 ? styles.tabPanelBox : ''} sx={{ height: '100%', p: 0 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

const SliderCard = ({ value, label }) => {
  const { t } = useTranslation();

  return (
    <Paper className={styles.sliderCard}>
      <Box className={styles.cardHeader}>
        <Typography className={styles.cardLabel}>{t(label)}</Typography>
      </Box>
      <Box className={styles.cardValue}>
        {label.includes('REVENUE') || label.includes('TRANSACTIONS') ? '$' : ''}
        {value}
      </Box>
    </Paper>
  );
};

SliderCard.propTypes = {
  value: PropTypes.number,
  label: PropTypes.string,
  isLocked: PropTypes.bool,
};

const getCompanyLogo = (company) => {
  switch (company) {
    case 'microsoft':
      return MicrosoftLogo;
    case 'aibusinessschool':
      return AibsLogo;
    default:
      return AibsLogo;
  }
};

const CockpitContent = () => {
  const user = useSelector((state) => state.auth.user);
  const [tabValue, setTabValue] = useState(0);
  const { t } = useTranslation();

  // Cockpit API'den veri çekme
  const { data: cockpitStatsData } = useGetStatsQuery(user?._id ? { userId: user._id } : undefined);

  // Tab değiştiğinde sadece state'i güncelle
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Sayfa yüklendiğinde veya boyut değiştiğinde yapılacak işlemler
  useEffect(() => {
    // Resize olayı için boş bir işleyici tanımla
    const handleResize = () => {};

    // Resize olayını dinle
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [tabValue]);
  /** 
  const cardData = cockpitData.statistics.map((stat) => ({
    value: stat.value,
    label: stat.label,
    isLocked: stat.isLocked || false,
  }));
*/
  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">Please log in.</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Box className={styles.mainContent}>
            <Grid container spacing={4}>
              {/* AI Score Section */}
              <Grid item xs={12} md={3}>
                <AIScoreCard
                  level={user?.journeyLevel?.name}
                  score={cockpitStatsData?.totalAIScore || 0}
                />
              </Grid>

              {/* Statistics Section */}
              <Grid item xs={12} md={9}>
                <Box className={styles.statisticsContainer}>
                  <Carousel
                    swiperProps={{
                      slidesPerView: 'auto',
                      spaceBetween: 1,
                      navigation: true,
                      pagination: false,
                    }}
                    breakpoints={{
                      320: { slidesPerView: 1, spaceBetween: 24 },
                      768: { slidesPerView: 2, spaceBetween: 24 },
                      1024: { slidesPerView: 4, spaceBetween: 24 },
                      1440: { slidesPerView: 4, spaceBetween: 24 },
                    }}
                  >
                    <div key="0" className={styles.sliderItem}>
                      <Paper className={styles.sliderCard}>
                        <Box className={styles.cardHeader}>
                          <Typography className={styles.cardLabel}>
                            {t('cockpit.statistics.appliedTotalUseCases')}
                          </Typography>
                        </Box>
                        <Box className={styles.cardValue}>
                          {cockpitStatsData?.totalUsecaseUsage
                            ? cockpitStatsData?.totalUsecaseUsage
                            : '-'}
                        </Box>
                      </Paper>
                    </div>

                    <div key="1" className={styles.sliderItem}>
                      <Paper className={styles.sliderCard}>
                        <Box className={styles.cardHeader}>
                          <Typography className={styles.cardLabel}>
                            {t('cockpit.statistics.createdApp')}
                          </Typography>
                        </Box>
                        <Box className={styles.cardValue}>
                          {cockpitStatsData?.totalSimpleApp
                            ? cockpitStatsData?.totalSimpleApp
                            : '-'}
                        </Box>
                      </Paper>
                    </div>

                    <div key="2" className={styles.sliderItem}>
                      <Paper className={styles.sliderCard}>
                        <Box className={styles.cardHeader}>
                          <Typography className={styles.cardLabel}>
                            {t('cockpit.statistics.createdWorkflow')}
                          </Typography>
                        </Box>
                        <Box className={styles.cardValue}>
                          {cockpitStatsData?.totalWorkflow ? cockpitStatsData?.totalWorkflow : '-'}
                        </Box>
                      </Paper>
                    </div>

                    <div key="3" className={styles.sliderItem}>
                      <Paper className={styles.sliderCard}>
                        <Box className={styles.cardHeader}>
                          <Typography className={styles.cardLabel}>
                            {t('cockpit.statistics.appliedUniqueUseCases')}
                          </Typography>
                        </Box>
                        <Box className={styles.cardValue}>
                          {cockpitStatsData?.totalUniqueUsecase
                            ? cockpitStatsData?.totalUniqueUsecase
                            : '-'}
                        </Box>
                      </Paper>
                    </div>

                    <div key="4" className={styles.sliderItem}>
                      <Paper className={styles.sliderCard}>
                        <Box className={styles.cardHeader}>
                          <Typography className={styles.cardLabel}>
                            {t('cockpit.statistics.submittedIdeas')}
                          </Typography>
                        </Box>
                        <Box className={styles.cardValue}>
                          {cockpitStatsData?.totalIdeas
                            ? cockpitStatsData?.totalIdeas
                            : '-'}
                        </Box>
                      </Paper>
                    </div>

                    <div key="5" className={styles.sliderItem}>
                      <Paper className={styles.sliderCard}>
                        <Box className={styles.cardHeader}>
                          <Typography className={styles.cardLabel}>
                            {t('cockpit.statistics.obtainedCertificates')}
                          </Typography>
                        </Box>
                        <Box className={styles.cardValue}>
                          {cockpitStatsData?.totalCertificates
                            ? cockpitStatsData?.totalCertificates
                            : '-'}
                        </Box>
                      </Paper>
                    </div>

                    <div key="6" className={styles.sliderItem}>
                      <Paper className={styles.sliderCard}>
                        <Box className={styles.cardHeader}>
                          <Typography className={styles.cardLabel}>
                            {t('cockpit.statistics.completedTrainingJourneys')}
                          </Typography>
                        </Box>
                        <Box className={styles.cardValue}>
                          {cockpitStatsData?.getUserCompletedCourses
                            ? cockpitStatsData?.getUserCompletedCourses
                            : '-'}
                        </Box>
                      </Paper>
                    </div>
                  </Carousel>
                </Box>
              </Grid>

              {/* Vertical Tabs Section */}
              <Grid
                item
                xs={12}
                className={styles.tabsGrid}
                sx={{
                  borderRadius: '12px',
                  overflow: 'visible',
                  mt: 3, // marginTop
                }}
              >
                <Box className={styles.tabsWrapper}>
                  <Tabs
                    orientation="vertical"
                    variant="scrollable"
                    value={tabValue}
                    onChange={handleTabChange}
                  >
                    <Tab
                      icon={<SchoolIcon />}
                      iconPosition="start"
                      label={t('cockpit.tabs.myTrainings')}
                    />
                    <Tab
                      icon={<AppsIcon />}
                      iconPosition="start"
                      label={t('cockpit.tabs.topUseCaseApps')}
                    />
                    <Tab
                      icon={<BuildIcon />}
                      iconPosition="start"
                      label={t('cockpit.tabs.createdApps')}
                    />
                    <Tab
                      icon={<LightbulbIcon />}
                      iconPosition="start"
                      label={t('cockpit.tabs.ideasSubmitted')}
                    />
                    <Tab
                      icon={<EmojiEventsIcon />}
                      iconPosition="start"
                      label={t('cockpit.tabs.certificates')}
                    />
                  </Tabs>

                  <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    <TabPanel value={tabValue} index={0}>
                      <TrainingTabs userId={user?._id} />
                    </TabPanel>
                    <TabPanel value={tabValue} index={1}>
                      <ShortcutUseCaseApps
                        title={t('apply.favorites.title')}
                        subtitle={t('apply.favorites.subtitle')}
                        useCases={cockpitData.favoriteUseCases}
                      />

                      <Divider sx={{ my: 4, borderColor: 'rgba(0, 0, 0, 0.12)', height: '1px' }} />

                      <UseCaseApps
                        title={t('cockpit.common.mostFrequentlyUsedUseCases')}
                        subtitle={t('cockpit.common.specialSelectionForYou')}
                        useCases={cockpitData.frequentUseCases}
                      />
                    </TabPanel>
                    <TabPanel value={tabValue} index={2}>
                      <CreatedApps userId={user?._id} />
                    </TabPanel>
                    <TabPanel value={tabValue} index={3}>
                      <SubmittedIdeas ideas={cockpitData.submittedIdeas} />
                    </TabPanel>
                    <TabPanel value={tabValue} index={4}>
                      <CertificatesTable userId={user?._id} />
                    </TabPanel>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </Container>
  );
};

const CockpitPage = () => {
  return (
    <Suspense
      fallback={
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '100vh',
          }}
        >
          <CircularProgress />
        </Box>
      }
    >
      <CockpitContent />
    </Suspense>
  );
};

export default CockpitPage;
