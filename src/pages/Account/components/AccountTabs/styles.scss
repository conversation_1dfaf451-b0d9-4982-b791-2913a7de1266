.account-tabs {
  border-bottom: 1px solid var(--divider-color);
  margin-bottom: var(--spacing-4);

  .tabs {
    min-height: unset;

    font-size: var(--font-size-sm) !important;
    .MuiTabs-flexContainer {
      gap: var(--spacing-2);
      padding-bottom: var(--spacing-3);
      justify-content: space-between;
      width: 100%;
    }

    .MuiTab-root {
      text-transform: none;
      min-height: 40px;
      padding: var(--spacing-2) var(--spacing-3);
      background-color: var(--bg-light);
      border-radius: var(--border-radius-md);
      color: var(--text-secondary);

      &.Mui-selected {
        background-color: var(--bg-paper);
        color: var(--text-primary);
        box-shadow: var(--shadow-sm);
      }
    }

    &__spacer {
      flex-grow: 1;
    }

    &__reset-container {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-left: auto;
    }
  }

  .reset-button {
    display: flex;
    align-items: center;
    background-color: #FFF1F3;
    border-radius: 20px;
    padding: var(--spacing-1) var(--spacing-3);
    gap: var(--spacing-2);
    margin-left: auto;

    &__icon {
      color: var(--error-color);
    }

    &__text {
      color: var(--error-color);
      font-size: var(--font-size-sm);
      text-transform: initial;
    }
  }

  .reset-modal {
    &__title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
    }

    &__description {
      margin-top: var(--spacing-2);
      color: var(--text-secondary);
      font-size: var(--font-size-sm);
    }

    &__checkboxes {
      margin-top: var(--spacing-3);
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);

      .MuiFormControlLabel-root {
        margin: 0;
        
        .MuiCheckbox-root {
          padding: var(--spacing-2);
        }

        .MuiTypography-root {
          font-size: var(--font-size-sm);
          color: var(--text-primary);
        }
      }
    }

    &__submit {
      margin-top: var(--spacing-3);
      width: 100%;
      height: 40px;
      background-color: #FFF1F3;
      color: var(--error-color);
      font-size: var(--font-size-sm);
      text-transform: none;
      border-radius: var(--border-radius-md);
    }
  }
} 