import { useState } from 'react';
import { <PERSON>, Tabs, Tab, <PERSON><PERSON><PERSON>, Button, Modal, IconButton } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import CloseIcon from '@mui/icons-material/Close';
import './styles.scss';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

const AccountTabs = ({
  activeTab,
  handleTabChange,
  displayResetButton,
  handleDeleteOnboarding,
  isDeletingOnboarding = false,
}) => {
  const user = useSelector((state) => state.auth.user);
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const renderModal = () => {
    const style = {
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      width: 400,
      maxWidth: '90%',
      bgcolor: 'background.paper',
      boxShadow: 24,
      p: 4,
      borderRadius: 2,
    };

    return (
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <Box sx={style} className="reset-modal">
          <Box sx={{ position: 'relative' }}>
            <IconButton
              aria-label="close"
              onClick={handleClose}
              sx={{
                position: 'absolute',
                right: -8,
                top: -8,
                color: (theme) => theme.palette.grey[500],
              }}
            >
              <CloseIcon />
            </IconButton>
            <Typography
              className="reset-modal__title"
              variant="h6"
              color="text.primary"
              fontWeight={600}
            >
              {t('accountSettings.resetModal.title')}
            </Typography>
            <Typography
              className="reset-modal__description"
              variant="subtitle2"
              color="text.secondary"
            >
              {t('accountSettings.resetModal.description')}
            </Typography>
            <Box
              sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 3 }}
            >
              <Button
                variant="outlined"
                sx={{ textTransform: 'capitalize', width: '45%' }}
                onClick={() => {
                  handleDeleteOnboarding(user?._id);
                }}
              >
                {isDeletingOnboarding
                  ? t('accountSettings.resetModal.deleting')
                  : t('accountSettings.resetModal.onboardData')}
              </Button>
              <Button
                variant="outlined"
                sx={{ textTransform: 'capitalize', width: '45%' }}
                onClick={() => {
                  handleClose();
                }}
              >
                {t('accountSettings.resetModal.journeyData')}
              </Button>
            </Box>
          </Box>
        </Box>
      </Modal>
    );
  };

  return (
    <Box className="account-tabs">
      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        className="tabs"
        centered={false}
        variant="scrollable"
        scrollButtons="auto"
      >
        <Tab label={t('accountSettings.tabs.basicInfo')} />
        <Tab label={t('accountSettings.tabs.personalInfo')} />
        <Box className="tabs__spacer" />
        {displayResetButton && (
          <Box className="tabs__reset-container">
            <Button className="reset-button" onClick={handleOpen}>
              <DeleteIcon className="reset-button__icon" fontSize="small" />
              <Typography className="reset-button__text">
                {t('accountSettings.resetModal.resetButton')}
              </Typography>
            </Button>
            {renderModal()}
          </Box>
        )}
      </Tabs>
    </Box>
  );
};

AccountTabs.propTypes = {
  activeTab: PropTypes.number.isRequired,
  handleTabChange: PropTypes.func.isRequired,
  displayResetButton: PropTypes.bool.isRequired,
  handleDeleteOnboarding: PropTypes.func.isRequired,
  isDeletingOnboarding: PropTypes.bool,
};

export default AccountTabs;
