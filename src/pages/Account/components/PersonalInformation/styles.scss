@use '../../../../styles/abstracts/variables' as *;

.personal-info-card,
.user-data-card {
  padding: $spacing-3 $spacing-4;

  .personal-info__title,
  .user-data__title {
    margin: 0;
    font-weight: $font-weight-semibold !important;
  }

  .divider {
    margin: 0 (-$spacing-4);
    width: calc(100% + #{2 * $spacing-4});
    position: relative;
    left: 0;
    margin-top: $spacing-3;
  }

  .form-field {
    margin-top: $spacing-4;

    .MuiTextField-root {
      background-color: $bg-light;
    }
  }
}

.form-field {
  &__label {
    margin-bottom: $spacing-2 !important;
    font-weight: $font-weight-medium !important;
    color: $text-primary !important;
  }

  &__required {
    color: $error-color !important;
    margin-left: $spacing-1 !important;
  }
}

// Card styles
.personal-info-card,
.user-data-card {
  box-shadow: $shadow-sm !important;
  border-radius: $border-radius-md !important;
  background-color: $bg-paper !important;
  border: 1px solid $border-color !important;

  .MuiSelect-select,
  .MuiTextField-root {
    border-radius: $border-radius-sm !important;
  }
}
.save-button {
  margin-top: $spacing-3;
  text-transform: initial;
  padding-left: 32px;
  padding-right: 32px;
}