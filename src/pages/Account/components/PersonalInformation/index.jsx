import { forwardRef, useEffect } from 'react';
import { Card, Typography, Grid, TextField, Box, Divider, Select, Tooltip } from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import './styles.scss';
import { useSelector } from 'react-redux';
import { useIndustryQuery } from '../../../../redux/services/cds-api';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

const PersonalInformation = () => {
  const user = useSelector((state) => state.auth.user);
  const { t, i18n } = useTranslation();
  const { data: industry } = useIndustryQuery();

  const { control, setValue } = useForm({
    defaultValues: {
      industry: user?.onboarding?.industry_label?.slug || '',
      language: user?.onboarding?.language || '',
      businessFunction: user?.onboarding?.function_label?.slug || '',
      isManager: user?.onboarding?.management_role_label?.slug || '',
      technicalLevel: user?.onboarding?.technical_background_label?.slug || '',
      aiKnowledge: user?.onboarding?.ai_knowledge_label?.slug || '',
    },
  });

  useEffect(() => {
    if (industry?.data && user?.onboarding?.industry_label) {
      const selectedIndustry = industry.data.find(
        (item) =>
          item.slug ===
          (typeof user.onboarding.industry_label === 'string'
            ? user.onboarding.industry_label
            : user.onboarding.industry_label.slug)
      );

      if (selectedIndustry) {
        setValue('industry', selectedIndustry.slug);
      } else {
        setValue('industry', '');
      }
    }
  }, [industry?.data, user?.onboarding?.industry_label, setValue]);

  const getTranslatedValue = (field) => {
    if (!field) return '';
    if (typeof field === 'string') return field;
    return field.translations?.[i18n.language] || field.translations?.en || field.slug || '';
  };

  // Dil kodunu tam dil ismine dönüştürme fonksiyonu
  const getLanguageName = (langCode) => {
    switch (langCode) {
      case 'en':
        return 'English';
      case 'de':
        return 'Deutsch';
      default:
        return langCode;
    }
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card className="personal-info-card">
          <Typography variant="subtitle1" className="personal-info__title">
            {t('accountSettings.tabs.language')}
          </Typography>
          <Divider className="divider" />
          <Box sx={{ mt: 3 }}>
            <Controller
              name="language"
              control={control}
              render={({ field }) => (
                <FormField
                  {...field}
                  value={getLanguageName(user?.onboarding?.language) || ''}
                  label={t('accountSettings.personalInfo.language.label')}
                  required
                  disabled
                />
              )}
            />
          </Box>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card className="user-data-card">
          <Typography variant="subtitle1" className="user-data__title">
            {t('accountSettings.resetModal.userData')}
          </Typography>
          <Divider className="divider" />
          <Box sx={{ mt: 3 }}>
            <Controller
              name="businessFunction"
              control={control}
              render={({ field }) => (
                <FormField
                  {...field}
                  value={getTranslatedValue(user?.onboarding?.function_label)}
                  label={t('accountSettings.personalInfo.businessFunction.label')}
                  required
                  disabled
                />
              )}
            />

            <Controller
              name="businessJobRole"
              control={control}
              render={({ field }) => (
                <FormField
                  {...field}
                  value={getTranslatedValue(user?.onboarding?.job_role_label)}
                  label={t('accountSettings.personalInfo.businessJobRole.label')}
                  required
                  disabled
                />
              )}
            />

            <Controller
              name="isManager"
              control={control}
              render={({ field }) => (
                <FormField
                  {...field}
                  value={getTranslatedValue(user?.onboarding?.management_role_label)}
                  label={t('accountSettings.personalInfo.managementRole.label')}
                  required
                  disabled
                />
              )}
            />

            <Controller
              name="technicalLevel"
              control={control}
              render={({ field }) => (
                <FormField
                  {...field}
                  value={getTranslatedValue(user?.onboarding?.technical_background_label)}
                  label={t('accountSettings.personalInfo.technicalBackground.label')}
                  required
                  disabled
                />
              )}
            />

            <Controller
              name="aiKnowledge"
              control={control}
              render={({ field }) => (
                <FormField
                  {...field}
                  value={getTranslatedValue(user?.onboarding?.ai_knowledge_label)}
                  label={t('accountSettings.personalInfo.aiKnowledge.label')}
                  required
                  disabled
                />
              )}
            />

            <Controller
              name="industry"
              control={control}
              render={({ field }) => (
                <FormField
                  {...field}
                  value={getTranslatedValue(user?.onboarding?.industry_label)}
                  label={t('accountSettings.personalInfo.industry.label')}
                  required
                  disabled
                />
              )}
            />
          </Box>
        </Card>
      </Grid>
    </Grid>
  );
};

const FormField = forwardRef(
  ({ label, required, children, error, helperText, select, ...props }, ref) => {
    const { t } = useTranslation();

    const content = (
      <div className="form-field">
        <Typography variant="subtitle2" className="form-field__label">
          {label} {required && <span className="form-field__required">*</span>}
        </Typography>
        {select ? (
          <Select fullWidth size="small" error={error} ref={ref} {...props}>
            {children}
          </Select>
        ) : (
          <TextField
            fullWidth
            size="small"
            error={error}
            helperText={helperText}
            ref={ref}
            {...props}
          />
        )}
      </div>
    );

    if (props.disabled) {
      return (
        <Tooltip
          title={t('accountSettings.personalInfo.disabledFieldTooltip')}
          arrow
          placement="top"
        >
          {content}
        </Tooltip>
      );
    }

    return content;
  }
);

FormField.propTypes = {
  label: PropTypes.string,
  required: PropTypes.bool,
  children: PropTypes.node,
  error: PropTypes.bool,
  helperText: PropTypes.string,
  select: PropTypes.bool,
  disabled: PropTypes.bool,
};

FormField.displayName = 'FormField';

export default PersonalInformation;
