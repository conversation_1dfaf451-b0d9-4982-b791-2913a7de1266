import { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Box,
  TextField,
  Divider,
  IconButton,
  CircularProgress,
} from '@mui/material';
import Button from '../../../../components/Button/Button';
import { useChangePasswordMutation } from '../../../../redux/services/auth-api';
import { toast } from 'react-toastify';
import './styles.scss';
import { useSelector } from 'react-redux';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

const PasswordChange = () => {
  const [changePassword] = useChangePasswordMutation();
  const { t } = useTranslation();
  const user = useSelector((state) => state.auth.user);
  const [isLoading, setIsLoading] = useState(false);

  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [passwordStrength, setPasswordStrength] = useState({
    lowercase: false,
    uppercase: false,
    number: false,
    special: false,
    length: false,
  });
  const [passwordsMatch, setPasswordsMatch] = useState(true);
  const [showPasswords, setShowPasswords] = useState({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false,
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    if (name === 'newPassword') {
      checkPasswordStrength(value);
    }

    if (name === 'confirmPassword' || name === 'newPassword') {
      setPasswordsMatch(
        name === 'confirmPassword'
          ? formData.newPassword === value
          : value === formData.confirmPassword
      );
    }
  };

  const checkPasswordStrength = (password) => {
    setPasswordStrength({
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[!@#$%^&*.]/.test(password),
      length: password.length >= 8,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsLoading(true);

    changePassword({
      currentPassword: formData.currentPassword,
      newPassword: formData.newPassword,
      email: user?.email,
    })
      .then((res) => {
        setIsLoading(false);
        if (res.data?.status === 'Success') {
          toast.success(t('notifications.passwordChanged'));
        } else if (res.error) {
          toast.error(res.error.data?.data || t('notifications.error'));
        }
      })
      .catch(() => {
        setIsLoading(false);
        toast.error(t('notifications.error'));
      });
  };

  const getCheckmark = (condition) => (condition ? '✓' : '✕');

  const calculatePasswordStrengthScore = () => {
    const criteria = Object.values(passwordStrength);
    const metCriteria = criteria.filter(Boolean).length;
    return (metCriteria / criteria.length) * 100;
  };

  const getStrengthColor = (score) => {
    if (score <= 25) return '#ff4d4d';
    if (score <= 50) return '#ffa64d';
    if (score <= 75) return '#ffff4d';
    return '#4CAF50';
  };

  const getStrengthText = (score) => {
    if (score <= 25) return t('accountSettings.passwordChange.veryWeak');
    if (score <= 50) return t('accountSettings.passwordChange.weak');
    if (score <= 75) return t('accountSettings.passwordChange.medium');
    return t('accountSettings.passwordChange.strong');
  };

  const handleClickShowPassword = (field) => {
    setShowPasswords((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  return (
    <Card className="password-change">
      <div className="password-change__header">
        <Typography variant="subtitle1" className="password-change__title">
          {t('accountSettings.passwordChange.title')}
        </Typography>
      </div>

      <Divider className="password-change__divider" />

      <Box component="form" className="password-change__form" onSubmit={handleSubmit}>
        <div className="form-group">
          <Typography variant="subtitle2">
            {t('accountSettings.passwordChange.currentPassword')}
          </Typography>
          <TextField
            fullWidth
            size="small"
            type={showPasswords.currentPassword ? 'text' : 'password'}
            name="currentPassword"
            value={formData.currentPassword}
            onChange={handleInputChange}
            className="password-field"
            InputProps={{
              endAdornment: (
                <IconButton onClick={() => handleClickShowPassword('currentPassword')} edge="end">
                  {showPasswords.currentPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              ),
            }}
          />
        </div>

        <div className="form-group">
          <Typography variant="subtitle2">
            {t('accountSettings.passwordChange.newPassword')}
          </Typography>
          <TextField
            fullWidth
            size="small"
            type={showPasswords.newPassword ? 'text' : 'password'}
            name="newPassword"
            value={formData.newPassword}
            onChange={handleInputChange}
            className="password-field"
            InputProps={{
              endAdornment: (
                <IconButton onClick={() => handleClickShowPassword('newPassword')} edge="end">
                  {showPasswords.newPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              ),
            }}
          />

          {formData.newPassword && (
            <>
              <Typography variant="body2" color="text.secondary" className="strength-label">
                {t('accountSettings.passwordChange.passwordStrength')}:{' '}
                {getStrengthText(calculatePasswordStrengthScore())}
              </Typography>

              <div className="strength-bar-container">
                <div
                  className="strength-bar"
                  style={{
                    width: `${calculatePasswordStrengthScore()}%`,
                    backgroundColor: getStrengthColor(calculatePasswordStrengthScore()),
                  }}
                />
              </div>

              <div className="password-requirements">
                <Typography
                  variant="caption"
                  component="div"
                  color={
                    passwordStrength.lowercase && passwordStrength.uppercase
                      ? 'success.main'
                      : 'text.secondary'
                  }
                >
                  {getCheckmark(passwordStrength.lowercase && passwordStrength.uppercase)}{' '}
                  {t('accountSettings.passwordChange.lowercaseUppercase')}
                </Typography>
                <Typography
                  variant="caption"
                  component="div"
                  color={passwordStrength.number ? 'success.main' : 'text.secondary'}
                >
                  {getCheckmark(passwordStrength.number)}{' '}
                  {t('accountSettings.passwordChange.number')}
                </Typography>
                <Typography
                  variant="caption"
                  component="div"
                  color={passwordStrength.special ? 'success.main' : 'text.secondary'}
                >
                  {getCheckmark(passwordStrength.special)}{' '}
                  {t('accountSettings.passwordChange.specialChar')}
                </Typography>
                <Typography
                  variant="caption"
                  component="div"
                  color={passwordStrength.length ? 'success.main' : 'text.secondary'}
                >
                  {getCheckmark(passwordStrength.length)}{' '}
                  {t('accountSettings.passwordChange.minLength')}
                </Typography>
              </div>
            </>
          )}
        </div>

        <div className="form-group">
          <Typography variant="subtitle2">
            {t('accountSettings.passwordChange.confirmPassword')}
          </Typography>
          <TextField
            fullWidth
            size="small"
            type={showPasswords.confirmPassword ? 'text' : 'password'}
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleInputChange}
            className="password-field"
            error={!passwordsMatch && formData.confirmPassword !== ''}
            helperText={
              !passwordsMatch && formData.confirmPassword !== ''
                ? t('accountSettings.passwordChange.passwordsNotMatch')
                : ''
            }
            InputProps={{
              endAdornment: (
                <IconButton onClick={() => handleClickShowPassword('confirmPassword')} edge="end">
                  {showPasswords.confirmPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              ),
            }}
          />
        </div>

        <div className="form-actions">
          <Button
            variant="contained"
            color="primary"
            className="save-button"
            type="submit"
            disabled={
              !Object.values(passwordStrength).every(Boolean) ||
              !formData.confirmPassword ||
              !passwordsMatch ||
              isLoading
            }
          >
            {isLoading ? (
              <span className="button-loading">
                <CircularProgress size={20} color="inherit" />
                &nbsp;
                {t('accountSettings.userProfile.saving')}
              </span>
            ) : (
              t('accountSettings.userProfile.saveChanges')
            )}
          </Button>
        </div>
      </Box>
    </Card>
  );
};

export default PasswordChange;
