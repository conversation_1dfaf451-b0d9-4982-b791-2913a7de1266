@use '../../../../styles/abstracts/variables' as *;
@use '../../../../styles/components/input' as *;

.password-change {
  border: 1px solid $border-color !important;
  border-radius: $border-radius-md !important;
  box-shadow: $shadow-sm !important;
  &__header {
    padding: 16px 24px 0;
    font-weight: $font-weight-semibold !important;
  }

  &__title {
    padding-bottom: 16px;
    margin-bottom: $spacing-3;
    font-weight: $font-weight-bold !important;
    & > * {
      font-weight: $font-weight-bold !important;
    }
  }

  &__subtitle {
    color: $text-secondary;
    margin-bottom: $spacing-4;
  }

  &__form {
    padding: 24px;

    .form-group {
      margin-bottom: 24px;

      .MuiTypography-subtitle2 {
        margin-bottom: 8px;
      }

      .password-field {
        background-color: #F4F6F8;
      }

      .strength-label {
        margin-bottom: 16px;
      }

      .password-requirements {
        margin-top: $spacing-4;
        
        &-title {
          font-weight: $font-weight-semibold;
          margin-bottom: $spacing-2;
        }

        &-list {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            display: flex;
            align-items: center;
            gap: $spacing-2;
            margin-bottom: $spacing-2;
            color: $text-secondary;

            &.valid {
              color: $success-color;

              svg {
                color: $success-color;
              }
            }

            &.invalid {
              color: $error-color;

              svg {
                color: $error-color;
              }
            }

            svg {
              width: 16px;
              height: 16px;
              color: $text-secondary;
            }
          }
        }
      }
      
      .strength-label {
        margin-top: 16px;
        margin-bottom: 4px;
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;

      .save-button {
        text-transform: initial;
        padding-left: 32px;
        padding-right: 32px;
      }

      .button-loading {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  &__button-group {
    display: flex;
    gap: $spacing-3;
    margin-top: $spacing-4;
  }
}

.strength-bar-container {
  width: 100%;
  height: 4px;
  background-color: #e0e0e0;
  border-radius: 2px;
  margin: 8px 0;
  overflow: hidden;
}

.strength-bar {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-label {
  margin-top: 16px;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
} 