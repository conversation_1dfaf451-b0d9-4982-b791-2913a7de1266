@use '../../../../styles/abstracts/variables' as *;
@use '../../../../styles/components/input' as *;

.user-profile {
  border: 1px solid $border-color !important;
  border-radius: $border-radius-md !important;
  box-shadow: $shadow-sm !important;
  &__header {
    display: flex;
    align-items: center;
    gap: $spacing-3;
    padding: 16px 24px 0 24px;
  }

  &__avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background-color: $bg-light;
    display: flex;
    align-items: center;
    justify-content: center;
    color: $text-secondary;
    font-size: $font-size-xl;
    font-weight: $font-weight-medium;
  }

  &__info {
    &-name {
      font-weight: $font-weight-bold;
      margin-bottom: $spacing-1;
    }

    &-email {
      color: $text-secondary;
      font-size: $font-size-sm;
    }
  }

  &__title {
    padding-bottom: 16px;
    font-weight: 600 !important;
  }

  &__subtitle {
    margin-bottom: 24px;
  }

  &__form {
    padding: 24px;
    margin-top: 0;

    .form-group {
      margin-bottom: 24px;

      .MuiTypography-subtitle2 {
        margin-bottom: 8px;
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;

      .save-button {
        text-transform: initial;
        padding-left: 32px;
        padding-right: 32px;
      }
    }
  }
} 