import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Box, TextField, Divider } from '@mui/material';
import Button from '../../../../components/Button/Button';
import './styles.scss';

const UserProfile = ({ user, handleUpdateUser, isUpdatingUser }) => {
  const { t } = useTranslation();
  const [name, setName] = useState(user?.name);
  const [surname, setSurname] = useState(user?.surname);

  const handleChangeName = (e) => {
    setName(e.target.value);
  };

  const handleChangeSurname = (e) => {
    setSurname(e.target.value);
  };

  return (
    <Card className="user-profile">
      <Box className="user-profile__header">
        <Typography variant="subtitle1" className="user-profile__title">
          {t('accountSettings.userProfile.title')}
        </Typography>
      </Box>

      <Divider />

      <Box component="form" className="user-profile__form">
        <Box className="form-group">
          <Typography variant="subtitle2">{t('accountSettings.userProfile.firstName')}</Typography>
          <TextField fullWidth size="small" defaultValue={name} onChange={handleChangeName} />
        </Box>

        <Box className="form-group">
          <Typography variant="subtitle2">{t('accountSettings.userProfile.lastName')}</Typography>
          <TextField fullWidth size="small" defaultValue={surname} onChange={handleChangeSurname} />
        </Box>

        <Box className="form-group">
          <Typography variant="subtitle2">Email</Typography>
          <TextField fullWidth size="small" defaultValue={user?.email} disabled />
        </Box>

        <Box className="form-actions">
          <Button
            variant="contained"
            color="primary"
            className="save-button"
            onClick={() => {
              handleUpdateUser(user?._id, name, surname);
            }}
          >
            {isUpdatingUser ? 'Updating...' : t('accountSettings.userProfile.saveChanges')}
          </Button>
        </Box>
      </Box>
    </Card>
  );
};

export default UserProfile;
