import { useEffect, useState, Suspense } from 'react';
import { Container, Card, Grid, Box, CircularProgress } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { useUpdateUserMutation, useDeleteOnboardingMutation } from '../../redux/services/user-api';
import UserProfile from './components/UserProfile';
import PasswordChange from './components/PasswordChange';
import AccountTabs from './components/AccountTabs';
import PersonalInformation from './components/PersonalInformation';
import './styles.scss';
import { toast } from 'react-toastify';
import { setCredentials } from '../../redux/features/auth/authSlice';
import { useTranslation } from 'react-i18next';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader.jsx';

const AccountSettingsContent = () => {
  const user = useSelector((state) => state.auth.user);
  const [updateUser, { isLoading: isUpdatingUser }] = useUpdateUserMutation();
  const [deleteOnboarding, { isLoading: isDeletingOnboarding }] = useDeleteOnboardingMutation();
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState(0);
  const [displayResetButton, setDisplayResetButton] = useState(false);
  const { t, i18n } = useTranslation();

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  useEffect(() => {
    user?.role === 'Administrator' ? setDisplayResetButton(true) : setDisplayResetButton(false);
  }, [user]);

  const handleDeleteOnboarding = (userId) => {
    deleteOnboarding({ userId }).then((res) => {
      if (res.data.status === 'success') {
        dispatch(
          setCredentials({
            ...user,
            user: {
              ...user,
              onboarding: null,
            },
          })
        );

        // Onboarding resetlendiğinde dil ayarlarını da sıfırla
        localStorage.removeItem('userLanguage');
        i18n.changeLanguage('en');

        toast.success(t('notifications.userDataReset'));
      }
    });
  };

  const handleUpdateUser = (userId, name, surname) => {
    updateUser({ userId, name, surname }).then((res) => {
      if (res.data.status === 'success') {
        toast.success(t('notifications.userUpdated'));
        const updatedUser = {
          ...res.data.data,
          token: user.token,
        };

        dispatch(
          setCredentials({
            user: updatedUser,
            token: user.token,
          })
        );
      }
    });
  };

  return (
    <Container maxWidth="lg" className="account-settings">
      <WelcomeHeader
        isPageView={true}
        title={t('accountSettings.title')}
        description={t('accountSettings.subtitle')}
      />

      <Card className="account-settings__card">
        <AccountTabs
          activeTab={activeTab}
          handleTabChange={handleTabChange}
          displayResetButton={displayResetButton}
          handleDeleteOnboarding={handleDeleteOnboarding}
          isDeletingOnboarding={isDeletingOnboarding}
        />

        <Box className={`tab-panel ${activeTab === 0 ? 'active' : ''}`}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={user?.sso_login ? 12 : 6}>
              <UserProfile
                user={user}
                handleUpdateUser={handleUpdateUser}
                isUpdatingUser={isUpdatingUser}
              />
            </Grid>
            {!user?.sso_login && (
              <Grid item xs={12} md={6}>
                <PasswordChange />
              </Grid>
            )}
          </Grid>
        </Box>

        <Box className={`tab-panel ${activeTab === 1 ? 'active' : ''}`}>
          <PersonalInformation />
        </Box>
      </Card>
    </Container>
  );
};

const AccountSettingsPage = () => {
  return (
    <Suspense
      fallback={
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: 'calc(100vh - 64px)',
          }}
        >
          <CircularProgress />
        </Box>
      }
    >
      <AccountSettingsContent />
    </Suspense>
  );
};

export default AccountSettingsPage;
