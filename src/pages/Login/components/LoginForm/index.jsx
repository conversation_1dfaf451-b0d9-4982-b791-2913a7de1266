import PropTypes from 'prop-types';
import {
  TextField,
  Button,
  Box,
  Typography,
  IconButton,
  FormControlLabel,
  Checkbox,
  Alert,
  CircularProgress,
} from '@mui/material';
import Snackbar from '@mui/material/Snackbar';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { useLocation } from 'react-router-dom';
import { useGetPublicSettingsQuery } from '../../../../redux/services/platform-settings-api';
import defaultLogo from '../../../../assets/images/logo.png';
import microsoftLogo from '../../../../assets/images/logos/microsoft-small.svg';
import { ENVIRONMENT } from '../../../../config-global';
import './styles.scss';

const LoginForm = ({
  register,
  handleSubmit,
  errors,
  showPassword,
  setShowPassword,
  rememberMe,
  setRememberMe,
  setCurrentStep,
  FORGOT_STEPS,
  onSubmit,
  isLoading,
  errMsg,
  setErrMsg,
  handleSsoLogin,
}) => {
  const { data: generalSettings, isLoading: isSettingsLoading } = useGetPublicSettingsQuery();
  const location = useLocation();

  const handleCloseSnackbar = () => {
    setErrMsg(null);
  };

  const logoUrl = isSettingsLoading ? null : generalSettings?.data?.platformLogo || defaultLogo;

  const isSsoEnabled = generalSettings?.data?.ssoEnabled === true;

  // Check if we're in development or preprod environment
  const isDevOrPreprod =
    ENVIRONMENT === 'development' || ENVIRONMENT === 'local' || ENVIRONMENT === 'preprod';

  // Check if form=true is in URL query parameters
  const searchParams = new URLSearchParams(location.search);
  const forceShowForm = searchParams.get('form') === 'true';

  return (
    <div className="login-form">
      <Box className="login-form__logo-container">
        {isSettingsLoading ? (
          <CircularProgress size={40} />
        ) : (
          <img
            src={logoUrl}
            alt={generalSettings?.data?.platformName || 'AI Business School'}
            className="login-form__logo"
            onError={(e) => {
              e.target.src = defaultLogo;
            }}
          />
        )}
      </Box>

      <Typography variant="h4" className="login-form__title">
        Welcome back
      </Typography>

      <Typography variant="body1" className="login-form__subtitle">
        Welcome to AI Adoption platform. Use your company email to access the platform.
      </Typography>

      {isSettingsLoading ? (
        // Show loading spinner while fetching settings
        <Box
          className="login-form__loading-container"
          style={{ marginTop: '24px', textAlign: 'center' }}
        >
          <CircularProgress size={40} />
        </Box>
      ) : (isDevOrPreprod && isSsoEnabled) || (forceShowForm && isSsoEnabled) ? (
        // In development/preprod environment with SSO enabled, or production with form=true and SSO enabled
        // Show both login form and SSO button
        <>
          <Box
            component="form"
            onSubmit={(e) => {
              e.preventDefault();
              handleSubmit(onSubmit)(e);
            }}
            className="login-form__form"
          >
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="Email address"
              autoComplete="email"
              autoFocus
              error={!!errors.email}
              helperText={errors.email?.message}
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address',
                },
              })}
              className="login-form__input"
            />

            <TextField
              margin="normal"
              required
              fullWidth
              id="password"
              label="Password"
              type={showPassword ? 'text' : 'password'}
              autoComplete="current-password"
              error={!!errors.password}
              helperText={errors.password?.message}
              {...register('password', {
                required: 'Password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters',
                },
              })}
              className="login-form__input"
              InputProps={{
                endAdornment: (
                  <IconButton onClick={() => setShowPassword(!showPassword)} edge="end">
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                ),
              }}
            />

            <Box className="login-form__actions">
              <FormControlLabel
                control={
                  <Checkbox
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    color="primary"
                  />
                }
                label="Remember me"
                className="login-form__remember-me"
              />
              <Button
                variant="text"
                onClick={() => setCurrentStep(FORGOT_STEPS.EMAIL)}
                className="login-form__forgot-password"
                disableRipple
                disableTouchRipple
              >
                Forgot password?
              </Button>
            </Box>

            <Button
              type="submit"
              fullWidth
              variant="contained"
              className="login-form__submit-button"
              disabled={isLoading}
              disableElevation
            >
              {isLoading ? 'Signing in...' : 'Sign in'}
            </Button>
          </Box>

          {isSsoEnabled && (
            <>
              <div className="login-form__divider">
                <span className="login-form__divider-text">OR</span>
              </div>

              <Box className="login-form__sso-container">
                <Button
                  variant="contained"
                  fullWidth
                  className="login-form__sso-button"
                  onClick={handleSsoLogin}
                  disabled={isLoading}
                  disableElevation
                  startIcon={
                    <img
                      src={microsoftLogo}
                      alt="Microsoft Logo"
                      className="login-form__microsoft-logo"
                    />
                  }
                >
                  {isLoading ? 'Signing in...' : 'Sign in with Microsoft Account'}
                </Button>
              </Box>
            </>
          )}
        </>
      ) : isSsoEnabled && !forceShowForm ? (
        // In production with SSO enabled and form=true not in URL, show only SSO button
        <Box className="login-form__sso-container" style={{ marginTop: '24px' }}>
          <Button
            variant="contained"
            fullWidth
            className="login-form__sso-button"
            onClick={handleSsoLogin}
            disabled={isLoading}
            disableElevation
            startIcon={
              <img
                src={microsoftLogo}
                alt="Microsoft Logo"
                className="login-form__microsoft-logo"
              />
            }
          >
            {isLoading ? 'Signing in...' : 'Sign in with Microsoft Account'}
          </Button>
        </Box>
      ) : (
        // In production with SSO disabled, or production with form=true in URL, show only login form
        <Box
          component="form"
          onSubmit={(e) => {
            e.preventDefault();
            handleSubmit(onSubmit)(e);
          }}
          className="login-form__form"
        >
          <TextField
            margin="normal"
            required
            fullWidth
            id="email"
            label="Email address"
            autoComplete="email"
            autoFocus
            error={!!errors.email}
            helperText={errors.email?.message}
            {...register('email', {
              required: 'Email is required',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Invalid email address',
              },
            })}
            className="login-form__input"
          />

          <TextField
            margin="normal"
            required
            fullWidth
            id="password"
            label="Password"
            type={showPassword ? 'text' : 'password'}
            autoComplete="current-password"
            error={!!errors.password}
            helperText={errors.password?.message}
            {...register('password', {
              required: 'Password is required',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters',
              },
            })}
            className="login-form__input"
            InputProps={{
              endAdornment: (
                <IconButton onClick={() => setShowPassword(!showPassword)} edge="end">
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              ),
            }}
          />

          <Box className="login-form__actions">
            <FormControlLabel
              control={
                <Checkbox
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  color="primary"
                />
              }
              label="Remember me"
              className="login-form__remember-me"
            />
            <Button
              variant="text"
              onClick={() => setCurrentStep(FORGOT_STEPS.EMAIL)}
              className="login-form__forgot-password"
              disableRipple
              disableTouchRipple
            >
              Forgot password?
            </Button>
          </Box>

          <Button
            type="submit"
            fullWidth
            variant="contained"
            className="login-form__submit-button"
            disabled={isLoading}
            disableElevation
          >
            {isLoading ? 'Signing in...' : 'Sign in'}
          </Button>
        </Box>
      )}

      <Snackbar
        open={!!errMsg}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          elevation={6}
          variant="filled"
          severity="error"
          onClose={handleCloseSnackbar}
          className="login-form__error-alert"
        >
          {errMsg}
        </Alert>
      </Snackbar>
    </div>
  );
};

LoginForm.propTypes = {
  register: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  errors: PropTypes.shape({
    email: PropTypes.shape({
      message: PropTypes.string,
    }),
    password: PropTypes.shape({
      message: PropTypes.string,
    }),
  }),
  showPassword: PropTypes.bool.isRequired,
  setShowPassword: PropTypes.func.isRequired,
  rememberMe: PropTypes.bool.isRequired,
  setRememberMe: PropTypes.func.isRequired,
  setCurrentStep: PropTypes.func.isRequired,
  FORGOT_STEPS: PropTypes.shape({
    EMAIL: PropTypes.string.isRequired,
  }).isRequired,
  onSubmit: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
  errMsg: PropTypes.string,
  setErrMsg: PropTypes.func.isRequired,
  handleSsoLogin: PropTypes.func,
};

export default LoginForm;
