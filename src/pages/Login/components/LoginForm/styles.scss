@use '../../../../styles/abstracts/variables' as *;

.login-form {
  &__title {
    font-weight: $font-weight-semibold;
    line-height: 1.235;
    font-size: $font-size-xxl;
    color: $text-primary;
    margin-bottom: $spacing-2;
  }

  &__subtitle {
    margin-bottom: $spacing-5;
    color: $text-secondary;
    font-size: $font-size-md;
    line-height: 1.5;
    font-weight: $font-weight-regular;
  }

  &__form {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  &__input {
    margin-bottom: $spacing-3;

    .MuiOutlinedInput-root {
      border-radius: $border-radius-md;
      background-color: rgba($bg-light, 0.7);
      transition: all 0.2s ease-in-out;

      input {
        font-size: $font-size-md;
        color: $text-primary;
      }

      &:hover {
        background-color: rgba($bg-light, 0.9);
      }

      &.Mui-focused {
        background-color: transparent;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      }
    }

    .MuiFormHelperText-root {
      margin-top: $spacing-1;
      font-size: $font-size-sm;

      &.Mui-error {
        color: $error-color;
      }
    }

    .MuiInputLabel-root {
      color: $text-secondary;
      font-size: $font-size-md;

      &.Mui-focused {
        color: $primary-color;
      }

      &.Mui-error {
        color: $error-color;
      }
    }
  }

  &__actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-4;
  }

  &__remember-me {
    .MuiFormControlLabel-label {
      color: $text-secondary;
      font-size: $font-size-sm;
    }

    .MuiCheckbox-root {
      color: $primary-color;
      padding: $spacing-1;

      &.Mui-checked {
        color: $primary-color;
      }
    }
  }

  &__forgot-password {
    text-transform: none !important;
    font-size: $font-size-sm;
    color: $primary-color;
    padding: $spacing-1 $spacing-2;
    border-radius: $border-radius-sm;
    transition: background-color 0.2s ease-in-out;

    &:hover {
      background-color: rgba($primary-color, 0.1);
      text-transform: none !important;
    }
  }

  &__submit-button {
    margin-top: $spacing-4;
    margin-bottom: $spacing-2;
    padding: $spacing-2 0;
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    text-transform: none !important;
    border-radius: $border-radius-md;
    background-color: $primary-color;
    color: $bg-paper;
    transition: all 0.2s ease-in-out;
    min-height: 48px;

    &:hover {
      background-color: $primary-color-dark;
      text-transform: none !important;
    }

    &:disabled {
      background-color: rgba($primary-color, 0.5);
      color: rgba($bg-paper, 0.7);
      text-transform: none !important;
    }
  }

  &__error-alert {
    width: 100%;

    .MuiAlert-message {
      color: $bg-paper;
    }

    .MuiAlert-icon {
      color: $bg-paper;
    }
  }

  &__logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: $spacing-4;
    min-height: 60px;

    .MuiCircularProgress-root {
      color: $primary-color;
    }
  }

  &__logo {
    max-width: 180px;
    height: auto;
    transition: opacity 0.2s ease-in-out;

    &:hover {
      opacity: 0.9;
    }
  }

  &__divider {
    display: flex;
    align-items: center;
    margin: $spacing-4 0;
    width: 100%;
    position: relative;
    text-align: center;

    &:before, &:after {
      content: '';
      flex: 1;
      border-bottom: 1px solid $divider-color;
    }

    &:before {
      margin-right: $spacing-2;
    }

    &:after {
      margin-left: $spacing-2;
    }
  }

  &__divider-text {
    color: $text-secondary;
    font-size: $font-size-sm;
    padding: 0 $spacing-2;
  }

  &__sso-container {
    margin-bottom: $spacing-4;
    width: 100%;
  }

  &__sso-button {
    padding: $spacing-2 0;
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    text-transform: none !important;
    border-radius: $border-radius-md;
    background-color: #0078d4; // Microsoft blue color
    color: $bg-paper;
    transition: all 0.2s ease-in-out;
    min-height: 48px;

    &:hover {
      background-color: #106ebe; // Darker Microsoft blue for hover
      text-transform: none !important;
    }

    &:disabled {
      background-color: rgba(#0078d4, 0.5);
      color: rgba($bg-paper, 0.7);
      text-transform: none !important;
    }
  }

  &__microsoft-logo {
    width: 20px;
    height: 20px;
    margin-right: $spacing-2;
  }
}