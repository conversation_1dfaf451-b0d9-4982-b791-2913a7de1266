@use '../../../../styles/abstracts/variables' as *;

.forgot-password {
  &__logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: $spacing-4;
    min-height: 60px;

    .MuiCircularProgress-root {
      color: $primary-color;
    }
  }

  &__logo {
    max-width: 180px;
    height: auto;
    transition: opacity 0.2s ease-in-out;

    &:hover {
      opacity: 0.9;
    }
  }

  &__title {
    font-weight: $font-weight-semibold;
    line-height: 1.235;
    font-size: $font-size-xxl;
    color: $text-primary;
    margin-bottom: $spacing-2;
  }

  &__subtitle {
    margin-bottom: $spacing-4;
    color: $text-secondary;
    font-size: $font-size-md;
    line-height: 1.5;
    font-weight: $font-weight-regular;
  }

  &__form {
    display: flex;
    flex-direction: column;
    width: 100%;
  }


  &__input {
    margin-top: 0;   

    .MuiOutlinedInput-root {
      margin-bottom: $spacing-4;
      border-radius: $border-radius-md;
      background-color: rgba($bg-light, 0.7);
      transition: all 0.2s ease-in-out;

      input {
        font-size: $font-size-md;
        color: $text-primary;
      }

      &:hover {
        background-color: rgba($bg-light, 0.9);
      }

      &.Mui-focused {
        background-color: transparent;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      }
    }

    .MuiFormHelperText-root {
      margin-top: $spacing-1;
      font-size: $font-size-sm;

      &.Mui-error {
        color: $error-color;
      }
    }

    .MuiInputLabel-root {
      color: $text-secondary;
      font-size: $font-size-md;

      &.Mui-focused {
        color: $primary-color;
      }

      &.Mui-error {
        color: $error-color;
      }
    }
  }

  &__submit-button {
    margin-top: $spacing-2;
    margin-bottom: $spacing-4;
    padding: $spacing-2 0;
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    text-transform: none !important;
    border-radius: $border-radius-md;
    background-color: $primary-color;
    color: $bg-paper;
    transition: all 0.2s ease-in-out;
    min-height: 48px;

    &:hover {
      background-color: $primary-color-dark;
      text-transform: none !important;
    }

    &:disabled {
      background-color: rgba($primary-color, 0.5);
      color: rgba($bg-paper, 0.7);
      text-transform: none !important;
    }
  }

  &__back-button {
    text-transform: none !important;
    font-size: $font-size-sm;
    color: $primary-color;
    padding: $spacing-1 $spacing-2;
    border-radius: $border-radius-sm;
    transition: background-color 0.2s ease-in-out;
    margin-top: 16px;

    &:hover {
      background-color: rgba($primary-color, 0.1);
      text-transform: none !important;
    }
  }

  &__loading-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 24px;

    .MuiCircularProgress-root {
      color: inherit;
    }
  }

  &__error-alert {
    width: 100%;

    .MuiAlert-message {
      color: $bg-paper;
    }

    .MuiAlert-icon {
      color: $bg-paper;
    }
  }
} 