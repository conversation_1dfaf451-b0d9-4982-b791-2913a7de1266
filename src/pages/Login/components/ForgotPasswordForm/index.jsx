import PropTypes from 'prop-types';
import { <PERSON><PERSON><PERSON>, Button, Box, Typography, Alert, CircularProgress } from '@mui/material';
import Snackbar from '@mui/material/Snackbar';
import { useGetGeneralSettingsQuery } from '../../../../redux/services/platform-settings-api';
import defaultLogo from '../../../../assets/images/logo.png';
import './styles.scss';

const ForgotPasswordForm = ({
  register,
  handleSubmit,
  errors,
  setCurrentStep,
  FORGOT_STEPS,
  onSubmit,
  isLoading,
  errMsg,
  setErrMsg,
}) => {
  const { data: generalSettings } = useGetGeneralSettingsQuery();
  const handleCloseSnackbar = () => {
    setErrMsg(null);
  };

  const logoUrl = generalSettings?.data?.platformLogo || defaultLogo;
  return (
    <div className="forgot-password">
      <Box className="forgot-password__logo-container">
        <img
          src={logoUrl}
          alt={generalSettings?.data?.platformName || 'AI Business School'}
          className="forgot-password__logo"
          onError={(e) => {
            e.target.src = defaultLogo;
          }}
        />
      </Box>

      <Typography variant="h4" className="forgot-password__title">
        Forgot password
      </Typography>

      <Typography variant="body2" className="forgot-password__subtitle">
        Enter your email address below and we&apos;ll send you a reset code.
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} className="forgot-password__form">
        <TextField
          required
          fullWidth
          margin="normal"
          id="email"
          label="Email address"
          autoComplete="email"
          autoFocus
          error={!!errors.email}
          helperText={errors.email?.message}
          {...register('email', {
            required: 'Email is required',
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: 'Invalid email address',
            },
          })}
          className="forgot-password__input"
        />

        <Button
          type="submit"
          fullWidth
          variant="contained"
          className="forgot-password__submit-button"
          disabled={isLoading}
          disableElevation
        >
          {isLoading ? (
            <Box className="forgot-password__loading-wrapper">
              <CircularProgress size={24} color="inherit" />
            </Box>
          ) : (
            'Send reset code'
          )}
        </Button>

        <Button
          fullWidth
          variant="text"
          onClick={() => setCurrentStep(FORGOT_STEPS.LOGIN)}
          className="forgot-password__back-button"
          disableRipple
          disableTouchRipple
        >
          Back to login
        </Button>
      </Box>

      <Snackbar
        open={!!errMsg}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          elevation={6}
          variant="filled"
          severity="error"
          onClose={handleCloseSnackbar}
          className="forgot-password__error-alert"
        >
          {errMsg}
        </Alert>
      </Snackbar>
    </div>
  );
};

ForgotPasswordForm.propTypes = {
  register: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  errors: PropTypes.shape({
    email: PropTypes.shape({
      message: PropTypes.string,
    }),
  }),
  setCurrentStep: PropTypes.func.isRequired,
  FORGOT_STEPS: PropTypes.shape({
    LOGIN: PropTypes.string.isRequired,
  }).isRequired,
  onSubmit: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
  errMsg: PropTypes.string,
  setErrMsg: PropTypes.func.isRequired,
};

export default ForgotPasswordForm;
