import { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import {
  TextField,
  Button,
  Box,
  Typography,
  IconButton,
  Alert,
  CircularProgress,
} from '@mui/material';
import Snackbar from '@mui/material/Snackbar';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import './styles.scss';

const OTPForm = ({
  register,
  handleSubmit,
  errors,
  showPassword,
  setShowPassword,
  showConfirmPassword,
  setShowConfirmPassword,
  setCurrentStep,
  FORGOT_STEPS,
  onSubmit,
  isLoading,
  handleResendCode,
  isResendLoading,
  forgotPasswordEmail,
  errMsg,
  setErrMsg,
  watch,
  trigger,
}) => {
  const { t } = useTranslation();

  const handleCloseSnackbar = () => {
    setErrMsg(null);
  };

  // New password'u izle
  const newPassword = watch('newPassword');
  const previousPasswordRef = useRef('');

  // Şifre zorluk seviyesi için state
  const [passwordStrength, setPasswordStrength] = useState({
    lowercase: false,
    uppercase: false,
    number: false,
    special: false,
    length: false,
  });

  // Şifre güçlülüğünü kontrol et
  const checkPasswordStrength = (password) => {
    setPasswordStrength({
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[!@#$%^&*.]/.test(password),
      length: password.length >= 8,
    });
  };

  // Şifre güçlülük skorunu hesapla
  const calculatePasswordStrengthScore = () => {
    const criteria = Object.values(passwordStrength);
    const metCriteria = criteria.filter(Boolean).length;
    return (metCriteria / criteria.length) * 100;
  };

  // Şifre güçlülük seviyesine göre renk belirle
  const getStrengthColor = (score) => {
    if (score <= 25) return '#ff4d4d';
    if (score <= 50) return '#ffa64d';
    if (score <= 75) return '#ffff4d';
    return '#4CAF50';
  };

  // Şifre güçlülük seviyesine göre metin belirle
  const getStrengthText = (score) => {
    if (score <= 25) return t('otp.passwordStrength.veryWeak');
    if (score <= 50) return t('otp.passwordStrength.weak');
    if (score <= 75) return t('otp.passwordStrength.medium');
    return t('otp.passwordStrength.strong');
  };

  // Koşul durumuna göre icon belirle
  const getCheckmark = (condition) => (condition ? '✓' : '✕');

  // New password değiştiğinde confirm password validasyonunu tetikle
  useEffect(() => {
    // İlk render'da boş kontrolü yapma
    if (newPassword !== undefined && previousPasswordRef.current !== newPassword) {
      previousPasswordRef.current = newPassword;
      // Şifre gücünü kontrol et
      checkPasswordStrength(newPassword || '');
      // Confirm password varsa ve validasyon yapılmışsa tekrar kontrol et
      if (watch('confirmPassword')) {
        trigger('confirmPassword');
      }
    }
  }, [newPassword, trigger, watch]);

  // Component mount olduğunda input değerlerini temizle
  useEffect(() => {
    // Mounted olduğunda tüm OTP input'larını temizle
    const clearInputs = () => {
      // Form alanlarını DOM üzerinden temizle
      for (let i = 1; i <= 6; i++) {
        const input = document.querySelector(`input[name="otp_digit_${i}"]`);
        if (input) {
          input.value = '';
        }
      }
    };

    // Önce timeout ile çalıştıralım (tarayıcının autocomplete'i doldurması sonrası)
    setTimeout(clearInputs, 100);
    // 500ms sonra bir kez daha temizle (bazı tarayıcılar geç doldurabilir)
    setTimeout(clearInputs, 500);

    // Mounted olduğunda hemen çalıştır
    clearInputs();
  }, []);

  return (
    <div className="otp-form">
      <Typography variant="h4" className="otp-form__title">
        {t('otp.title')}
      </Typography>

      <Typography variant="body2" className="otp-form__subtitle">
        {t('otp.subtitle', { email: forgotPasswordEmail })}
      </Typography>

      <Box
        component="form"
        onSubmit={handleSubmit(onSubmit)}
        className="otp-form__form"
        autoComplete="off"
      >
        <Typography variant="subtitle2" className="otp-form__verification-label">
          {t('otp.verification')}
        </Typography>

        <Box className="otp-form__otp-container">
          {[...Array(6)].map((_, index) => (
            <TextField
              key={index}
              required
              type="tel"
              inputProps={{
                maxLength: 1,
                autoComplete: 'new-random-otp-value',
                inputMode: 'numeric',
                pattern: '[0-9]*',
                'data-form-type': 'random-data',
                'data-lpignore': 'true',
              }}
              onPaste={(e) => {
                e.preventDefault();
                const pastedData = e.clipboardData.getData('text').trim();
                if (/^\d{6}$/.test(pastedData)) {
                  [...pastedData].forEach((digit, i) => {
                    const input = document.querySelector(`input[name="otp_digit_${i + 1}"]`);
                    if (input) {
                      input.value = digit;
                      const event = new Event('input', { bubbles: true });
                      input.dispatchEvent(event);
                    }
                  });
                }
              }}
              onInput={(e) => {
                const value = e.target.value;
                if (value && index < 5) {
                  const nextInput = document.querySelector(`input[name="otp_digit_${index + 2}"]`);
                  if (nextInput) {
                    nextInput.focus();
                  }
                }
              }}
              onKeyDown={(e) => {
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                  const prevInput = document.querySelector(`input[name="otp_digit_${index}"]`);
                  if (prevInput) {
                    prevInput.focus();
                  }
                }
              }}
              {...register(`otp_digit_${index + 1}`, {
                pattern: {
                  value: /^[0-9]$/,
                  message: t('otp.validation.mustBeNumber'),
                },
                required: t('otp.validation.required'),
                onChange: (e) => {
                  // Geçerli bir sayı girildiyse tetiklenecek
                  if (/^[0-9]$/.test(e.target.value)) {
                    // Tüm OTP alanlarının değerlerini kontrol et
                    const allValues = Array(6)
                      .fill()
                      .map((_, i) => watch(`otp_digit_${i + 1}`))
                      .filter(Boolean);

                    // 6 adet geçerli değer varsa, OTP validasyonunu tetikle
                    if (allValues.length === 6) {
                      trigger();
                    }
                  }
                },
              })}
              error={!!errors[`otp_digit_${index + 1}`]}
              helperText={errors[`otp_digit_${index + 1}`]?.message}
              className="otp-form__otp-input"
            />
          ))}
        </Box>

        <Box className="otp-form__resend-container">
          <Typography variant="caption" className="otp-form__resend-text">
            {t('otp.didntReceive')}
          </Typography>
          <Typography
            variant="caption"
            onClick={!isResendLoading ? handleResendCode : undefined}
            className="otp-form__resend-button"
            sx={{ cursor: isResendLoading ? 'default' : 'pointer' }}
          >
            {isResendLoading ? t('otp.sending') : t('otp.resendCode')}
          </Typography>
        </Box>

        <Box className="otp-form__password-section">
          <Typography variant="subtitle2" className="otp-form__password-label">
            {t('otp.newPassword')}
          </Typography>

          <TextField
            required
            fullWidth
            type={showPassword ? 'text' : 'password'}
            label={t('otp.newPassword')}
            error={!!errors.newPassword}
            helperText={errors.newPassword?.message}
            {...register('newPassword', {
              required: t('otp.validation.passwordRequired'),
              minLength: {
                value: 8,
                message: t('otp.validation.minLength'),
              },
              validate: {
                hasUpperCase: (value) => /[A-Z]/.test(value) || t('otp.validation.hasUpperCase'),
                hasLowerCase: (value) => /[a-z]/.test(value) || t('otp.validation.hasLowerCase'),
                hasNumber: (value) => /[0-9]/.test(value) || t('otp.validation.hasNumber'),
                hasSpecialChar: (value) =>
                  /[!@#$%^&*.]/.test(value) || t('otp.validation.hasSpecialChar'),
              },
            })}
            InputProps={{
              endAdornment: (
                <IconButton onClick={() => setShowPassword(!showPassword)} edge="end">
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              ),
            }}
            className="otp-form__password-input"
          />

          {newPassword && (
            <>
              <Typography
                variant="body2"
                color="text.secondary"
                className="otp-form__strength-label"
              >
                {t('otp.passwordStrength.title')}:{' '}
                {getStrengthText(calculatePasswordStrengthScore())}
              </Typography>

              <div className="otp-form__strength-bar-container">
                <div
                  className="otp-form__strength-bar"
                  style={{
                    width: `${calculatePasswordStrengthScore()}%`,
                    backgroundColor: getStrengthColor(calculatePasswordStrengthScore()),
                  }}
                />
              </div>

              <div className="otp-form__password-requirements">
                <Typography
                  variant="caption"
                  component="div"
                  color={
                    passwordStrength.lowercase && passwordStrength.uppercase
                      ? 'success.main'
                      : 'text.secondary'
                  }
                >
                  {getCheckmark(passwordStrength.lowercase && passwordStrength.uppercase)}{' '}
                  {t('otp.passwordStrength.requirements.lowercaseUppercase')}
                </Typography>
                <Typography
                  variant="caption"
                  component="div"
                  color={passwordStrength.number ? 'success.main' : 'text.secondary'}
                >
                  {getCheckmark(passwordStrength.number)}{' '}
                  {t('otp.passwordStrength.requirements.number')}
                </Typography>
                <Typography
                  variant="caption"
                  component="div"
                  color={passwordStrength.special ? 'success.main' : 'text.secondary'}
                >
                  {getCheckmark(passwordStrength.special)}{' '}
                  {t('otp.passwordStrength.requirements.specialChar')}
                </Typography>
                <Typography
                  variant="caption"
                  component="div"
                  color={passwordStrength.length ? 'success.main' : 'text.secondary'}
                >
                  {getCheckmark(passwordStrength.length)}{' '}
                  {t('otp.passwordStrength.requirements.minLength')}
                </Typography>
              </div>
            </>
          )}

          <TextField
            required
            fullWidth
            type={showConfirmPassword ? 'text' : 'password'}
            label={t('otp.confirmPassword')}
            error={!!errors.confirmPassword}
            helperText={errors.confirmPassword?.message}
            {...register('confirmPassword', {
              required: t('otp.validation.required'),
              validate: (value) => {
                const newPassword = watch('newPassword');
                return value === newPassword || t('otp.validation.passwordsMatch');
              },
            })}
            InputProps={{
              endAdornment: (
                <IconButton onClick={() => setShowConfirmPassword(!showConfirmPassword)} edge="end">
                  {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              ),
            }}
            className="otp-form__password-input"
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            className="otp-form__submit-button"
            disabled={isLoading}
            disableElevation
          >
            {isLoading ? (
              <Box className="otp-form__loading-wrapper">
                <CircularProgress size={24} color="inherit" />
              </Box>
            ) : (
              t('otp.resetPassword')
            )}
          </Button>

          <Button
            fullWidth
            variant="text"
            onClick={() => setCurrentStep(FORGOT_STEPS.LOGIN)}
            className="otp-form__back-button"
            disableRipple
            disableTouchRipple
          >
            {t('otp.backToLogin')}
          </Button>
        </Box>
      </Box>

      <Snackbar
        open={!!errMsg}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          elevation={6}
          variant="filled"
          severity="error"
          onClose={handleCloseSnackbar}
          className="otp-form__error-alert"
        >
          {errMsg}
        </Alert>
      </Snackbar>
    </div>
  );
};

OTPForm.propTypes = {
  register: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  errors: PropTypes.object,
  showPassword: PropTypes.bool.isRequired,
  setShowPassword: PropTypes.func.isRequired,
  showConfirmPassword: PropTypes.bool.isRequired,
  setShowConfirmPassword: PropTypes.func.isRequired,
  setCurrentStep: PropTypes.func.isRequired,
  FORGOT_STEPS: PropTypes.shape({
    LOGIN: PropTypes.string.isRequired,
  }).isRequired,
  onSubmit: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
  handleResendCode: PropTypes.func.isRequired,
  isResendLoading: PropTypes.bool.isRequired,
  forgotPasswordEmail: PropTypes.string.isRequired,
  errMsg: PropTypes.string,
  setErrMsg: PropTypes.func.isRequired,
  watch: PropTypes.func.isRequired,
  trigger: PropTypes.func.isRequired,
};

export default OTPForm;
