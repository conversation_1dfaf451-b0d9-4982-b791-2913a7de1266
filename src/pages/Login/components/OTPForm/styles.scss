@use '../../../../styles/abstracts/variables' as *;

.otp-form {
  &__title {
    margin-bottom: $spacing-4;
    font-weight: $font-weight-semibold;
    font-size: $font-size-xxl;
    color: $text-primary;
    line-height: 1.235;
  }

  &__subtitle {
    margin-bottom: $spacing-4 !important;
    color: $text-secondary;
    font-size: $font-size-md;
    line-height: 1.5;
  }

  &__form {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  &__verification-label {
    margin-bottom: $spacing-1;
    color: $text-primary;
    font-weight: $font-weight-medium;
    font-size: $font-size-sm;
  }

  &__otp-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: $spacing-1;
    margin-bottom: $spacing-2;
  }

  &__otp-input {
    flex: 1;

    .MuiOutlinedInput-root {
      border-radius: $border-radius-md;
      background-color: rgba($bg-light, 0.7);
      transition: all 0.2s ease-in-out;

      input {
        text-align: center;
        font-size: $font-size-lg;
        padding: $spacing-2 0;
        width: 100%;
        font-weight: $font-weight-semibold;
        color: $text-primary;
      }

      &:hover {
        background-color: rgba($bg-light, 0.9);
      }

      &.Mui-focused {
        background-color: transparent;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      }
    }
  }

  &__resend-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: $spacing-1;
  }

  &__resend-text {
    color: $text-secondary;
    font-size: $font-size-xs;
  }

  &__resend-button {
    font-size: $font-size-xs;
    font-weight: $font-weight-semibold;
    color: $primary-color;
    transition: opacity 0.2s ease-in-out;
    user-select: none;

    &:hover {
      opacity: 0.8;
    }

    &[data-disabled="true"] {
      color: rgba($primary-color, 0.5);
      cursor: default;
    }
  }

  &__password-section {
    margin-top: $spacing-4;
  }

  &__password-label {
    margin-bottom: $spacing-2 !important;
    color: $text-primary;
    font-weight: $font-weight-medium;
    font-size: $font-size-sm;
  }

  &__password-input {
    margin-bottom: $spacing-4 !important;

    &:last-of-type {
      margin-bottom: $spacing-4 !important;
    }

    .MuiOutlinedInput-root {
      border-radius: $border-radius-md;
      background-color: rgba($bg-light, 0.7);
      transition: all 0.2s ease-in-out;

      input {
        font-size: $font-size-md;
        color: $text-primary;
      }

      &:hover {
        background-color: rgba($bg-light, 0.9);
      }

      &.Mui-focused {
        background-color: transparent;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      }
    }

    .MuiFormHelperText-root {
      margin-top: $spacing-1;
      font-size: $font-size-sm;

      &.Mui-error {
        color: $error-color;
      }
    }
  }

  &__submit-button {
    margin-top: $spacing-4;
    margin-bottom: $spacing-2;
    padding: $spacing-2 0;
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    text-transform: none !important;
    border-radius: $border-radius-md;
    background-color: $primary-color;
    color: $bg-paper;
    transition: all 0.2s ease-in-out;
    min-height: 48px;

    &:hover {
      background-color: $primary-color-dark;
      text-transform: none !important;
    }

    &:disabled {
      background-color: rgba($primary-color, 0.5);
      color: rgba($bg-paper, 0.7);
      text-transform: none !important;
    }
  }

  &__loading-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 24px;

    .MuiCircularProgress-root {
      color: inherit;
      width: 24px !important;
      height: 24px !important;
    }
  }

  &__back-button {
    text-transform: none !important;
    font-size: $font-size-sm;
    color: $primary-color;
    padding: $spacing-1 $spacing-2;
    border-radius: $border-radius-sm;
    transition: background-color 0.2s ease-in-out;

    &:hover {
      background-color: rgba($primary-color, 0.1);
    }
  }

  &__error-alert {
    width: 100%;

    .MuiAlert-message {
      color: $bg-paper;
    }

    .MuiAlert-icon {
      color: $bg-paper;
    }
  }

  &__strength-label {
    margin-top: 10px;
    margin-bottom: 5px;
  }

  &__strength-bar-container {
    width: 100%;
    height: 4px;
    background-color: #e0e0e0;
    border-radius: 2px;
    margin-bottom: 15px;
    overflow: hidden;
  }

  &__strength-bar {
    height: 100%;
    transition: width 0.3s ease, background-color 0.3s ease;
  }

  &__password-requirements {
    margin-top: 10px;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 5px;
  }
} 