import { Box, Typography } from '@mui/material';
import './styles.scss';

const features = [
  {
    icon: '🎯',
    title: '1. Train',
    description: 'Access unique job-specific, personalized and interactive AI trainings',
  },
  {
    icon: '📈',
    title: '2. Apply',
    description:
      'Leverage instruments to facilitate a seamless integration of AI into your daily job routines',
  },
  {
    icon: '🚀',
    title: '3. Create',
    description: 'Create your own custom AI apps, workflows and agents',
  },
  {
    icon: '✍🏻',
    title: '4. Innovate',
    description: 'Share your optimization, innovation and your specific use case ideas ',
  },
];

const PromoSection = () => {
  return (
    <Box className="promo-section">
      <Box className="promo-section__container">
        {/* Promo Header */}
        <Box className="promo-section__header">
          <Typography variant="h4" component="h1" className="promo-section__title">
            AI Adoption, made easy
          </Typography>
        </Box>

        {/* Features Grid */}
        <Box className="promo-section__features">
          {features.map((feature, index) => (
            <Box key={index} className="promo-section__feature-card">
              <Box className="promo-section__feature-header">
                <Box className="promo-section__feature-icon">{feature.icon}</Box>
                <Typography variant="h6" className="promo-section__feature-title">
                  {feature.title}
                </Typography>
              </Box>
              <Typography variant="body2" className="promo-section__feature-description">
                {feature.description}
              </Typography>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default PromoSection;
