.promo-section {
  flex: 1;
  position: relative;
  display: none;
  flex-direction: column;
  background: linear-gradient(135deg, #0a2540 0%, #1a365d 100%);
  color: white;
  padding: 32px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  overflow-y: auto;
  max-height: 100vh;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }

  @media (min-width: 900px) {
    display: flex;
    padding: 48px 48px 32px;
  }

  &__container {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    height: 100%;
    gap: 40px;
    justify-content: center;
  }

  &__header {
    text-align: center;
    flex-shrink: 0;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    padding: 0 16px;

    @media (min-width: 900px) {
      padding: 0;
    }
  }

  &__logo {
    width: 50px;
    height: 50px;
    border-radius: 14px;
    background: linear-gradient(135deg, #00a6ff 0%, #0047ff 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    margin: 0 auto 24px;
    box-shadow: 0 4px 15px rgba(0, 71, 255, 0.2);

    @media (min-width: 900px) {
      width: 60px;
      height: 60px;
      border-radius: 16px;
      font-size: 24px;
      margin-bottom: 32px;
    }
  }

  &__title {
    margin-bottom: 12px;
    font-weight: bold;
    font-size: 2rem;

    @media (min-width: 900px) {
      font-size: 2.5rem;
      margin-bottom: 16px;
    }
  }

  &__subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    margin-bottom: 32px;
    line-height: 1.5;
    width: 100%;

    @media (min-width: 900px) {
      font-size: 1.1rem;
      margin-bottom: 48px;
    }
  }

  &__features {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 24px;
    align-content: start;
    min-height: min-content;
    width: 100%;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;

    @media (min-width: 900px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      max-width: 900px;
    }

    @media (min-width: 1200px) {
      gap: 20px;
      max-width: 1000px;
    }

    @media (min-height: 800px) {
      align-content: center;
    }
  }

  &__feature-card {
    padding: 20px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    height: auto;
    display: flex;
    flex-direction: column;

    @media (min-width: 900px) {
      padding: 24px;
    }

    &:hover {
      transform: translateY(-3px);
      background-color: rgba(255, 255, 255, 0.1);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }
  }

  &__feature-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;

    @media (min-width: 900px) {
      margin-bottom: 16px;
      gap: 16px;
    }
  }

  &__feature-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;

    @media (min-width: 900px) {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      font-size: 1.5rem;
    }
  }

  &__feature-title {
    color: white;
    margin: 0;
    font-size: 1rem;
    font-weight: 500;

    @media (min-width: 900px) {
      font-size: 1.25rem;
    }
  }

  &__feature-description {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
    font-size: 0.875rem;
    margin-top: 8px;

    @media (min-width: 900px) {
      font-size: 1rem;
      line-height: 1.6;
    }
  }
} 