import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  useLoginMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
} from '../../redux/services/auth-api';
import { platformSettingsApi } from '../../redux/services/platform-settings-api';
import { useDispatch } from 'react-redux';
import { setCredentials } from '../../redux/features/auth/authSlice';
import { setGeneralSettings } from '../../redux/features/settings/settingsSlice';
import { useForm } from 'react-hook-form';
import { Box } from '@mui/material';
import Loading from '../../components/Loading/Loading';
import Onboarding from '../../domains/onboarding';
import { useTranslation } from 'react-i18next';
import { PublicClientApplication } from '@azure/msal-browser';
import { SSO_MSAL_CONFIG } from '../../config-global';

// Components
import LoginForm from './components/LoginForm';
import ForgotPasswordForm from './components/ForgotPasswordForm';
import OTPForm from './components/OTPForm';
import PromoSection from './components/PromoSection';
import { developmentLogs } from '../../utils/developmentLogs';
// Styles
import './styles.scss';

const FORGOT_STEPS = {
  LOGIN: 'login',
  EMAIL: 'email',
  OTP: 'otp',
  NEW_PASSWORD: 'new_password',
};

const LoginPage = () => {
  const errRef = useRef();
  const [errMsg, setErrMsg] = useState(null);
  const [currentStep, setCurrentStep] = useState(FORGOT_STEPS.LOGIN);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [userData, setUserData] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const otpRefs = useRef([...Array(6)].map(() => React.createRef()));
  const [forgotPasswordEmail, setForgotPasswordEmail] = useState('');
  const [resendDisabled, setResendDisabled] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [rememberMe, setRememberMe] = useState(false);
  const [isSsoLoading, setIsSsoLoading] = useState(false);
  const { i18n } = useTranslation();

  // MSAL yapılandırması
  const msalConfig = useMemo(
    () => ({
      auth: {
        clientId: SSO_MSAL_CONFIG.clientId,
        authority: `https://login.microsoftonline.com/${SSO_MSAL_CONFIG.tenantId}`,
        redirectUri: SSO_MSAL_CONFIG.redirectUri,
      },
      cache: {
        cacheLocation: 'sessionStorage',
        storeAuthStateInCookie: false,
      },
    }),
    []
  );

  // MSAL istemcisi için state
  const [msalInstance, setMsalInstance] = useState(null);
  const [msalInitialized, setMsalInitialized] = useState(false);

  // MSAL istemcisini başlat
  useEffect(() => {
    const initializeMsal = async () => {
      try {
        const instance = new PublicClientApplication(msalConfig);
        await instance.initialize();
        setMsalInstance(instance);
        setMsalInitialized(true);
        console.log('MSAL initialized successfully');
      } catch (error) {
        console.error('MSAL initialization error:', error);
        setErrMsg('Microsoft authentication service initialization failed');
      }
    };

    initializeMsal();
  }, [msalConfig, setErrMsg]);

  // SSO için istek yapılandırması
  const loginRequest = {
    scopes: SSO_MSAL_CONFIG.scopes,
  };

  // SSO ile giriş işlevi
  const handleSsoLogin = async () => {
    try {
      setIsSsoLoading(true);

      // MSAL başlatılmadıysa hata göster
      if (!msalInstance || !msalInitialized) {
        console.error('MSAL not initialized yet');
        setErrMsg('Microsoft authentication service is not ready yet. Please try again.');
        setIsSsoLoading(false);
        return;
      }

      const loginResponse = await msalInstance.loginPopup(loginRequest);
      console.log('SSO Login Response:', loginResponse);

      if (loginResponse && loginResponse.account) {
        // SSO ile giriş başarılı, backend'e token gönder ve kullanıcı bilgilerini al
        // Bu kısım backend entegrasyonuna göre değişebilir
        // Şimdilik normal login işlemini kullanıyoruz
        const email = loginResponse.account.username;

        // Microsoft hesabından isim ve soyisim bilgilerini çıkar
        let name = '';
        let surname = '';

        if (loginResponse.account.name) {
          // Tam ismi parçalara ayır
          const nameParts = loginResponse.account.name
            .split(' ')
            .filter((part) => part.trim() !== '');

          if (nameParts.length >= 2) {
            // İlk parçayı ad, geri kalanını soyad olarak kabul et
            name = nameParts[0];
            surname = nameParts.slice(1).join(' ');
          } else {
            // Tek parça varsa, tamamını ad olarak kabul et
            name = loginResponse.account.name;
            // Surname boş kalacak
          }

          // Konsola bilgileri yazdır
          console.log('Parsed name info:', {
            fullName: loginResponse.account.name,
            firstName: name,
            surname: surname,
          });
        }

        // Backend'e SSO ile giriş yapıldığını bildir
        // Sadece email ve ssoLogin bilgisini gönder
        // Ad ve soyad bilgilerini sadece yeni kullanıcı oluşturulurken kullanılması için
        // opsiyonel olarak gönder
        const loginData = {
          email,
          ssoLogin: true,
        };

        // Ad ve soyad bilgilerini sadece yeni kullanıcı oluşturulurken kullanılması için ekle
        // Bu bilgiler backend tarafında sadece yeni kullanıcı oluşturulurken kullanılacak
        if (name) loginData.name = name;
        if (surname) loginData.surname = surname;

        const response = await login(loginData).unwrap();

        if (!response || !response.data) {
          setErrMsg(
            "We couldn't log you in. Please make sure your email and password are correct."
          );
          setIsSsoLoading(false);
          return;
        }

        setUserData(response.data);

        dispatch(
          setCredentials({
            user: response.data,
            token: response.data.token,
          })
        );

        // Kullanıcı oturum açtıktan sonra platform ayarlarını çek
        await fetchGeneralSettings();

        if (response.data.onboarding?.language) {
          localStorage.removeItem('i18nextLng');
          i18n.changeLanguage(response.data.onboarding.language);
          localStorage.setItem('i18nextLng', response.data.onboarding.language);
        }

        if (!response.data.onboarding) {
          setShowOnboarding(true);
        } else {
          const redirectTo = location.state?.from?.pathname || '/';
          navigate(redirectTo, { replace: true });
        }
      }
    } catch (error) {
      console.error('SSO Login Error:', error);
      setErrMsg(error.message || 'SSO login failed');
    } finally {
      setIsSsoLoading(false);
    }
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    formState: { isSubmitting },
    trigger,
    reset,
  } = useForm({
    mode: 'onSubmit',
  });

  const navigate = useNavigate();
  const location = useLocation();

  const [login, { isLoading }] = useLoginMutation();
  const [forgotPassword, { isLoading: isForgotLoading }] = useForgotPasswordMutation();
  const [resetPassword, { isLoading: isResetLoading }] = useResetPasswordMutation();
  const dispatch = useDispatch();

  // Kullanıcı giriş yaptıktan sonra platform ayarlarını çekmek için
  const fetchGeneralSettings = async () => {
    try {
      const response = await dispatch(
        platformSettingsApi.endpoints.getGeneralSettings.initiate()
      ).unwrap();

      // API yanıtındaki data'yı al
      if (response?.status === 'success' && response?.data) {
        // API'den doğrudan gelen data nesnesini genel ayarlar olarak kaydet
        dispatch(setGeneralSettings(response.data));
      } else {
        // Eğer response beklediğimiz gibi değilse, tüm yanıtı kaydet
        dispatch(setGeneralSettings(response));
      }
    } catch (error) {
      developmentLogs('Platform ayarları yüklenirken hata oluştu:', error);
    }
  };

  const onSubmit = async (data) => {
    if (isSubmitting) return;
    try {
      if (currentStep === FORGOT_STEPS.LOGIN) {
        const response = await login(data).unwrap();
        if (!response || !response.data) {
          setErrMsg(
            "We couldn't log you in. Please make sure your email and password are correct."
          );
          return;
        }
        setUserData(response.data);

        dispatch(
          setCredentials({
            user: response.data,
            token: response.data.token,
          })
        );

        // Kullanıcı oturum açtıktan sonra platform ayarlarını çek
        await fetchGeneralSettings();

        if (response.data.onboarding?.language) {
          localStorage.removeItem('i18nextLng');
          i18n.changeLanguage(response.data.onboarding.language);
          localStorage.setItem('i18nextLng', response.data.onboarding.language);
        }

        if (!response.data.onboarding) {
          setShowOnboarding(true);
        } else {
          const redirectTo = location.state?.from?.pathname || '/';
          navigate(redirectTo, { replace: true });
        }
      } else if (currentStep === FORGOT_STEPS.EMAIL) {
        const response = await forgotPassword(data.email).unwrap();

        if (response.status === 'Error' && response.data === 'Email not found') {
          setErrMsg(
            "We couldn't log you in. Please make sure your email and password are correct."
          );
          return;
        }

        if (response.status === 'Error' && response.data?.includes('duplicate key error')) {
          setForgotPasswordEmail(data.email);
          setCurrentStep(FORGOT_STEPS.OTP);
          reset({});
          return;
        }

        if (response.status === 'success') {
          setForgotPasswordEmail(data.email);
          setCurrentStep(FORGOT_STEPS.OTP);
          reset({});
        }
      } else if (currentStep === FORGOT_STEPS.OTP) {
        // OTP değerlerini al ve kontrol et
        const otpValues = Array(6)
          .fill()
          .map((_, i) => data[`otp_digit_${i + 1}`] || '');

        // OTP değerlerinden bir string oluştur
        const otp = otpValues.join('');

        // 6 haneli rakam kontrolü yap
        if (otp.length !== 6 || !/^\d{6}$/.test(otp)) {
          setErrMsg('Please enter a valid 6-digit verification code');
          return;
        }

        const resetData = {
          email: forgotPasswordEmail,
          otp,
          password: data.newPassword,
        };

        const response = await resetPassword(resetData).unwrap();
        if (response.status === 'Success') {
          const loginResponse = await login({
            email: forgotPasswordEmail,
            password: data.newPassword,
          }).unwrap();
          dispatch(
            setCredentials({
              user: loginResponse.data,
              token: loginResponse.data.token,
            })
          );

          // Şifre sıfırlama sonrası oturum açıldığında platform ayarlarını çek
          await fetchGeneralSettings();

          const redirectTo = location.state?.from?.pathname || '/';
          navigate(redirectTo, { replace: true });
        }
      }
    } catch (err) {
      console.error('Error:', err);
      if (err.data?.message === 'OTP not found or expired') {
        setErrMsg('Verification code is invalid or has expired. Please try again.');
      } else if (!err.status) {
        setErrMsg("We couldn't log you in. Please make sure your email and password are correct.");
      } else if (err.status === 400) {
        setErrMsg('Please fill in all required fields');
      } else {
        setErrMsg("We couldn't log you in. Please make sure your email and password are correct.");
      }
      errRef.current?.focus();
    }
  };

  const handleOtpPaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').trim();
    if (/^\d{6}$/.test(pastedData)) {
      [...pastedData].forEach((digit, index) => {
        if (otpRefs.current[index]?.current) {
          otpRefs.current[index].current.value = digit;
          const event = new Event('input', { bubbles: true });
          otpRefs.current[index].current.dispatchEvent(event);
        }
      });
      if (otpRefs.current[5]?.current) {
        otpRefs.current[5].current.focus();
      }
    }
  };

  const handleOtpInput = (e, index) => {
    const value = e.target.value;
    if (value && index < 5 && otpRefs.current[index + 1]?.current) {
      otpRefs.current[index + 1].current.focus();
    }
  };

  const handleOtpKeyDown = (e, index) => {
    if (
      e.key === 'Backspace' &&
      !e.target.value &&
      index > 0 &&
      otpRefs.current[index - 1]?.current
    ) {
      otpRefs.current[index - 1].current.focus();
    }
  };

  const handleResendCode = async () => {
    try {
      await forgotPassword(forgotPasswordEmail).unwrap();
      setResendDisabled(true);
      setResendTimer(30);
      const timer = setInterval(() => {
        setResendTimer((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setResendDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (err) {
      setErrMsg(err.data?.message || 'Failed to resend code');
    }
  };

  const renderForm = () => {
    switch (currentStep) {
      case FORGOT_STEPS.EMAIL:
        return (
          <ForgotPasswordForm
            register={register}
            handleSubmit={handleSubmit}
            errors={errors}
            onSubmit={onSubmit}
            isLoading={isForgotLoading}
            setCurrentStep={setCurrentStep}
            FORGOT_STEPS={FORGOT_STEPS}
            errMsg={errMsg}
            setErrMsg={setErrMsg}
          />
        );

      case FORGOT_STEPS.OTP:
        return (
          <OTPForm
            register={register}
            handleSubmit={handleSubmit}
            errors={errors}
            onSubmit={onSubmit}
            forgotPasswordEmail={forgotPasswordEmail}
            showPassword={showPassword}
            setShowPassword={setShowPassword}
            showConfirmPassword={showConfirmPassword}
            setShowConfirmPassword={setShowConfirmPassword}
            otpRefs={otpRefs}
            handleOtpPaste={handleOtpPaste}
            handleOtpInput={handleOtpInput}
            handleOtpKeyDown={handleOtpKeyDown}
            resendDisabled={resendDisabled}
            resendTimer={resendTimer}
            handleResendCode={handleResendCode}
            watch={watch}
            trigger={trigger}
            setCurrentStep={setCurrentStep}
            FORGOT_STEPS={FORGOT_STEPS}
            errMsg={errMsg}
            setErrMsg={setErrMsg}
            isLoading={isResetLoading}
            isResendLoading={isForgotLoading}
          />
        );

      default:
        return (
          <LoginForm
            register={register}
            handleSubmit={handleSubmit}
            errors={errors}
            showPassword={showPassword}
            setShowPassword={setShowPassword}
            rememberMe={rememberMe}
            setRememberMe={setRememberMe}
            setCurrentStep={setCurrentStep}
            FORGOT_STEPS={FORGOT_STEPS}
            onSubmit={onSubmit}
            isLoading={isLoading || isSsoLoading}
            errMsg={errMsg}
            setErrMsg={setErrMsg}
            handleSsoLogin={handleSsoLogin}
          />
        );
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <Box className="login-page">
      {/* Sol taraf - Promo Alanı */}
      <PromoSection />

      {/* Sağ taraf - Form Alanı */}
      <Box className="login-page__form-area">
        <Box className="login-page__form-area-container">{renderForm()}</Box>
      </Box>

      <Onboarding open={showOnboarding} userData={userData} />
    </Box>
  );
};

export default LoginPage;
