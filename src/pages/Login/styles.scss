.login-page {
  display: flex;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  &__form-area {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    backdrop-filter: blur(10px);

    &-container {
      width: 100%;
      max-width: 400px;
      padding: 16px;
      max-height: 90vh;
      overflow-y: auto;

      @media (min-width: 600px) {
        padding: 24px;
      }

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(0,0,0,0.1);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0,0,0,0.2);
        border-radius: 3px;
      }
    }
  }
}

// Common Input Styles
.custom-input {
  .MuiOutlinedInput-root {
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.02);
    transition: all 0.2s ease-in-out;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }

    &.Mui-focused {
      background-color: transparent;
      box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
    }
  }
}

// Common Button Styles
.custom-button {
  padding: 10px 0;
  font-size: 1rem;
  font-weight: 600;
  text-transform: none;
  border-radius: 8px;

  &--contained {
    @extend .custom-button;
    margin: 12px 0 16px;
  }

  &--text {
    @extend .custom-button;
    min-width: auto;
    padding: 0;
  }
}

// Typography Styles
.heading-primary {
  margin-bottom: 12px;
  font-weight: 600;
}

.text-secondary {
  margin-bottom: 24px;
  color: var(--text-secondary);
}

// Form Layout
.form-group {
  margin-bottom: 12px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

// Remember Me Checkbox
.remember-me {
  .MuiFormControlLabel-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
  }
} 