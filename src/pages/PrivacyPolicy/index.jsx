import { Box, Container, Grid, Typography, CircularProgress } from '@mui/material';
import { useSelector } from 'react-redux';
import { Suspense } from 'react';
import { useTranslation } from 'react-i18next';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader.jsx';
import CourseCard, { CardContent } from '@/components/CourseCard/CourseCard';
import WhiteContainer from '../../components/WhiteContainer/WhiteContainer.jsx';
import { creatorTools } from '../../mockData/creatorTools.js';
import { AdvancedCreatorTools } from '../../mockData/AdvancedCreatorTools.js';
import './PrivacyPolicy.scss';

const PrivacyPolicyContent = () => {
  const user = useSelector((state) => state.auth.user);
  const { t } = useTranslation();

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">{t('common.pleaseLogin')}</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('privacyPolicy.title')}
            description={t('privacyPolicy.subtitle')}
            showProgress={false}
          />
        </Grid>
        <Grid item xs={12}>
          <WhiteContainer>
            <Box className="privacy-policy__content">
              <Typography variant="h6" gutterBottom>
                General
              </Typography>
              <Typography variant="body1" paragraph>
                AI Business School (hereinafter referred to as "AIBS"), collects and processes
                personal data carefully and only to the extent permitted by law. In the following
                AIBS explains which policies AIBS complies with and for what purpose AIBS collects
                and processes personal data via this website and other websites and applications
                operated by us or affiliated companies.When you register and use AIBS services, AIBS
                collects certain personal data so that AIBS can provide its services targeted to
                you. Within the scope of communication and data collection, the following data is
                stored by AIBS or by third party companies that provide the corresponding services:
              </Typography>

              <Box component="ul">
                <Typography component="li">Registration data (e.g. name, surname)</Typography>
                <Typography component="li">
                  User-entered data (e.g. ideas, comments, form entries, API Key and endpoint for
                  Azure OpenAI)
                </Typography>
                <Typography component="li">Analytics and traffic logs</Typography>
                <Typography component="li">
                  User-triggered actions logs (e.g. login time, logout time, visited pages)
                </Typography>
              </Box>

              <Typography variant="body1" paragraph>
                If you are registered to one or more of AIBS Business Services initiated, sponsored
                or paid by your employer or school or another third party, AIBS may share
                information about your interests, progress and results with them. AIBS may combine
                such information with information that AIBS receives from other sources, online and
                offline, and use such combined information in accordance with this Privacy
                Policy.The purpose of this data capture is:
              </Typography>

              <Box component="ul">
                <Typography component="li">to identify and authenticate users,</Typography>
                <Typography component="li">
                  to plan and execute communication and mobilization measures,
                </Typography>
                <Typography component="li">
                  to provide the business services and functions,
                </Typography>
                <Typography component="li">
                  to facilitate ideation, innovation, usage of the preffered API Key usage
                </Typography>
                <Typography component="li">
                  to give the user the opportunity to comment on content
                </Typography>
                <Typography component="li">
                  to gather feedback to enable us to continually improve and extend our platform,
                  services and your user experience
                </Typography>
                <Typography component="li">
                  to be able to provide and improve our services in a contractual and targeted
                  manner,
                </Typography>
                <Typography component="li">
                  to provide your company with insights, reporting and analytics to continuous track
                  progress and outcomes,
                </Typography>
                <Typography component="li">
                  to use data in anonymized form for research purposes, for example, to develop new
                  products and services,
                </Typography>
                <Typography component="li">to minimize the risk of fraud,</Typography>
                <Typography component="li">
                  to comply with applicable legislation and case law and to be able to respond to
                  requests from administrative or other state authorities,
                </Typography>
                <Typography component="li">
                  to protect AIBS, your company and third parties.
                </Typography>
              </Box>

              <Typography variant="body1" paragraph>
                In the context of the European General Data Protection Regulation (GDPR) regulations
                apply which may also be applicable to companies and service providers domiciled in
                Switzerland. As far as these provisions are applicable, AIBS complies with these
                provisions. The most important of these rights are listed below. Further details can
                be found in the legal basis, which can be accessed via the following{' '}
                <Typography component="span" sx={{ color: 'primary.main', cursor: 'pointer' }}>
                  link
                </Typography>
                .
              </Typography>

              <Typography variant="body1">
                The central aspects of these legal guidelines are listed below:
              </Typography>

              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Right to information
                </Typography>
                <Typography variant="body1" paragraph>
                  Where personal data are collected, the data subjects are to be provided with a
                  range of information regarding the collection of data, in particular what data are
                  collected and for what purpose.
                </Typography>

                <Typography variant="h6" gutterBottom>
                  Right of access by the data subject
                </Typography>
                <Typography variant="body1" paragraph>
                  According to the GDPR, the person affected by the data processing is entitled to
                  demand a confirmation that personal data are processed by him or that this is not
                  the case. Where data is processed, the GDPR establishes various rights (e.g. the
                  right to obtain a copy of the data).
                </Typography>

                <Typography variant="h6" gutterBottom>
                  Right to rectification
                </Typography>
                <Typography variant="body1" paragraph>
                  The data subject shall have the right to request the controller of the data to
                  rectify any inaccurate personal data concerning him/her without delay. Taking into
                  account the purposes of the processing, the data subject has the right to request
                  the completion of incomplete personal data, including by means of a supplementary
                  declaration.
                </Typography>

                <Typography variant="h6" gutterBottom>
                  Right to erasure ("right to be forgotten")
                </Typography>
                <Typography variant="body1" paragraph>
                  The data subject has the right to request that personal data relating to him/her
                  be deleted immediately and the personal data must be deleted immediately if one of
                  the reasons listed in the GDPR applies, for example that the data are no longer
                  necessary for the purpose for which they were collected.
                </Typography>

                <Typography variant="h6" gutterBottom>
                  Right to restriction of processing
                </Typography>
                <Typography variant="body1" paragraph>
                  In certain cases, the data subject has the right to require the GDPR to restrict
                  the processing of data. If such a restriction is requested, the data may only be
                  retained, but may no longer be processed.
                </Typography>

                <Typography variant="h6" gutterBottom>
                  Right of notification
                </Typography>
                <Typography variant="body1" paragraph>
                  According to the GDPR, all recipients to whom personal data have been disclosed
                  must be informed of any correction or deletion of personal data or any restriction
                  on processing, unless this proves impossible or involves disproportionate effort.
                </Typography>

                <Typography variant="h6" gutterBottom>
                  Right to data transferability
                </Typography>
                <Typography variant="body1" paragraph>
                  The data subject has the right to receive the data that he has provided in a
                  structured, common and machine-readable format and has the right to transfer this
                  data to another data controller, for example to change the service provider.
                  However, this right can only be exercised if the data processing is based on the
                  consent of the data subject or on a contract.
                </Typography>

                <Typography variant="h6" gutterBottom>
                  Right to object
                </Typography>
                <Typography variant="body1" paragraph>
                  The data subject shall also have the right to object at any time, for reasons
                  arising from his or her particular situation, to certain processing of personal
                  data relating to him or her, including profiling based on these provisions.
                  Subsequently, the data may no longer be processed, unless the data processor can
                  prove compelling reasons for the processing which are worthy of protection and
                  which outweigh the interests, rights and freedoms of the data subject, or the
                  processing serves to assert, exercise or defend legal claims. Where personal data
                  are processed for direct marketing purposes, the data subject shall have the right
                  to object at any time to the processing of personal data concerning him/her for
                  the purposes of such advertising, including profiling in so far as it is related
                  to such direct marketing.
                </Typography>

                <Typography variant="h6" gutterBottom>
                  Right to waive an automated decision in individual cases
                </Typography>
                <Typography variant="body1" paragraph>
                  In addition, the data subject has the right not to be subject to a decision based
                  exclusively on automated processing, including profiling, which has legal effect
                  against him or her or significantly affects him or her in a similar manner.
                </Typography>

                <Typography variant="h6" gutterBottom>
                  Right to be informed of data protection violations
                </Typography>
                <Typography variant="body1" paragraph>
                  Should the provisions on the protection of personal data be violated, the person
                  affected by the violation will be informed, provided that this entails a high risk
                  for personal rights and freedoms.
                </Typography>

                <Typography variant="h6" gutterBottom>
                  Special protection for children
                </Typography>
                <Typography variant="body1" paragraph>
                  Furthermore, the GDPR also provides special protection for children. For example,
                  in the case of services offered directly to a child, consent to the processing of
                  the child's data must be given or authorised by the holder of parental
                  responsibility, whereby the age limit in question may be defined differently in
                  the various countries within the scope of application of the GDPR.
                </Typography>

                <Typography variant="h6" gutterBottom>
                  Technical measures for data security
                </Typography>
                <Typography variant="body1" paragraph>
                  AIBS protects personal data through appropriate technical and organisational
                  security measures and stores them on secure servers. The website is protected
                  against manipulation by the usual state-of-the-art measures and against access,
                  modification or distribution by unauthorised persons. This includes taking data
                  protection aspects into account as early as the planning phase of our services
                  ("privacy by design"), and our new products or services are offered with data
                  protection-friendly default settings ("privacy by default").
                </Typography>

                <Typography variant="h6" gutterBottom>
                  Transfer of data to third parties
                </Typography>
                <Typography variant="body1" paragraph>
                  AIBS is entitled to pass on your personal data to service providers for the
                  purposes of the contract, including abroad. These are, for example, cloud service
                  providers, other companies in the group, other providers of services relevant to
                  the provision of corporate services, including, for example, IT service providers,
                  management consultants and lawyers, and public authorities. These third parties
                  are also obliged to comply with the legal provisions on data protection. A
                  complete list of the data processors involved can be requested from us.
                </Typography>

                <Box className="address-block">
                  <Typography variant="body1">AI Business School</Typography>
                  <Typography variant="body1">Kantonsstrasse 56</Typography>
                  <Typography variant="body1">8807 Freienbach</Typography>
                  <Typography variant="body1">Switzerland</Typography>
                </Box>
              </Box>
            </Box>
          </WhiteContainer>
        </Grid>
      </Grid>
    </Container>
  );
};

const CreatePage = () => {
  return (
    <Suspense
      fallback={
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: 'calc(100vh - 64px)', // Header yüksekliğini çıkarıyoruz
          }}
        >
          <CircularProgress />
        </Box>
      }
    >
      <CreateContent />
    </Suspense>
  );
};

export default PrivacyPolicyContent;
