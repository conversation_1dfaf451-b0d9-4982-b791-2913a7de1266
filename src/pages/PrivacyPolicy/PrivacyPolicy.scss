@use '../../styles/abstracts/variables' as *;

.privacy-policy {
  &__content {
    padding: 0 !important;

    h1 {
      color: $primary-text-color-dark;
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      margin-bottom: $spacing-3;
    }

    h2 {
      color: $primary-text-color-dark;
      font-size: calc($font-size-xxl - 2px);
      font-weight: $font-weight-bold;
      margin-bottom: $spacing-3;
    }

    h3 {
      color: $primary-text-color-dark;
      font-size: $font-size-xl;
      font-weight: $font-weight-semibold;
      margin-bottom: $spacing-2;
    }

    h4 {
      color: $primary-text-color-dark;
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      margin-bottom: $spacing-2;
    }

    h5 {
      color: $primary-text-color-dark;
      font-size: calc($font-size-lg - 1px);
      font-weight: $font-weight-medium;
      margin-bottom: $spacing-2;
    }

    h6 {
      color: $primary-text-color-dark;
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      margin-bottom: $spacing-2;
    }

    p {
      color: $primary-text-color-dark !important;
      font-size: calc($font-size-md - 1px);
      line-height: 1.6;
      margin-bottom: $spacing-3;
    }

    ul {
      list-style-type: disc;
      padding-left: $spacing-4;
      margin-bottom: $spacing-4;

      li {
        color: $primary-text-color-dark;
        font-size: calc($font-size-md - 1px);
        line-height: 1.6;
        margin-bottom: $spacing-2;
        padding-left: $spacing-2;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .address-block {
      margin-top: $spacing-5;
      padding: $spacing-4 0 0 0;
      border-top: 1px solid $divider-color;

      p {
        margin-bottom: $spacing-1;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .last-updated {
      color: rgba($text-secondary, 0.3);
      font-size: $font-size-sm;
      margin-top: $spacing-3;
      margin-bottom:0;
    }
  }
} 