import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import ScrollToTop from './components/ScrollToTop';
import TitleUpdater from './components/TitleUpdater';
import {
  HomePage,
  TrainingPage,
  CoursePage,
  AccountSettingsPage,
  LoginPage,
  ToolkitPage,
  ApplyPage,
  CreatePage,
  InnovatePage,
  CockpitPage,
  WorkflowCreatorPage,
  MicrosoftCopilotPage,
  AiUseCasesPage,
  ManagementUseCasesPage,
  ITUseCasesPage,
  PlaygroundsPage,
  ChatGPTPlayground,
  DALLEPlayground,
  HeygenPlayground,
  ChatGPTDeveloperToolsPlayground,
  OpenAIPage,
  MicrosoftPage,
  GithubCopilot,
  AIPlanetPage,
  AIBusinessSchoolPage,
  AdvancedGenAICreator,
  SimpleAIAppCreatorPlayground,
  AgenticWorkflowCreatorPlayground,
  ChatGPTCheatSheetPage,
  DALLECheatSheetPage,
  HeygenVideoCreatorPage,
  WorkflowRun,
  ShareYourIdeaPage,
  ConditionPage,
  GenAIPage,
  PrivacyPolicyPage,
  TermsAndConditionsPage,
  SingleAIAppCreator,
  SimpleAIAppCreatorTutorialPage,
  AgenticWorkflowCreatorTutorialPage,
  HeygenTutorialPage,
  MicrosoftToolkits,
  Top10CopilotFeatures,
  PromptingCheatSheet,
  Microsoft365ToolGuidelinesForCopilot,
  ITCheatSheet,
  AdvancedGenAICreatorTutorialPage,
  SpesificSalesUseCasesPage,
  NotFoundPage,
} from './pages';
import PromptLibraryPage from './pages/PromptLibrary';
import Layout from './components/Layout/Layout';
import AuthProvider from './providers/AuthProvider';
import CreateSimpleAIApp from './domains/apps/simple-ai-apps/create';
import EditSimpleAIApp from './domains/apps/simple-ai-apps/edit';
import CourseLearnPage from './pages/Course/Learn';
import CourseLandingPage from './pages/Course';
import SingleApp from './pages/SingleApp';
import PanelRedirect from './components/PanelRedirect/PanelRedirect';

const AppRoutes = () => {
  // URL'deki çift slashları düzeltmek için
  const normalizeURL = (path) => {
    if (path.includes('//')) {
      return path.replace(/\/+/g, '/');
    }
    return null;
  };

  // Mevcut URL'i kontrol et
  const currentPath = window.location.pathname;
  const normalizedPath = normalizeURL(currentPath);

  // Eğer çift slash varsa, düzeltilmiş URL'e yönlendir
  if (normalizedPath) {
    window.history.replaceState({}, '', normalizedPath);
    // Tarayıcı adres çubuğunda görünen URL'i güncelledik,
    // ama React Router'ın bu değişikliği görmesi için sayfayı yenilememiz gerekiyor
    if (normalizedPath !== currentPath) {
      window.location.href = normalizedPath;
      return null; // Yenileme gerçekleşene kadar render etme
    }
  }

  return (
    <Router>
      <ScrollToTop />
      <TitleUpdater />
      <Routes>
        <Route element={<AuthProvider />}>
          <Route element={<Layout />}>
            {/* Protected Routes */}
            <Route path="/" element={<HomePage />} />
            <Route path="/training" element={<TrainingPage />} />
            <Route path="/apply" element={<ApplyPage />} />
            <Route path="/create" element={<CreatePage />} />
            <Route path="/innovate" element={<InnovatePage />} />
            <Route path="/cockpit" element={<CockpitPage />} />
            <Route path="/courses" element={<CoursePage />} />
            <Route path="/panel" element={<PanelRedirect />} />
            <Route
              path="/course/learn/:courseId/:chapterId/:topicId"
              element={<CourseLearnPage />}
            />
            <Route path="/course/:courseId" element={<CourseLandingPage />} />
            <Route path="/account-settings" element={<AccountSettingsPage />} />
            <Route path="/workflow-creator" element={<WorkflowCreatorPage />} />
            <Route path="/microsoft-copilot" element={<MicrosoftCopilotPage />} />
            <Route path="/ai-usecases" element={<AiUseCasesPage />} />
            <Route path="/management-usecases" element={<ManagementUseCasesPage />} />
            <Route path="/it-usecases" element={<ITUseCasesPage />} />
            <Route path="/usecase/:slug" element={<SingleApp />} />
            <Route path="/ai_apps/:slug" element={<SingleAIAppCreator />} />
            <Route path="/prompt-library" element={<PromptLibraryPage />} />
            <Route path="/prompt-library/:provider" element={<PromptLibraryPage />} />
            <Route path="/prompt-library/:provider/:app" element={<PromptLibraryPage />} />
            <Route
              path="/prompt-library/:provider/:app/:function"
              element={<PromptLibraryPage />}
            />
            <Route path="/toolkits" element={<ToolkitPage />}>
              <Route path="microsoft" element={<MicrosoftToolkits />} />
              <Route
                path="microsoft/top-10-copilot-to-try-first-with-copilot"
                element={<Top10CopilotFeatures />}
              />
              <Route path="microsoft/prompting-cheat-sheet" element={<PromptingCheatSheet />} />
              <Route
                path="microsoft/microsoft-365-tool-guidelines-for-copilot"
                element={<Microsoft365ToolGuidelinesForCopilot />}
              />
            </Route>
            <Route path="/playgrounds" element={<PlaygroundsPage />}>
              <Route path="chatgpt" element={<ChatGPTPlayground />} />
              <Route path="dall-e" element={<DALLEPlayground />} />
              <Route path="heygen" element={<HeygenPlayground />} />
              <Route path="chatgpt-developer-tools" element={<ChatGPTDeveloperToolsPlayground />} />
              <Route path="advanced-genai-creator" element={<AdvancedGenAICreator />} />

              <Route path="simple-ai-app-creator" element={<SimpleAIAppCreatorPlayground />} />
              <Route
                path="agentic-workflow-creator"
                element={<AgenticWorkflowCreatorPlayground />}
              />
            </Route>
            <Route path="tools">
              <Route path="openai" element={<OpenAIPage />} />
              <Route path="microsoft" element={<MicrosoftPage />} />
              <Route path="github-copilot" element={<GithubCopilot />} />
              <Route path="ai-planet" element={<AIPlanetPage />} />
              <Route path="ai-business-school" element={<AIBusinessSchoolPage />} />
            </Route>
            <Route path="simple-ai-app">
              <Route path="create" element={<CreateSimpleAIApp />} />
              <Route path="edit/:slug" element={<EditSimpleAIApp />} />
            </Route>
            <Route path="tutorials">
              <Route path="simple-ai-app-creator" element={<SimpleAIAppCreatorTutorialPage />} />
              <Route
                path="agentic-workflow-creator"
                element={<AgenticWorkflowCreatorTutorialPage />}
              />
              <Route path="heygen" element={<HeygenTutorialPage />} />
              <Route path="advanced-genai-creator" element={<AdvancedGenAICreatorTutorialPage />} />
              <Route path="specific-sales-use-cases" element={<SpesificSalesUseCasesPage />} />
            </Route>
            <Route path="sheets">
              <Route path="chatgpt" element={<ChatGPTCheatSheetPage />} />
              <Route path="dall-e" element={<DALLECheatSheetPage />} />
              <Route path="it" element={<ITCheatSheet />} />
            </Route>
            <Route path="heygen-video-creator" element={<HeygenVideoCreatorPage />} />
            <Route path="/ai_workflows/:workflowName" element={<WorkflowRun />} />
            <Route path="share-your-idea" element={<ShareYourIdeaPage />} />
            <Route path="/condition" element={<ConditionPage />} />
            <Route path="/genai-stack" element={<GenAIPage />} />
            {/* Public Routes */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
            <Route path="/terms-and-conditions" element={<TermsAndConditionsPage />} />
            {/* Catch-all route for 404 */}
            <Route path="*" element={<NotFoundPage />} />
          </Route>
        </Route>
      </Routes>
    </Router>
  );
};

export default AppRoutes;
