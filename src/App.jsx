import { Suspense } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import theme from './theme';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';
import MSALProvider from './providers/MSALProvider';
import { BrowserRouter } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Loading component
const LoadingComponent = () => (
  <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
    <CircularProgress />
  </Box>
);

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <MSALProvider>
        <BrowserRouter>
          <Suspense fallback={<LoadingComponent />}>{/* ... diğer bileşenler ... */}</Suspense>
          <ToastContainer />
        </BrowserRouter>
      </MSALProvider>
    </ThemeProvider>
  );
}

export default App;
