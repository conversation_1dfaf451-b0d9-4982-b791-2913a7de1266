import { useLocation, Navigate, Outlet } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { selectCurrentToken, logOut } from '../redux/features/auth/authSlice';
import { useState, useEffect, useCallback } from 'react';
import Onboarding from '../domains/onboarding';
import { jwtDecode } from 'jwt-decode';

const AuthProvider = () => {
  const token = useSelector(selectCurrentToken);
  const user = useSelector((state) => state.auth.user);
  const location = useLocation();
  const dispatch = useDispatch();
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [userData, setUserData] = useState(null);

  // Public routes that don't require authentication
  const publicPaths = ['/login'];
  const isPublicPath = publicPaths.includes(location.pathname);

  // Onboarding'in görünmeyeceği özel sayfalar
  const specialPages = ['/terms-and-conditions', '/privacy-policy'];
  const isSpecialPage = specialPages.includes(location.pathname);

  // Token geçerliliğini kontrol eden fonksiyon
  const isTokenExpired = useCallback((token) => {
    if (!token) return true;

    try {
      const decodedToken = jwtDecode(token);
      const currentTime = Date.now() / 1000;

      // Token'ın süresi dolmuşsa true döndür
      return decodedToken.exp < currentTime;
    } catch (error) {
      console.error('Token decode error:', error);
      return true;
    }
  }, []);

  // Token kontrolü ve gerekirse logout işlemi
  const checkTokenAndLogout = useCallback(() => {
    if (token && isTokenExpired(token) && !isPublicPath) {
      console.log('Token expired, logging out...');
      dispatch(logOut());
    }
  }, [token, isTokenExpired, isPublicPath, dispatch]);

  // Sayfa yüklendiğinde ve token değiştiğinde token kontrolü yap
  useEffect(() => {
    checkTokenAndLogout();
  }, [checkTokenAndLogout]);

  // Periyodik olarak token kontrolü yap (her 5 dakikada bir)
  useEffect(() => {
    if (token && !isPublicPath) {
      const tokenCheckInterval = setInterval(
        () => {
          checkTokenAndLogout();
        },
        5 * 60 * 1000
      ); // 5 dakika

      return () => clearInterval(tokenCheckInterval);
    }
  }, [token, isPublicPath, checkTokenAndLogout]);

  useEffect(() => {
    if (token && user && !user.onboarding && !isPublicPath && !isSpecialPage) {
      setUserData({
        ...user,
        onboarding: null,
      });
      setShowOnboarding(true);
    }
  }, [token, user, isPublicPath, isSpecialPage]);

  if ((!token || isTokenExpired(token)) && !isPublicPath) {
    // Redirect to login if not authenticated and trying to access protected route
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (token && !isTokenExpired(token) && isPublicPath) {
    // Kullanıcı giriş yapmışsa ve state'de bir yönlendirme varsa oraya git
    const redirectTo = location.state?.from?.pathname || '/';
    return <Navigate to={redirectTo} replace />;
  }

  // If the route is public or user is authenticated, render the route
  return (
    <>
      <Outlet />
      <Onboarding open={showOnboarding} userData={userData} />
    </>
  );
};

export default AuthProvider;
