import React, { createContext, useContext, useState, useEffect } from 'react';
import { PublicClientApplication } from '@azure/msal-browser';
import { MSAL_CONFIG } from '../config-global';

// Context oluşturma
const MSALContext = createContext(null);

// MSAL konfigürasyonu
const msalConfig = {
  auth: {
    clientId: MSAL_CONFIG.clientId,
    authority: `https://login.microsoftonline.com/${MSAL_CONFIG.tenantId}`,
    redirectUri: window.location.origin,
  },
  cache: {
    cacheLocation: 'sessionStorage',
    storeAuthStateInCookie: false,
  }
};

// MSAL Public Client Application örneği
const msalInstance = new PublicClientApplication(msalConfig);

// İstek için gerekli bilgiler
const loginRequest = {
  scopes: MSAL_CONFIG.scopes
};

// Context hook'u
export const useMSAL = () => {
  const context = useContext(MSALContext);
  if (!context) {
    throw new Error('useMS<PERSON> hook must be used within an MSALProvider');
  }
  return context;
};

const MSALProvider = ({ children }) => {
  const [account, setAccount] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Sayfa yüklendiğinde, aktif hesabı kontrol et
    const accounts = msalInstance.getAllAccounts();
    if (accounts.length > 0) {
      setAccount(accounts[0]);
      setIsAuthenticated(true);
    }
  }, []);

  // Login işlemi
  const login = async () => {
    try {
      const loginResponse = await msalInstance.loginPopup(loginRequest);
      setAccount(loginResponse.account);
      setIsAuthenticated(true);
      return loginResponse;
    } catch (error) {
      console.error('Login error:', error);
      return null;
    }
  };

  // Logout işlemi
  const logout = () => {
    msalInstance.logoutPopup({
      postLogoutRedirectUri: window.location.origin,
    });
    setAccount(null);
    setIsAuthenticated(false);
  };

  // Microsoft Graph API'ye erişim için token alma
  const getToken = async () => {
    if (!account) return null;

    const silentRequest = {
      scopes: loginRequest.scopes,
      account: account
    };

    try {
      const response = await msalInstance.acquireTokenSilent(silentRequest);
      return response.accessToken;
    } catch (error) {
      // Silent token acquisition failed, fallback to interactive method
      try {
        const response = await msalInstance.acquireTokenPopup(silentRequest);
        return response.accessToken;
      } catch (err) {
        console.error('Token acquisition error:', err);
        return null;
      }
    }
  };

  // Context value
  const contextValue = {
    msalInstance,
    account,
    isAuthenticated,
    login,
    logout,
    getToken
  };

  return (
    <MSALContext.Provider value={contextValue}>
      {children}
    </MSALContext.Provider>
  );
};

export default MSALProvider; 