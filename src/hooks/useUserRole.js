import { useSelector } from 'react-redux';

/**
 * <PERSON>llanıcı rollerini kontrol etmek için kull<PERSON> hook
 * @returns {Object} Kullanıcı rolü kontrol sonuçları
 */
const useUserRole = () => {
  // Redux'tan kullanıcı bilgilerini al
  const user = useSelector((state) => state.auth?.user);

  /**
   * Kullanıcının belirli bir role sahip olup olmadığını kontrol eder
   * @param {string} roleName - Kontrol edilecek rol adı
   * @returns {boolean} Kullanıcı role sahipse true, değilse false
   */
  const hasRole = (roleName) => {
    return user?.role?.toLowerCase() === roleName.toLowerCase();
  };

  /**
   * Kullanıcının administrator rolüne sahip olup olmadığını kontrol eder
   * @returns {boolean} Kullanıcı administrator rolüne sahipse true, değilse false
   */
  const isAdministrator = () => {
    return hasRole('administrator');
  };

  /**
   * <PERSON>llanı<PERSON>ının limited-use rolüne sahip olup olmadığını kontrol eder
   * @returns {boolean} Kullanıcı limited-use rolüne sahipse true, değilse false
   */
  const isLimitedUser = () => {
    return hasRole('limited-use');
  };

  /**
   * Kullanıcının journey bilgilerine göre level'ını döndürür
   * @returns {string} Kullanıcının journey level'ı (lowercase)
   */
  const getUserJourneyLevel = () => {
    return user?.journeyLevel?.name?.toLowerCase() || 'beginner';
  };

  return {
    user,
    hasRole,
    isAdministrator,
    isLimitedUser,
    getUserJourneyLevel,
  };
};

export default useUserRole;
