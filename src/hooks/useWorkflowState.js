import { useState } from 'react';
import { useLocalStorage } from './useLocalStorage';

export const useWorkflowState = () => {
  const [droppedElements, setDroppedElements] = useLocalStorage('dropElementsData', []);

  const [settings, setSettings] = useState({
    temperature: 1,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
  });

  const handleElementChange = (id, field, value) => {
    setDroppedElements((elements) =>
      elements.map((element) => (element.id === id ? { ...element, [field]: value } : element))
    );
  };

  return {
    droppedElements,
    setDroppedElements,
    settings,
    setSettings,
    handleElementChange,
  };
};
