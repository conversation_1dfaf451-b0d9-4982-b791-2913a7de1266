/**
 * HTML içeriğini dönüştürmek için kullanılan yardımcı fonksiyon
 * @param {string} htmlContent - Dönüştürülecek HTML içeriği
 * @returns {string} Dönüştürülmüş HTML içeriği
 */

export const transformHtmlContent = (htmlContent) => {
  if (!htmlContent) return '';

  try {
    // Adım 1: HTML içeriğini temizleyelim
    let cleaned = htmlContent
      // Tüm açılan ve kapanan ul etiketlerini kaldır
      .replace(/<\/?ul[^>]*>/g, '')
      // div class="ewa-rteLine" etiketlerini kaldır ama içeriği koru
      .replace(/<div[^>]*class="ewa-rteLine"[^>]*>/g, '')
      .replace(/<\/div>/g, ' ') // div kapanışlarını boşluk ile değiştir
      // li etiketlerini p etiketlerine dönüştür (class korunarak)
      .replace(/<li([^>]*)>/g, '<p$1>')
      .replace(/<\/li>/g, '</p>')
      // Ardışık boşlukları tek boşluğa dönüştür
      .replace(/\s+/g, ' ')
      // HTML karakter kodlarını düzelt
      .replace(/&nbsp;/g, ' ')
      // Boş p etiketlerini kaldır
      .replace(/<p>\s*<\/p>/g, '')
      // Satır başı karakterlerini kaldır
      .replace(/\\n/g, ' ')
      // Başlangıç ve sondaki boşlukları temizle
      .trim();

    // Adım 2: DOM manipülasyonu ile daha temiz metin elde etme
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = cleaned;

    // Adım 3: Başlık (h1, h2, ..., h6) içindeki paragrafları düzeltme
    const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach((heading) => {
      const paragraphs = heading.querySelectorAll('p');

      if (paragraphs.length > 0) {
        const parent = heading.parentNode;
        paragraphs.forEach((p) => {
          const clone = p.cloneNode(true);
          parent.insertBefore(clone, heading);
        });
        parent.removeChild(heading);
      } else {
        const textContent = heading.textContent.trim();
        if (textContent) {
          const p = document.createElement('p');
          p.textContent = textContent;
          heading.parentNode.replaceChild(p, heading);
        } else {
          heading.parentNode.removeChild(heading);
        }
      }
    });

    // Adım 4: Kalan p etiketlerini temizle
    const paragraphs = tempDiv.querySelectorAll('p');
    paragraphs.forEach((p) => {
      p.removeAttribute('class');
      const textContent = p.textContent.trim();
      if (textContent) {
        p.innerHTML = textContent;
      }
    });

    return tempDiv.innerHTML;
  } catch (error) {
    console.error('HTML dönüştürme hatası:', error);
    return htmlContent;
  }
};

/**
 * Markdown kod işaretçilerini temizler (```html, ```, ` gibi)
 * @param {string} content - Temizlenecek içerik
 * @returns {string} Temizlenmiş içerik
 */
const cleanMarkdownCodeMarkers = (content) => {
  if (!content) return '';

  return (
    content
      // ```html, ```js gibi başlangıç kod bloğu işaretçilerini temizle
      .replace(
        /```(?:html|jsx|js|css|ts|tsx|json|yaml|xml|markdown|md|bash|sh|text|plain|[a-zA-Z0-9_+-]*)/g,
        ''
      )
      // Kapanış kod bloğu işaretçilerini temizle
      .replace(/```/g, '')
      // Çift backtick'leri temizle
      .replace(/``/g, '')
      // Tek backtick'lerin içeriğini koru ama backtick'leri kaldır
      .replace(/`([^`]*)`/g, '$1')
  );
};

/**
 * API'den gelen HTML içeriğini güvenli bir şekilde görüntülemek için temizleme ve dönüştürme işlemi
 * Bu fonksiyon API'den gelen HTML'i güvenli bir şekilde görüntülemeye hazırlar
 *
 * @param {string} htmlContent - Dönüştürülecek HTML içeriği
 * @param {Object} options - Dönüştürme seçenekleri
 * @param {boolean} options.preserveHtmlTags - HTML etiketlerini koru (varsayılan: true)
 * @param {boolean} options.formatCodeBlocks - Kod bloklarını formatla (varsayılan: true)
 * @returns {string} Temizlenmiş ve hazırlanmış HTML
 */
export const sanitizeApiHtmlContent = (htmlContent, options = {}) => {
  if (!htmlContent) return '';

  const { preserveHtmlTags = true, formatCodeBlocks = true } = options;

  try {
    // HTML içeriğini string'e çevir ve Markdown kod işaretçilerini hemen temizle
    let content = cleanMarkdownCodeMarkers(String(htmlContent));

    // HTML etiketlerini korumak istemiyorsak, HTML'i text'e çevir
    if (!preserveHtmlTags) {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = content;
      return tempDiv.textContent || '';
    }

    // Tehlikeli olabilecek script tagları ve inline event attribute'larını temizle
    content = content
      // Script taglarını kaldır
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      // On* event attribute'larını kaldır
      .replace(/\s+on\w+\s*=\s*["']?[^"'\s>]*["']?/gi, '')
      // href="javascript:..." attribute'larını kaldır
      .replace(/href\s*=\s*["']?javascript:[^"'\s>]*["']?/gi, '');

    // Pre ve Code tagları arasındaki içeriği formatla
    if (formatCodeBlocks) {
      // Kod bloklarını tespit et ve işaretle
      content = content.replace(
        /<pre><code([^>]*)>([\s\S]*?)<\/code><\/pre>/g,
        (match, attributes, codeContent) => {
          // HTML special karakterleri koru
          const escapedContent = codeContent
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');

          return `<pre><code${attributes}>${escapedContent}</code></pre>`;
        }
      );
    }

    return content;
  } catch (error) {
    console.error('HTML sanitize hatası:', error);
    return htmlContent;
  }
};

/**
 * HTML içeriği dönüştürmek için React bileşeni için özel hook
 * @param {string} content - Dönüştürülecek HTML içeriği
 * @param {Object} options - Dönüştürme seçenekleri
 * @returns {Object} Dönüştürülmüş içerik ve yardımcı fonksiyonlar
 */
export const useHtmlTransformer = (content, options = {}) => {
  const {
    sanitize = true,
    preserveHtmlTags = true,
    formatCodeBlocks = true,
    transformHtml = false,
    className = 'html-content',
  } = options;

  // İçeriği işle
  let processedContent = content || '';

  // Sanitize işlemi
  if (sanitize) {
    processedContent = sanitizeApiHtmlContent(processedContent, {
      preserveHtmlTags,
      formatCodeBlocks,
    });
  }

  // Transform işlemi (eski dönüştürücü)
  if (transformHtml) {
    processedContent = transformHtmlContent(processedContent);
  }

  return {
    content: processedContent,
    // dangerouslySetInnerHTML propu artık döndürmüyoruz, bu işi HtmlRenderer halledecek
    className,
    // renderHtml metodu da kaldırıldı, bu işlevi HtmlRenderer üstlendi
  };
};

// Örnek kullanım:
/*
import { transformHtmlContent, useHtmlTransformer, sanitizeApiHtmlContent } from '../middleware/html-transformer';

// Doğrudan fonksiyon olarak kullanım:
const htmlContent = transformHtmlContent(rawContent, {
  sanitize: true,
  allowImages: true,
  allowLinks: true,
  allowedTags: ['p', 'br', 'b']
});

// React bileşeninde hook olarak kullanım:
const MyComponent = ({ content }) => {
  const { dangerouslySetInnerHTML } = useHtmlTransformer(content, {
    sanitize: true,
    allowImages: true
  });

  return <div {...dangerouslySetInnerHTML} />;
};

// renderHtml metodu ile kullanım:
const MyComponent = ({ content }) => {
  const { renderHtml } = useHtmlTransformer(content, {
    sanitize: true,
    className: 'my-custom-class'
  });

  return (
    <div className="container">
      {renderHtml()} // Ya da {renderHtml('override-class')}
    </div>
  );
};
*/
