// Helper functions for condition checking
const evaluateCondition = (condition, userData) => {
  //console.log('Evaluating condition:', {
  //  condition,
  //  userValue: userData[condition.field],
  //  userData

  //});


  // Eğer operator "all" ise, her zaman true dön
  if (condition.operator === 'all') {
    return true;
  }

  const userValue = userData[condition.field];
  
  switch (condition.operator) {
    case '==':
     // console.log('Comparing:', userValue, '===', condition.value);
      return userValue === condition.value;
    case '!=':
      return userValue !== condition.value;
    case 'includes':
      return Array.isArray(userValue) && userValue.includes(condition.value);
    default:
      return true;
  }
};

const checkConditions = (conditions, userData) => {
  if (!conditions || conditions.length === 0) return true;
  return conditions.every(condition => 
    evaluateCondition(condition, userData)
  );
};

// Process nested blocks
const processNestedBlocks = (block, userData) => {
  if (block.props?.children && Array.isArray(block.props.children)) {
    return {
      ...block,
      props: {
        ...block.props,
        children: block.props.children.filter(child => 
          checkConditions(child.props?.condition, userData)
        )
      }
    };
  }
  return block;
};

// Main personalization functions
export const personalizeApi = {
  // For API responses
  transformResponse: (response, userData) => {
    if (!response || !userData) return response;

    return {
      ...response,
      data: Array.isArray(response.data) 
        ? response.data.filter(item => checkConditions(item.conditionRules, userData))
        : response.data
    };
  },

  // For page content (mockPageData)
  filterPageContent: (content, userData) => {
    // Önce filtreleme
    const filteredContent = content.filter(block => {
      if (!block.props.condition) return true;
      return block.props.condition.every(condition => {

        // Segment kontrolü
        if (condition.segment) {
          return userData.segment?._id === condition.segment;
        }
        const userValue = userData[condition.field]; 
        switch (condition.operator) {
          case 'includes':
            return Array.isArray(condition.value) && condition.value.includes(userValue.slug);
          case '==':
            return userValue.slug === condition.value;
          case '!=':
            return userValue.slug !== condition.value;
          case '>':
            return userValue.slug > condition.value;
          case '<':
            return userValue.slug < condition.value;
          case '>=':
            return userValue.slug >= condition.value;
          case '<=':
            return userValue.slug <= condition.value;
          case 'in':
            return Array.isArray(condition.value) && condition.value.includes(userValue.slug);
          case 'not_in':
            return Array.isArray(condition.value) && !condition.value.includes(userValue.slug);
          default:
            return false;
        }
      });
    });

    // Sonra sıralama
    return filteredContent.sort((a, b) => {
      const aProps = a.props;
      const bProps = b.props;

      // Her iki öğe için uygun sorting rule'u bul
      const aRule = aProps.sorting?.rules?.find(rule => 
        rule.conditions.every(condition => 
          userData[condition.field] === condition.value
        )
      );

      const bRule = bProps.sorting?.rules?.find(rule => 
        rule.conditions.every(condition => 
          userData[condition.field] === condition.value
        )
      );

      // Eğer rule bulunduysa order'ı kullan, bulunamadıysa default_order'ı kullan
      const aOrder = aRule?.order || aProps.sorting?.default_order || 999;
      const bOrder = bRule?.order || bProps.sorting?.default_order || 999;

      return aOrder - bOrder;
    });
  },

  // For replacing variables in strings
  replaceVariables: (text, userData) => {
    if (typeof text !== 'string') return text;

    return text
      .replace('{username}', userData?.name || '')
      .replace('{userFunction}', userData?.onboarding?.function_label || '')
      .replace('{userLanguage}', userData?.language || '')
      // Add more replacements as needed
  },

  // Process props to replace variables
  processProps: (props, userData) => {
    const processedProps = { ...props };
    
    // Props içindeki değerleri işle
    Object.keys(processedProps).forEach(key => {
      if (typeof processedProps[key] === 'string') {
        processedProps[key] = processedProps[key].replace(
          /\{([^}]+)\}/g,
          (match, field) => {
            // Özel durumlar için mapping
            const mappings = {
              username: userData.name,
              userFunction: userData.function_label,
              // Diğer özel mappingler buraya eklenebilir
            };

            // Önce özel mapping'e bak
            if (mappings.hasOwnProperty(field)) {
              return mappings[field] || '';
            }

            // Eğer özel mapping yoksa direkt userData'dan al
            return userData[field] || '';
          }
        );
      }
    });

    return processedProps;
  },

  getSortingOrder: (item, userData) => {
    const sorting = item.props.sorting;
    if (!sorting) return sorting?.default_order || 999;

    // Eşleşen ilk rule'u bul
    const matchedRule = sorting.rules?.find(rule => 
      rule.conditions.every(condition => {
        const userValue = userData[condition.field];
        return userValue === condition.value;
      })
    );

    return matchedRule?.order || sorting.default_order || 999;
  }
}; 