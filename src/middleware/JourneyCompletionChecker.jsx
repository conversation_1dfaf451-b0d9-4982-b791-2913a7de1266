import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useGetJourneysQuery, useGetJourneyTrackingQuery } from '@/redux/services/journey-api';
import useJourneyLevelUpgrade from '../domains/journey/hooks/useJourneyLevelUpgrade';
import useJourneyCardHandlers from '../domains/journey/hooks/useJourneyCardHandlers';
import JourneyLevelUpModal from '../domains/journey/components/JourneyLevelUpModal';
import { developmentLogs } from '@/utils/developmentLogs';

/**
 * Herhangi bir sayfada journey tamamlanma durumunu kontrol eden ve
 * tamamlanma modalını gösteren bileşen.
 *
 * Bu bileşen Layout.jsx içinde kullanılmalıdır.
 */
const JourneyCompletionChecker = () => {
  const user = useSelector((state) => state.auth.user);
  const selectedLevel = useSelector((state) => state.training.selectedLevel);
  const [isInitialized, setIsInitialized] = useState(false);

  // Journey level upgrade hook'unu kullan
  const {
    showConfetti,
    showCongratulations,
    completedLevel,
    handleCloseCongratulations,
    checkAndUpgradeLevel,
  } = useJourneyLevelUpgrade();

  // Journey verilerini ve tracking verilerini çekiyoruz
  const { data: journeyData, refetch: refetchJourneys } = useGetJourneysQuery(
    {
      function: user?.onboarding?.function_label,
      levels: selectedLevel || user?.journeyLevel?.name,
      managementRole: user?.onboarding?.management_role_label?.slug,
    },
    {
      skip: !user?._id || (!selectedLevel && !user?.journeyLevel?.name),
    }
  );

  // User ID kontrolü
  const { data: trackingData, refetch: refetchTracking } = useGetJourneyTrackingQuery(user?._id, {
    skip: !user?._id,
    refetchOnMountOrArgChange: true,
  });

  // JourneyCard işlemleri için hook'u kullan (modal içeriği için)
  const { getCurrentLevelModalContent } = useJourneyCardHandlers({
    trackingData,
    selectedLevel: selectedLevel || user?.journeyLevel?.name,
    user,
  });

  // Tracking güncellemelerini dinle
  useEffect(() => {
    // localStorage'daki güncellemeleri dinle
    const handleStorageChange = (e) => {
      if (e.key === 'journey_tracking_updated') {
        developmentLogs('Journey tracking updated detected in localStorage');
        refetchTracking();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Custom event'i dinle
    const handleTrackingUpdate = (e) => {
      developmentLogs('Journey tracking update event received', e?.detail);

      // Önce tracking verilerini yenile
      refetchTracking().then(() => {
        // Tracking verisi güncellendiğinde, seviye tamamlanma durumunu kontrol et
        if (user?._id) {
          setTimeout(() => {
            // Journey verilerini de yenile
            refetchJourneys().then((journeyResult) => {
              const updatedJourneyData = journeyResult?.data;

              if (updatedJourneyData?.length > 0 && trackingData) {
                checkAndUpgradeLevel({
                  journeyData: updatedJourneyData,
                  trackingData,
                  selectedLevel: selectedLevel || user?.journeyLevel?.name,
                  user,
                  refetchJourneys,
                  refetchTracking,
                });

                developmentLogs('Journey completion check triggered after tracking update');
              }
            });
          }, 500); // Tracking verisinin güncellenmesi için kısa bir süre bekle
        }
      });
    };

    window.addEventListener('update_training_progress', handleTrackingUpdate);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('update_training_progress', handleTrackingUpdate);
    };
  }, [refetchTracking, refetchJourneys, trackingData, user, selectedLevel, checkAndUpgradeLevel]);

  // Journey tamamlanma kontrolü
  useEffect(() => {
    if (!isInitialized && journeyData?.length > 0 && trackingData && user?._id) {
      // Level yükseltme işlemini hook ile kontrol et
      checkAndUpgradeLevel({
        journeyData,
        trackingData,
        selectedLevel: selectedLevel || user?.journeyLevel?.name,
        user,
        refetchJourneys,
        refetchTracking,
      });

      setIsInitialized(true);
    }
  }, [
    journeyData,
    trackingData,
    selectedLevel,
    user,
    refetchJourneys,
    refetchTracking,
    isInitialized,
    checkAndUpgradeLevel,
  ]);

  // Tracking verisi değiştiğinde tekrar kontrol et
  useEffect(() => {
    if (isInitialized && journeyData?.length > 0 && trackingData && user?._id) {
      // Level yükseltme işlemini hook ile kontrol et
      checkAndUpgradeLevel({
        journeyData,
        trackingData,
        selectedLevel: selectedLevel || user?.journeyLevel?.name,
        user,
        refetchJourneys,
        refetchTracking,
      });
    }
  }, [trackingData]);

  if (!user) return null;

  return (
    <JourneyLevelUpModal
      open={showCongratulations}
      onClose={handleCloseCongratulations}
      modalContent={
        getCurrentLevelModalContent(
          completedLevel || selectedLevel || user?.journeyLevel?.name,
          true
        )?.content || ''
      }
      showConfetti={showConfetti}
      levelData={
        completedLevel ? getCurrentLevelModalContent(completedLevel, false)?.levelData : null
      }
    />
  );
};

export default JourneyCompletionChecker;
