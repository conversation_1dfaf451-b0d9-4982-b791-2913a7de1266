// Rule değerlendirme fonksiyonu
const evaluateRule = (rule, userData) => {
  const value = userData[rule.field];

  switch (rule.operator) {
    case 'in':
      const isIn = Array.isArray(rule.value) && rule.value.includes(value);
      return isIn;
    case 'not_in':
      const isNotIn = Array.isArray(rule.value) && !rule.value.includes(value);
      return isNotIn;
    case 'equals':
      const isEqual = value === rule.value;
      return isEqual;
    case 'greater_than':
      const isGreater = parseFloat(value) > parseFloat(rule.value);
      return isGreater;
    case 'exists':
      const exists = value !== undefined && value !== null;
      return exists;
    default:
      return false;
  }
};

// Rule group değerlendirme fonksiyonu
const evaluateRuleGroup = (group, userData) => {
  if (!group || !group.rules) {
    return false;
  }

  const result =
    group.logicalOperator === 'AND'
      ? group.rules.every((rule) => {
          const ruleResult =
            rule.type === 'GROUP'
              ? evaluateRuleGroup(rule, userData)
              : evaluateRule(rule, userData);

          return ruleResult;
        })
      : group.rules.some((rule) => {
          const ruleResult =
            rule.type === 'GROUP'
              ? evaluateRuleGroup(rule, userData)
              : evaluateRule(rule, userData);
          return ruleResult;
        });
  return result;
};

// User'ın hangi segmente ait olduğunu bulan fonksiyon
const findUserSegment = (segments, userData) => {
  if (!segments || !Array.isArray(segments)) {
    return null;
  }

  // Priority'ye göre sırala (yüksekten düşüğe)
  const sortedSegments = [...segments].sort((a, b) => a.priority - b.priority);

  // İlk eşleşen segmenti bul
  const matchedSegment = sortedSegments.find((segment) => {
    if (segment.ruleGroup) {
      const result = evaluateRuleGroup(segment.ruleGroup, userData);

      return result;
    } else if (segment.rules) {
      const result = evaluateRuleGroup(
        {
          type: 'GROUP',
          logicalOperator: 'AND',
          rules: segment.rules,
        },
        userData
      );

      return result;
    }
    return false;
  });

  if (!matchedSegment) {
    return sortedSegments.find((segment) => segment.name === 'Standard User') || null;
  }

  return matchedSegment;
};

export const segmentEvaluator = {
  evaluateRule,
  evaluateRuleGroup,
  findUserSegment,
};
