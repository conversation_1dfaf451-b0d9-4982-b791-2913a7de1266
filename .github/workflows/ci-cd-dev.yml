name: deploy-to-development-server

on:
  push:
    tags:
      - '[0-9]+.[0-9]+.[0-9]+-dev'

jobs:
  adoptionv2dev-aibusinessschool-com:
    name: adoptionv2dev-aibusinessschool-com
    runs-on: vm-adoptionv2devweb
    steps:
      - name: clone repository
        run: |
          cd /home/<USER>/devops
          sudo rm -rf ${{ github.event.repository.name }}
          sudo git clone --branch dev https://${{ secrets.ADOPTIONV2 }}@github.com/${{ github.repository_owner }}/${{ github.event.repository.name }}.git
          
      - name: docker build & run
        run: |
          cd /home/<USER>/devops
          sudo docker compose build --build-arg BUILD_ENV=development
          sudo docker compose up -d --force-recreate
          
      - name: list running docker containers
        run: |
          sudo docker ps